2025-07-31 01:44:40,384 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: fc5b22e9
2025-07-31 01:44:40,384 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 01:44:40,384 - INFO - ✅ Selenium متوفر
2025-07-31 01:44:40,388 - INFO - ✅ Playwright متوفر
2025-07-31 01:44:40,641 - INFO - ✅ Pillow متوفر
2025-07-31 01:44:40,641 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 01:44:41,454 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 01:44:41,458 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 01:44:41,825 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:44:41,835 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 01:44:50,360 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 01:44:50,361 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 01:44:50,361 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:44:50,551 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_001\before_diagnostic_test.png (73533 bytes)
2025-07-31 01:44:51,321 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Test XSS
2025-07-31 01:44:51,321 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Test XSS
2025-07-31 01:44:51,321 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 01:44:51,321 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 01:44:51,321 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 01:44:51,322 - INFO -    - vulnerability_type: XSS
2025-07-31 01:44:51,322 - INFO -    - vulnerability_name: Test XSS
2025-07-31 01:44:51,322 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 01:44:51,322 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 01:44:51,322 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 01:44:51,322 - INFO -    - Vulnerability: Test XSS
2025-07-31 01:44:51,323 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 01:44:51,323 - INFO -    - Type: XSS
2025-07-31 01:44:51,323 - INFO -    - Evidence count: 4
2025-07-31 01:44:51,323 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 01:44:51,323 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 01:44:51,323 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:44:51,323 - INFO -    - actual_response_content: 28 حرف - Test response from v4 system...
2025-07-31 01:44:51,324 - INFO -    - vulnerability_impact_data: 34 حرف - High impact vulnerability detected...
2025-07-31 01:44:51,324 - INFO -    - exploitation_results: <class 'list'> - ['Result 1', 'Result 2']
2025-07-31 01:44:51,324 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:44:51,324 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 01:44:51,324 - INFO -    - raw_actual_response: 28 حرف
2025-07-31 01:44:51,325 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 01:44:51,325 - INFO -    - raw_vulnerability_impact: 34 حرف
2025-07-31 01:44:51,325 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 01:44:51,325 - INFO -    - js_vulnerability_name: Test XSS
2025-07-31 01:44:51,325 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 01:44:51,325 - INFO -    - v4_actual_response: 28 حرف
2025-07-31 01:44:51,325 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 01:44:51,339 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Identifier 'vulnerabilityName' has already been declared
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 01:44:51,366 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/json - المرحلة: after
2025-07-31 01:44:51,422 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:44:51,431 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/json
2025-07-31 01:44:51,432 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 01:45:04,455 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 01:45:04,455 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=SQL Injection
2025-07-31 01:45:04,455 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:45:04,455 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Test
2025-07-31 01:45:04,455 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Test
2025-07-31 01:45:04,455 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 01:45:04,455 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 01:45:04,455 - INFO -    - payload_data: None
2025-07-31 01:45:04,455 - INFO -    - vulnerability_type: SQL Injection
2025-07-31 01:45:04,464 - INFO -    - vulnerability_name: SQL Injection Test
2025-07-31 01:45:04,464 - INFO -    - real_payload: None
2025-07-31 01:45:04,466 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 01:45:04,466 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 01:45:04,468 - INFO -    - Vulnerability: SQL Injection Test
2025-07-31 01:45:04,469 - INFO -    - Payload: None
2025-07-31 01:45:04,470 - INFO -    - Type: SQL Injection
2025-07-31 01:45:04,470 - INFO -    - Evidence count: 4
2025-07-31 01:45:04,471 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 01:45:04,472 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 01:45:04,473 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_content', 'test_results']
2025-07-31 01:45:04,473 - INFO -    - actual_response_content: 41 حرف - This is real response data from v4 system...
2025-07-31 01:45:04,474 - INFO -    - vulnerability_impact_data: 39 حرف - Critical vulnerability with high impact...
2025-07-31 01:45:04,475 - INFO -    - exploitation_results: <class 'list'> - ['SQL injection successful', 'Database access gained', 'Sensitive data extracted']
2025-07-31 01:45:04,475 - INFO -    - response_content: 27 حرف - Additional response content...
2025-07-31 01:45:04,475 - INFO -    - test_results: <class 'list'> - ['Test 1 passed', 'Test 2 passed']
2025-07-31 01:45:04,475 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results', 'response_content', 'test_results']
2025-07-31 01:45:04,475 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 01:45:04,478 - INFO -    - raw_actual_response: 41 حرف
2025-07-31 01:45:04,478 - INFO -    - raw_exploitation_results: 3 نتيجة
2025-07-31 01:45:04,479 - INFO -    - raw_vulnerability_impact: 39 حرف
2025-07-31 01:45:04,479 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 01:45:04,480 - INFO -    - js_vulnerability_name: SQL Injection Test
2025-07-31 01:45:04,480 - INFO -    - js_payload: No payload...
2025-07-31 01:45:04,481 - INFO -    - v4_actual_response: 41 حرف
2025-07-31 01:45:04,481 - INFO -    - v4_exploitation_results: 3 نتيجة
2025-07-31 01:45:04,497 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Identifier 'vulnerabilityName' has already been declared
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 01:45:09,510 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 01:45:09,710 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_002\after_v4_integration_test.png (19646 bytes)
2025-07-31 01:45:09,711 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get - المرحلة: before
2025-07-31 01:45:09,766 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:45:09,774 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get
2025-07-31 01:45:15,707 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 01:45:15,707 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('stage test')</script>, type=XSS
2025-07-31 01:45:15,707 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:45:15,872 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_003\before_stage_before_test.png (32089 bytes)
2025-07-31 01:45:15,874 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get - المرحلة: during
2025-07-31 01:45:15,927 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:45:15,936 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get
2025-07-31 01:45:21,651 - INFO - 🔥 تطبيق تأثيرات المرحلة: during
2025-07-31 01:45:21,651 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('stage test')</script>, type=XSS
2025-07-31 01:45:21,651 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:45:21,840 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_003\during_stage_during_test.png (32045 bytes)
2025-07-31 01:45:21,841 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get - المرحلة: after
2025-07-31 01:45:21,896 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:45:21,903 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get
2025-07-31 01:45:21,903 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 01:45:39,984 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 01:45:39,984 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('stage test')</script>, type=XSS
2025-07-31 01:45:39,984 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:45:39,987 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Multi-Stage Test
2025-07-31 01:45:39,987 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Multi-Stage Test
2025-07-31 01:45:39,987 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 01:45:39,987 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 01:45:39,987 - INFO -    - payload_data: <script>alert('stage test')</script>
2025-07-31 01:45:39,992 - INFO -    - vulnerability_type: XSS
2025-07-31 01:45:39,994 - INFO -    - vulnerability_name: Multi-Stage Test
2025-07-31 01:45:39,995 - INFO -    - real_payload: <script>alert('stage test')</script>
2025-07-31 01:45:39,996 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 01:45:39,996 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 01:45:39,997 - INFO -    - Vulnerability: Multi-Stage Test
2025-07-31 01:45:39,998 - INFO -    - Payload: <script>alert('stage test')</script>
2025-07-31 01:45:39,998 - INFO -    - Type: XSS
2025-07-31 01:45:40,000 - INFO -    - Evidence count: 4
2025-07-31 01:45:40,000 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 01:45:40,001 - WARNING -    - v4_real_data فارغ أو None!
2025-07-31 01:45:40,002 - WARNING - ⚠️ v4_real_data فارغ أو None!
2025-07-31 01:45:40,002 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 01:45:40,003 - INFO -    - js_vulnerability_name: Multi-Stage Test
2025-07-31 01:45:40,003 - INFO -    - js_payload: <script>alert(\'stage test\')</script>...
2025-07-31 01:45:40,003 - INFO -    - v4_actual_response: 0 حرف
2025-07-31 01:45:40,003 - INFO -    - v4_exploitation_results: 0 نتيجة
2025-07-31 01:45:40,025 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Identifier 'vulnerabilityName' has already been declared
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 01:45:45,033 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 01:45:45,207 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\diagnostic_003\after_stage_after_test.png (34728 bytes)
2025-07-31 01:45:46,300 - INFO - ✅ تم إغلاق Playwright
2025-07-31 01:56:01,146 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 5867486e
2025-07-31 01:56:01,146 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 01:56:01,147 - INFO - ✅ Selenium متوفر
2025-07-31 01:56:01,147 - INFO - ✅ Playwright متوفر
2025-07-31 01:56:01,289 - INFO - ✅ Pillow متوفر
2025-07-31 01:56:01,291 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 01:56:01,934 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 01:56:03,716 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 01:56:03,717 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 01:56:03,717 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 01:56:03,717 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 01:56:03,717 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 01:56:03,717 - INFO -    - vulnerability_type: XSS
2025-07-31 01:56:03,717 - INFO -    - vulnerability_name: Visual Test
2025-07-31 01:56:03,719 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 01:56:03,719 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 01:56:03,719 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 01:56:03,719 - INFO -    - Vulnerability: Visual Test
2025-07-31 01:56:03,720 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 01:56:03,720 - INFO -    - Type: XSS
2025-07-31 01:56:03,720 - INFO -    - Evidence count: 4
2025-07-31 01:56:03,720 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 01:56:03,721 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 01:56:03,721 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:56:03,721 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 01:56:03,721 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 01:56:03,722 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 01:56:03,722 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:56:03,722 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 01:56:03,722 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 01:56:03,722 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 01:56:03,722 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 01:56:03,723 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 01:56:03,723 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 01:56:03,723 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 01:56:03,723 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 01:56:03,723 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 01:56:03,740 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 01:56:08,780 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 01:56:08,838 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:56:08,845 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 01:56:08,846 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 01:56:20,882 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 01:56:20,882 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 01:56:20,882 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:56:20,882 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 01:56:20,888 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 01:56:20,888 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 01:56:20,888 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 01:56:20,890 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 01:56:20,890 - INFO -    - vulnerability_type: XSS
2025-07-31 01:56:20,890 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 01:56:20,893 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 01:56:20,894 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 01:56:20,895 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 01:56:20,896 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 01:56:20,897 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 01:56:20,897 - INFO -    - Type: XSS
2025-07-31 01:56:20,898 - INFO -    - Evidence count: 4
2025-07-31 01:56:20,898 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 01:56:20,899 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 01:56:20,899 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:56:20,899 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 01:56:20,899 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 01:56:20,901 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 01:56:20,901 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:56:20,901 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 01:56:20,903 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 01:56:20,903 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 01:56:20,903 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 01:56:20,904 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 01:56:20,904 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 01:56:20,904 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 01:56:20,905 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 01:56:20,905 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 01:56:20,923 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 01:56:25,930 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 01:56:26,149 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 01:56:47,038 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 01:56:47,093 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:56:47,099 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 01:56:53,479 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 01:56:53,479 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 01:56:53,479 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:56:53,676 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 01:56:53,676 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 01:56:53,735 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 01:56:53,743 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 01:56:53,744 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 01:57:05,701 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 01:57:05,701 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 01:57:05,701 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 01:57:05,701 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 01:57:05,701 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 01:57:05,701 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 01:57:05,701 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 01:57:05,701 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 01:57:05,701 - INFO -    - vulnerability_type: XSS
2025-07-31 01:57:05,701 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 01:57:05,701 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 01:57:05,710 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 01:57:05,712 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 01:57:05,712 - INFO -    - Vulnerability: Comparison Test
2025-07-31 01:57:05,713 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 01:57:05,714 - INFO -    - Type: XSS
2025-07-31 01:57:05,715 - INFO -    - Evidence count: 4
2025-07-31 01:57:05,715 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 01:57:05,716 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 01:57:05,718 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:57:05,718 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 01:57:05,719 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 01:57:05,720 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 01:57:05,720 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 01:57:05,720 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 01:57:05,720 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 01:57:05,720 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 01:57:05,720 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 01:57:05,720 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 01:57:05,720 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 01:57:05,720 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 01:57:05,724 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 01:57:05,724 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 01:57:05,741 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 01:57:10,746 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 01:57:10,939 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\after_comparison_after.png (73533 bytes)
2025-07-31 01:57:11,719 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:03:01,452 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 6d5916fa
2025-07-31 02:03:01,452 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:03:01,454 - INFO - ✅ Selenium متوفر
2025-07-31 02:03:01,454 - INFO - ✅ Playwright متوفر
2025-07-31 02:03:01,594 - INFO - ✅ Pillow متوفر
2025-07-31 02:03:01,594 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:03:02,193 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:03:06,561 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Test
2025-07-31 02:03:06,561 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Test
2025-07-31 02:03:06,561 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:03:06,561 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:03:06,561 - INFO -    - payload_data: <script>alert('test')</script>
2025-07-31 02:03:06,562 - INFO -    - vulnerability_type: XSS
2025-07-31 02:03:06,562 - INFO -    - vulnerability_name: Visual Test
2025-07-31 02:03:06,562 - INFO -    - real_payload: <script>alert('test')</script>
2025-07-31 02:03:06,562 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:03:06,562 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:03:06,563 - INFO -    - Vulnerability: Visual Test
2025-07-31 02:03:06,563 - INFO -    - Payload: <script>alert('test')</script>
2025-07-31 02:03:06,563 - INFO -    - Type: XSS
2025-07-31 02:03:06,563 - INFO -    - Evidence count: 4
2025-07-31 02:03:06,563 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:03:06,564 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:03:06,564 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:03:06,564 - INFO -    - actual_response_content: 18 حرف - Test response data...
2025-07-31 02:03:06,564 - INFO -    - vulnerability_impact_data: 16 حرف - High impact test...
2025-07-31 02:03:06,565 - INFO -    - exploitation_results: <class 'list'> - ['Test result 1', 'Test result 2']
2025-07-31 02:03:06,565 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:03:06,565 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:03:06,565 - INFO -    - raw_actual_response: 18 حرف
2025-07-31 02:03:06,565 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:03:06,565 - INFO -    - raw_vulnerability_impact: 16 حرف
2025-07-31 02:03:06,565 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:03:06,565 - INFO -    - js_vulnerability_name: Visual Test
2025-07-31 02:03:06,567 - INFO -    - js_payload: <script>alert(\'test\')</script>...
2025-07-31 02:03:06,567 - INFO -    - v4_actual_response: 18 حرف
2025-07-31 02:03:06,567 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:03:06,579 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:03:11,628 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:03:11,677 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:03:11,684 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:03:11,684 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:03:23,735 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:03:23,735 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('visual test')</script>, type=XSS
2025-07-31 02:03:23,735 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:03:23,735 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Visual Diagnostic Test
2025-07-31 02:03:23,735 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Visual Diagnostic Test
2025-07-31 02:03:23,735 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:03:23,735 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:03:23,743 - INFO -    - payload_data: <script>alert('visual test')</script>
2025-07-31 02:03:23,743 - INFO -    - vulnerability_type: XSS
2025-07-31 02:03:23,743 - INFO -    - vulnerability_name: Visual Diagnostic Test
2025-07-31 02:03:23,745 - INFO -    - real_payload: <script>alert('visual test')</script>
2025-07-31 02:03:23,746 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:03:23,746 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:03:23,747 - INFO -    - Vulnerability: Visual Diagnostic Test
2025-07-31 02:03:23,749 - INFO -    - Payload: <script>alert('visual test')</script>
2025-07-31 02:03:23,749 - INFO -    - Type: XSS
2025-07-31 02:03:23,750 - INFO -    - Evidence count: 4
2025-07-31 02:03:23,750 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:03:23,750 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:03:23,752 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:03:23,752 - INFO -    - actual_response_content: 31 حرف - Visual diagnostic test response...
2025-07-31 02:03:23,755 - INFO -    - vulnerability_impact_data: 22 حرف - Critical visual impact...
2025-07-31 02:03:23,755 - INFO -    - exploitation_results: <class 'list'> - ['Visual test 1', 'Visual test 2']
2025-07-31 02:03:23,755 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:03:23,757 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:03:23,757 - INFO -    - raw_actual_response: 31 حرف
2025-07-31 02:03:23,758 - INFO -    - raw_exploitation_results: 2 نتيجة
2025-07-31 02:03:23,758 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:03:23,758 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:03:23,760 - INFO -    - js_vulnerability_name: Visual Diagnostic Test
2025-07-31 02:03:23,760 - INFO -    - js_payload: <script>alert(\'visual test\')</script>...
2025-07-31 02:03:23,761 - INFO -    - v4_actual_response: 31 حرف
2025-07-31 02:03:23,761 - INFO -    - v4_exploitation_results: 2 نتيجة
2025-07-31 02:03:23,764 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:03:28,790 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:03:29,010 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\visual_diagnostic\after_visual_diagnostic_test.png (73533 bytes)
2025-07-31 02:03:49,844 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: before
2025-07-31 02:03:49,894 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:03:49,900 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:03:55,596 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-31 02:03:55,596 - INFO - 🔥 استخدام البيانات الحقيقية: payload=None, type=Unknown
2025-07-31 02:03:55,596 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:03:55,823 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\before_comparison_before.png (73533 bytes)
2025-07-31 02:03:55,824 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/html - المرحلة: after
2025-07-31 02:03:55,873 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-31 02:03:55,880 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/html
2025-07-31 02:03:55,880 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-31 02:04:08,168 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-31 02:04:08,168 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('comparison')</script>, type=XSS
2025-07-31 02:04:08,168 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: name 'exploitation_result' is not defined
2025-07-31 02:04:08,168 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Comparison Test
2025-07-31 02:04:08,173 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Comparison Test
2025-07-31 02:04:08,173 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-31 02:04:08,173 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-31 02:04:08,175 - INFO -    - payload_data: <script>alert('comparison')</script>
2025-07-31 02:04:08,175 - INFO -    - vulnerability_type: XSS
2025-07-31 02:04:08,175 - INFO -    - vulnerability_name: Comparison Test
2025-07-31 02:04:08,175 - INFO -    - real_payload: <script>alert('comparison')</script>
2025-07-31 02:04:08,179 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-31 02:04:08,179 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-31 02:04:08,181 - INFO -    - Vulnerability: Comparison Test
2025-07-31 02:04:08,181 - INFO -    - Payload: <script>alert('comparison')</script>
2025-07-31 02:04:08,182 - INFO -    - Type: XSS
2025-07-31 02:04:08,183 - INFO -    - Evidence count: 4
2025-07-31 02:04:08,183 - INFO - 🔥 فحص البيانات الحقيقية المُمررة:
2025-07-31 02:04:08,184 - INFO -    - v4_real_data موجود: <class 'dict'>
2025-07-31 02:04:08,184 - INFO -    - المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:04:08,185 - INFO -    - actual_response_content: 20 حرف - Comparison test data...
2025-07-31 02:04:08,185 - INFO -    - vulnerability_impact_data: 22 حرف - High impact comparison...
2025-07-31 02:04:08,185 - INFO -    - exploitation_results: <class 'list'> - ['Comparison result 1']
2025-07-31 02:04:08,185 - INFO - ✅ v4_real_data موجود! المفاتيح: ['actual_response_content', 'vulnerability_impact_data', 'exploitation_results']
2025-07-31 02:04:08,185 - INFO - 📊 البيانات الخام من v4_real_data:
2025-07-31 02:04:08,185 - INFO -    - raw_actual_response: 20 حرف
2025-07-31 02:04:08,185 - INFO -    - raw_exploitation_results: 1 نتيجة
2025-07-31 02:04:08,185 - INFO -    - raw_vulnerability_impact: 22 حرف
2025-07-31 02:04:08,188 - INFO - 🔥 تمرير البيانات إلى JavaScript:
2025-07-31 02:04:08,188 - INFO -    - js_vulnerability_name: Comparison Test
2025-07-31 02:04:08,189 - INFO -    - js_payload: <script>alert(\'comparison\')</script>...
2025-07-31 02:04:08,189 - INFO -    - v4_actual_response: 20 حرف
2025-07-31 02:04:08,189 - INFO -    - v4_exploitation_results: 1 نتيجة
2025-07-31 02:04:08,198 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected token '.'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-31 02:04:13,213 - INFO - 📸 التقاط صورة after بعد تطبيق التأثيرات...
2025-07-31 02:04:13,425 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\comparison_test\after_comparison_after.png (73533 bytes)
2025-07-31 02:04:14,213 - INFO - ✅ تم إغلاق Playwright
2025-07-31 02:04:52,008 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: aa4e9a46
2025-07-31 02:04:52,008 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-31 02:04:52,012 - INFO - ✅ Selenium متوفر
2025-07-31 02:04:52,012 - INFO - ✅ Playwright متوفر
2025-07-31 02:04:52,157 - INFO - ✅ Pillow متوفر
2025-07-31 02:04:52,157 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-31 02:04:52,892 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-31 02:04:54,902 - INFO - ✅ تم إغلاق Playwright
