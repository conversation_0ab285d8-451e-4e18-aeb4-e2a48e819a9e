// اختبار إصلاح استخدام بيانات النظام v4 مباشرة
console.log('🔍 اختبار إصلاح استخدام بيانات النظام v4 مباشرة...');

async function testV4DataFix() {
    console.log('🚀 بدء اختبار إصلاح بيانات النظام v4...');
    
    // اختبار ثغرة SQL Injection مع بيانات فريدة من النظام v4
    const sqlTestData = {
        url: 'http://testphp.vulnweb.com/login.php',
        vulnerability_name: 'SQL_Injection_V4_Test',
        vulnerability_type: 'SQL_Injection',
        payload_data: "admin' UNION SELECT 1,database(),version() -- v4_test",
        target_parameter: 'username',
        report_id: `v4_fix_test_sql_${Date.now()}`,
        stage: 'after',
        filename: 'after_SQL_Injection_V4_Test',
        // بيانات فريدة من النظام v4
        v4_data: {
            test_results: [
                'Real HTTP Response Code: 200',
                'SQL Injection Response Size: 4567 characters',
                'Database Version: MySQL 5.7.33',
                'Response Time: 234ms',
                'Payload Successfully Injected: admin\' UNION SELECT 1,database(),version() -- v4_test'
            ],
            exploitation_data: {
                status: 'successful_sql_injection',
                method: 'UNION_based_injection',
                payload_used: "admin' UNION SELECT 1,database(),version() -- v4_test",
                target_url: 'http://testphp.vulnweb.com/login.php',
                evidence_count: 4,
                database_info: 'MySQL 5.7.33 detected'
            },
            verification_data: {
                proof: [
                    'Created SQL injection URL: http://testphp.vulnweb.com/login.php?username=admin\' UNION SELECT 1,database(),version() -- v4_test',
                    'SQL injection response analysis completed: successful',
                    'Database information extracted: MySQL 5.7.33',
                    'Evidence collected: 4 SQL injection indicators',
                    'Vulnerability confirmed: SQL_Injection with UNION attack'
                ]
            },
            response_data: `<!DOCTYPE html>
<html>
<head><title>SQL Injection Test Response</title></head>
<body>
<h1>Login Response - SQL Injection Detected</h1>
<div class="sql-result">
    <p>Database: testphp_db</p>
    <p>Version: MySQL 5.7.33</p>
    <p>User: root@localhost</p>
    <p>Payload: admin' UNION SELECT 1,database(),version() -- v4_test</p>
</div>
<div class="error-message">
    SQL Error: You have an error in your SQL syntax near 'UNION SELECT 1,database(),version()' at line 1
</div>
<script>
    console.log('SQL Injection payload executed successfully');
    document.body.style.border = '5px solid red';
</script>
</body>
</html>`,
            actual_response_content: `🔥 SQL INJECTION EXPLOITATION RESPONSE 🔥

Target: http://testphp.vulnweb.com/login.php
Payload: admin' UNION SELECT 1,database(),version() -- v4_test
Method: UNION-based SQL Injection
Status: SUCCESSFUL EXPLOITATION

Database Information Extracted:
- Database Name: testphp_db
- MySQL Version: 5.7.33
- Current User: root@localhost
- Server Info: MySQL Community Server

SQL Injection Evidence:
1. Payload successfully injected into username parameter
2. UNION SELECT statement executed
3. Database metadata extracted
4. SQL error messages revealed structure
5. Response contains injected data

Security Impact: CRITICAL
- Full database access possible
- Data extraction confirmed
- Authentication bypass achieved
- Server information disclosed

Recommendation: Immediate patching required for SQL injection vulnerability`,
            success_indicators: [
                'SQL Injection vulnerability confirmed',
                'Database information extracted',
                'UNION attack successful',
                'Authentication bypass achieved',
                'Critical security impact identified'
            ],
            error_messages: []
        }
    };
    
    console.log('📤 إرسال بيانات SQL Injection من النظام v4:');
    console.log(`   💉 Payload: ${sqlTestData.payload_data}`);
    console.log(`   📊 Response Size: ${sqlTestData.v4_data.response_data.length} characters`);
    console.log(`   🔍 Evidence Count: ${sqlTestData.v4_data.exploitation_data.evidence_count}`);
    
    try {
        const response = await fetch('http://localhost:8000/v4_website', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(sqlTestData)
        });
        
        console.log(`📡 حالة الاستجابة: ${response.status}`);
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ نجح الطلب!');
            console.log(`📝 الرسالة: ${result.message || 'تم بنجاح'}`);
            
            if (result.success) {
                console.log(`📸 حجم الصورة: ${result.file_size || 0} bytes`);
                console.log(`📁 مسار الصورة: ${result.file_path || 'غير محدد'}`);
                
                // التحقق من استخدام البيانات الصحيحة
                console.log('\n🔍 فحص البيانات المستخدمة:');
                if (result.vulnerability_name_used) {
                    console.log(`   🎯 اسم الثغرة: ${result.vulnerability_name_used}`);
                }
                if (result.vulnerability_type_used) {
                    console.log(`   🔍 نوع الثغرة: ${result.vulnerability_type_used}`);
                }
                if (result.payload_used) {
                    console.log(`   💉 Payload: ${result.payload_used}`);
                }
                
                // فحص البيانات من النظام v4
                if (result.v4_data_used) {
                    console.log('\n📊 بيانات النظام v4 المستخدمة:');
                    console.log(`   ✅ تم استخدام بيانات النظام v4`);
                } else {
                    console.log('\n❌ لم يتم استخدام بيانات النظام v4');
                }
                
            } else {
                console.log(`❌ فشل: ${result.error || 'خطأ غير محدد'}`);
            }
            
        } else {
            const errorText = await response.text();
            console.log(`❌ فشل الطلب: ${response.status}`);
            console.log(`📄 تفاصيل الخطأ: ${errorText}`);
        }
        
    } catch (error) {
        console.error(`❌ خطأ في الطلب: ${error.message}`);
    }
    
    console.log('\n🎯 ملخص الاختبار:');
    console.log('✅ تم اختبار استخدام بيانات النظام v4 مباشرة');
    console.log('📋 تحقق من سجلات سيرفر Python لرؤية البيانات المستخدمة');
    console.log('🔍 ابحث عن "البيانات المستخدمة من النظام v4" في السجلات');
}

// تشغيل الاختبار
testV4DataFix()
    .then(() => {
        console.log('\n🎉 انتهى اختبار إصلاح بيانات النظام v4!');
    })
    .catch(error => {
        console.error('❌ خطأ في اختبار إصلاح بيانات النظام v4:', error);
    });
