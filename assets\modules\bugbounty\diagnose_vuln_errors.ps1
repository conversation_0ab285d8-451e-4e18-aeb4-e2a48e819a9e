# 🔍 فحص شامل لمشاكل vuln is not defined في نظام Bug Bounty v4.0
# تاريخ الإنشاء: $(Get-Date)

Write-Host "🔍 بدء الفحص الشامل لمشاكل vuln is not defined..." -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Yellow

$filePath = "assets/modules/bugbounty/BugBountyCore.js"
$totalIssues = 0
$fixedIssues = 0

# 1. البحث عن استخدامات vuln في template strings
Write-Host "`n📋 1. فحص استخدامات vuln في template strings..." -ForegroundColor Green
$templateStringIssues = Select-String -Path $filePath -Pattern '\$\{.*vuln\.' -AllMatches
if ($templateStringIssues) {
    Write-Host "❌ وجدت $($templateStringIssues.Count) استخدام محتمل لـ vuln في template strings:" -ForegroundColor Red
    foreach ($issue in $templateStringIssues) {
        Write-Host "   السطر $($issue.LineNumber): $($issue.Line.Trim())" -ForegroundColor Yellow
        $totalIssues++
    }
} else {
    Write-Host "✅ لا توجد مشاكل في template strings" -ForegroundColor Green
}

# 2. البحث عن استدعاءات دوال بمعاملات خاطئة
Write-Host "`n📋 2. فحص استدعاءات generateInteractiveDialogue..." -ForegroundColor Green
$dialogueIssues = Select-String -Path $filePath -Pattern 'generateInteractiveDialogue\(vuln[,\)]' -AllMatches
if ($dialogueIssues) {
    Write-Host "❌ وجدت $($dialogueIssues.Count) استدعاء خاطئ لـ generateInteractiveDialogue:" -ForegroundColor Red
    foreach ($issue in $dialogueIssues) {
        Write-Host "   السطر $($issue.LineNumber): $($issue.Line.Trim())" -ForegroundColor Yellow
        $totalIssues++
    }
} else {
    Write-Host "✅ جميع استدعاءات generateInteractiveDialogue صحيحة" -ForegroundColor Green
    $fixedIssues++
}

# 3. البحث عن استدعاءات generateDynamicExpertAnalysisForVulnerability
Write-Host "`n📋 3. فحص استدعاءات generateDynamicExpertAnalysisForVulnerability..." -ForegroundColor Green
$expertIssues = Select-String -Path $filePath -Pattern 'generateDynamicExpertAnalysisForVulnerability\(vuln\)' -AllMatches
if ($expertIssues) {
    Write-Host "❌ وجدت $($expertIssues.Count) استدعاء خاطئ لـ generateDynamicExpertAnalysisForVulnerability:" -ForegroundColor Red
    foreach ($issue in $expertIssues) {
        Write-Host "   السطر $($issue.LineNumber): $($issue.Line.Trim())" -ForegroundColor Yellow
        $totalIssues++
    }
} else {
    Write-Host "✅ جميع استدعاءات generateDynamicExpertAnalysisForVulnerability صحيحة" -ForegroundColor Green
    $fixedIssues++
}

# 4. البحث عن تضارب أسماء الدوال
Write-Host "`n📋 4. فحص تضارب أسماء الدوال..." -ForegroundColor Green
$persistentFunctions = Select-String -Path $filePath -Pattern 'generatePersistentResults\(' -AllMatches
$persistentForVulnFunctions = Select-String -Path $filePath -Pattern 'generatePersistentResultsForVulnerability\(' -AllMatches

Write-Host "   وجدت $($persistentFunctions.Count) استدعاء لـ generatePersistentResults" -ForegroundColor Cyan
Write-Host "   وجدت $($persistentForVulnFunctions.Count) استدعاء لـ generatePersistentResultsForVulnerability" -ForegroundColor Cyan

if ($persistentFunctions.Count -gt 1) {
    Write-Host "⚠️ قد يوجد تضارب في أسماء الدوال" -ForegroundColor Yellow
} else {
    Write-Host "✅ لا يوجد تضارب في أسماء الدوال" -ForegroundColor Green
    $fixedIssues++
}

# 5. البحث عن استدعاءات async بدون await
Write-Host "`n📋 5. فحص استدعاءات createVulnerabilityObject بدون await..." -ForegroundColor Green
$asyncIssues = Select-String -Path $filePath -Pattern '(?<!await\s)this\.createVulnerabilityObject\(' -AllMatches
if ($asyncIssues) {
    Write-Host "❌ وجدت $($asyncIssues.Count) استدعاء لـ createVulnerabilityObject بدون await:" -ForegroundColor Red
    foreach ($issue in $asyncIssues) {
        Write-Host "   السطر $($issue.LineNumber): $($issue.Line.Trim())" -ForegroundColor Yellow
        $totalIssues++
    }
} else {
    Write-Host "✅ جميع استدعاءات createVulnerabilityObject تستخدم await" -ForegroundColor Green
    $fixedIssues++
}

# 6. البحث عن متغيرات غير معرفة في النظام المثابر
Write-Host "`n📋 6. فحص متغيرات غير معرفة في النظام المثابر..." -ForegroundColor Green
$persistentErrors = Select-String -Path $filePath -Pattern 'خطأ في النظام المثابر' -Context 2
if ($persistentErrors) {
    Write-Host "⚠️ وجدت $($persistentErrors.Count) مكان يحتوي على رسائل خطأ النظام المثابر" -ForegroundColor Yellow
    foreach ($error in $persistentErrors) {
        Write-Host "   السطر $($error.LineNumber): $($error.Line.Trim())" -ForegroundColor Yellow
    }
}

# 7. فحص استخدامات vuln بدون تعريف
Write-Host "`n📋 7. فحص جميع استخدامات vuln..." -ForegroundColor Green
$vulnUsages = Select-String -Path $filePath -Pattern '\bvuln\.' -AllMatches
Write-Host "   وجدت $($vulnUsages.Count) استخدام لـ vuln في الملف" -ForegroundColor Cyan

# فحص الدوال التي تحتوي على vuln كمعامل
$functionDefinitions = Select-String -Path $filePath -Pattern '^\s*(async\s+)?[a-zA-Z_][a-zA-Z0-9_]*\s*\([^)]*vuln[^)]*\)' -AllMatches
Write-Host "   وجدت $($functionDefinitions.Count) دالة تأخذ vuln كمعامل" -ForegroundColor Cyan

Write-Host "`n" + "=" * 80 -ForegroundColor Yellow
Write-Host "📊 ملخص النتائج:" -ForegroundColor Cyan
Write-Host "   إجمالي المشاكل المكتشفة: $totalIssues" -ForegroundColor $(if ($totalIssues -eq 0) { "Green" } else { "Red" })
Write-Host "   المشاكل المصلحة: $fixedIssues" -ForegroundColor Green
Write-Host "   المشاكل المتبقية: $($totalIssues)" -ForegroundColor $(if ($totalIssues -eq 0) { "Green" } else { "Red" })

if ($totalIssues -eq 0) {
    Write-Host "`n🎉 ممتاز! لا توجد مشاكل vuln is not defined" -ForegroundColor Green
} else {
    Write-Host "`n⚠️ يجب إصلاح المشاكل المتبقية" -ForegroundColor Yellow
}

Write-Host "`n✅ انتهى الفحص الشامل" -ForegroundColor Green
