// اختبار فعلي لثغرات متعددة مع بيانات فريدة ديناميكية
console.log('🔍 اختبار فعلي لثغرات متعددة مع بيانات فريدة...');

// ثغرات حقيقية مختلفة مع payloads فريدة
const realVulnerabilities = [
    {
        name: 'SQL_Injection_Login_Advanced',
        type: 'SQL_Injection',
        payload: "admin' UNION SELECT 1,2,3,database(),version(),user() -- advanced_test_2025",
        parameter: 'username',
        url: 'http://testphp.vulnweb.com/login.php',
        description: 'SQL Injection متقدم في نموذج تسجيل الدخول'
    },
    {
        name: 'XSS_Reflected_Search',
        type: 'Cross_Site_Scripting',
        payload: "<script>document.body.style.background='red';alert('XSS_REFLECTED_2025_SUCCESS');</script>",
        parameter: 'searchFor',
        url: 'http://testphp.vulnweb.com/search.php',
        description: 'XSS منعكس في صفحة البحث'
    },
    {
        name: 'LFI_Directory_Traversal',
        type: 'Local_File_Inclusion',
        payload: "../../../../etc/passwd%00",
        parameter: 'file',
        url: 'http://testphp.vulnweb.com/include.php',
        description: 'Local File Inclusion مع Directory Traversal'
    },
    {
        name: 'Command_Injection_System',
        type: 'Command_Injection',
        payload: "; cat /etc/passwd; whoami; id; uname -a #",
        parameter: 'cmd',
        url: 'http://testphp.vulnweb.com/exec.php',
        description: 'Command Injection في تنفيذ الأوامر'
    },
    {
        name: 'IDOR_User_Profile_Access',
        type: 'Insecure_Direct_Object_Reference',
        payload: "../../admin/users.php?id=1&action=view",
        parameter: 'user_id',
        url: 'http://testphp.vulnweb.com/userinfo.php',
        description: 'IDOR للوصول لملفات المستخدمين'
    }
];

async function testRealDynamicVulnerabilities() {
    console.log('🚀 بدء الاختبار الفعلي للثغرات المتعددة...');
    console.log(`📊 عدد الثغرات للاختبار: ${realVulnerabilities.length}`);
    
    const results = [];
    
    for (let i = 0; i < realVulnerabilities.length; i++) {
        const vuln = realVulnerabilities[i];
        console.log(`\n${'='.repeat(100)}`);
        console.log(`🎯 اختبار ${i + 1}/${realVulnerabilities.length}: ${vuln.name}`);
        console.log(`📝 النوع: ${vuln.type}`);
        console.log(`🌐 URL: ${vuln.url}`);
        console.log(`💉 Payload: ${vuln.payload}`);
        console.log(`🔧 Parameter: ${vuln.parameter}`);
        console.log(`📄 الوصف: ${vuln.description}`);
        console.log(`${'='.repeat(100)}`);
        
        // إنشاء بيانات فريدة لكل ثغرة
        const uniqueTestData = {
            url: vuln.url,
            vulnerability_name: vuln.name,
            vulnerability_type: vuln.type,
            payload_data: vuln.payload,
            target_parameter: vuln.parameter,
            report_id: `real_test_${vuln.name.toLowerCase()}_${Date.now()}`,
            stage: 'after',
            filename: `after_${vuln.name}_exploitation`,
            // إضافة بيانات v4 محاكية
            v4_data: {
                test_results: [
                    `Real HTTP Response Code: 200`,
                    `Response Size: ${Math.floor(Math.random() * 5000) + 2000} characters`,
                    `Response Time: ${Math.floor(Math.random() * 500) + 100}ms`,
                    `Payload Delivered: ${vuln.payload}`,
                    `Target Parameter: ${vuln.parameter}`
                ],
                exploitation_data: {
                    status: 'successful',
                    method: vuln.type,
                    payload_used: vuln.payload,
                    target_url: vuln.url,
                    evidence_count: Math.floor(Math.random() * 5) + 3
                },
                verification_data: {
                    proof: [
                        `Created test URL: ${vuln.url}?${vuln.parameter}=${encodeURIComponent(vuln.payload)}`,
                        `Response analysis completed: successful`,
                        `Evidence collected: ${Math.floor(Math.random() * 3) + 2} items`,
                        `Vulnerability confirmed: ${vuln.type}`,
                        `Exploitation method: ${vuln.name}`
                    ]
                },
                response_data: `<!DOCTYPE html><html><head><title>Test Response for ${vuln.name}</title></head><body><h1>Vulnerability Test Results</h1><p>Payload: ${vuln.payload}</p><p>Type: ${vuln.type}</p><p>Status: Exploited Successfully</p><div id="exploit-result">Real server response with ${vuln.type} vulnerability</div></body></html>`,
                actual_response_content: `🔥 REAL EXPLOITATION RESPONSE FOR ${vuln.name.toUpperCase()} 🔥\n\nPayload Used: ${vuln.payload}\nVulnerability Type: ${vuln.type}\nTarget Parameter: ${vuln.parameter}\nExploitation Status: SUCCESSFUL\n\nServer Response:\n${vuln.url} responded with vulnerability confirmation\nPayload was processed and executed\nEvidence of ${vuln.type} vulnerability found\n\nSecurity Impact: HIGH\nRecommendation: Immediate patching required`,
                success_indicators: [
                    'Vulnerability testing completed',
                    `${vuln.type} confirmed`,
                    'Payload execution successful',
                    'Evidence collected'
                ],
                error_messages: []
            }
        };
        
        console.log('📤 إرسال بيانات الثغرة الفريدة...');
        console.log(`   🆔 Report ID: ${uniqueTestData.report_id}`);
        console.log(`   📁 Filename: ${uniqueTestData.filename}`);
        
        try {
            const startTime = Date.now();
            
            // استخدام endpoint /v4_website لمحاكاة النظام الحقيقي
            const response = await fetch('http://localhost:8000/v4_website', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(uniqueTestData)
            });
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`📡 حالة الاستجابة: ${response.status}`);
            console.log(`⏱️ مدة الطلب: ${duration}ms`);
            
            if (response.ok) {
                const result = await response.json();
                console.log('✅ نجح الطلب!');
                console.log(`📝 الرسالة: ${result.message || 'تم بنجاح'}`);
                
                // فحص تفصيلي للنتائج
                if (result.success) {
                    console.log(`📸 حجم الصورة: ${result.file_size || 0} bytes`);
                    console.log(`📁 مسار الصورة: ${result.file_path || 'غير محدد'}`);
                    
                    // التحقق من البيانات المستخدمة
                    if (result.vulnerability_name_used) {
                        console.log(`🎯 اسم الثغرة المستخدم: ${result.vulnerability_name_used}`);
                        if (result.vulnerability_name_used === vuln.name) {
                            console.log(`   ✅ اسم الثغرة صحيح ومطابق!`);
                        } else {
                            console.log(`   ⚠️ اسم الثغرة مختلف!`);
                        }
                    }
                    
                    if (result.vulnerability_type_used) {
                        console.log(`🔍 نوع الثغرة المستخدم: ${result.vulnerability_type_used}`);
                        if (result.vulnerability_type_used.toLowerCase().includes(vuln.type.toLowerCase().split('_')[0])) {
                            console.log(`   ✅ نوع الثغرة صحيح!`);
                        } else {
                            console.log(`   ⚠️ نوع الثغرة مختلف!`);
                        }
                    }
                    
                    if (result.payload_used) {
                        console.log(`💉 Payload المستخدم: ${result.payload_used}`);
                        if (result.payload_used === vuln.payload) {
                            console.log(`   ✅ Payload صحيح ومطابق!`);
                        } else {
                            console.log(`   ⚠️ Payload مختلف!`);
                        }
                    }
                    
                    // فحص البيانات الحقيقية
                    if (result.real_data_included) {
                        console.log(`📊 البيانات الحقيقية: متضمنة`);
                    } else {
                        console.log(`📊 البيانات الحقيقية: غير متضمنة`);
                    }
                    
                } else {
                    console.log(`❌ فشل: ${result.error || 'خطأ غير محدد'}`);
                }
                
                results.push({
                    vulnerability: vuln.name,
                    type: vuln.type,
                    success: result.success || false,
                    duration: duration,
                    payload_correct: result.payload_used === vuln.payload,
                    type_correct: result.vulnerability_type_used && result.vulnerability_type_used.toLowerCase().includes(vuln.type.toLowerCase().split('_')[0]),
                    name_correct: result.vulnerability_name_used === vuln.name,
                    file_size: result.file_size || 0
                });
                
            } else {
                const errorText = await response.text();
                console.log(`❌ فشل الطلب: ${response.status}`);
                console.log(`📄 تفاصيل الخطأ: ${errorText}`);
                
                results.push({
                    vulnerability: vuln.name,
                    type: vuln.type,
                    success: false,
                    error: `HTTP ${response.status}: ${errorText}`,
                    duration: duration
                });
            }
            
        } catch (error) {
            console.error(`❌ خطأ في الطلب: ${error.message}`);
            
            results.push({
                vulnerability: vuln.name,
                type: vuln.type,
                success: false,
                error: error.message,
                duration: 0
            });
        }
        
        // انتظار بين الثغرات
        if (i < realVulnerabilities.length - 1) {
            console.log('\n⏳ انتظار 3 ثواني قبل الثغرة التالية...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }
    
    // ملخص شامل للنتائج
    console.log(`\n${'='.repeat(120)}`);
    console.log('📊 ملخص شامل لنتائج الاختبار الفعلي:');
    console.log(`${'='.repeat(120)}`);
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`✅ نجح: ${successful.length}/${results.length} ثغرة`);
    console.log(`❌ فشل: ${failed.length}/${results.length} ثغرة`);
    
    if (successful.length > 0) {
        console.log('\n✅ الثغرات الناجحة مع التفاصيل:');
        successful.forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.vulnerability} (${result.type})`);
            console.log(`      ⏱️ المدة: ${result.duration}ms`);
            console.log(`      📁 حجم الصورة: ${(result.file_size / 1024).toFixed(1)}KB`);
            console.log(`      💉 Payload صحيح: ${result.payload_correct ? '✅ نعم' : '❌ لا'}`);
            console.log(`      🔍 النوع صحيح: ${result.type_correct ? '✅ نعم' : '❌ لا'}`);
            console.log(`      🎯 الاسم صحيح: ${result.name_correct ? '✅ نعم' : '❌ لا'}`);
        });
    }
    
    if (failed.length > 0) {
        console.log('\n❌ الثغرات الفاشلة:');
        failed.forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.vulnerability} (${result.type})`);
            console.log(`      📄 الخطأ: ${result.error}`);
        });
    }
    
    // إحصائيات التحقق من البيانات
    const payloadCorrect = successful.filter(r => r.payload_correct).length;
    const typeCorrect = successful.filter(r => r.type_correct).length;
    const nameCorrect = successful.filter(r => r.name_correct).length;
    
    console.log(`\n📊 إحصائيات دقة البيانات:`);
    console.log(`   💉 Payloads صحيحة: ${payloadCorrect}/${successful.length}`);
    console.log(`   🔍 أنواع صحيحة: ${typeCorrect}/${successful.length}`);
    console.log(`   🎯 أسماء صحيحة: ${nameCorrect}/${successful.length}`);
    
    const overallAccuracy = successful.length > 0 ? 
        ((payloadCorrect + typeCorrect + nameCorrect) / (successful.length * 3) * 100).toFixed(1) : 0;
    
    console.log(`\n🎯 معدل النجاح الإجمالي: ${((successful.length / results.length) * 100).toFixed(1)}%`);
    console.log(`🎯 معدل دقة البيانات: ${overallAccuracy}%`);
    console.log('\n📁 تحقق من مجلد screenshots لرؤية الصور المولدة');
    console.log('📋 تحقق من سجلات سيرفر Python لرؤية التفاصيل الكاملة');
    
    return results;
}

// تشغيل الاختبار
testRealDynamicVulnerabilities()
    .then(results => {
        console.log('\n🎉 انتهى الاختبار الفعلي للثغرات المتعددة بنجاح!');
        console.log('🔍 راجع النتائج أعلاه لرؤية دقة البيانات الديناميكية');
    })
    .catch(error => {
        console.error('❌ خطأ في الاختبار الفعلي:', error);
    });
