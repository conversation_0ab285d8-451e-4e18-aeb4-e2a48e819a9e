#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاستجابة المحسنة والمفصلة
"""

import requests
import json
import time
import os

def test_enhanced_response():
    """اختبار الاستجابة المحسنة والمفصلة"""
    
    print("🔥 اختبار الاستجابة المحسنة والمفصلة...")
    print("🌐 السيرفر يجب أن يعمل على: http://localhost:8000")
    
    # التحقق من تشغيل السيرفر
    try:
        response = requests.get("http://localhost:8000", timeout=5)
        print("✅ السيرفر يعمل ويستجيب")
    except requests.exceptions.RequestException as e:
        print(f"❌ السيرفر لا يعمل: {e}")
        return
    
    # اختبار واحد للمقارنة
    test_data = {
        "url": "https://httpbin.org/get?id=1&user=admin&debug=true&system=production",
        "vulnerability_name": "Enhanced SQL Injection Test",
        "vulnerability_type": "SQL Injection",
        "payload": "' OR 1=1 UNION SELECT * FROM users --",
        "report_id": "enhanced_response_test",
        "stage": "after"
    }
    
    print(f"\n📸 اختبار الاستجابة المحسنة:")
    print(f"   🎯 النوع: {test_data['vulnerability_type']}")
    print(f"   💉 Payload: {test_data['payload']}")
    print(f"   🔗 URL: {test_data['url']}")
    
    try:
        print("   📤 إرسال طلب إلى السيرفر...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8000/capture",
            json=test_data,
            timeout=90
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"   📥 استجابة السيرفر: {response.status_code} (استغرق {duration:.1f}s)")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ نجح الطلب!")
            
            if result.get('success'):
                image_path = result.get('screenshot_path')
                if image_path and os.path.exists(image_path):
                    file_size = os.path.getsize(image_path)
                    print(f"   📁 مسار الصورة: {os.path.basename(image_path)}")
                    print(f"   📊 حجم الملف: {file_size:,} bytes ({file_size/1024:.1f} KB)")
                    
                    # فتح الصورة للفحص
                    import webbrowser
                    webbrowser.open(f"file:///{os.path.abspath(image_path)}")
                    print(f"   🌐 تم فتح الصورة للفحص")
                    
                    print(f"\n🔍 تحقق من قسم REAL SERVER RESPONSE:")
                    print(f"   ✅ هل يعرض تفاصيل الطلب كاملة؟")
                    print(f"   ✅ هل يعرض جميع HTTP Headers؟")
                    print(f"   ✅ هل يعرض Response Body كامل؟")
                    print(f"   ✅ هل يعرض معلومات الاستغلال؟")
                    print(f"   ✅ هل يعرض تحليل الأمان؟")
                    
                else:
                    print("   ❌ ملف الصورة غير موجود")
            else:
                error_msg = result.get('error', 'خطأ غير محدد')
                print(f"   ❌ فشل الطلب: {error_msg}")
        else:
            print(f"   ❌ خطأ في السيرفر: {response.status_code}")
            print(f"   📝 الرسالة: {response.text[:200]}...")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    except Exception as e:
        print(f"   ❌ خطأ عام: {e}")
    
    print(f"\n📂 مجلد الصورة: assets/modules/bugbounty/screenshots/enhanced_response_test/")
    print(f"🔍 ابحث عن: after_Enhanced SQL Injection Test_after.png")

if __name__ == "__main__":
    test_enhanced_response()
