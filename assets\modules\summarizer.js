// وحدة توليد الملخصات الذكية
// Smart Summarization Module

class SmartSummarizer {
    constructor() {
        this.summaryTypes = {
            conversation: 'ملخص المحادثة',
            document: 'ملخص المستند',
            video: 'ملخص الفيديو',
            code: 'ملخص الكود',
            general: 'ملخص عام'
        };
        this.summaryLevels = {
            brief: 'موجز',
            detailed: 'مفصل',
            comprehensive: 'شامل'
        };
    }

    // توليد ملخص للمحادثة الحالية بالذكاء الاصطناعي
    async generateConversationSummary(level = 'detailed') {
        try {
            console.log('📝 توليد ملخص المحادثة بالذكاء الاصطناعي...');

            if (!currentConversation || currentConversation.length === 0) {
                return 'لا توجد محادثة لتلخيصها';
            }

            // تحليل أساسي للمحادثة
            const basicAnalysis = this.analyzeConversation(currentConversation, level);

            // إنشاء prompt للذكاء الاصطناعي
            const conversationText = currentConversation
                .map(msg => `${msg.sender === 'user' ? 'المستخدم' : 'المساعد'}: ${msg.content}`)
                .join('\n');

            const summaryPrompt = `قم بإنشاء ملخص ذكي ومفيد للمحادثة التالية:

المحادثة:
${conversationText}

معلومات إضافية:
- إجمالي الرسائل: ${basicAnalysis.totalMessages}
- مدة المحادثة: ${basicAnalysis.duration}
- مستوى التفصيل المطلوب: ${level}

المطلوب إنشاء ملخص يشمل:
1. 📋 نظرة عامة على المحادثة
2. 🎯 المواضيع الرئيسية المطروحة
3. ❓ الأسئلة المهمة التي طرحها المستخدم
4. ✅ الحلول والإجابات المقدمة
5. 📚 المفاهيم التقنية المشروحة
6. 💡 النصائح والتوصيات المقدمة
7. 📊 تقييم مدى فائدة المحادثة
8. 🔄 اقتراحات للمتابعة

اجعل الملخص واضحاً ومنظماً ومفيداً:`;

            let aiSummary = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لتلخيص المحادثة...');
                const response = await window.openRouterIntegration.smartSendMessage(summaryPrompt, {
                    mode: 'summarization',
                    temperature: 0.4,
                    maxTokens: 2500
                });
                if (response && response.text) {
                    aiSummary = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!aiSummary && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لتلخيص المحادثة...');
                const response = await window.huggingFaceManager.sendMessage(summaryPrompt);
                if (response && response.text) {
                    aiSummary = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!aiSummary && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لتلخيص المحادثة...');
                aiSummary = await technicalAssistant.getResponse(summaryPrompt);
            }

            // استخدام الملخص الذكي أو الأساسي
            const finalSummary = aiSummary || basicAnalysis;

            this.displaySummary(finalSummary, 'conversation');

            return finalSummary;
        } catch (error) {
            console.error('❌ خطأ في توليد ملخص المحادثة:', error);
            return 'حدث خطأ في توليد الملخص';
        }
    }

    // تحليل المحادثة
    analyzeConversation(conversation, level) {
        const analysis = {
            totalMessages: conversation.length,
            userMessages: conversation.filter(msg => msg.sender === 'user').length,
            assistantMessages: conversation.filter(msg => msg.sender === 'assistant').length,
            startTime: conversation[0]?.timestamp,
            endTime: conversation[conversation.length - 1]?.timestamp,
            duration: this.calculateDuration(conversation[0]?.timestamp, conversation[conversation.length - 1]?.timestamp)
        };

        // استخراج المواضيع
        const topics = this.extractTopics(conversation);
        
        // استخراج النقاط المهمة
        const keyPoints = this.extractKeyPoints(conversation);
        
        // بناء الملخص
        return this.buildConversationSummary(analysis, topics, keyPoints, level);
    }

    // استخراج المواضيع من المحادثة
    extractTopics(conversation) {
        const topics = new Set();
        const technicalKeywords = {
            'برمجة': ['javascript', 'python', 'html', 'css', 'كود', 'برمجة', 'function', 'class'],
            'شبكات': ['شبكة', 'network', 'إنترنت', 'بروتوكول', 'ip'],
            'قواعد البيانات': ['database', 'sql', 'قاعدة بيانات', 'جدول'],
            'أمان': ['security', 'أمان', 'تشفير', 'حماية'],
            'تصميم': ['design', 'تصميم', 'واجهة', 'ui', 'ux'],
            'عام': ['مساعدة', 'شرح', 'تعلم', 'كيف']
        };

        conversation.forEach(msg => {
            const content = msg.content.toLowerCase();
            for (const [topic, keywords] of Object.entries(technicalKeywords)) {
                if (keywords.some(keyword => content.includes(keyword))) {
                    topics.add(topic);
                }
            }
        });

        return Array.from(topics);
    }

    // استخراج النقاط المهمة
    extractKeyPoints(conversation) {
        const keyPoints = [];
        
        conversation.forEach((msg, index) => {
            if (msg.sender === 'user') {
                // أسئلة المستخدم
                if (msg.content.includes('كيف') || msg.content.includes('ما هو') || msg.content.includes('شرح')) {
                    keyPoints.push({
                        type: 'سؤال',
                        content: msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : ''),
                        timestamp: msg.timestamp
                    });
                }
            } else if (msg.sender === 'assistant') {
                // إجابات مهمة من المساعد
                if (msg.content.length > 200) {
                    keyPoints.push({
                        type: 'إجابة مفصلة',
                        content: msg.content.substring(0, 150) + '...',
                        timestamp: msg.timestamp
                    });
                }
            }
        });

        return keyPoints.slice(0, 10); // أهم 10 نقاط
    }

    // بناء ملخص المحادثة
    buildConversationSummary(analysis, topics, keyPoints, level) {
        let summary = `📊 **ملخص المحادثة**\n\n`;
        
        // معلومات أساسية
        summary += `📈 **إحصائيات:**\n`;
        summary += `• إجمالي الرسائل: ${analysis.totalMessages}\n`;
        summary += `• رسائل المستخدم: ${analysis.userMessages}\n`;
        summary += `• رسائل المساعد: ${analysis.assistantMessages}\n`;
        summary += `• مدة المحادثة: ${analysis.duration}\n\n`;

        // المواضيع المطروحة
        if (topics.length > 0) {
            summary += `🎯 **المواضيع المطروحة:**\n`;
            topics.forEach(topic => {
                summary += `• ${topic}\n`;
            });
            summary += '\n';
        }

        // النقاط المهمة (حسب مستوى التفصيل)
        if (level !== 'brief' && keyPoints.length > 0) {
            summary += `⭐ **النقاط المهمة:**\n`;
            const pointsToShow = level === 'comprehensive' ? keyPoints : keyPoints.slice(0, 5);
            pointsToShow.forEach((point, index) => {
                summary += `${index + 1}. **${point.type}:** ${point.content}\n`;
            });
            summary += '\n';
        }

        // توصيات
        summary += `💡 **التوصيات:**\n`;
        if (topics.includes('برمجة')) {
            summary += `• مراجعة أمثلة الكود المقدمة\n`;
            summary += `• تطبيق المفاهيم عملياً\n`;
        }
        if (topics.length > 1) {
            summary += `• ربط المواضيع المختلفة ببعضها\n`;
        }
        summary += `• حفظ المحادثة للمراجعة لاحقاً\n`;

        return summary;
    }

    // توليد ملخص لمستند نصي بالذكاء الاصطناعي
    async generateDocumentSummary(text, level = 'detailed') {
        try {
            console.log('📄 توليد ملخص المستند بالذكاء الاصطناعي...');

            if (!text || text.trim().length === 0) {
                return 'لا يوجد نص لتلخيصه';
            }

            // تحليل أساسي للمستند
            const basicAnalysis = this.analyzeDocument(text);

            // إنشاء prompt للذكاء الاصطناعي
            const documentPrompt = `قم بإنشاء ملخص ذكي وشامل للنص التالي:

النص المطلوب تلخيصه:
"${text}"

معلومات إضافية:
- عدد الكلمات: ${basicAnalysis.wordCount}
- عدد الجمل: ${basicAnalysis.sentenceCount}
- وقت القراءة المقدر: ${basicAnalysis.readingTime} دقيقة
- مستوى التفصيل: ${level}

المطلوب إنشاء ملخص يشمل:
1. 📋 الفكرة الرئيسية للنص
2. 🎯 النقاط الأساسية والمهمة
3. 📚 المفاهيم الرئيسية المطروحة
4. 🔍 التفاصيل المهمة (حسب مستوى التفصيل)
5. 📊 الإحصائيات أو الأرقام المهمة إن وجدت
6. 💡 الخلاصة والاستنتاجات
7. 🎯 الجمهور المستهدف للنص
8. 📈 مستوى الصعوبة والتخصص

اجعل الملخص واضحاً ومفيداً ومنظماً بشكل جيد:`;

            let aiSummary = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لتلخيص المستند...');
                const response = await window.openRouterIntegration.smartSendMessage(documentPrompt, {
                    mode: 'summarization',
                    temperature: 0.3,
                    maxTokens: 3000
                });
                if (response && response.text) {
                    aiSummary = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!aiSummary && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لتلخيص المستند...');
                const response = await window.huggingFaceManager.sendMessage(documentPrompt);
                if (response && response.text) {
                    aiSummary = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!aiSummary && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لتلخيص المستند...');
                aiSummary = await technicalAssistant.getResponse(documentPrompt);
            }

            // استخدام الملخص الذكي أو الأساسي
            const finalSummary = aiSummary || this.buildDocumentSummary(basicAnalysis, level);

            this.displaySummary(finalSummary, 'document');

            return finalSummary;
        } catch (error) {
            console.error('❌ خطأ في توليد ملخص المستند:', error);
            return 'حدث خطأ في توليد الملخص';
        }
    }

    // تحليل المستند
    analyzeDocument(text) {
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const words = text.split(/\s+/).filter(w => w.trim().length > 0);
        const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

        // استخراج الكلمات المفتاحية
        const keywords = this.extractKeywords(text);
        
        // تحديد الجمل المهمة
        const importantSentences = this.findImportantSentences(sentences, keywords);

        return {
            wordCount: words.length,
            sentenceCount: sentences.length,
            paragraphCount: paragraphs.length,
            keywords: keywords,
            importantSentences: importantSentences,
            readingTime: Math.ceil(words.length / 200) // متوسط 200 كلمة/دقيقة
        };
    }

    // استخراج الكلمات المفتاحية
    extractKeywords(text) {
        const words = (text && typeof text === 'string') ? text.toLowerCase().split(/\s+/) : [];
        const stopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'التي', 'الذي', 'أن', 'كان', 'يكون'];
        
        const wordFreq = {};
        words.forEach(word => {
            word = word.replace(/[^\u0600-\u06FFa-zA-Z]/g, ''); // إزالة علامات الترقيم
            if (word.length > 2 && !stopWords.includes(word)) {
                wordFreq[word] = (wordFreq[word] || 0) + 1;
            }
        });

        return Object.entries(wordFreq)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .map(([word, freq]) => ({ word, frequency: freq }));
    }

    // العثور على الجمل المهمة
    findImportantSentences(sentences, keywords) {
        const keywordList = keywords.map(k => k.word);
        
        return sentences
            .map(sentence => {
                const score = keywordList.reduce((acc, keyword) => {
                    return acc + (sentence.toLowerCase().includes(keyword) ? 1 : 0);
                }, 0);
                return { sentence: sentence.trim(), score };
            })
            .filter(item => item.score > 0)
            .sort((a, b) => b.score - a.score)
            .slice(0, 5)
            .map(item => item.sentence);
    }

    // بناء ملخص المستند
    buildDocumentSummary(analysis, level) {
        let summary = `📄 **ملخص المستند**\n\n`;
        
        // إحصائيات
        summary += `📊 **إحصائيات:**\n`;
        summary += `• عدد الكلمات: ${analysis.wordCount}\n`;
        summary += `• عدد الجمل: ${analysis.sentenceCount}\n`;
        summary += `• عدد الفقرات: ${analysis.paragraphCount}\n`;
        summary += `• وقت القراءة المقدر: ${analysis.readingTime} دقيقة\n\n`;

        // الكلمات المفتاحية
        if (analysis.keywords.length > 0) {
            summary += `🔑 **الكلمات المفتاحية:**\n`;
            const keywordsToShow = level === 'brief' ? analysis.keywords.slice(0, 5) : analysis.keywords;
            keywordsToShow.forEach(keyword => {
                summary += `• ${keyword.word} (${keyword.frequency} مرة)\n`;
            });
            summary += '\n';
        }

        // الجمل المهمة
        if (level !== 'brief' && analysis.importantSentences.length > 0) {
            summary += `⭐ **النقاط الرئيسية:**\n`;
            const sentencesToShow = level === 'comprehensive' ? analysis.importantSentences : analysis.importantSentences.slice(0, 3);
            sentencesToShow.forEach((sentence, index) => {
                summary += `${index + 1}. ${sentence}\n`;
            });
            summary += '\n';
        }

        return summary;
    }

    // عرض الملخص في واجهة المستخدم
    displaySummary(summary, type) {
        const displayArea = document.getElementById('displayArea');
        const displayContent = document.getElementById('displayContent');
        const displayTitle = document.getElementById('displayTitle');

        // إنشاء واجهة الملخص
        const summaryContainer = document.createElement('div');
        summaryContainer.className = 'summary-container';
        summaryContainer.style.padding = '20px';
        summaryContainer.style.lineHeight = '1.6';

        // تحويل النص إلى HTML
        const htmlSummary = this.convertMarkdownToHtml(summary);
        summaryContainer.innerHTML = htmlSummary;

        // أزرار التحكم
        const controlsDiv = document.createElement('div');
        controlsDiv.style.marginTop = '20px';
        controlsDiv.style.display = 'flex';
        controlsDiv.style.gap = '10px';

        const copyBtn = document.createElement('button');
        copyBtn.innerHTML = '<i class="fas fa-copy"></i> نسخ الملخص';
        copyBtn.className = 'tool-btn';
        copyBtn.onclick = () => this.copySummary(summary);

        const saveBtn = document.createElement('button');
        saveBtn.innerHTML = '<i class="fas fa-download"></i> حفظ الملخص';
        saveBtn.className = 'tool-btn';
        saveBtn.onclick = () => this.saveSummary(summary, type);

        const shareBtn = document.createElement('button');
        shareBtn.innerHTML = '<i class="fas fa-share"></i> مشاركة';
        shareBtn.className = 'tool-btn';
        shareBtn.onclick = () => this.shareSummary(summary);

        controlsDiv.appendChild(copyBtn);
        controlsDiv.appendChild(saveBtn);
        controlsDiv.appendChild(shareBtn);
        summaryContainer.appendChild(controlsDiv);

        // عرض الملخص
        displayTitle.textContent = this.summaryTypes[type] || 'ملخص';
        displayContent.innerHTML = '';
        displayContent.appendChild(summaryContainer);
        displayArea.style.display = 'flex';

        // إضافة الملخص للمحادثة
        addMessageToChat('assistant', `تم إنشاء ${this.summaryTypes[type]} بنجاح! يمكنك مراجعته في منطقة العرض.`);
    }

    // تحويل Markdown إلى HTML بسيط
    convertMarkdownToHtml(text) {
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^• (.+)$/gm, '<li>$1</li>')
            .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
            .replace(/\n\n/g, '</p><p>')
            .replace(/^(.+)$/gm, '<p>$1</p>')
            .replace(/<p><\/p>/g, '');
    }

    // نسخ الملخص
    copySummary(summary) {
        navigator.clipboard.writeText(summary).then(() => {
            addMessageToChat('assistant', 'تم نسخ الملخص إلى الحافظة');
        }).catch(err => {
            console.error('خطأ في النسخ:', err);
            addMessageToChat('assistant', 'حدث خطأ في نسخ الملخص');
        });
    }

    // حفظ الملخص
    saveSummary(summary, type) {
        const blob = new Blob([summary], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.summaryTypes[type]}_${new Date().toISOString().split('T')[0]}.txt`;
        a.click();
        
        URL.revokeObjectURL(url);
        addMessageToChat('assistant', 'تم حفظ الملخص بنجاح');
    }

    // مشاركة الملخص
    shareSummary(summary) {
        if (navigator.share) {
            navigator.share({
                title: 'ملخص من المساعد التقني',
                text: summary
            }).then(() => {
                addMessageToChat('assistant', 'تم مشاركة الملخص');
            }).catch(err => {
                console.error('خطأ في المشاركة:', err);
            });
        } else {
            this.copySummary(summary);
            addMessageToChat('assistant', 'تم نسخ الملخص للمشاركة');
        }
    }

    // حساب المدة بين وقتين
    calculateDuration(startTime, endTime) {
        if (!startTime || !endTime) return 'غير محدد';
        
        const start = new Date(startTime);
        const end = new Date(endTime);
        const diffMs = end - start;
        const diffMins = Math.floor(diffMs / 60000);
        
        if (diffMins < 1) return 'أقل من دقيقة';
        if (diffMins < 60) return `${diffMins} دقيقة`;
        
        const hours = Math.floor(diffMins / 60);
        const mins = diffMins % 60;
        return `${hours} ساعة و ${mins} دقيقة`;
    }
}

// إنشاء مثيل الملخص الذكي
const smartSummarizer = new SmartSummarizer();

// تصدير الملخص للاستخدام العام
window.smartSummarizer = smartSummarizer;
