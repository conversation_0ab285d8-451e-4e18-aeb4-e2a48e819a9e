#!/usr/bin/env python3
"""
اختبار شامل للإصلاحات - ثغرات متعددة
"""

import requests
import json
import time
import threading
from datetime import datetime

class VulnerabilityTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.results = {}
        
    def test_server_health(self):
        """اختبار صحة السيرفر"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            print(f"✅ السيرفر يعمل: {response.status_code}")
            return True
        except Exception as e:
            print(f"❌ السيرفر لا يعمل: {e}")
            return False
    
    def test_vulnerability(self, vuln_data):
        """اختبار ثغرة واحدة"""
        vuln_name = vuln_data['name']
        print(f"\n{'='*80}")
        print(f"🎯 اختبار الثغرة: {vuln_name}")
        print(f"🔗 URL: {vuln_data['url']}")
        print(f"💉 Payload: {vuln_data['payload']}")
        print(f"📝 النوع: {vuln_data['type']}")
        
        start_time = time.time()
        
        # بيانات الطلب
        test_data = {
            "url": vuln_data["url"],
            "filename": f"test_{vuln_name.lower().replace(' ', '_')}",
            "report_id": f"comprehensive_test_{int(time.time())}",
            "vulnerability_name": vuln_name,
            "vulnerability_type": vuln_data["type"],
            "stage": "after",
            "payload_data": vuln_data["payload"],
            "target_parameter": vuln_data.get("param", "id")
        }
        
        try:
            print(f"📤 إرسال طلب...")
            response = requests.post(
                f"{self.base_url}/v4_website",
                json=test_data,
                timeout=180  # 3 دقائق
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"📥 كود الاستجابة: {response.status_code}")
            print(f"⏱️ مدة الاستجابة: {duration:.2f} ثانية")
            
            result = {
                'vulnerability': vuln_name,
                'status_code': response.status_code,
                'duration': duration,
                'success': False,
                'error': None,
                'target_url_status': None,
                'screenshot_path': None
            }
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print(f"✅ نجح الطلب!")
                    
                    result['success'] = True
                    result['response_data'] = response_data
                    
                    # فحص URL المستهدف
                    if 'target_url' in response_data:
                        target_url = response_data['target_url']
                        print(f"🔗 URL المستهدف: {target_url}")
                        
                        try:
                            url_response = requests.get(target_url, timeout=10)
                            result['target_url_status'] = url_response.status_code
                            
                            if url_response.status_code == 404:
                                print(f"❌ مشكلة 404 مكتشفة!")
                                result['error'] = "404 Not Found"
                            elif url_response.status_code == 503:
                                print(f"❌ مشكلة 503 مكتشفة!")
                                result['error'] = "503 Service Unavailable"
                            else:
                                print(f"✅ URL يعمل بشكل صحيح: {url_response.status_code}")
                                
                        except Exception as url_error:
                            print(f"❌ خطأ في اختبار URL: {url_error}")
                            result['error'] = f"URL Error: {url_error}"
                    
                    # فحص مسار الصورة
                    if 'screenshot_path' in response_data:
                        result['screenshot_path'] = response_data['screenshot_path']
                        print(f"📸 مسار الصورة: {response_data['screenshot_path']}")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ خطأ في تحليل JSON: {e}")
                    result['error'] = f"JSON Error: {e}"
                    
            else:
                print(f"❌ فشل الطلب: {response.status_code}")
                result['error'] = f"HTTP {response.status_code}"
                try:
                    error_text = response.text[:500]
                    print(f"📄 الاستجابة: {error_text}...")
                    result['response_text'] = error_text
                except:
                    pass
            
            self.results[vuln_name] = result
            return result
            
        except requests.exceptions.Timeout:
            print(f"⏰ انتهت مهلة الطلب للثغرة: {vuln_name}")
            result = {
                'vulnerability': vuln_name,
                'status_code': None,
                'duration': time.time() - start_time,
                'success': False,
                'error': 'Timeout',
                'target_url_status': None
            }
            self.results[vuln_name] = result
            return result
            
        except Exception as e:
            print(f"❌ خطأ في اختبار {vuln_name}: {e}")
            result = {
                'vulnerability': vuln_name,
                'status_code': None,
                'duration': time.time() - start_time,
                'success': False,
                'error': str(e),
                'target_url_status': None
            }
            self.results[vuln_name] = result
            return result
    
    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        
        # قائمة الثغرات للاختبار
        vulnerabilities = [
            {
                "name": "SQL Injection Basic",
                "url": "https://httpbin.org/get?id=1",
                "payload": "' UNION SELECT 1,2,3 --",
                "type": "SQL Injection",
                "param": "id"
            },
            {
                "name": "SQL Injection Advanced",
                "url": "https://httpbin.org/get?user_id=123",
                "payload": "' UNION SELECT user(),database(),version() --",
                "type": "SQL Injection",
                "param": "user_id"
            },
            {
                "name": "XSS Reflected",
                "url": "https://httpbin.org/get?search=test",
                "payload": "<script>alert('XSS')</script>",
                "type": "Cross-Site Scripting",
                "param": "search"
            },
            {
                "name": "XSS Stored",
                "url": "https://httpbin.org/get?comment=hello",
                "payload": "<img src=x onerror=alert('XSS')>",
                "type": "Cross-Site Scripting",
                "param": "comment"
            },
            {
                "name": "Command Injection",
                "url": "https://httpbin.org/get?cmd=whoami",
                "payload": "; ls -la",
                "type": "Command Injection",
                "param": "cmd"
            },
            {
                "name": "Local File Inclusion",
                "url": "https://httpbin.org/get?file=index.php",
                "payload": "../../../../etc/passwd",
                "type": "Local File Inclusion",
                "param": "file"
            },
            {
                "name": "Directory Traversal",
                "url": "https://httpbin.org/get?path=uploads",
                "payload": "../../../etc/passwd",
                "type": "Directory Traversal",
                "param": "path"
            },
            {
                "name": "LDAP Injection",
                "url": "https://httpbin.org/get?username=admin",
                "payload": "admin)(&(password=*))",
                "type": "LDAP Injection",
                "param": "username"
            }
        ]
        
        print("🔥" * 80)
        print("🔥 اختبار شامل للإصلاحات - ثغرات متعددة")
        print("🔥" * 80)
        
        # اختبار صحة السيرفر
        if not self.test_server_health():
            return
        
        print(f"\n📊 سيتم اختبار {len(vulnerabilities)} ثغرة مختلفة...")
        
        # اختبار كل ثغرة
        for i, vuln in enumerate(vulnerabilities, 1):
            print(f"\n🔄 [{i}/{len(vulnerabilities)}] بدء اختبار: {vuln['name']}")
            self.test_vulnerability(vuln)
            
            # انتظار بين الاختبارات
            if i < len(vulnerabilities):
                print(f"⏳ انتظار 3 ثواني قبل الاختبار التالي...")
                time.sleep(3)
        
        # تحليل النتائج
        self.analyze_results()
    
    def analyze_results(self):
        """تحليل النتائج النهائية"""
        print("\n" + "🔥" * 80)
        print("🔥 تحليل النتائج النهائية")
        print("🔥" * 80)
        
        successful = []
        failed = []
        errors_404 = []
        errors_503 = []
        timeouts = []
        
        for vuln_name, result in self.results.items():
            if result['success']:
                successful.append(vuln_name)
                if result.get('error') == "404 Not Found":
                    errors_404.append(vuln_name)
                elif result.get('error') == "503 Service Unavailable":
                    errors_503.append(vuln_name)
            elif result.get('error') == 'Timeout':
                timeouts.append(vuln_name)
            else:
                failed.append(vuln_name)
        
        print(f"\n📊 إحصائيات الاختبار:")
        print(f"   ✅ نجحت: {len(successful)}")
        print(f"   ❌ فشلت: {len(failed)}")
        print(f"   🔴 404 Errors: {len(errors_404)}")
        print(f"   🟡 503 Errors: {len(errors_503)}")
        print(f"   ⏰ Timeouts: {len(timeouts)}")
        
        if successful:
            print(f"\n✅ الثغرات الناجحة:")
            for vuln in successful:
                result = self.results[vuln]
                status = "✅" if not result.get('error') else f"⚠️ ({result['error']})"
                print(f"   {status} {vuln} - {result['duration']:.1f}s")
        
        if errors_404:
            print(f"\n🔴 مشاكل 404 (تحتاج إصلاح):")
            for vuln in errors_404:
                print(f"   ❌ {vuln}")
        
        if errors_503:
            print(f"\n🟡 مشاكل 503 (مشاكل سيرفر):")
            for vuln in errors_503:
                print(f"   ⚠️ {vuln}")
        
        if failed:
            print(f"\n❌ الثغرات الفاشلة:")
            for vuln in failed:
                result = self.results[vuln]
                print(f"   ❌ {vuln} - {result.get('error', 'Unknown error')}")
        
        if timeouts:
            print(f"\n⏰ انتهت مهلتها:")
            for vuln in timeouts:
                print(f"   ⏰ {vuln}")
        
        # تقييم الإصلاح
        print(f"\n🎯 تقييم الإصلاح:")
        if len(errors_404) == 0:
            print("   ✅ تم إصلاح مشكلة 404 بنجاح!")
        else:
            print(f"   ❌ لا تزال هناك {len(errors_404)} ثغرة تعاني من مشكلة 404")
        
        success_rate = (len(successful) / len(self.results)) * 100
        print(f"   📈 معدل النجاح: {success_rate:.1f}%")

def main():
    tester = VulnerabilityTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
