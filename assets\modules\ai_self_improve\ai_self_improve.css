/* AI Self-Improvement System Styles */

/* Main Panel */
.ai-improvement-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    color: white;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.ai-improvement-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 20px 20px 0 0;
}

.ai-improvement-header h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.scan-btn, .close-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.scan-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.scan-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(39, 174, 96, 0.4);
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Body */
.ai-improvement-body {
    padding: 30px;
}

/* Status Section */
.status-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-item i {
    font-size: 1.5rem;
    color: #f39c12;
}

.status-item span {
    font-size: 1rem;
    font-weight: 500;
}

.status-item strong {
    color: #f39c12;
    font-weight: 700;
}

/* Suggestions Container */
.suggestions-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.suggestions-header h4 {
    margin: 0;
    font-size: 1.4rem;
    color: white;
}

.filter-controls {
    display: flex;
    gap: 15px;
}

.filter-select {
    padding: 8px 15px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 0.9rem;
    cursor: pointer;
}

/* Suggestions List */
.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.suggestion-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    backdrop-filter: blur(5px);
}

.suggestion-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.suggestion-item.selected {
    border-color: #f39c12;
    background: rgba(243, 156, 18, 0.2);
}

.suggestion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.suggestion-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.suggestion-type {
    background: rgba(52, 152, 219, 0.8);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.suggestion-severity {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.severity-critical {
    background: #e74c3c;
    color: white;
}

.severity-high {
    background: #f39c12;
    color: white;
}

.severity-medium {
    background: #f1c40f;
    color: #333;
}

.severity-low {
    background: #27ae60;
    color: white;
}

.suggestion-file {
    font-size: 0.9rem;
    color: #bdc3c7;
    display: flex;
    align-items: center;
    gap: 5px;
}

.suggestion-description {
    margin: 10px 0;
    font-size: 1rem;
    line-height: 1.5;
}

.suggestion-preview {
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #ecf0f1;
    border-left: 4px solid #3498db;
}

/* No Suggestions */
.no-suggestions {
    text-align: center;
    padding: 60px 20px;
    color: #bdc3c7;
}

.no-suggestions i {
    font-size: 4rem;
    color: #27ae60;
    margin-bottom: 20px;
}

.no-suggestions h3 {
    margin: 0 0 10px 0;
    font-size: 1.5rem;
    color: white;
}

/* Suggestion Details */
.suggestion-details {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.details-header h4 {
    margin: 0;
    font-size: 1.4rem;
    color: white;
}

.ai-request-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.request-ai-btn {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 180px;
    justify-content: center;
}

.request-ai-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(155, 89, 182, 0.4);
}

.quick-request-btn {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 120px;
    justify-content: center;
}

.quick-request-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(243, 156, 18, 0.4);
}

.details-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: #f39c12;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-item span, .detail-item p {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
    margin: 0;
}

.detail-item pre {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    border-left: 4px solid #3498db;
    margin: 0;
}

/* AI Response Section */
.ai-response-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.response-header h4 {
    margin: 0;
    font-size: 1.4rem;
    color: white;
}

.ai-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.ai-info span {
    background: rgba(155, 89, 182, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

.confidence {
    background: rgba(39, 174, 96, 0.8) !important;
}

/* Code Comparison */
.code-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.code-before, .code-after {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.code-before {
    border-left: 4px solid #e74c3c;
}

.code-after {
    border-left: 4px solid #27ae60;
}

.code-before h5, .code-after h5 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-before pre, .code-after pre {
    background: rgba(0, 0, 0, 0.4);
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin: 0;
    color: #ecf0f1;
}

/* Explanation */
.explanation {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.explanation h5 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #f39c12;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.accept-btn, .reject-btn, .modify-btn, .test-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 120px;
    justify-content: center;
}

.accept-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.reject-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

.modify-btn {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.test-btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.accept-btn:hover, .reject-btn:hover, .modify-btn:hover, .test-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* History Section */
.history-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.history-header h4 {
    margin: 0;
    font-size: 1.4rem;
    color: white;
}

.clear-history-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.clear-history-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(231, 76, 60, 0.4);
}

/* Messages */
.ai-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    z-index: 10001;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    animation: slideIn 0.3s ease;
}

.ai-message-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.ai-message-error {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.ai-message-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.ai-message-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-improvement-content {
        width: 98%;
        max-height: 95vh;
    }
    
    .ai-improvement-header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .ai-improvement-body {
        padding: 20px;
    }
    
    .status-section {
        grid-template-columns: 1fr;
    }
    
    .code-comparison {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .details-content {
        grid-template-columns: 1fr;
    }
}
