#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص المحتوى الفعلي لقسم REAL SERVER RESPONSE في الصور
"""

import os
import glob
from PIL import Image
import numpy as np

def check_real_response_content():
    """فحص المحتوى الفعلي لقسم REAL SERVER RESPONSE في كل صورة"""
    
    print("🔍 فحص المحتوى الفعلي لقسم REAL SERVER RESPONSE...")
    
    # البحث عن جميع الصور
    pattern = 'assets/modules/bugbounty/screenshots/**/after_*.png'
    images = glob.glob(pattern, recursive=True)
    
    if not images:
        print("❌ لم يتم العثور على أي صور")
        return
    
    print(f"📊 فحص {len(images)} صورة...")
    
    results = []
    
    for img_path in images:
        try:
            print(f"\n📸 فحص: {os.path.basename(img_path)}")
            print(f"   📂 المجلد: {os.path.basename(os.path.dirname(img_path))}")
            
            img = Image.open(img_path)
            img_array = np.array(img)
            
            # تحويل إلى RGB إذا كانت RGBA
            if img_array.shape[2] == 4:
                img_array = img_array[:, :, :3]
            
            height, width = img_array.shape[:2]
            print(f"   📐 الأبعاد: {width}x{height}")
            
            # البحث عن منطقة قسم REAL SERVER RESPONSE
            # عادة يكون في النصف السفلي من الصورة
            bottom_half = img_array[height//2:, :]
            
            # فحص التنوع اللوني في النصف السفلي
            # إذا كان هناك محتوى نصي، سيكون هناك تنوع أكبر
            color_variance = np.var(bottom_half)
            
            # فحص وجود نص أبيض على خلفية داكنة (نمط قسم REAL SERVER RESPONSE)
            # البحث عن مناطق بيضاء (نص) على خلفية داكنة
            gray_bottom = np.mean(bottom_half, axis=2)
            
            # حساب نسبة البكسلات البيضاء (النص)
            white_pixels = np.sum(gray_bottom > 200)  # بكسلات بيضاء
            dark_pixels = np.sum(gray_bottom < 100)   # بكسلات داكنة
            total_pixels = gray_bottom.size
            
            white_ratio = white_pixels / total_pixels
            dark_ratio = dark_pixels / total_pixels
            
            print(f"   📊 تنوع الألوان: {color_variance:.2f}")
            print(f"   📊 نسبة البكسلات البيضاء: {white_ratio:.3f}")
            print(f"   📊 نسبة البكسلات الداكنة: {dark_ratio:.3f}")
            
            # تحديد وجود محتوى نصي
            has_text_content = False
            content_quality = "غير محدد"
            
            if white_ratio > 0.1 and dark_ratio > 0.3:
                # نسبة جيدة من النص الأبيض على خلفية داكنة
                has_text_content = True
                content_quality = "محتوى نصي غني"
            elif white_ratio > 0.05:
                has_text_content = True
                content_quality = "محتوى نصي متوسط"
            elif color_variance > 1000:
                has_text_content = True
                content_quality = "محتوى متنوع"
            else:
                has_text_content = False
                content_quality = "محتوى قليل أو فارغ"
            
            # فحص خاص لمنطقة قسم REAL SERVER RESPONSE
            # البحث عن منطقة محددة في الصورة حيث يظهر القسم
            response_section_found = False
            
            # فحص الثلث السفلي من الصورة بحثاً عن نمط قسم الاستجابة
            bottom_third = img_array[2*height//3:, :]
            bottom_third_gray = np.mean(bottom_third, axis=2)
            
            # البحث عن خطوط أفقية (حدود القسم)
            horizontal_lines = 0
            for row in bottom_third_gray:
                if np.std(row) < 10:  # خط أفقي منتظم
                    horizontal_lines += 1
            
            if horizontal_lines > 5:
                response_section_found = True
            
            print(f"   🔍 وجود محتوى نصي: {'✅ نعم' if has_text_content else '❌ لا'}")
            print(f"   📝 جودة المحتوى: {content_quality}")
            print(f"   📋 قسم الاستجابة: {'✅ موجود' if response_section_found else '❌ غير واضح'}")
            
            # تحديد نوع الثغرة من اسم الملف
            filename = os.path.basename(img_path).lower()
            vuln_type = "غير محدد"
            if 'sql' in filename:
                vuln_type = "SQL Injection"
            elif 'xss' in filename:
                vuln_type = "XSS"
            elif 'command' in filename:
                vuln_type = "Command Injection"
            elif 'directory' in filename or 'traversal' in filename:
                vuln_type = "Directory Traversal"
            elif 'ldap' in filename:
                vuln_type = "LDAP Injection"
            elif 'xxe' in filename:
                vuln_type = "XXE Injection"
            elif 'ssti' in filename:
                vuln_type = "SSTI"
            elif 'information' in filename or 'disclosure' in filename:
                vuln_type = "Information Disclosure"
            
            print(f"   🎯 نوع الثغرة: {vuln_type}")
            
            # حفظ النتائج
            results.append({
                'file': os.path.basename(img_path),
                'folder': os.path.basename(os.path.dirname(img_path)),
                'vuln_type': vuln_type,
                'has_content': has_text_content,
                'content_quality': content_quality,
                'response_section': response_section_found,
                'white_ratio': white_ratio,
                'dark_ratio': dark_ratio,
                'color_variance': color_variance
            })
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص الصورة: {e}")
            results.append({
                'file': os.path.basename(img_path),
                'folder': os.path.basename(os.path.dirname(img_path)),
                'vuln_type': 'خطأ',
                'has_content': False,
                'content_quality': 'خطأ في الفحص',
                'response_section': False,
                'error': str(e)
            })
    
    # تحليل النتائج
    print(f"\n" + "="*80)
    print("🎯 تحليل نتائج فحص المحتوى:")
    print("="*80)
    
    # تصنيف النتائج
    with_content = [r for r in results if r['has_content']]
    without_content = [r for r in results if not r['has_content']]
    with_response_section = [r for r in results if r.get('response_section', False)]
    
    print(f"✅ صور تحتوي على محتوى نصي: {len(with_content)}/{len(results)}")
    print(f"❌ صور بدون محتوى نصي: {len(without_content)}/{len(results)}")
    print(f"📋 صور تحتوي على قسم استجابة واضح: {len(with_response_section)}/{len(results)}")
    
    if with_content:
        print(f"\n✅ الصور التي تحتوي على محتوى:")
        for result in with_content:
            print(f"   📸 {result['file']} ({result['vuln_type']})")
            print(f"      📝 جودة المحتوى: {result['content_quality']}")
            print(f"      📋 قسم الاستجابة: {'✅' if result.get('response_section') else '❌'}")
    
    if without_content:
        print(f"\n❌ الصور بدون محتوى نصي:")
        for result in without_content:
            print(f"   📸 {result['file']} ({result['vuln_type']})")
            print(f"      📝 المشكلة: {result['content_quality']}")
    
    # تحليل حسب نوع الثغرة
    print(f"\n🎯 تحليل حسب نوع الثغرة:")
    vuln_types = {}
    for result in results:
        vtype = result['vuln_type']
        if vtype not in vuln_types:
            vuln_types[vtype] = {'total': 0, 'with_content': 0}
        vuln_types[vtype]['total'] += 1
        if result['has_content']:
            vuln_types[vtype]['with_content'] += 1
    
    for vtype, stats in vuln_types.items():
        success_rate = (stats['with_content'] / stats['total']) * 100
        print(f"   🎯 {vtype}: {stats['with_content']}/{stats['total']} ({success_rate:.1f}%)")
        if success_rate < 100:
            print(f"      ⚠️  بعض صور {vtype} لا تحتوي على استجابة في القسم")
    
    print(f"\n💡 التوصيات:")
    if without_content:
        print(f"   🔧 فحص الثغرات التي لا تحتوي على استجابة")
        print(f"   🌐 التأكد من أن الموقع المستهدف يرجع استجابة حقيقية")
        print(f"   🔍 فحص كود التقاط الاستجابة للثغرات المحددة")

if __name__ == "__main__":
    check_real_response_content()
