#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 تشخيص شامل للسيرفر الحقيقي v4 - اختبار SERVER RESPONSE الحقيقي
================================================================================
هذا الملف يختبر:
1. تشغيل السيرفر الحقيقي python_web_service.py
2. إرسال بيانات حقيقية للـ endpoint /v4_website
3. فحص استجابة السيرفر الكاملة مع البيانات الحقيقية من v4
4. التحقق من عدم وجود أخطاء في قسم SERVER RESPONSE
"""

import asyncio
import sys
import os
import json
import time
import requests
import subprocess
import threading
from datetime import datetime
from pathlib import Path

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.insert(0, 'assets/modules/bugbounty')

class RealV4ServerDiagnostic:
    def __init__(self):
        self.server_url = "http://localhost:8000"
        self.server_process = None
        self.test_results = {}
        
    def start_real_server(self):
        """تشغيل السيرفر الحقيقي python_web_service.py"""
        print("🌐 تشغيل السيرفر الحقيقي python_web_service.py...")
        
        try:
            # التحقق إذا كان السيرفر يعمل بالفعل
            response = requests.get(f"{self.server_url}/health", timeout=3)
            if response.status_code == 200:
                print("✅ السيرفر يعمل بالفعل")
                return True
        except:
            pass
        
        # تشغيل السيرفر الحقيقي
        server_path = Path("assets/modules/bugbounty/python_web_service.py")
        if not server_path.exists():
            print(f"❌ ملف السيرفر غير موجود: {server_path}")
            return False
            
        print(f"🚀 تشغيل السيرفر من: {server_path}")
        self.server_process = subprocess.Popen([
            sys.executable, str(server_path)
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # انتظار تشغيل السيرفر
        print("⏳ انتظار تشغيل السيرفر...")
        for i in range(15):  # انتظار 15 ثانية
            try:
                time.sleep(1)
                response = requests.get(f"{self.server_url}/health", timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ السيرفر يعمل بنجاح: {data.get('service', 'Unknown')}")
                    return True
            except:
                print(f"⏳ محاولة {i+1}/15...")
                continue
        
        print("❌ فشل في تشغيل السيرفر")
        return False
    
    def test_real_vulnerability(self, vuln_type, url, payload, description):
        """اختبار ثغرة حقيقية مع السيرفر الحقيقي"""
        print(f"\n🔍 اختبار {vuln_type}: {description}")
        print(f"🌐 URL: {url}")
        print(f"💉 Payload: {payload}")
        
        try:
            # إنشاء بيانات v4 حقيقية كما يرسلها النظام الحقيقي
            v4_real_data = {
                'test_results': f'Real {vuln_type} vulnerability test results from v4 system',
                'exploitation_status': f'{vuln_type} exploitation successful',
                'verification_proof': f'Verified {vuln_type} vulnerability with payload: {payload}',
                'response_data': f'Complete HTTP response data for {vuln_type} vulnerability',
                'error_messages': [],
                'success_indicators': [
                    f'{vuln_type} payload executed successfully',
                    f'Server responded with vulnerable behavior',
                    f'Exploitation confirmed at {datetime.now()}'
                ],
                'vulnerability_meta': {
                    'type': vuln_type,
                    'severity': 'Critical',
                    'confidence': 'High',
                    'impact': f'Critical {vuln_type} vulnerability detected',
                    'remediation': f'Fix {vuln_type} vulnerability by proper input validation'
                },
                'original_v4_data': {
                    'scan_id': f'v4_scan_{int(time.time())}',
                    'target_url': url,
                    'payload_used': payload,
                    'vulnerability_type': vuln_type,
                    'timestamp': datetime.now().isoformat(),
                    'scanner_version': 'v4.0.0',
                    'detection_method': f'{vuln_type}_detection_engine'
                }
            }
            
            # البيانات التي يرسلها النظام v4 للسيرفر
            request_data = {
                'url': url,
                'filename': f'{vuln_type.lower()}_test',
                'report_id': f'diagnostic_{vuln_type.lower()}_{int(time.time())}',
                'vulnerability_name': f'{vuln_type} Test',
                'vulnerability_type': vuln_type,
                'stage': 'after',
                'payload_data': payload,
                'target_parameter': 'test_param',
                'response_callback': None,
                # البيانات الحقيقية من النظام v4
                'real_test_results': v4_real_data['test_results'],
                'real_exploitation_status': v4_real_data['exploitation_status'],
                'real_verification_proof': v4_real_data['verification_proof'],
                'real_response_data': v4_real_data['response_data'],
                'real_error_messages': v4_real_data['error_messages'],
                'real_success_indicators': v4_real_data['success_indicators'],
                'vulnerability_meta': v4_real_data['vulnerability_meta'],
                'v4_data_main': v4_real_data['original_v4_data']
            }
            
            print(f"📤 إرسال البيانات للسيرفر الحقيقي...")
            print(f"📊 حجم البيانات: {len(str(request_data))} حرف")
            
            # إرسال الطلب للسيرفر الحقيقي
            response = requests.post(
                f"{self.server_url}/v4_website",
                json=request_data,
                timeout=60,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"📥 استجابة السيرفر: {response.status_code}")
            
            if response.status_code == 200:
                result_data = response.json()
                
                # فحص البيانات المُستلمة
                success = result_data.get('success', False)
                screenshot_data = result_data.get('screenshot_data')
                v4_real_data_received = result_data.get('v4_real_data', {})
                
                print(f"✅ نجح الاختبار: {success}")
                print(f"📸 بيانات الصورة: {'موجودة' if screenshot_data else 'غير موجودة'}")
                print(f"📊 بيانات v4 المُستلمة: {len(str(v4_real_data_received))} حرف")
                
                # فحص تفصيلي لبيانات v4
                if v4_real_data_received:
                    print(f"🔍 فحص بيانات v4 المُستلمة:")
                    for key, value in v4_real_data_received.items():
                        if isinstance(value, str):
                            print(f"   - {key}: {len(value)} حرف - {value[:50]}...")
                        elif isinstance(value, list):
                            print(f"   - {key}: {len(value)} عنصر")
                        elif isinstance(value, dict):
                            print(f"   - {key}: {len(value)} مفتاح")
                        else:
                            print(f"   - {key}: {type(value)} - {value}")
                
                result = {
                    'vulnerability_type': vuln_type,
                    'url': url,
                    'payload': payload,
                    'server_response': result_data,
                    'v4_data_size': len(str(v4_real_data_received)),
                    'server_response_available': True,
                    'screenshot_available': bool(screenshot_data),
                    'status': 'SUCCESS' if success else 'PARTIAL_SUCCESS'
                }
                
                print(f"✅ نجح اختبار {vuln_type}")
                return result
                
            else:
                error_text = response.text
                print(f"❌ فشل الطلب: {response.status_code}")
                print(f"📄 رسالة الخطأ: {error_text}")
                
                return {
                    'vulnerability_type': vuln_type,
                    'status': 'FAILED',
                    'error': f'HTTP {response.status_code}: {error_text}',
                    'server_response_available': False
                }
                
        except Exception as e:
            print(f"❌ فشل اختبار {vuln_type}: {str(e)}")
            return {
                'vulnerability_type': vuln_type,
                'status': 'FAILED',
                'error': str(e),
                'server_response_available': False
            }
    
    def run_comprehensive_test(self):
        """تشغيل اختبار شامل للسيرفر الحقيقي"""
        print("🚀 بدء الاختبار الشامل للسيرفر الحقيقي v4")
        print("=" * 80)
        
        # قائمة الثغرات للاختبار مع مواقع حقيقية
        vulnerabilities = [
            {
                'type': 'XSS',
                'url': 'https://httpbin.org/html',
                'payload': "<script>alert('XSS_TEST')</script>",
                'description': 'Cross-Site Scripting vulnerability test with real server'
            },
            {
                'type': 'SQLi',
                'url': 'https://httpbin.org/get',
                'payload': "1' UNION SELECT 1,2,3--",
                'description': 'SQL Injection vulnerability test with real server'
            },
            {
                'type': 'LFI',
                'url': 'https://httpbin.org/anything',
                'payload': "../../../etc/passwd",
                'description': 'Local File Inclusion vulnerability test with real server'
            },
            {
                'type': 'RCE',
                'url': 'https://httpbin.org/post',
                'payload': "whoami;id;uname -a",
                'description': 'Remote Code Execution vulnerability test with real server'
            }
        ]
        
        results = []
        
        for vuln in vulnerabilities:
            result = self.test_real_vulnerability(
                vuln['type'],
                vuln['url'],
                vuln['payload'],
                vuln['description']
            )
            results.append(result)
            
            # انتظار قصير بين الاختبارات
            time.sleep(3)
        
        self.test_results = results
        return results

    def generate_report(self):
        """إنشاء تقرير شامل للسيرفر الحقيقي"""
        print("\n" + "=" * 80)
        print("📋 تقرير الاختبار الشامل للسيرفر الحقيقي v4")
        print("=" * 80)

        successful_tests = [r for r in self.test_results if r.get('status') == 'SUCCESS']
        partial_tests = [r for r in self.test_results if r.get('status') == 'PARTIAL_SUCCESS']
        failed_tests = [r for r in self.test_results if r.get('status') == 'FAILED']

        print(f"📊 إجمالي الاختبارات: {len(self.test_results)}")
        print(f"✅ نجحت بالكامل: {len(successful_tests)}")
        print(f"⚠️ نجحت جزئياً: {len(partial_tests)}")
        print(f"❌ فشلت: {len(failed_tests)}")

        print("\n🔍 تحليل مفصل للسيرفر الحقيقي:")

        for result in self.test_results:
            vuln_type = result.get('vulnerability_type', 'Unknown')
            status = result.get('status', 'Unknown')

            print(f"\n📋 {vuln_type}:")
            print(f"   حالة: {'✅' if status == 'SUCCESS' else '⚠️' if status == 'PARTIAL_SUCCESS' else '❌'} {status}")

            if status in ['SUCCESS', 'PARTIAL_SUCCESS']:
                print(f"   URL: {result.get('url', 'N/A')}")
                print(f"   Payload: {result.get('payload', 'N/A')}")
                print(f"   حجم بيانات v4: {result.get('v4_data_size', 0)} حرف")
                print(f"   استجابة السيرفر: ✅ متوفرة")
                print(f"   الصورة: {'✅ متوفرة' if result.get('screenshot_available') else '❌ غير متوفرة'}")

                # فحص تفصيلي لاستجابة السيرفر
                server_response = result.get('server_response', {})
                if server_response:
                    print(f"   📊 تفاصيل استجابة السيرفر:")
                    print(f"      - success: {server_response.get('success', 'N/A')}")
                    print(f"      - screenshot_data: {'موجود' if server_response.get('screenshot_data') else 'غير موجود'}")
                    print(f"      - v4_real_data: {'موجود' if server_response.get('v4_real_data') else 'غير موجود'}")
                    print(f"      - file_path: {server_response.get('file_path', 'N/A')}")
                    print(f"      - file_size: {server_response.get('file_size', 'N/A')} بايت")

            else:
                print(f"   الخطأ: {result.get('error', 'Unknown error')}")
                print(f"   استجابة السيرفر: ❌ غير متوفرة")

        # حفظ التقرير في ملف JSON
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'server_url': self.server_url,
            'total_tests': len(self.test_results),
            'successful_tests': len(successful_tests),
            'partial_tests': len(partial_tests),
            'failed_tests': len(failed_tests),
            'success_rate': (len(successful_tests) + len(partial_tests)) / len(self.test_results) * 100,
            'detailed_results': self.test_results
        }

        with open('real_v4_server_diagnostic_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)

        print(f"\n💾 تم حفظ التقرير المفصل في: real_v4_server_diagnostic_report.json")

        # التوصيات
        print("\n💡 التوصيات:")
        if len(successful_tests) == len(self.test_results):
            print("✅ جميع الاختبارات نجحت - السيرفر الحقيقي v4 يعمل بشكل مثالي")
            print("✅ قسم SERVER RESPONSE يحتوي على جميع البيانات المطلوبة")
            print("✅ لا توجد أخطاء في معالجة الاستجابات الحقيقية")
        elif len(successful_tests) + len(partial_tests) > 0:
            print("⚠️ بعض الاختبارات نجحت جزئياً - يحتاج مراجعة")
            for failed in failed_tests:
                print(f"   - {failed.get('vulnerability_type')}: {failed.get('error')}")
        else:
            print("❌ جميع الاختبارات فشلت - يحتاج فحص السيرفر")

    def cleanup(self):
        """تنظيف الموارد"""
        print("\n🔒 تنظيف الموارد...")

        if self.server_process:
            print("⏹️ إيقاف السيرفر...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
                print("✅ تم إيقاف السيرفر بنجاح")
            except subprocess.TimeoutExpired:
                print("⚠️ إجبار إيقاف السيرفر...")
                self.server_process.kill()
                self.server_process.wait()
                print("✅ تم إجبار إيقاف السيرفر")

        print("✅ تم تنظيف جميع الموارد")

def main():
    """الدالة الرئيسية"""
    diagnostic = RealV4ServerDiagnostic()

    try:
        print("🔍 بدء التشخيص الشامل للسيرفر الحقيقي v4")
        print("=" * 80)

        # تشغيل السيرفر الحقيقي
        if not diagnostic.start_real_server():
            print("❌ فشل في تشغيل السيرفر الحقيقي - إنهاء الاختبار")
            return

        # تشغيل الاختبارات
        diagnostic.run_comprehensive_test()

        # إنشاء التقرير
        diagnostic.generate_report()

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # تنظيف الموارد
        diagnostic.cleanup()

if __name__ == "__main__":
    print("🔍 بدء التشخيص الشامل للسيرفر الحقيقي v4")
    main()
