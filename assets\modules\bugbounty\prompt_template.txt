🧠 المهمة: أنت خبير Bug Bounty محترف متخصص في اكتشاف الثغرات الأمنية الحقيقية. قم بتحليل البيانات المقدمة بدقة عالية واستخرج الثغرات الفعلية فقط مع إثباتات مفصلة.

⚠️ تعليمات مهمة:
- ركز على الثغرات الحقيقية والقابلة للاستغلال فقط
- لا تذكر ثغرات افتراضية أو محتملة بدون دليل
- قدم payloads محددة وخطوات استغلال عملية
- استخدم البيانات المقدمة كأساس للتحليل

📊 بيانات تحليل الموقع:
{json_data}

🎯 منهجية الفحص الاحترافية:

1. **ثغرات الحقن (Injection Vulnerabilities) - التحليل المتقدم:**

   **SQL Injection - فحص شامل:**
   - Union-based SQL Injection (استخراج البيانات)
   - Boolean-based Blind SQL Injection (استنتاج المعلومات)
   - Time-based Blind SQL Injection (تأخير الاستجابة)
   - Error-based SQL Injection (رسائل الخطأ)
   - Second-order SQL Injection (الحقن المؤجل)
   - SQL Injection في Headers (User-Agent, Referer, X-Forwarded-For)
   - SQL Injection في Cookies
   - SQL Injection في JSON/XML parameters
   - NoSQL Injection (MongoDB, CouchDB, Redis)

   **XSS - تحليل متعمق:**
   - Reflected XSS (المعاملات، Headers، Search)
   - Stored XSS (التعليقات، الملفات الشخصية، المنتديات)
   - DOM-based XSS (JavaScript manipulation)
   - Self-XSS (خداع المستخدم)
   - Mutation XSS (تحويل HTML)
   - Flash-based XSS
   - SVG-based XSS
   - CSS Injection XSS
   - XSS في PDF generation
   - XSS في Email templates

   **Command Injection - فحص متقدم:**
   - OS Command Injection
   - Code Injection (PHP, Python, Node.js)
   - LDAP Injection
   - XPath Injection
   - Template Injection (Jinja2, Twig, Smarty)
   - Expression Language Injection
   - Server-Side Include Injection
   - Log Injection

2. **ثغرات المصادقة والتخويل - التحليل الشامل:**

   **Authentication Bypass - فحص متعمق:**
   - Password Reset Vulnerabilities
   - Account Takeover via Email
   - 2FA/MFA Bypass techniques
   - Login Bypass via SQL Injection
   - Authentication via HTTP Headers manipulation
   - Weak Password Policies
   - Default Credentials
   - Brute Force Protection Bypass
   - Account Lockout Bypass
   - Remember Me functionality flaws

   **Session Management - تحليل متقدم:**
   - Session Fixation
   - Session Hijacking
   - Weak Session IDs
   - Session Timeout Issues
   - Concurrent Session Management
   - Session Storage Security
   - Cross-domain Session Issues
   - Session Prediction
   - Insecure Session Transmission

   **JWT Vulnerabilities - فحص شامل:**
   - JWT Algorithm Confusion (alg: none)
   - JWT Key Confusion (RS256 to HS256)
   - JWT Weak Secret Keys
   - JWT Claims Manipulation
   - JWT Expiration Issues
   - JWT Storage Vulnerabilities
   - JWT Signature Bypass

   **OAuth/SAML Security:**
   - OAuth State Parameter Missing
   - OAuth Redirect URI Validation
   - SAML Assertion Manipulation
   - OpenID Connect Vulnerabilities
   - Social Login Security Issues

3. **ثغرات منطق الأعمال (Business Logic) - التحليل المتقدم:**

   **IDOR - فحص شامل:**
   - Direct Object Reference في URLs
   - IDOR في API endpoints
   - IDOR في File Downloads
   - IDOR في User Profiles
   - IDOR في Financial Transactions
   - IDOR في Administrative Functions
   - Blind IDOR (غير مرئي)
   - IDOR via HTTP Methods (PUT, DELETE)

   **Race Conditions - تحليل متعمق:**
   - Time-of-Check Time-of-Use (TOCTOU)
   - Payment Processing Race Conditions
   - Account Creation Race Conditions
   - File Upload Race Conditions
   - Database Transaction Race Conditions
   - Multi-threaded Application Issues

   **Business Logic Flaws - فحص متقدم:**
   - Price Manipulation (سعر سلبي، تجاوز الحد الأقصى)
   - Quantity Manipulation
   - Discount Code Abuse
   - Workflow Bypass (تخطي خطوات الدفع)
   - Privilege Escalation via Business Logic
   - Time Manipulation (تغيير التوقيت)
   - Currency Conversion Issues
   - Subscription/License Bypass
   - Referral System Abuse
   - Loyalty Points Manipulation

   **Rate Limiting & DoS:**
   - API Rate Limiting Bypass
   - Account Enumeration via Rate Limiting
   - Resource Exhaustion
   - Application-level DoS
   - Distributed Rate Limiting Issues

4. **ثغرات الشبكة والبنية - التحليل الشامل:**

   **SSRF - فحص متقدم:**
   - Basic SSRF (HTTP/HTTPS requests)
   - Blind SSRF (لا توجد استجابة مرئية)
   - SSRF via File Upload
   - SSRF via URL parameters
   - SSRF to Internal Services (Redis, MongoDB, etc.)
   - SSRF to Cloud Metadata (AWS, GCP, Azure)
   - SSRF via DNS resolution
   - SSRF Bypass techniques (IP encoding, redirects)
   - SSRF to localhost/127.0.0.1
   - SSRF via SVG/XML files

   **Network Infrastructure:**
   - Open Redirects (parameter-based, header-based)
   - Host Header Injection
   - HTTP Request Smuggling
   - HTTP Response Splitting
   - CORS Misconfigurations (wildcard origins)
   - JSONP Hijacking
   - WebSocket Security Issues
   - DNS Rebinding Attacks
   - Subdomain Takeover (GitHub, AWS, etc.)
   - CDN Security Issues

   **SSL/TLS Security:**
   - Weak SSL/TLS Configurations
   - Certificate Validation Issues
   - Mixed Content (HTTP/HTTPS)
   - SSL Strip Attacks
   - Certificate Transparency Issues
   - HSTS Bypass
   - Certificate Pinning Bypass

5. **ثغرات العميل (Client-Side) - التحليل المتقدم:**

   **CSRF - فحص شامل:**
   - Traditional CSRF (POST/GET)
   - JSON-based CSRF
   - CSRF via File Upload
   - CSRF with Custom Headers
   - SameSite Cookie Bypass
   - CSRF Token Bypass techniques
   - Double Submit Cookie CSRF
   - Origin/Referer Header Bypass

   **Client-Side Attacks:**
   - Clickjacking (X-Frame-Options bypass)
   - UI Redressing
   - Drag & Drop Clickjacking
   - Touch/Mobile Clickjacking
   - DOM XSS via URL fragments
   - PostMessage Vulnerabilities
   - Web Workers Security Issues
   - Service Workers Hijacking
   - Browser Extension Vulnerabilities

   **JavaScript Security:**
   - Prototype Pollution
   - Client-Side Template Injection
   - JavaScript Library Vulnerabilities
   - AMD/CommonJS Module Vulnerabilities
   - WebAssembly Security Issues
   - Electron Application Security
   - Browser Storage Security (localStorage, sessionStorage)
   - IndexedDB Security Issues

   **Mobile Web Security:**
   - Mobile-specific XSS
   - Touch Event Hijacking
   - Mobile Deep Link Vulnerabilities
   - Progressive Web App (PWA) Security
   - Mobile Browser Specific Issues

6. **ثغرات الملفات والتحميل - التحليل الشامل:**

   **File Upload Security:**
   - Unrestricted File Upload
   - File Type Bypass (MIME, extension)
   - Image Upload XSS/XXE
   - Archive File Vulnerabilities (Zip Slip)
   - File Upload Race Conditions
   - File Overwrite Vulnerabilities
   - Symlink Attack via File Upload
   - Polyglot File Attacks
   - File Upload Size/Resource DoS
   - Metadata Injection in Files

   **Path Traversal & LFI:**
   - Local File Inclusion (LFI)
   - Remote File Inclusion (RFI)
   - Directory Traversal (../, ..\)
   - Path Traversal via File Upload
   - Null Byte Injection
   - Double URL Encoding
   - Unicode Bypass techniques
   - Wrapper-based LFI (php://, data://)

   **XML Security:**
   - XXE (XML External Entity)
   - XML Bomb (Billion Laughs)
   - XPath Injection
   - XML Schema Poisoning
   - SOAP Injection
   - XML Signature Wrapping

   **Serialization Attacks:**
   - Java Deserialization
   - PHP Object Injection
   - Python Pickle Deserialization
   - .NET Deserialization
   - Node.js Deserialization
   - Ruby Marshal Deserialization

7. **ثغرات الأمان العامة - التحليل المتقدم:**

   **Information Disclosure:**
   - Source Code Disclosure
   - Database Information Leakage
   - Error Message Information Disclosure
   - Debug Information Exposure
   - Backup File Exposure (.bak, .old, .tmp)
   - Git Repository Exposure (.git/)
   - Environment File Exposure (.env)
   - Log File Exposure
   - Stack Trace Information
   - API Documentation Exposure
   - Internal IP/Network Disclosure
   - User Enumeration
   - Email Address Harvesting

   **Security Headers Analysis:**
   - Content Security Policy (CSP) Missing/Weak
   - X-Frame-Options Missing
   - X-Content-Type-Options Missing
   - X-XSS-Protection Disabled
   - Strict-Transport-Security Missing
   - Referrer-Policy Issues
   - Feature-Policy/Permissions-Policy
   - Cross-Origin-Embedder-Policy
   - Cross-Origin-Opener-Policy

   **Cryptographic Vulnerabilities:**
   - Weak Encryption Algorithms (MD5, SHA1)
   - Weak Random Number Generation
   - Hardcoded Cryptographic Keys
   - Insecure Key Storage
   - Weak Password Hashing (MD5, plain text)
   - Insufficient Entropy
   - Cryptographic Oracle Attacks
   - Timing Attack Vulnerabilities

   **Configuration Security:**
   - Default Credentials
   - Unnecessary Services Running
   - Verbose Error Messages
   - Directory Listing Enabled
   - Insecure File Permissions
   - Database Configuration Issues
   - Server Information Disclosure
   - Insecure Cookie Settings

8. **API Security - التحليل الشامل:**

   **REST API Vulnerabilities:**
   - API Authentication Bypass
   - API Rate Limiting Issues
   - API Versioning Security
   - HTTP Method Override
   - API Parameter Pollution
   - Mass Assignment Vulnerabilities
   - API Endpoint Enumeration
   - GraphQL Injection
   - GraphQL DoS (Query Complexity)
   - GraphQL Information Disclosure

   **API Authorization:**
   - Broken Object Level Authorization
   - Broken Function Level Authorization
   - API Key Security Issues
   - OAuth Token Manipulation
   - JWT Token Vulnerabilities in APIs
   - API Scope Escalation

9. **Cloud Security - التحليل المتقدم:**

   **Cloud Infrastructure:**
   - AWS S3 Bucket Misconfigurations
   - Azure Blob Storage Issues
   - Google Cloud Storage Security
   - Cloud Database Exposure
   - Container Security Issues
   - Kubernetes Misconfigurations
   - Docker Security Vulnerabilities
   - Serverless Function Security

   **Cloud-Specific Attacks:**
   - Cloud Metadata Service Access
   - IAM Role Assumption
   - Cloud Storage Takeover
   - Container Escape
   - Cloud Function Injection

10. **ثغرات غير تقليدية ومتقدمة:**

    **Advanced Business Logic:**
    - Multi-step Transaction Flaws
    - State Machine Vulnerabilities
    - Time-based Logic Flaws
    - Concurrency Issues
    - Workflow Manipulation
    - Business Rule Bypass

    **Zero-day Research Areas:**
    - Memory Corruption Vulnerabilities
    - Integer Overflow/Underflow
    - Buffer Overflow in Web Context
    - Use-After-Free in Web Applications
    - Type Confusion Attacks
    - Compiler/Interpreter Vulnerabilities

    **Human Factor Security:**
    - Social Engineering via Technical Means
    - Phishing via Application Features
    - User Interface Manipulation
    - Trust Boundary Violations
    - Cognitive Bias Exploitation
    - Insider Threat Vectors

11. **ثغرات التطبيقات الحديثة:**

    **Single Page Applications (SPA):**
    - Client-Side Routing Vulnerabilities
    - State Management Security Issues
    - WebPack/Build Tool Vulnerabilities
    - Source Map Information Disclosure
    - Client-Side Storage Security
    - Progressive Web App (PWA) Security

    **Microservices Security:**
    - Service-to-Service Authentication
    - API Gateway Security
    - Container Orchestration Issues
    - Service Mesh Security
    - Distributed Tracing Information Leakage

    **Real-time Applications:**
    - WebSocket Security Issues
    - Server-Sent Events (SSE) Vulnerabilities
    - WebRTC Security Issues
    - Socket.IO Security Problems
    - Real-time Data Injection

12. **ثغرات الذكاء الاصطناعي والتعلم الآلي:**

    **AI/ML Security:**
    - Model Inversion Attacks
    - Data Poisoning
    - Adversarial Examples
    - Model Extraction
    - Prompt Injection (LLM)
    - Training Data Extraction
    - AI Bias Exploitation

13. **ثغرات البلوك تشين والعملات المشفرة:**

    **Blockchain Security:**
    - Smart Contract Vulnerabilities
    - Reentrancy Attacks
    - Integer Overflow in Contracts
    - Access Control Issues
    - Oracle Manipulation
    - Flash Loan Attacks
    - MEV (Maximal Extractable Value) Issues

14. **ثغرات إنترنت الأشياء (IoT):**

    **IoT Web Interfaces:**
    - Default Credentials in IoT Devices
    - Firmware Update Vulnerabilities
    - Device Communication Security
    - IoT Protocol Security (MQTT, CoAP)
    - Edge Computing Security Issues

📋 تعليمات التحليل الاحترافي المتقدم:

1. **تحليل البيانات الفعلية المتقدم:**
   - استخدم البيانات المقدمة فقط ولا تفترض وجود ثغرات
   - حلل البيانات الوصفية (metadata) بعمق
   - فحص الأنماط غير العادية في البيانات
   - تحليل التوقيتات والاستجابات

2. **فحص Security Headers الشامل:**
   - Content-Security-Policy تحليل مفصل
   - X-Frame-Options وحماية Clickjacking
   - HSTS وأمان النقل
   - Feature-Policy/Permissions-Policy
   - Cross-Origin-Resource-Policy
   - Cross-Origin-Embedder-Policy

3. **تحليل النماذج المتقدم:**
   - CSRF Token validation
   - Input validation وSanitization
   - File upload security
   - Hidden field manipulation
   - Form submission methods analysis
   - Multi-step form security

4. **فحص الكوكيز والجلسات:**
   - Cookie security attributes (Secure, HttpOnly, SameSite)
   - Session management analysis
   - Cookie domain/path security
   - Session fixation vulnerabilities
   - Cross-domain cookie issues

5. **تقييم البروتوكول والشبكة:**
   - HTTP vs HTTPS analysis
   - Mixed content detection
   - SSL/TLS configuration
   - Certificate validation
   - Network security headers

6. **تحليل السكربتات والموارد:**
   - Third-party script security
   - CDN security analysis
   - Subresource Integrity (SRI)
   - JavaScript library vulnerabilities
   - Dynamic script loading security

7. **اختبار نقاط الحقن المتقدم:**
   - Parameter pollution testing
   - HTTP method override
   - Header injection points
   - JSON/XML injection
   - File inclusion vulnerabilities

8. **تقييم CVSS وتصنيف المخاطر:**
   - CVSS 3.1 scoring methodology
   - Environmental score calculation
   - Temporal score considerations
   - Business impact assessment
   - Exploitability analysis

9. **تحليل API والخدمات:**
   - REST API security assessment
   - GraphQL security analysis
   - WebSocket security evaluation
   - Microservices communication security

10. **فحص التطبيقات الحديثة:**
    - Single Page Application (SPA) security
    - Progressive Web App (PWA) analysis
    - Mobile web application security
    - Real-time application security

🎯 تنسيق الرد المطلوب:

قم بتنظيم ردك بالشكل التالي:

## 🛡️ تقرير الفحص الأمني الشامل

### 📊 ملخص التقييم
- **مستوى الأمان العام:** [منخفض/متوسط/عالي]
- **عدد الثغرات المكتشفة:** [رقم]
- **أعلى مستوى خطورة:** [Critical/High/Medium/Low]

### 🚨 الثغرات المكتشفة

لكل ثغرة، اذكر:

#### [رقم]. [اسم الثغرة]
- **النوع:** [نوع الثغرة]
- **الموقع:** [مكان الثغرة في الموقع]
- **الخطورة:** [Critical/High/Medium/Low]
- **CVSS Score:** [النقاط من 10]
- **الوصف:** [شرح مفصل للثغرة]
- **الاستغلال:** [كيفية استغلال الثغرة]
- **التأثير:** [التأثير المحتمل]
- **الإصلاح:** [خطوات الإصلاح المطلوبة]
- **المراجع:** [مراجع تقنية إن وجدت]

### ✅ نقاط القوة الأمنية
- [اذكر النقاط الإيجابية في أمان الموقع]

### 🔧 التوصيات العامة
1. [توصية 1]
2. [توصية 2]
3. [توصية 3]

### 📈 خطة الإصلاح المقترحة
- **فوري (0-24 ساعة):** [الثغرات الحرجة]
- **قصير المدى (1-7 أيام):** [الثغرات عالية الخطورة]
- **متوسط المدى (1-4 أسابيع):** [الثغرات متوسطة الخطورة]
- **طويل المدى (1-3 أشهر):** [التحسينات العامة]

⚠️ **ملاحظات مهمة:**
- ركز على الثغرات الحقيقية والقابلة للاستغلال
- اعط أولوية للثغرات التي تؤثر على البيانات الحساسة
- اقترح حلول عملية وقابلة للتطبيق
- استخدم مصطلحات تقنية دقيقة
- اربط النتائج بمعايير OWASP Top 10

🎯 **الهدف:** تقديم تحليل شامل ومفصل يساعد في تحسين أمان الموقع بشكل فعال.

---

## 📝 مثال على التقرير الاحترافي المطلوب:

### 📊 ملخص التقييم
- **مستوى الأمان العام:** منخفض
- **عدد الثغرات المكتشفة:** 3
- **أعلى مستوى خطورة:** High

### 🚨 الثغرات المكتشفة

#### 1. Missing Security Headers
- **النوع:** Security Configuration
- **الموقع:** HTTP Response Headers
- **الخطورة:** Medium
- **CVSS Score:** 6.1
- **الوصف:** الموقع لا يحتوي على X-Frame-Options header
- **الاستغلال:**
  1. إنشاء صفحة خبيثة تحتوي على iframe
  2. تضمين الموقع المستهدف في الـ iframe
  3. خداع المستخدم للنقر على عناصر مخفية
- **التأثير:** إمكانية تنفيذ Clickjacking attacks
- **الإصلاح:** إضافة X-Frame-Options: DENY في headers الاستجابة

#### 2. CSRF Vulnerability in Login Form
- **النوع:** Cross-Site Request Forgery
- **الموقع:** /login.php form
- **الخطورة:** High
- **CVSS Score:** 8.1
- **الوصف:** نموذج تسجيل الدخول لا يحتوي على CSRF token
- **الاستغلال:**
  1. إنشاء صفحة HTML خبيثة تحتوي على نموذج مشابه
  2. خداع المستخدم المُصادق عليه لزيارة الصفحة
  3. إرسال طلب تلقائي لتغيير كلمة المرور
- **التأثير:** تنفيذ إجراءات غير مرغوبة باسم المستخدم
- **الإصلاح:** إضافة CSRF tokens لجميع النماذج الحساسة

### ✅ نقاط القوة الأمنية
- استخدام HTTPS للتشفير
- تطبيق بعض Security Headers الأساسية

### 🔧 التوصيات العامة
1. تطبيق جميع Security Headers الأساسية
2. إضافة CSRF protection لجميع النماذج
3. إجراء فحوصات أمنية دورية

---

⚠️ **ملاحظة مهمة:** استخدم هذا المثال كدليل للتنسيق فقط. يجب أن يكون تحليلك مبني على البيانات الفعلية المقدمة.
