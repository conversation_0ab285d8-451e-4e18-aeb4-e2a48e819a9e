#!/usr/bin/env python3
"""
اختبار مباشر لمشكلة 404 في صور بعد الاستغلال
"""

import requests
import json
import time

def test_different_vulnerabilities():
    """اختبار ثغرات مختلفة لرؤية أيها يسبب 404"""
    
    vulnerabilities = [
        {
            "name": "SQL Injection",
            "url": "https://httpbin.org/get?id=1",
            "payload": "' UNION SELECT 1,2,3 --",
            "type": "SQL Injection"
        },
        {
            "name": "XSS",
            "url": "https://httpbin.org/get?search=test",
            "payload": "<script>alert('XSS')</script>",
            "type": "Cross-Site Scripting"
        },
        {
            "name": "Command Injection",
            "url": "https://httpbin.org/get?cmd=whoami",
            "payload": "; ls -la",
            "type": "Command Injection"
        },
        {
            "name": "LFI",
            "url": "https://httpbin.org/get?file=index.php",
            "payload": "../../../../etc/passwd",
            "type": "Local File Inclusion"
        }
    ]
    
    print("🔥 اختبار ثغرات مختلفة لرؤية مشكلة 404...")
    
    for vuln in vulnerabilities:
        print(f"\n{'='*60}")
        print(f"🎯 اختبار: {vuln['name']}")
        print(f"🔗 URL: {vuln['url']}")
        print(f"💉 Payload: {vuln['payload']}")
        
        # بيانات الطلب
        test_data = {
            "url": vuln["url"],
            "filename": f"test_{vuln['name'].lower().replace(' ', '_')}",
            "report_id": "404_test",
            "vulnerability_name": vuln["name"],
            "vulnerability_type": vuln["type"],
            "stage": "after",
            "payload_data": vuln["payload"],
            "target_parameter": list(vuln["url"].split('?')[1].split('&')[0].split('='))[0] if '?' in vuln["url"] else "id"
        }
        
        try:
            print(f"📤 إرسال طلب...")
            response = requests.post(
                "http://localhost:8000/v4_website",
                json=test_data,
                timeout=120
            )
            
            print(f"📥 كود الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ نجح الطلب!")
                
                # فحص URL المستهدف
                if 'target_url' in result:
                    target_url = result['target_url']
                    print(f"🔗 URL المستهدف: {target_url}")
                    
                    # اختبار URL
                    try:
                        url_response = requests.get(target_url, timeout=10)
                        if url_response.status_code == 404:
                            print(f"❌ مشكلة 404 مكتشفة في {vuln['name']}!")
                            print(f"🔧 URL المشكل: {target_url}")
                        else:
                            print(f"✅ URL يعمل بشكل صحيح: {url_response.status_code}")
                    except Exception as url_error:
                        print(f"❌ خطأ في اختبار URL: {url_error}")
                
                # فحص مسار الصورة
                if 'screenshot_path' in result:
                    screenshot_path = result['screenshot_path']
                    print(f"📸 مسار الصورة: {screenshot_path}")
                
            else:
                print(f"❌ فشل الطلب: {response.status_code}")
                print(f"📄 الاستجابة: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print(f"⏰ انتهت مهلة الطلب للثغرة: {vuln['name']}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {vuln['name']}: {e}")
        
        print(f"⏳ انتظار 5 ثواني قبل الاختبار التالي...")
        time.sleep(5)

def main():
    print("🔥" * 60)
    print("🔥 اختبار مباشر لمشكلة 404 في صور بعد الاستغلال")
    print("🔥" * 60)
    
    # اختبار صحة السيرفر
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"✅ السيرفر يعمل: {health_response.status_code}")
    except Exception as e:
        print(f"❌ السيرفر لا يعمل: {e}")
        return
    
    # اختبار الثغرات
    test_different_vulnerabilities()
    
    print("\n" + "🔥" * 60)
    print("🔥 انتهى الاختبار")
    print("🔥" * 60)

if __name__ == "__main__":
    main()
