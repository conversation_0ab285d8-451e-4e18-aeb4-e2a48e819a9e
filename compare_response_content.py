#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مقارنة محتوى الاستجابة الفعلي في قسم REAL SERVER RESPONSE بين الثغرات المختلفة
"""

import os
import glob
from PIL import Image
import requests

def compare_response_content():
    """مقارنة محتوى الاستجابة بين الثغرات المختلفة"""
    
    print("🔍 مقارنة محتوى الاستجابة بين الثغرات المختلفة...")
    
    # تعريف الثغرات المختبرة مع URLs الخاصة بها
    vulnerabilities = {
        'SQL Injection': 'https://httpbin.org/get?id=1',
        'XSS': 'https://httpbin.org/get?search=test', 
        'Command Injection': 'https://httpbin.org/get?cmd=whoami',
        'Directory Traversal': 'https://httpbin.org/get?file=config.txt',
        'LDAP Injection': 'https://httpbin.org/get?username=admin',
        'XXE Injection': 'https://httpbin.org/post',
        'SSTI': 'https://httpbin.org/get?template=hello'
    }
    
    print("🌐 أولاً: فحص الاستجابات الحقيقية من المواقع...")
    
    # فحص الاستجابة الحقيقية من كل URL
    real_responses = {}
    
    for vuln_name, url in vulnerabilities.items():
        try:
            print(f"\n📡 اختبار {vuln_name}:")
            print(f"   🔗 URL: {url}")
            
            if 'post' in url:
                # طلب POST للـ XXE
                response = requests.post(url, 
                    data='<?xml version="1.0"?><!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><root>&xxe;</root>',
                    headers={'Content-Type': 'application/xml'},
                    timeout=10)
            else:
                # طلب GET للباقي
                response = requests.get(url, timeout=10)
            
            print(f"   📥 Status Code: {response.status_code}")
            print(f"   📊 Response Size: {len(response.text)} characters")
            print(f"   📋 Content-Type: {response.headers.get('Content-Type', 'غير محدد')}")
            
            # عرض أول 200 حرف من الاستجابة
            preview = response.text[:200].replace('\n', ' ').replace('\r', '')
            print(f"   📝 معاينة الاستجابة: {preview}...")
            
            real_responses[vuln_name] = {
                'status_code': response.status_code,
                'content_type': response.headers.get('Content-Type', ''),
                'size': len(response.text),
                'content': response.text,
                'preview': preview
            }
            
            # تحليل نوع المحتوى
            if 'application/json' in response.headers.get('Content-Type', ''):
                print(f"   ✅ استجابة JSON - يجب أن تظهر في قسم REAL SERVER RESPONSE")
                try:
                    import json
                    json_data = json.loads(response.text)
                    print(f"   📊 JSON keys: {list(json_data.keys())}")
                except:
                    print(f"   ⚠️ JSON غير صالح")
            elif 'text/html' in response.headers.get('Content-Type', ''):
                print(f"   📄 استجابة HTML")
            else:
                print(f"   📄 نوع محتوى آخر")
                
        except Exception as e:
            print(f"   ❌ خطأ في الطلب: {e}")
            real_responses[vuln_name] = {
                'error': str(e),
                'status_code': 0,
                'size': 0
            }
    
    # مقارنة الاستجابات
    print(f"\n" + "="*80)
    print("📊 مقارنة الاستجابات الحقيقية:")
    print("="*80)
    
    # ترتيب حسب حجم الاستجابة
    sorted_responses = sorted(real_responses.items(), 
                            key=lambda x: x[1].get('size', 0), 
                            reverse=True)
    
    for vuln_name, response_data in sorted_responses:
        if 'error' not in response_data:
            print(f"\n🎯 {vuln_name}:")
            print(f"   📊 حجم الاستجابة: {response_data['size']} حرف")
            print(f"   📋 نوع المحتوى: {response_data['content_type']}")
            print(f"   📝 معاينة: {response_data['preview']}")
            
            # تحديد جودة الاستجابة للعرض في الصورة
            if response_data['size'] > 500:
                print(f"   ✅ استجابة غنية - يجب أن تملأ قسم REAL SERVER RESPONSE")
            elif response_data['size'] > 200:
                print(f"   📊 استجابة متوسطة")
            else:
                print(f"   ⚠️ استجابة قصيرة - قد تبدو فارغة في القسم")
        else:
            print(f"\n❌ {vuln_name}: خطأ - {response_data['error']}")
    
    # تحليل الفروق
    print(f"\n🔍 تحليل الفروق:")
    
    # العثور على أكبر وأصغر استجابة
    valid_responses = {k: v for k, v in real_responses.items() if 'error' not in v}
    
    if len(valid_responses) > 1:
        largest = max(valid_responses.items(), key=lambda x: x[1]['size'])
        smallest = min(valid_responses.items(), key=lambda x: x[1]['size'])
        
        print(f"📈 أكبر استجابة: {largest[0]} ({largest[1]['size']} حرف)")
        print(f"📉 أصغر استجابة: {smallest[0]} ({smallest[1]['size']} حرف)")
        print(f"📊 الفرق: {largest[1]['size'] - smallest[1]['size']} حرف")
        
        if largest[1]['size'] - smallest[1]['size'] > 100:
            print(f"⚠️ فرق كبير في أحجام الاستجابات!")
            print(f"💡 هذا يفسر لماذا بعض الثغرات تظهر استجابة أكثر من أخرى")
    
    # فحص نوع المحتوى
    json_responses = [k for k, v in valid_responses.items() 
                     if 'application/json' in v.get('content_type', '')]
    html_responses = [k for k, v in valid_responses.items() 
                     if 'text/html' in v.get('content_type', '')]
    
    print(f"\n📋 تصنيف حسب نوع المحتوى:")
    if json_responses:
        print(f"✅ استجابات JSON (غنية): {', '.join(json_responses)}")
    if html_responses:
        print(f"📄 استجابات HTML: {', '.join(html_responses)}")
    
    # التوصيات
    print(f"\n💡 التفسير والحلول:")
    print(f"🌐 جميع الطلبات تذهب لـ httpbin.org لكن مع معاملات مختلفة")
    print(f"📊 httpbin.org يرجع نفس البنية JSON لكن مع معاملات مختلفة")
    print(f"🔍 الفرق في المحتوى يأتي من:")
    print(f"   • معاملات URL مختلفة (id, search, cmd, file, etc.)")
    print(f"   • طول أسماء المعاملات")
    print(f"   • نوع الطلب (GET vs POST)")
    
    print(f"\n🔧 لجعل جميع الثغرات تعرض استجابة غنية:")
    print(f"   1. استخدام URLs مختلفة لكل ثغرة")
    print(f"   2. إضافة معاملات أكثر للحصول على استجابة أكبر")
    print(f"   3. استخدام مواقع مختلفة حسب نوع الثغرة")
    
    # اقتراح URLs محسنة
    print(f"\n🚀 اقتراح URLs محسنة للحصول على استجابات أغنى:")
    enhanced_urls = {
        'SQL Injection': 'https://httpbin.org/get?id=1&user=admin&table=users&debug=true',
        'XSS': 'https://httpbin.org/get?search=<script>alert(1)</script>&page=1&sort=name',
        'Command Injection': 'https://httpbin.org/get?cmd=whoami&system=linux&shell=bash',
        'Directory Traversal': 'https://httpbin.org/get?file=../../../etc/passwd&path=/var/www',
        'LDAP Injection': 'https://httpbin.org/get?username=admin&domain=company.com&filter=*',
        'XXE Injection': 'https://httpbin.org/post',  # يبقى كما هو
        'SSTI': 'https://httpbin.org/get?template={{7*7}}&engine=jinja2&data=test'
    }
    
    for vuln_name, enhanced_url in enhanced_urls.items():
        print(f"   🎯 {vuln_name}: {enhanced_url}")

if __name__ == "__main__":
    compare_response_content()
