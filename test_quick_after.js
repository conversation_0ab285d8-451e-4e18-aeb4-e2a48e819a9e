const https = require('https');
const http = require('http');

async function testQuickAfter() {
    console.log('🔥 اختبار سريع لصور بعد الاستغلال...');
    
    const testData = {
        url: 'http://testphp.vulnweb.com/login.php',
        report_id: 'quick_test_' + Date.now(),
        vulnerability_name: 'Quick XSS Test',
        vulnerability_type: 'Cross-Site Scripting',
        stage: 'after',
        payload_data: '<script>alert("XSS_EXPLOITED")</script>',
        target_parameter: 'search',
        actual_response_content: '🔥 QUICK TEST RESPONSE DATA 🔥\n\nThis is a test response showing that the vulnerability was exploited successfully.\n\nPayload: <script>alert("XSS_EXPLOITED")</script>\nStatus: EXPLOITED\nTime: ' + new Date().toISOString(),
        exploitation_results: [
            'XSS payload executed successfully',
            'JavaScript code injection confirmed',
            'Client-side code execution achieved',
            'Security bypass confirmed'
        ],
        vulnerability_impact_data: 'High severity XSS vulnerability allows arbitrary JavaScript execution in victim browsers. This can lead to session hijacking, data theft, and complete account compromise.'
    };

    try {
        console.log('📤 إرسال طلب اختبار سريع...');

        const postData = JSON.stringify(testData);
        const options = {
            hostname: 'localhost',
            port: 5000,
            path: '/capture_vulnerability_screenshot',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const response = await new Promise((resolve, reject) => {
            const req = http.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => data += chunk);
                res.on('end', () => {
                    try {
                        resolve({
                            status: res.statusCode,
                            data: JSON.parse(data)
                        });
                    } catch (e) {
                        resolve({
                            status: res.statusCode,
                            data: data
                        });
                    }
                });
            });

            req.on('error', reject);
            req.write(postData);
            req.end();
        });

        console.log('📡 حالة الاستجابة:', response.status);
        console.log('📄 تفاصيل الاستجابة:', JSON.stringify(response.data, null, 2));

        if (response.data.success) {
            console.log('✅ نجح الاختبار السريع!');
            console.log('📸 مسار الصورة:', response.data.screenshot_path);
        } else {
            console.log('❌ فشل الاختبار السريع');
        }

    } catch (error) {
        console.log('❌ خطأ في الطلب:', error.message);
        console.log('📄 تفاصيل الخطأ:', error);
    }

    console.log('🎉 انتهى الاختبار السريع!');
}

testQuickAfter();
