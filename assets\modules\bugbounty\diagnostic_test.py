#!/usr/bin/env python3
"""
اختبار تشخيصي شامل لخدمة التقاط الصور
يتحقق من جميع المشاكل ويقوم بإصلاحها
"""

import asyncio
import sys
import os
import json
import traceback
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from screenshot_service import ScreenshotService

class DiagnosticTester:
    def __init__(self):
        self.service = ScreenshotService()
        self.test_results = []
        self.errors_found = []
        
    async def run_full_diagnostic(self):
        """تشغيل اختبار تشخيصي شامل"""
        print("🔍 بدء الاختبار التشخيصي الشامل لخدمة التقاط الصور")
        print("=" * 80)
        
        # اختبار 1: تهيئة الخدمة
        await self.test_service_initialization()
        
        # اختبار 2: التقاط صورة بسيطة
        await self.test_basic_screenshot()
        
        # اختبار 3: تطبيق التأثيرات البصرية
        await self.test_visual_effects()
        
        # اختبار 4: اختبار البيانات الحقيقية من v4
        await self.test_v4_data_integration()
        
        # اختبار 5: اختبار مراحل مختلفة (before/during/after)
        await self.test_different_stages()
        
        # تقرير النتائج
        await self.generate_diagnostic_report()
        
    async def test_service_initialization(self):
        """اختبار تهيئة الخدمة"""
        print("\n🔧 اختبار 1: تهيئة الخدمة")
        try:
            # اختبار تهيئة Playwright
            await self.service.initialize_playwright()
            print("✅ تم تهيئة Playwright بنجاح")
            self.test_results.append({"test": "playwright_init", "status": "success"})
            
        except Exception as e:
            print(f"❌ فشل في تهيئة Playwright: {e}")
            self.errors_found.append({"test": "playwright_init", "error": str(e)})
            self.test_results.append({"test": "playwright_init", "status": "failed", "error": str(e)})
            
    async def test_basic_screenshot(self):
        """اختبار التقاط صورة بسيطة"""
        print("\n📸 اختبار 2: التقاط صورة بسيطة")
        try:
            result = await self.service.capture_with_playwright(
                url="https://httpbin.org/html",
                filename="diagnostic_test",
                stage="before",
                report_id="diagnostic_001"
            )
            
            if result and result.get('success'):
                print(f"✅ تم التقاط الصورة بنجاح: {result['path']}")
                print(f"📊 حجم الملف: {result['file_size']:,} بايت")
                self.test_results.append({"test": "basic_screenshot", "status": "success", "file_size": result['file_size']})
            else:
                print("❌ فشل في التقاط الصورة")
                self.errors_found.append({"test": "basic_screenshot", "error": "Screenshot failed"})
                self.test_results.append({"test": "basic_screenshot", "status": "failed"})
                
        except Exception as e:
            print(f"❌ خطأ في التقاط الصورة: {e}")
            self.errors_found.append({"test": "basic_screenshot", "error": str(e)})
            self.test_results.append({"test": "basic_screenshot", "status": "failed", "error": str(e)})
            
    async def test_visual_effects(self):
        """اختبار التأثيرات البصرية"""
        print("\n🎭 اختبار 3: التأثيرات البصرية")
        try:
            # إنشاء صفحة جديدة للاختبار
            page = await self.service.playwright_browser.new_page()
            await page.goto("https://httpbin.org/html")
            
            # اختبار تطبيق التأثيرات
            await self.service.apply_vulnerability_effects_async(
                page=page,
                vulnerability_name="Test XSS",
                stage="after",
                payload_data="<script>alert('test')</script>",
                vulnerability_type="XSS",
                v4_real_data={
                    'actual_response_content': 'Test response from v4 system',
                    'vulnerability_impact_data': 'High impact vulnerability detected',
                    'exploitation_results': ['Result 1', 'Result 2']
                }
            )
            
            # التحقق من وجود التأثيرات
            effects_check = await page.evaluate("""
                () => {
                    const banner = document.querySelector('[data-hack-element="banner"]');
                    const bodyStyle = window.getComputedStyle(document.body);
                    return {
                        bannerExists: banner ? true : false,
                        bodyBackground: bodyStyle.background,
                        bodyBorder: bodyStyle.border,
                        documentTitle: document.title
                    };
                }
            """)
            
            print(f"🔍 فحص التأثيرات: {effects_check}")
            
            if effects_check['bannerExists']:
                print("✅ تم تطبيق التأثيرات البصرية بنجاح")
                self.test_results.append({"test": "visual_effects", "status": "success", "effects": effects_check})
            else:
                print("⚠️ لم يتم العثور على بعض التأثيرات")
                self.test_results.append({"test": "visual_effects", "status": "partial", "effects": effects_check})
                
            await page.close()
            
        except Exception as e:
            print(f"❌ خطأ في اختبار التأثيرات: {e}")
            traceback.print_exc()
            self.errors_found.append({"test": "visual_effects", "error": str(e)})
            self.test_results.append({"test": "visual_effects", "status": "failed", "error": str(e)})
            
    async def test_v4_data_integration(self):
        """اختبار تكامل البيانات من النظام v4"""
        print("\n🔗 اختبار 4: تكامل البيانات من النظام v4")
        try:
            # بيانات اختبار من النظام v4
            test_v4_data = {
                'actual_response_content': 'This is real response data from v4 system',
                'vulnerability_impact_data': 'Critical vulnerability with high impact',
                'exploitation_results': [
                    'SQL injection successful',
                    'Database access gained',
                    'Sensitive data extracted'
                ],
                'response_content': 'Additional response content',
                'test_results': ['Test 1 passed', 'Test 2 passed']
            }
            
            result = await self.service.capture_with_playwright(
                url="https://httpbin.org/json",
                filename="v4_integration_test",
                stage="after",
                report_id="diagnostic_002",
                vulnerability_name="SQL Injection Test",
                vulnerability_type="SQL Injection",
                v4_real_data=test_v4_data
            )
            
            if result and result.get('success'):
                print("✅ تم دمج بيانات النظام v4 بنجاح")
                print(f"📊 حجم الملف مع التأثيرات: {result['file_size']:,} بايت")
                self.test_results.append({"test": "v4_integration", "status": "success", "file_size": result['file_size']})
            else:
                print("❌ فشل في دمج بيانات النظام v4")
                self.test_results.append({"test": "v4_integration", "status": "failed"})
                
        except Exception as e:
            print(f"❌ خطأ في اختبار تكامل v4: {e}")
            self.errors_found.append({"test": "v4_integration", "error": str(e)})
            self.test_results.append({"test": "v4_integration", "status": "failed", "error": str(e)})
            
    async def test_different_stages(self):
        """اختبار المراحل المختلفة"""
        print("\n🎯 اختبار 5: المراحل المختلفة (before/during/after)")
        stages = ["before", "during", "after"]
        
        for stage in stages:
            try:
                print(f"  🔄 اختبار مرحلة: {stage}")
                
                result = await self.service.capture_with_playwright(
                    url="https://httpbin.org/get",
                    filename=f"stage_{stage}_test",
                    stage=stage,
                    report_id="diagnostic_003",
                    vulnerability_name="Multi-Stage Test",
                    payload_data="<script>alert('stage test')</script>",
                    vulnerability_type="XSS"
                )
                
                if result and result.get('success'):
                    print(f"    ✅ مرحلة {stage}: نجحت ({result['file_size']:,} بايت)")
                    self.test_results.append({"test": f"stage_{stage}", "status": "success", "file_size": result['file_size']})
                else:
                    print(f"    ❌ مرحلة {stage}: فشلت")
                    self.test_results.append({"test": f"stage_{stage}", "status": "failed"})
                    
            except Exception as e:
                print(f"    ❌ خطأ في مرحلة {stage}: {e}")
                self.errors_found.append({"test": f"stage_{stage}", "error": str(e)})
                self.test_results.append({"test": f"stage_{stage}", "status": "failed", "error": str(e)})
                
    async def generate_diagnostic_report(self):
        """إنشاء تقرير تشخيصي شامل"""
        print("\n" + "=" * 80)
        print("📋 تقرير التشخيص النهائي")
        print("=" * 80)
        
        # إحصائيات عامة
        total_tests = len(self.test_results)
        successful_tests = len([t for t in self.test_results if t['status'] == 'success'])
        failed_tests = len([t for t in self.test_results if t['status'] == 'failed'])
        partial_tests = len([t for t in self.test_results if t['status'] == 'partial'])
        
        print(f"📊 إجمالي الاختبارات: {total_tests}")
        print(f"✅ نجحت: {successful_tests}")
        print(f"❌ فشلت: {failed_tests}")
        print(f"⚠️ جزئية: {partial_tests}")
        print(f"📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
        
        # تفاصيل الأخطاء
        if self.errors_found:
            print(f"\n🚨 الأخطاء المكتشفة ({len(self.errors_found)}):")
            for i, error in enumerate(self.errors_found, 1):
                print(f"  {i}. {error['test']}: {error['error']}")
                
        # حفظ التقرير
        report_data = {
            'timestamp': str(asyncio.get_event_loop().time()),
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'failed_tests': failed_tests,
            'partial_tests': partial_tests,
            'success_rate': (successful_tests/total_tests)*100,
            'test_results': self.test_results,
            'errors_found': self.errors_found
        }
        
        report_path = Path("diagnostic_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
            
        print(f"\n💾 تم حفظ التقرير في: {report_path}")
        
        # إغلاق الخدمة
        try:
            await self.service.cleanup()
            print("🔒 تم إغلاق الخدمة بنجاح")
        except:
            pass

async def main():
    """تشغيل الاختبار التشخيصي"""
    tester = DiagnosticTester()
    await tester.run_full_diagnostic()

if __name__ == "__main__":
    print("🚀 بدء الاختبار التشخيصي الشامل")
    asyncio.run(main())
    print("✅ انتهى الاختبار التشخيصي")
