{"system": {"name": "Bug Bounty v4 with Python Integration", "version": "4.0.1", "description": "نظام Bug Bounty المتقدم مع تكامل Python لالتقاط الصور الحقيقية", "author": "Bug Bounty Team", "last_updated": "2024-12-19"}, "python_service": {"enabled": true, "service_name": "screenshot_service.py", "default_browser": "chrome", "fallback_browser": "firefox", "timeout": 30, "max_retries": 3, "session_cleanup_interval": 300, "temp_folder_cleanup": true}, "screenshot_settings": {"default_width": 1920, "default_height": 1080, "quality": 90, "format": "png", "wait_time": 3, "scroll_pause": 1, "full_page": true, "hide_scrollbars": true, "mobile_emulation": false}, "folders": {"screenshots_base": "./screenshots", "temp_folder": "./screenshots/temp", "reports_folder": "./reports", "logs_folder": "./logs", "auto_create_folders": true, "folder_permissions": "755"}, "security": {"validate_urls": true, "allowed_protocols": ["http", "https"], "blocked_domains": [], "max_file_size": "10MB", "sanitize_filenames": true, "secure_temp_files": true}, "performance": {"max_concurrent_captures": 3, "memory_limit": "512MB", "cpu_limit": 80, "disk_space_check": true, "min_free_space": "1GB", "cleanup_old_files": true, "max_file_age_days": 30}, "logging": {"enabled": true, "level": "INFO", "file_logging": true, "console_logging": true, "max_log_size": "50MB", "log_rotation": true, "keep_logs_days": 7}, "integration": {"bugbounty_core": {"enabled": true, "auto_initialize": true, "fallback_methods": ["impact_visualizer", "canvas_generation"], "error_handling": "graceful", "retry_failed_captures": true}, "report_integration": {"auto_embed_images": true, "image_compression": true, "base64_encoding": true, "thumbnail_generation": false, "watermark": false}}, "browser_settings": {"chrome": {"headless": true, "disable_gpu": true, "no_sandbox": true, "disable_dev_shm_usage": true, "window_size": "1920,1080", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}, "firefox": {"headless": true, "window_size": "1920,1080", "disable_blink_features": "AutomationControlled"}, "edge": {"headless": true, "window_size": "1920,1080", "disable_extensions": true}}, "vulnerability_testing": {"capture_before": true, "capture_during": true, "capture_after": true, "wait_between_captures": 2, "highlight_changes": false, "compare_images": false, "save_diff_images": false}, "api_settings": {"enable_rest_api": false, "api_port": 8080, "api_host": "localhost", "authentication": false, "rate_limiting": true, "max_requests_per_minute": 60}, "monitoring": {"health_check_interval": 60, "statistics_collection": true, "performance_monitoring": true, "error_tracking": true, "alert_on_failures": false, "alert_threshold": 5}, "advanced": {"experimental_features": false, "debug_mode": false, "verbose_logging": false, "profiling": false, "memory_profiling": false, "performance_profiling": false}, "localization": {"language": "ar", "timezone": "Asia/Riyadh", "date_format": "DD/MM/YYYY", "time_format": "HH:mm:ss", "rtl_support": true}, "backup": {"auto_backup": false, "backup_interval_hours": 24, "backup_location": "./backups", "keep_backups": 7, "compress_backups": true}, "notifications": {"enabled": false, "email_notifications": false, "webhook_notifications": false, "desktop_notifications": false, "sound_notifications": false}, "testing": {"test_mode": false, "mock_responses": false, "test_data_generation": false, "automated_testing": false, "test_report_generation": true}, "compatibility": {"minimum_python_version": "3.8", "supported_os": ["Windows", "Linux", "macOS"], "required_packages": ["selenium>=4.15.0", "playwright>=1.40.0", "Pillow>=10.0.0", "requests>=2.31.0", "beautifulsoup4>=4.12.0"]}, "error_codes": {"SUCCESS": 0, "GENERAL_ERROR": 1, "BROWSER_ERROR": 2, "NETWORK_ERROR": 3, "FILE_ERROR": 4, "PERMISSION_ERROR": 5, "TIMEOUT_ERROR": 6, "VALIDATION_ERROR": 7, "DEPENDENCY_ERROR": 8, "CONFIGURATION_ERROR": 9}, "default_payloads": {"xss": ["<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>", "javascript:alert('XSS')"], "sql_injection": ["' OR '1'='1", "'; DROP TABLE users; --", "1' UNION SELECT NULL,NULL,NULL--"], "command_injection": ["; ls -la", "| whoami", "&& cat /etc/passwd"]}, "file_extensions": {"screenshots": [".png", ".jpg", ".jpeg"], "reports": [".html", ".pdf", ".json"], "logs": [".log", ".txt"], "configs": [".json", ".yaml", ".yml"]}, "mime_types": {"png": "image/png", "jpg": "image/jpeg", "jpeg": "image/jpeg", "html": "text/html", "json": "application/json", "pdf": "application/pdf"}}