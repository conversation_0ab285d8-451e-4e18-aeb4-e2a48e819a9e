#!/usr/bin/env python3
"""
AI Self-Improvement System for Technical Assistant
Advanced system for autonomous code improvement using external AI assistance
"""

import json
import time
import hashlib
import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ImprovementOpportunity:
    """Represents a potential improvement opportunity"""
    id: str
    type: str  # 'bug', 'performance', 'feature', 'security', 'code_quality'
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    file_path: str
    line_numbers: List[int]
    current_code: str
    context: str
    suggested_improvement: str = ""
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()
        if not self.id:
            self.id = hashlib.md5(f"{self.file_path}_{self.description}_{self.timestamp}".encode()).hexdigest()[:8]

@dataclass
class AIResponse:
    """Response from external AI agent"""
    agent_name: str
    response_text: str
    suggested_code: str
    explanation: str
    confidence: float
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()

class ImprovementWatcher:
    """Monitors code and identifies improvement opportunities"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.watched_files = []
        self.improvement_patterns = self._load_improvement_patterns()
        self.last_scan_time = None
        
    def _load_improvement_patterns(self) -> Dict[str, List[str]]:
        """Load patterns that indicate improvement opportunities"""
        return {
            'performance': [
                'document.getElementById',  # Could use caching
                'for.*in.*document',       # DOM queries in loops
                'setTimeout.*0',           # Unnecessary timeouts
                'setInterval.*[0-9]{1,2}', # Very frequent intervals
            ],
            'security': [
                'innerHTML.*\\+',          # Potential XSS
                'eval\\(',                 # Dangerous eval usage
                'document.write',          # Unsafe DOM manipulation
                'window.location.*\\+',    # URL manipulation
            ],
            'code_quality': [
                'function.*{[\\s\\S]{500,}', # Very long functions
                'var ',                    # Use let/const instead
                'console.log',             # Remove debug logs
                '//.*TODO',               # Unfinished tasks
                '//.*FIXME',              # Known issues
            ],
            'bug_potential': [
                '==.*null',               # Use strict equality
                'parseInt.*[^,]',         # Missing radix
                'catch.*{\\s*}',          # Empty catch blocks
                'if.*=.*[^=]',           # Assignment in condition
            ]
        }
    
    async def scan_for_improvements(self) -> List[ImprovementOpportunity]:
        """Scan project files for improvement opportunities"""
        opportunities = []
        
        # Scan JavaScript files
        js_files = list(self.project_root.glob("**/*.js"))
        for file_path in js_files:
            if self._should_scan_file(file_path):
                file_opportunities = await self._scan_file(file_path)
                opportunities.extend(file_opportunities)
        
        # Scan HTML files
        html_files = list(self.project_root.glob("**/*.html"))
        for file_path in html_files:
            if self._should_scan_file(file_path):
                file_opportunities = await self._scan_html_file(file_path)
                opportunities.extend(file_opportunities)
        
        self.last_scan_time = datetime.now()
        logger.info(f"Found {len(opportunities)} improvement opportunities")
        return opportunities
    
    def _should_scan_file(self, file_path: Path) -> bool:
        """Determine if file should be scanned"""
        # Skip node_modules, .git, etc.
        skip_dirs = {'node_modules', '.git', '__pycache__', '.vscode'}
        return not any(part in skip_dirs for part in file_path.parts)
    
    async def _scan_file(self, file_path: Path) -> List[ImprovementOpportunity]:
        """Scan a single JavaScript file"""
        opportunities = []
        
        try:
            content = file_path.read_text(encoding='utf-8')
            lines = content.split('\n')
            
            for category, patterns in self.improvement_patterns.items():
                for pattern in patterns:
                    import re
                    matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
                    
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        
                        # Get context (surrounding lines)
                        start_line = max(0, line_num - 3)
                        end_line = min(len(lines), line_num + 3)
                        context_lines = lines[start_line:end_line]
                        
                        opportunity = ImprovementOpportunity(
                            id="",
                            type=category,
                            severity=self._determine_severity(category, pattern),
                            description=self._generate_description(category, pattern, match.group()),
                            file_path=str(file_path.relative_to(self.project_root)),
                            line_numbers=[line_num],
                            current_code=lines[line_num - 1] if line_num <= len(lines) else "",
                            context='\n'.join(context_lines)
                        )
                        opportunities.append(opportunity)
                        
        except Exception as e:
            logger.error(f"Error scanning file {file_path}: {e}")
        
        return opportunities
    
    async def _scan_html_file(self, file_path: Path) -> List[ImprovementOpportunity]:
        """Scan HTML file for improvements"""
        opportunities = []
        
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Check for common HTML issues
            html_patterns = {
                'accessibility': [
                    '<img(?![^>]*alt=)',  # Images without alt text
                    '<input(?![^>]*aria-label)(?![^>]*id)',  # Inputs without labels
                ],
                'performance': [
                    '<script(?![^>]*async)(?![^>]*defer)',  # Blocking scripts
                    '<link.*stylesheet(?![^>]*media)',     # CSS without media queries
                ],
                'security': [
                    'target="_blank"(?![^>]*rel="noopener")',  # Unsafe external links
                ]
            }
            
            for category, patterns in html_patterns.items():
                for pattern in patterns:
                    import re
                    matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
                    
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        
                        opportunity = ImprovementOpportunity(
                            id="",
                            type=category,
                            severity='medium',
                            description=self._generate_html_description(category, pattern),
                            file_path=str(file_path.relative_to(self.project_root)),
                            line_numbers=[line_num],
                            current_code=match.group(),
                            context=self._get_html_context(content, match.start(), match.end())
                        )
                        opportunities.append(opportunity)
                        
        except Exception as e:
            logger.error(f"Error scanning HTML file {file_path}: {e}")
        
        return opportunities
    
    def _determine_severity(self, category: str, pattern: str) -> str:
        """Determine severity based on category and pattern"""
        severity_map = {
            'security': 'high',
            'bug_potential': 'high',
            'performance': 'medium',
            'code_quality': 'low'
        }
        return severity_map.get(category, 'medium')
    
    def _generate_description(self, category: str, pattern: str, matched_text: str) -> str:
        """Generate human-readable description"""
        descriptions = {
            'document.getElementById': "Repeated DOM queries could be cached for better performance",
            'innerHTML.*\\+': "String concatenation with innerHTML may be vulnerable to XSS attacks",
            'var ': "Consider using 'let' or 'const' instead of 'var' for better scoping",
            'console.log': "Debug console.log statements should be removed in production",
            '//.*TODO': "TODO comment indicates unfinished functionality",
            'eval\\(': "eval() usage is dangerous and should be avoided",
        }
        
        for key, desc in descriptions.items():
            if key in pattern:
                return desc
        
        return f"Potential {category} issue detected: {matched_text}"
    
    def _generate_html_description(self, category: str, pattern: str) -> str:
        """Generate description for HTML issues"""
        descriptions = {
            '<img(?![^>]*alt=)': "Image missing alt attribute for accessibility",
            'target="_blank"(?![^>]*rel="noopener")': "External link missing rel='noopener' security attribute",
            '<script(?![^>]*async)(?![^>]*defer)': "Script tag blocking page rendering, consider async/defer",
        }
        
        for key, desc in descriptions.items():
            if key in pattern:
                return desc
        
        return f"HTML {category} issue detected"
    
    def _get_html_context(self, content: str, start: int, end: int) -> str:
        """Get context around HTML match"""
        lines = content.split('\n')
        line_num = content[:start].count('\n')
        
        start_line = max(0, line_num - 2)
        end_line = min(len(lines), line_num + 3)
        
        return '\n'.join(lines[start_line:end_line])

class ExternalAIAgentInterface:
    """Interface for communicating with external AI agents"""
    
    def __init__(self):
        self.available_agents = self._discover_available_agents()
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def _discover_available_agents(self) -> Dict[str, Dict[str, Any]]:
        """Discover available AI agents"""
        agents = {
            'lm_studio': {
                'name': 'LM Studio Local Model',
                'endpoint': 'http://127.0.0.1:1234/v1/chat/completions',
                'type': 'openai_compatible',
                'available': False
            },
            'augment_ai': {
                'name': 'Augment AI Assistant',
                'endpoint': 'vscode_extension',
                'type': 'vscode_extension',
                'available': True  # Assume available in VS Code
            },
            'openai_gpt4': {
                'name': 'OpenAI GPT-4',
                'endpoint': 'https://api.openai.com/v1/chat/completions',
                'type': 'openai_api',
                'available': False  # Requires API key
            }
        }
        
        return agents
    
    async def check_agent_availability(self, agent_name: str) -> bool:
        """Check if an AI agent is available"""
        if agent_name not in self.available_agents:
            return False
        
        agent = self.available_agents[agent_name]
        
        if agent['type'] == 'openai_compatible':
            try:
                async with self.session.get(f"{agent['endpoint'].replace('/chat/completions', '/models')}", 
                                          timeout=aiohttp.ClientTimeout(total=5)) as response:
                    return response.status == 200
            except:
                return False
        elif agent['type'] == 'vscode_extension':
            # Augment AI is available in VS Code context
            return True
        
        return False
    
    async def request_improvement(self, opportunity: ImprovementOpportunity, 
                                agent_name: str = None) -> Optional[AIResponse]:
        """Request improvement suggestion from external AI"""
        
        if not agent_name:
            # Find first available agent
            for name in self.available_agents:
                if await self.check_agent_availability(name):
                    agent_name = name
                    break
        
        if not agent_name:
            logger.error("No AI agents available")
            return None
        
        agent = self.available_agents[agent_name]
        
        # Prepare the prompt
        prompt = self._create_improvement_prompt(opportunity)
        
        try:
            if agent['type'] in ['openai_compatible', 'openai_api']:
                return await self._request_openai_compatible(agent, prompt, opportunity)
            elif agent['type'] == 'vscode_extension':
                return await self._request_vscode_extension(agent, prompt, opportunity)
        except Exception as e:
            logger.error(f"Error requesting improvement from {agent_name}: {e}")
            return None
    
    def _create_improvement_prompt(self, opportunity: ImprovementOpportunity) -> str:
        """Create a detailed prompt for the AI agent"""
        prompt = f"""
مرحباً زميلي المبرمج! أحتاج مساعدتك في تحسين كود JavaScript.

**المشكلة المكتشفة:**
- النوع: {opportunity.type}
- الخطورة: {opportunity.severity}
- الوصف: {opportunity.description}

**الملف:** {opportunity.file_path}
**السطر:** {opportunity.line_numbers}

**الكود الحالي:**
```javascript
{opportunity.current_code}
```

**السياق:**
```javascript
{opportunity.context}
```

**المطلوب منك:**
1. تحليل المشكلة وتأكيد وجودها
2. اقتراح حل محسن ومحدث
3. كتابة الكود المحسن
4. شرح الفوائد من التحسين

**تنسيق الرد:**
يرجى الرد بتنسيق JSON كالتالي:
```json
{{
    "analysis": "تحليل المشكلة",
    "improved_code": "الكود المحسن",
    "explanation": "شرح التحسينات",
    "benefits": ["فائدة 1", "فائدة 2"],
    "confidence": 0.95
}}
```

شكراً لك!
"""
        return prompt
    
    async def _request_openai_compatible(self, agent: Dict, prompt: str, 
                                       opportunity: ImprovementOpportunity) -> AIResponse:
        """Request from OpenAI-compatible API"""
        
        payload = {
            "model": "gpt-3.5-turbo",  # Default model
            "messages": [
                {
                    "role": "system",
                    "content": "أنت مبرمج خبير متخصص في JavaScript وتطوير الويب. مهمتك مساعدة زملائك في تحسين الكود."
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 1500
        }
        
        headers = {"Content-Type": "application/json"}
        
        async with self.session.post(agent['endpoint'], 
                                   json=payload, 
                                   headers=headers,
                                   timeout=aiohttp.ClientTimeout(total=30)) as response:
            
            if response.status == 200:
                data = await response.json()
                content = data['choices'][0]['message']['content']
                
                # Try to parse JSON response
                try:
                    import re
                    json_match = re.search(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
                    if json_match:
                        parsed = json.loads(json_match.group(1))
                        
                        return AIResponse(
                            agent_name=agent['name'],
                            response_text=content,
                            suggested_code=parsed.get('improved_code', ''),
                            explanation=parsed.get('explanation', ''),
                            confidence=parsed.get('confidence', 0.8)
                        )
                except:
                    pass
                
                # Fallback to text response
                return AIResponse(
                    agent_name=agent['name'],
                    response_text=content,
                    suggested_code="",
                    explanation=content,
                    confidence=0.7
                )
            else:
                raise Exception(f"API request failed with status {response.status}")
    
    async def _request_vscode_extension(self, agent: Dict, prompt: str,
                                      opportunity: ImprovementOpportunity) -> AIResponse:
        """Request from VS Code extension (Augment AI)"""
        
        # For Augment AI, we'll create a structured request
        # This would be handled by the VS Code extension interface
        
        return AIResponse(
            agent_name=agent['name'],
            response_text="تم إرسال الطلب إلى Augment AI",
            suggested_code="// سيتم تقديم الكود المحسن من Augment AI",
            explanation="يرجى انتظار رد Augment AI في VS Code",
            confidence=0.9
        )

if __name__ == "__main__":
    # Test the system
    async def test_system():
        watcher = ImprovementWatcher(".")
        opportunities = await watcher.scan_for_improvements()
        
        print(f"Found {len(opportunities)} opportunities:")
        for opp in opportunities[:3]:  # Show first 3
            print(f"- {opp.type}: {opp.description}")
    
    asyncio.run(test_system())
