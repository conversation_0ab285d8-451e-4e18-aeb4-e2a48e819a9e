// اختبار فعلي وحقيقي للنظام v4 - محاكاة دقيقة لكيفية إرسال البيانات
console.log('🔍 اختبار فعلي وحقيقي للنظام v4...');

async function testRealV4System() {
    console.log('🚀 بدء اختبار النظام v4 الحقيقي...');
    
    // محاكاة دقيقة لكيفية إرسال النظام v4 للبيانات
    const realV4Tests = [
        {
            name: 'SQL_Injection_Real_Test',
            description: 'اختبار SQL Injection حقيقي كما يرسله النظام v4',
            data: {
                url: 'http://testphp.vulnweb.com/login.php',
                filename: 'after_SQL_Injection',
                report_id: 'testphp_vulnweb_com',
                vulnerability_name: 'SQL Injection in Login Form',
                vulnerability_type: 'SQL_Injection',
                stage: 'after',
                payload_data: "admin' UNION SELECT 1,2,3,database(),version() -- real_test",
                target_parameter: 'username',
                // بيانات v4_data كما يرسلها النظام الحقيقي
                v4_data: {
                    test_results: [
                        'Real HTTP Response Code: 200',
                        'Response Size: 3456 characters',
                        'Response Time: 287ms',
                        'SQL Injection confirmed in username parameter',
                        'Database information extracted successfully'
                    ],
                    exploitation_data: {
                        status: 'successful_sql_injection',
                        method: 'UNION_based_injection',
                        payload_used: "admin' UNION SELECT 1,2,3,database(),version() -- real_test",
                        target_url: 'http://testphp.vulnweb.com/login.php?username=admin\' UNION SELECT 1,2,3,database(),version() -- real_test',
                        evidence_count: 5,
                        database_info: 'MySQL 5.7.33 detected',
                        exploitation_time: '2025-07-30T21:15:00Z'
                    },
                    verification_data: {
                        proof: [
                            'Created SQL injection URL with UNION attack',
                            'Database version extracted: MySQL 5.7.33',
                            'Database name revealed: testphp_db',
                            'SQL error messages confirmed vulnerability',
                            'Authentication bypass achieved',
                            'Full database access confirmed'
                        ]
                    },
                    response_data: `<!DOCTYPE html>
<html>
<head><title>Login Response - SQL Injection Detected</title></head>
<body>
<h1>Database Query Results</h1>
<div class="sql-results">
    <p><strong>Database:</strong> testphp_db</p>
    <p><strong>Version:</strong> MySQL 5.7.33</p>
    <p><strong>User:</strong> root@localhost</p>
    <p><strong>Tables:</strong> users, products, orders</p>
</div>
<div class="error-log">
    <p>SQL Error: You have an error in your SQL syntax near 'UNION SELECT 1,2,3,database(),version()' at line 1</p>
    <p>Query: SELECT * FROM users WHERE username='admin' UNION SELECT 1,2,3,database(),version() -- real_test' AND password='...'</p>
</div>
<script>
    console.log('SQL Injection payload executed successfully');
    document.body.style.backgroundColor = '#ffcccc';
</script>
</body>
</html>`,
                    actual_response_content: `🔥 SQL INJECTION EXPLOITATION RESULTS 🔥

Target: http://testphp.vulnweb.com/login.php
Payload: admin' UNION SELECT 1,2,3,database(),version() -- real_test
Method: UNION-based SQL Injection
Status: SUCCESSFUL EXPLOITATION

Database Information Extracted:
- Database Name: testphp_db
- MySQL Version: 5.7.33
- Current User: root@localhost
- Available Tables: users, products, orders
- Server Info: MySQL Community Server

SQL Injection Evidence:
1. Payload successfully injected into username parameter
2. UNION SELECT statement executed without errors
3. Database metadata extracted successfully
4. SQL error messages revealed database structure
5. Response contains injected database information
6. Authentication bypass confirmed

Security Impact: CRITICAL
- Full database access achieved
- Sensitive data extraction possible
- Authentication mechanisms bypassed
- Server configuration disclosed

Technical Details:
- Injection Point: username parameter
- Attack Vector: UNION-based injection
- Database Type: MySQL 5.7.33
- Privilege Level: root access
- Data Extraction: Confirmed

Recommendation: Immediate patching required for SQL injection vulnerability`,
                    success_indicators: [
                        'SQL Injection vulnerability confirmed',
                        'Database information successfully extracted',
                        'UNION attack executed successfully',
                        'Authentication bypass achieved',
                        'Critical security impact identified',
                        'Full database access confirmed'
                    ],
                    error_messages: [],
                    real_exploitation_evidence: [
                        'Database version disclosure: MySQL 5.7.33',
                        'Database name extraction: testphp_db',
                        'User privilege escalation: root@localhost',
                        'Table enumeration: users, products, orders'
                    ],
                    exploitation_results: [
                        'Successful UNION-based SQL injection',
                        'Database metadata extraction completed',
                        'Authentication bypass confirmed',
                        'Critical vulnerability impact verified'
                    ],
                    vulnerability_impact_data: 'Critical SQL injection vulnerability allows full database access, authentication bypass, and sensitive data extraction. Immediate remediation required.'
                }
            }
        },
        {
            name: 'XSS_Real_Test',
            description: 'اختبار XSS حقيقي كما يرسله النظام v4',
            data: {
                url: 'http://testphp.vulnweb.com/search.php',
                filename: 'after_XSS_Cross_Site_Scripting',
                report_id: 'testphp_vulnweb_com',
                vulnerability_name: 'XSS Cross Site Scripting',
                vulnerability_type: 'Cross_Site_Scripting',
                stage: 'after',
                payload_data: "<script>alert('XSS_REAL_TEST_2025');document.body.style.background='red';</script>",
                target_parameter: 'searchFor',
                v4_data: {
                    test_results: [
                        'Real HTTP Response Code: 200',
                        'Response Size: 2890 characters',
                        'Response Time: 156ms',
                        'XSS payload executed successfully',
                        'JavaScript injection confirmed'
                    ],
                    exploitation_data: {
                        status: 'successful_xss_injection',
                        method: 'reflected_xss',
                        payload_used: "<script>alert('XSS_REAL_TEST_2025');document.body.style.background='red';</script>",
                        target_url: 'http://testphp.vulnweb.com/search.php?searchFor=<script>alert(\'XSS_REAL_TEST_2025\');document.body.style.background=\'red\';</script>',
                        evidence_count: 4,
                        xss_type: 'reflected',
                        exploitation_time: '2025-07-30T21:15:30Z'
                    },
                    verification_data: {
                        proof: [
                            'XSS payload reflected in search results',
                            'JavaScript execution confirmed',
                            'DOM manipulation successful',
                            'Alert dialog triggered',
                            'Page styling modified by injected script'
                        ]
                    },
                    response_data: `<!DOCTYPE html>
<html>
<head><title>Search Results - XSS Detected</title></head>
<body>
<h1>Search Results</h1>
<div class="search-results">
    <p>Search query: <script>alert('XSS_REAL_TEST_2025');document.body.style.background='red';</script></p>
    <p>No results found for your search.</p>
</div>
<script>
    // Injected XSS payload executed here
    alert('XSS_REAL_TEST_2025');
    document.body.style.background='red';
</script>
</body>
</html>`,
                    actual_response_content: `🔥 XSS CROSS-SITE SCRIPTING EXPLOITATION RESULTS 🔥

Target: http://testphp.vulnweb.com/search.php
Payload: <script>alert('XSS_REAL_TEST_2025');document.body.style.background='red';</script>
Method: Reflected Cross-Site Scripting
Status: SUCCESSFUL EXPLOITATION

XSS Injection Evidence:
1. Payload successfully injected into searchFor parameter
2. JavaScript code executed in browser context
3. Alert dialog displayed to user
4. DOM manipulation confirmed (background color changed)
5. No input sanitization detected

Security Impact: HIGH
- Client-side code execution achieved
- User session hijacking possible
- Sensitive data theft potential
- Phishing attacks feasible

Technical Details:
- Injection Point: searchFor parameter
- Attack Vector: Reflected XSS
- Payload Type: JavaScript injection
- Execution Context: Browser DOM
- Sanitization: None detected

Recommendation: Implement proper input validation and output encoding`,
                    success_indicators: [
                        'XSS vulnerability confirmed',
                        'JavaScript execution successful',
                        'DOM manipulation achieved',
                        'High security impact identified'
                    ],
                    error_messages: [],
                    real_exploitation_evidence: [
                        'JavaScript payload execution confirmed',
                        'Alert dialog triggered successfully',
                        'DOM styling modification achieved',
                        'No input filtering detected'
                    ],
                    exploitation_results: [
                        'Successful reflected XSS injection',
                        'Client-side code execution confirmed',
                        'High vulnerability impact verified'
                    ],
                    vulnerability_impact_data: 'High-impact XSS vulnerability allows client-side code execution, potential session hijacking, and user data theft. Input validation required.'
                }
            }
        }
    ];
    
    console.log(`📊 عدد الاختبارات: ${realV4Tests.length}`);
    
    for (let i = 0; i < realV4Tests.length; i++) {
        const test = realV4Tests[i];
        console.log(`\n${'='.repeat(100)}`);
        console.log(`🎯 اختبار ${i + 1}/${realV4Tests.length}: ${test.name}`);
        console.log(`📝 الوصف: ${test.description}`);
        console.log(`🌐 URL: ${test.data.url}`);
        console.log(`💉 Payload: ${test.data.payload_data}`);
        console.log(`🔧 Parameter: ${test.data.target_parameter}`);
        console.log(`📊 v4_data Size: ${JSON.stringify(test.data.v4_data).length} characters`);
        console.log(`${'='.repeat(100)}`);
        
        try {
            const startTime = Date.now();
            
            const response = await fetch('http://localhost:8000/v4_website', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(test.data)
            });
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`📡 حالة الاستجابة: ${response.status}`);
            console.log(`⏱️ مدة الطلب: ${duration}ms`);
            
            if (response.ok) {
                const result = await response.json();
                console.log('✅ نجح الطلب!');
                console.log(`📝 الرسالة: ${result.message || 'تم بنجاح'}`);
                
                if (result.success) {
                    console.log(`📸 حجم الصورة: ${result.file_size || 0} bytes`);
                    console.log(`📁 مسار الصورة: ${result.file_path || 'غير محدد'}`);
                    
                    // فحص البيانات المستخدمة
                    console.log('\n🔍 فحص البيانات المستخدمة:');
                    if (result.vulnerability_name_used) {
                        console.log(`   🎯 اسم الثغرة: ${result.vulnerability_name_used}`);
                    }
                    if (result.vulnerability_type_used) {
                        console.log(`   🔍 نوع الثغرة: ${result.vulnerability_type_used}`);
                    }
                    if (result.payload_used) {
                        console.log(`   💉 Payload: ${result.payload_used.substring(0, 100)}...`);
                    }
                    
                } else {
                    console.log(`❌ فشل: ${result.error || 'خطأ غير محدد'}`);
                }
                
            } else {
                const errorText = await response.text();
                console.log(`❌ فشل الطلب: ${response.status}`);
                console.log(`📄 تفاصيل الخطأ: ${errorText}`);
            }
            
        } catch (error) {
            console.error(`❌ خطأ في الطلب: ${error.message}`);
        }
        
        // انتظار بين الاختبارات
        if (i < realV4Tests.length - 1) {
            console.log('\n⏳ انتظار 3 ثواني قبل الاختبار التالي...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }
    
    console.log(`\n${'='.repeat(120)}`);
    console.log('📊 ملخص الاختبار الفعلي للنظام v4:');
    console.log(`${'='.repeat(120)}`);
    console.log('✅ تم اختبار النظام v4 بالبيانات الحقيقية');
    console.log('📋 تحقق من سجلات سيرفر Python لرؤية كيفية استقبال البيانات');
    console.log('🔍 ابحث عن "البيانات المستخدمة من النظام v4" في السجلات');
    console.log('📁 تحقق من مجلد screenshots لرؤية الصور المولدة');
}

// تشغيل الاختبار
testRealV4System()
    .then(() => {
        console.log('\n🎉 انتهى الاختبار الفعلي للنظام v4!');
    })
    .catch(error => {
        console.error('❌ خطأ في الاختبار الفعلي:', error);
    });
