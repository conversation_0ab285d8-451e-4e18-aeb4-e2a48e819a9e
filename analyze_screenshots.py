#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل الصور للتحقق من محتوى قسم REAL SERVER RESPONSE
"""

import os
import glob
from PIL import Image
import json

def analyze_screenshots():
    """تحليل جميع الصور في المجلدات للتحقق من الفروق"""
    
    print("🔍 تحليل جميع الصور في المجلدات...")
    
    # البحث عن جميع الصور
    pattern = 'assets/modules/bugbounty/screenshots/**/after_*.png'
    images = glob.glob(pattern, recursive=True)
    
    if not images:
        print("❌ لم يتم العثور على أي صور")
        return
    
    print(f"📊 تم العثور على {len(images)} صورة")
    
    # تجميع الصور حسب المجلد
    folders = {}
    for img_path in images:
        folder = os.path.dirname(img_path)
        folder_name = os.path.basename(folder)
        if folder_name not in folders:
            folders[folder_name] = []
        folders[folder_name].append(img_path)
    
    print(f"📁 عدد المجلدات: {len(folders)}")
    
    # تحليل كل مجلد
    for folder_name, folder_images in folders.items():
        print(f"\n📂 مجلد: {folder_name}")
        print(f"   📊 عدد الصور: {len(folder_images)}")
        
        # تحليل كل صورة في المجلد
        for img_path in folder_images:
            try:
                img = Image.open(img_path)
                file_size = os.path.getsize(img_path)
                
                print(f"   📸 {os.path.basename(img_path)}")
                print(f"      📐 الأبعاد: {img.size}")
                print(f"      📊 الحجم: {file_size:,} bytes ({file_size/1024:.1f} KB)")
                
                # تحليل حجم الملف لتحديد المحتوى
                if file_size < 50000:  # أقل من 50KB
                    print(f"      ⚠️  حجم صغير - قد يكون محتوى قليل أو لا توجد استجابة")
                elif file_size > 200000:  # أكبر من 200KB
                    print(f"      ✅ حجم كبير - محتوى غني مع استجابة حقيقية")
                else:
                    print(f"      📊 حجم متوسط")
                
                # فحص اسم الملف لتحديد نوع الثغرة
                filename = os.path.basename(img_path).lower()
                if 'sql' in filename:
                    print(f"      🎯 نوع: SQL Injection")
                elif 'xss' in filename:
                    print(f"      🎯 نوع: XSS")
                elif 'command' in filename:
                    print(f"      🎯 نوع: Command Injection")
                elif 'information' in filename:
                    print(f"      🎯 نوع: Information Disclosure")
                elif 'disclosure' in filename:
                    print(f"      🎯 نوع: Information Disclosure")
                else:
                    print(f"      🎯 نوع: غير محدد")
                
                # فحص تاريخ الإنشاء
                creation_time = os.path.getctime(img_path)
                import datetime
                creation_date = datetime.datetime.fromtimestamp(creation_time)
                print(f"      🕒 تاريخ الإنشاء: {creation_date.strftime('%Y-%m-%d %H:%M:%S')}")
                
            except Exception as e:
                print(f"   ❌ خطأ في فحص {img_path}: {e}")
    
    # مقارنة الأحجام
    print(f"\n📊 مقارنة أحجام الصور:")
    all_sizes = []
    for img_path in images:
        try:
            size = os.path.getsize(img_path)
            folder_name = os.path.basename(os.path.dirname(img_path))
            all_sizes.append((os.path.basename(img_path), size, folder_name))
        except:
            pass
    
    # ترتيب حسب الحجم
    all_sizes.sort(key=lambda x: x[1], reverse=True)
    
    print(f"🔝 أكبر 5 صور:")
    for name, size, folder in all_sizes[:5]:
        print(f"   📸 {name} ({folder}): {size:,} bytes ({size/1024:.1f} KB)")
    
    print(f"🔻 أصغر 5 صور:")
    for name, size, folder in all_sizes[-5:]:
        print(f"   📸 {name} ({folder}): {size:,} bytes ({size/1024:.1f} KB)")
    
    # تحليل الفرق في الأحجام
    if len(all_sizes) > 1:
        max_size = all_sizes[0][1]
        min_size = all_sizes[-1][1]
        avg_size = sum(size for _, size, _ in all_sizes) / len(all_sizes)
        
        print(f"\n📈 إحصائيات الأحجام:")
        print(f"   📊 أكبر حجم: {max_size:,} bytes ({max_size/1024:.1f} KB)")
        print(f"   📊 أصغر حجم: {min_size:,} bytes ({min_size/1024:.1f} KB)")
        print(f"   📊 متوسط الحجم: {avg_size:,.0f} bytes ({avg_size/1024:.1f} KB)")
        print(f"   📊 الفرق: {max_size - min_size:,} bytes")
        
        # تحديد الصور الشاذة
        print(f"\n🔍 تحليل الصور الشاذة:")
        for name, size, folder in all_sizes:
            if size < avg_size * 0.5:  # أقل من نصف المتوسط
                print(f"   ⚠️  صورة صغيرة جداً: {name} ({folder}) - {size/1024:.1f} KB")
                print(f"       💡 قد تكون بدون استجابة حقيقية في قسم REAL SERVER RESPONSE")
            elif size > avg_size * 2:  # أكبر من ضعف المتوسط
                print(f"   ✅ صورة كبيرة (محتوى غني): {name} ({folder}) - {size/1024:.1f} KB")
                print(f"       💡 تحتوي على استجابة حقيقية كاملة في قسم REAL SERVER RESPONSE")
    
    # تحليل المجلدات
    print(f"\n📁 تحليل المجلدات:")
    folder_stats = {}
    for folder_name, folder_images in folders.items():
        sizes = []
        for img_path in folder_images:
            try:
                sizes.append(os.path.getsize(img_path))
            except:
                pass
        
        if sizes:
            avg_folder_size = sum(sizes) / len(sizes)
            max_folder_size = max(sizes)
            min_folder_size = min(sizes)
            
            folder_stats[folder_name] = {
                'count': len(folder_images),
                'avg_size': avg_folder_size,
                'max_size': max_folder_size,
                'min_size': min_folder_size
            }
            
            print(f"   📂 {folder_name}:")
            print(f"      📊 عدد الصور: {len(folder_images)}")
            print(f"      📊 متوسط الحجم: {avg_folder_size/1024:.1f} KB")
            print(f"      📊 أكبر صورة: {max_folder_size/1024:.1f} KB")
            print(f"      📊 أصغر صورة: {min_folder_size/1024:.1f} KB")
            
            # تحديد جودة المحتوى
            if avg_folder_size > 150000:  # أكبر من 150KB
                print(f"      ✅ مجلد عالي الجودة - يحتوي على استجابات حقيقية")
            elif avg_folder_size < 50000:  # أقل من 50KB
                print(f"      ⚠️  مجلد منخفض الجودة - قد لا يحتوي على استجابات حقيقية")
            else:
                print(f"      📊 مجلد متوسط الجودة")
    
    # الخلاصة والتوصيات
    print(f"\n🎯 الخلاصة والتحليل:")
    
    # تحديد المجلدات الجيدة والسيئة
    good_folders = []
    bad_folders = []
    
    for folder_name, stats in folder_stats.items():
        if stats['avg_size'] > 150000:
            good_folders.append(folder_name)
        elif stats['avg_size'] < 50000:
            bad_folders.append(folder_name)
    
    if good_folders:
        print(f"✅ مجلدات تحتوي على استجابات حقيقية: {', '.join(good_folders)}")
    
    if bad_folders:
        print(f"⚠️  مجلدات قد لا تحتوي على استجابات حقيقية: {', '.join(bad_folders)}")
    
    print(f"\n💡 التفسير المحتمل:")
    print(f"   🌐 المواقع مثل httpbin.org ترجع استجابات JSON حقيقية")
    print(f"   📊 الصور الكبيرة تحتوي على قسم REAL SERVER RESPONSE مع البيانات الكاملة")
    print(f"   ⚠️  الصور الصغيرة قد تحتوي على رسالة 'لا توجد بيانات' أو محتوى قليل")
    print(f"   🔗 نوع URL والموقع المستهدف يؤثر على حجم الاستجابة")

if __name__ == "__main__":
    analyze_screenshots()
