/**
 * Bug Bounty Report Exporter v4.0
 * مُصدر التقارير الاحترافية مع دعم الصور الحقيقية والاستغلال الفعلي
 * متوافق مع Bug Bounty System v4.0
 */

class BugBountyReportExporter {
    constructor() {
        this.version = '4.0';
        this.systemName = 'Bug Bounty Report Exporter v4.0';
        this.reportData = null;
        this.targetUrl = null;
        this.visualizations = [];
        this.screenshots = [];
        this.exploitationResults = [];
        this.realTestingData = [];

        console.log(`✅ ${this.systemName} تم تحميله بنجاح`);
        console.log('📊 ميزات v4.0: تصدير الصور + نتائج الاستغلال + البيانات الحقيقية');
    }

    // استخراج الصور من محتوى التقرير
    extractImagesFromContent(content) {
        const images = [];

        // البحث عن صور base64 في المحتوى
        const base64Regex = /data:image\/[^;]+;base64,([^"'\s]+)/g;
        let match;

        while ((match = base64Regex.exec(content)) !== null) {
            images.push({
                type: 'base64',
                data: match[1],
                fullSrc: match[0]
            });
        }

        // البحث عن img tags مع src
        const imgTagRegex = /<img[^>]+src="([^"]+)"[^>]*>/g;
        while ((match = imgTagRegex.exec(content)) !== null) {
            if (match[1].startsWith('data:image')) {
                const base64Data = match[1].split(',')[1];
                if (base64Data) {
                    images.push({
                        type: 'base64',
                        data: base64Data,
                        fullSrc: match[1],
                        tag: match[0]
                    });
                }
            }
        }

        return images;
    }

    // فحص إذا كانت البيانات SVG
    isSVGData(base64Data) {
        try {
            const decoded = atob(base64Data);
            return decoded.includes('<svg') || decoded.includes('xmlns="http://www.w3.org/2000/svg"');
        } catch (error) {
            return false;
        }
    }

    // تحويل SVG إلى PNG
    async convertSVGToPNG(base64Data) {
        return new Promise((resolve) => {
            try {
                // فك تشفير SVG
                const svgString = atob(base64Data);

                // إنشاء canvas لتحويل SVG إلى PNG
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = function() {
                    canvas.width = img.width || 300;
                    canvas.height = img.height || 200;
                    ctx.drawImage(img, 0, 0);

                    // تحويل إلى PNG base64
                    const pngDataUrl = canvas.toDataURL('image/png');
                    resolve(pngDataUrl);
                };

                img.onerror = function() {
                    // في حالة الفشل، إرجاع البيانات الأصلية
                    resolve(`data:image/png;base64,${base64Data}`);
                };

                // تحميل SVG
                const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);
                img.src = url;

            } catch (error) {
                console.warn('⚠️ فشل تحويل SVG إلى PNG:', error);
                resolve(`data:image/png;base64,${base64Data}`);
            }
        });
    }

    // إنشاء تقرير HTML مع دعم الصور المُحسن
    generateHTMLReport(reportContent, targetUrl) {
        try {
            console.log('📄 إنشاء تقرير HTML مع دعم الصور المُحسن...');

            // التأكد من وجود الصور في المحتوى
            const images = this.extractImagesFromContent(reportContent);
            console.log(`📸 تم العثور على ${images.length} صورة في التقرير HTML`);

            // إنشاء HTML محسن مع CSS للصور
            const htmlContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bug Bounty Security Report - ${targetUrl}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .vulnerability-image {
            max-width: 100%;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        .image-error {
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            color: #721c24;
            text-align: center;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .content-section {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .content-array {
            margin: 10px 0;
        }
        .array-item {
            margin: 10px 0;
            padding: 10px;
            background: #fff;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        .content-object {
            margin: 10px 0;
        }
        .object-property {
            margin: 10px 0;
            padding: 10px;
            background: #fff;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        .item-header, .property-header {
            color: #495057;
            margin: 0 0 10px 0;
            font-size: 14px;
        }
        .text-content {
            line-height: 1.6;
            color: #333;
        }
        .image-card h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        .vulnerability-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ تقرير Bug Bounty الأمني</h1>
            <p><strong>الهدف:</strong> ${targetUrl}</p>
            <p><strong>التاريخ:</strong> ${new Date().toLocaleString('ar')}</p>
            <p><strong>عدد الصور:</strong> ${images.length} صورة حقيقية</p>
        </div>

        <div class="content">
            ${this.processContentWithImages(reportContent)}
        </div>
    </div>
</body>
</html>`;

            return htmlContent;

        } catch (error) {
            console.error('❌ خطأ في إنشاء تقرير HTML:', error);
            return `<html><body><h1>خطأ في إنشاء التقرير</h1><p>${error.message}</p></body></html>`;
        }
    }

    // معالجة المحتوى مع تحسين عرض الصور وإصلاح المسارات
    processContentWithImages(content) {
        // تحسين عرض الصور في HTML
        let processedContent = content;

        // إصلاح مسارات الصور النسبية إلى مطلقة
        processedContent = processedContent.replace(
            /src="\.\/assets\/modules\/bugbounty\/screenshots\/([^"]+)"/g,
            (match, imagePath) => {
                // تحويل المسار النسبي إلى مطلق
                const absolutePath = `file:///${process.cwd().replace(/\\/g, '/')}/assets/modules/bugbounty/screenshots/${imagePath}`;
                return `src="${absolutePath}"`;
            }
        );

        // تحسين img tags مع إضافة فحص الأخطاء
        processedContent = processedContent.replace(
            /<img([^>]+)>/g,
            '<img$1 class="vulnerability-image" loading="lazy" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';">'
        );

        // إضافة رسائل خطأ للصور المفقودة
        processedContent = processedContent.replace(
            /(<img[^>]+>)/g,
            '$1<div class="image-error" style="display:none; background:#f8d7da; padding:10px; border-radius:5px; color:#721c24; text-align:center;">❌ الصورة غير متاحة</div>'
        );

        // تحسين grid الصور وإزالة inline styles
        processedContent = processedContent.replace(
            /<div style="display: grid; grid-template-columns: 1fr 1fr 1fr;([^>]*)>/g,
            '<div class="image-grid">'
        );

        // تحسين image containers وإزالة inline styles
        processedContent = processedContent.replace(
            /<div style="text-align: center; padding: 15px;([^>]*)>/g,
            '<div class="image-card">'
        );

        // إزالة جميع inline styles المتبقية
        processedContent = processedContent.replace(/style="[^"]*"/g, '');

        return processedContent;
    }

    // إنشاء تقرير PDF مع دعم الصور
    async generatePDFReport(reportContent, targetUrl) {
        try {
            // استخدام jsPDF لإنشاء PDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // إعداد الخط العربي
            doc.setFont('helvetica');

            // استخراج الصور من المحتوى
            const images = this.extractImagesFromContent(reportContent);
            console.log(`📸 تم العثور على ${images.length} صورة في التقرير`);
            doc.setFontSize(16);
            
            // العنوان
            doc.text('Bug Bounty Security Report', 20, 20);
            doc.setFontSize(12);
            doc.text(`Target: ${targetUrl}`, 20, 35);
            doc.text(`Date: ${new Date().toLocaleString()}`, 20, 45);
            
            // المحتوى مع الصور
            const lines = this.formatContentForPDF(reportContent);
            let yPosition = 60;

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];

                if (yPosition > 270) {
                    doc.addPage();
                    yPosition = 20;
                }

                // إضافة الصور إذا وُجدت
                if (images.length > 0 && i % 10 === 0) { // إضافة صورة كل 10 أسطر
                    const imageIndex = Math.floor(i / 10);
                    if (imageIndex < images.length) {
                        try {
                            const image = images[imageIndex];

                            // تحديد نوع الصورة الصحيح
                            let imageFormat = 'PNG';
                            let dataUrl = image.fullSrc || `data:image/png;base64,${image.data}`;

                            // فحص إذا كانت الصورة SVG
                            if (dataUrl.includes('data:image/svg+xml') ||
                                (image.data && this.isSVGData(image.data))) {
                                // تحويل SVG إلى PNG للـ PDF
                                dataUrl = await this.convertSVGToPNG(image.data);
                                imageFormat = 'PNG';
                            }

                            doc.addImage(dataUrl, imageFormat, 20, yPosition, 80, 60);
                            yPosition += 70; // مساحة للصورة
                        } catch (error) {
                            console.warn('⚠️ فشل إضافة صورة للـ PDF:', error);
                        }
                    }
                }

                doc.text(line, 20, yPosition);
                yPosition += 7;
            }
            
            // حفظ الملف
            const fileName = `bug_bounty_report_${this.sanitizeFileName(targetUrl)}_${Date.now()}.pdf`;
            doc.save(fileName);
            
            return fileName;
        } catch (error) {
            console.error('خطأ في إنشاء PDF:', error);
            return this.generateTextReport(reportContent, targetUrl);
        }
    }

    // إنشاء تقرير نصي
    generateTextReport(reportContent, targetUrl) {
        const reportText = `
BUG BOUNTY SECURITY REPORT
==========================

Target: ${targetUrl}
Date: ${new Date().toLocaleString()}
Generated by: Advanced Bug Bounty System

${this.cleanTextContent(reportContent)}

---
Report generated by Bug Bounty Core v2.0
Professional Security Analysis System
        `.trim();

        // إنشاء ملف للتحميل
        const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        
        const fileName = `bug_bounty_report_${this.sanitizeFileName(targetUrl)}_${Date.now()}.txt`;
        
        // إنشاء رابط التحميل
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = fileName;
        downloadLink.style.display = 'none';
        
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        
        // تنظيف الذاكرة
        setTimeout(() => URL.revokeObjectURL(url), 1000);
        
        return fileName;
    }

    // إنشاء تقرير JSON
    generateJSONReport(reportContent, analysisData, targetUrl) {
        const jsonReport = {
            metadata: {
                target: targetUrl,
                timestamp: new Date().toISOString(),
                generator: 'Bug Bounty Core v2.0',
                report_type: 'comprehensive_security_analysis'
            },
            analysis_data: analysisData,
            security_report: reportContent,
            summary: this.extractSummary(reportContent),
            vulnerabilities: this.extractVulnerabilities(reportContent)
        };

        const blob = new Blob([JSON.stringify(jsonReport, null, 2)], { 
            type: 'application/json;charset=utf-8' 
        });
        const url = URL.createObjectURL(blob);
        
        const fileName = `bug_bounty_analysis_${this.sanitizeFileName(targetUrl)}_${Date.now()}.json`;
        
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = fileName;
        downloadLink.style.display = 'none';
        
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        
        setTimeout(() => URL.revokeObjectURL(url), 1000);
        
        return fileName;
    }

    // تنظيف اسم الملف
    sanitizeFileName(url) {
        return url.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 50);
    }

    // تنسيق المحتوى للـ PDF
    formatContentForPDF(content) {
        // إزالة HTML tags وتنسيق النص
        const cleanContent = content.replace(/<[^>]*>/g, '');
        const lines = cleanContent.split('\n');
        const formattedLines = [];
        
        lines.forEach(line => {
            const trimmedLine = line.trim();
            if (trimmedLine) {
                // تقسيم الأسطر الطويلة
                if (trimmedLine.length > 80) {
                    const words = trimmedLine.split(' ');
                    let currentLine = '';
                    
                    words.forEach(word => {
                        if ((currentLine + word).length > 80) {
                            if (currentLine) {
                                formattedLines.push(currentLine.trim());
                                currentLine = word + ' ';
                            } else {
                                formattedLines.push(word);
                            }
                        } else {
                            currentLine += word + ' ';
                        }
                    });
                    
                    if (currentLine.trim()) {
                        formattedLines.push(currentLine.trim());
                    }
                } else {
                    formattedLines.push(trimmedLine);
                }
            }
        });
        
        return formattedLines;
    }

    // تنظيف المحتوى النصي
    cleanTextContent(content) {
        return content
            .replace(/<[^>]*>/g, '')
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/\s+/g, ' ')
            .trim();
    }

    // استخراج الملخص
    extractSummary(content) {
        const summaryMatch = content.match(/ملخص التقييم(.*?)الثغرات المكتشفة/s);
        return summaryMatch ? this.cleanTextContent(summaryMatch[1]) : 'ملخص غير متاح';
    }

    // استخراج الثغرات
    extractVulnerabilities(content) {
        const vulnerabilities = [];
        const vulnPattern = /#### (\d+)\. ([^#]+?)(?=####|\n### |$)/gs;
        let match;
        
        while ((match = vulnPattern.exec(content)) !== null) {
            const vulnText = match[2];
            const vulnerability = {
                id: match[1],
                name: this.extractField(vulnText, 'اسم الثغرة'),
                type: this.extractField(vulnText, 'النوع'),
                location: this.extractField(vulnText, 'الموقع'),
                severity: this.extractField(vulnText, 'الخطورة'),
                cvss: this.extractField(vulnText, 'CVSS Score'),
                description: this.extractField(vulnText, 'الوصف'),
                impact: this.extractField(vulnText, 'التأثير'),
                remediation: this.extractField(vulnText, 'الإصلاح')
            };
            vulnerabilities.push(vulnerability);
        }
        
        return vulnerabilities;
    }

    // استخراج حقل معين
    extractField(text, fieldName) {
        const pattern = new RegExp(`\\*\\*${fieldName}:\\*\\*\\s*([^\\n*]+)`, 'i');
        const match = text.match(pattern);
        return match ? match[1].trim() : 'غير محدد';
    }

    // إنشاء حاوي التحميل الاحترافي
    createDownloadContainer(reportContent, analysisData, targetUrl) {
        const container = document.createElement('div');
        container.className = 'bug-bounty-download-container';
        container.innerHTML = `
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 20px 0; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                <div style="text-align: center; margin-bottom: 20px;">
                    <h3 style="margin: 0; color: #fff; font-size: 1.5em;">📄 تقرير Bug Bounty جاهز</h3>
                    <p style="margin: 10px 0; opacity: 0.9;">تقرير فحص أمني شامل للموقع: ${targetUrl}</p>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <button onclick="window.bugBountyExporter.downloadPDF('${targetUrl}')" 
                            style="background: #e74c3c; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-size: 1em; transition: all 0.3s; box-shadow: 0 4px 15px rgba(231,76,60,0.3);">
                        📄 تحميل PDF
                    </button>
                    
                    <button onclick="window.bugBountyExporter.downloadText('${targetUrl}')" 
                            style="background: #27ae60; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-size: 1em; transition: all 0.3s; box-shadow: 0 4px 15px rgba(39,174,96,0.3);">
                        📝 تحميل نصي
                    </button>
                    
                    <button onclick="window.bugBountyExporter.downloadJSON('${targetUrl}')" 
                            style="background: #3498db; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-size: 1em; transition: all 0.3s; box-shadow: 0 4px 15px rgba(52,152,219,0.3);">
                        📊 تحميل JSON
                    </button>
                    
                    <button onclick="navigator.clipboard.writeText(window.bugBountyExporter.getCleanReport())" 
                            style="background: #9b59b6; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-size: 1em; transition: all 0.3s; box-shadow: 0 4px 15px rgba(155,89,182,0.3);">
                        📋 نسخ التقرير
                    </button>
                </div>
                
                <div style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <p style="margin: 0; font-size: 0.9em; opacity: 0.8;">
                        📅 تم إنشاء التقرير: ${new Date().toLocaleString('ar-SA')}
                    </p>
                </div>
            </div>
        `;
        
        // حفظ البيانات للاستخدام لاحقاً
        this.reportData = reportContent;
        this.analysisData = analysisData;
        this.targetUrl = targetUrl;
        
        return container;
    }

    // دوال التحميل للأزرار
    downloadPDF(targetUrl) {
        if (this.reportData) {
            this.generatePDFReport(this.reportData, targetUrl);
        }
    }

    downloadText(targetUrl) {
        if (this.reportData) {
            this.generateTextReport(this.reportData, targetUrl);
        }
    }

    downloadJSON(targetUrl) {
        if (this.reportData && this.analysisData) {
            this.generateJSONReport(this.reportData, this.analysisData, targetUrl);
        }
    }

    getCleanReport() {
        return this.reportData ? this.cleanTextContent(this.reportData) : '';
    }
}

// إنشاء مثيل عام
window.bugBountyExporter = new BugBountyReportExporter();

// تصدير الكلاس
window.BugBountyReportExporter = BugBountyReportExporter;
window.ReportExporter = BugBountyReportExporter; // للتوافق مع الاختبارات
