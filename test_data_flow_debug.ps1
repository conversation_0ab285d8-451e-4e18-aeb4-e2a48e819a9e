# اختبار شامل لتتبع تدفق البيانات من النظام v4 إلى سيرفر Python
Write-Host "Testing data flow from v4 system to Python server..." -ForegroundColor Green

# 1. اختبار استقبال البيانات في سيرفر Python
Write-Host "`n📡 اختبار 1: استقبال البيانات في سيرفر Python" -ForegroundColor Yellow

$testData = @{
    url = "http://testphp.vulnweb.com"
    vulnerability_name = "Test_SQL_Injection"
    vulnerability_type = "SQL_Injection"
    stage = "after"
    payload_data = "' OR '1'='1' -- test123"
    target_parameter = "id"
    report_id = "debug_test_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
} | ConvertTo-Json

Write-Host "📤 إرسال بيانات اختبار:" -ForegroundColor Cyan
Write-Host $testData -ForegroundColor White

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/vulnerability_sequence" -Method POST -Body $testData -ContentType "application/json" -TimeoutSec 60
    Write-Host "✅ استجابة سيرفر Python:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor White
} catch {
    Write-Host "❌ خطأ في الاتصال بسيرفر Python: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. اختبار تنوع البيانات
Write-Host "`n📊 اختبار 2: تنوع البيانات لثغرات مختلفة" -ForegroundColor Yellow

$vulnerabilities = @(
    @{
        name = "SQL_Injection_Login"
        type = "SQL_Injection"
        payload = "admin' OR '1'='1' -- hack123"
        param = "username"
    },
    @{
        name = "XSS_Search_Field"
        type = "Cross_Site_Scripting"
        payload = "<script>alert('XSS_test456')</script>"
        param = "search"
    },
    @{
        name = "LFI_File_Include"
        type = "Local_File_Inclusion"
        payload = "../../../etc/passwd?test789"
        param = "file"
    },
    @{
        name = "Command_Injection_Upload"
        type = "Command_Injection"
        payload = "; cat /etc/passwd # test999"
        param = "filename"
    }
)

foreach ($vuln in $vulnerabilities) {
    Write-Host "`n🎯 اختبار ثغرة: $($vuln.name)" -ForegroundColor Cyan
    
    $vulnData = @{
        url = "http://testphp.vulnweb.com"
        vulnerability_name = $vuln.name
        vulnerability_type = $vuln.type
        stage = "after"
        payload_data = $vuln.payload
        target_parameter = $vuln.param
        report_id = "debug_$($vuln.name)_$(Get-Date -Format 'HHmmss')"
    } | ConvertTo-Json
    
    Write-Host "📤 البيانات المرسلة:" -ForegroundColor Gray
    Write-Host "   Payload: $($vuln.payload)" -ForegroundColor White
    Write-Host "   Parameter: $($vuln.param)" -ForegroundColor White
    
    try {
        $vulnResponse = Invoke-RestMethod -Uri "http://localhost:8000/vulnerability_sequence" -Method POST -Body $vulnData -ContentType "application/json" -TimeoutSec 30
        
        if ($vulnResponse.success) {
            Write-Host "✅ نجح: $($vulnResponse.message)" -ForegroundColor Green
            if ($vulnResponse.payload_used) {
                Write-Host "💉 Payload مستخدم: $($vulnResponse.payload_used)" -ForegroundColor Yellow
                
                # التحقق من أن payload فريد
                if ($vulnResponse.payload_used -eq $vuln.payload) {
                    Write-Host "✅ Payload صحيح ومطابق" -ForegroundColor Green
                } else {
                    Write-Host "⚠️ Payload مختلف عن المرسل!" -ForegroundColor Red
                    Write-Host "   المرسل: $($vuln.payload)" -ForegroundColor White
                    Write-Host "   المستلم: $($vulnResponse.payload_used)" -ForegroundColor White
                }
            } else {
                Write-Host "❌ لا يوجد payload في الاستجابة" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ فشل: $($vulnResponse.error)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ خطأ في الطلب: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 2
}

# 3. اختبار مراقبة سجلات سيرفر Python
Write-Host "`n📋 اختبار 3: فحص آخر السجلات في سيرفر Python" -ForegroundColor Yellow

try {
    # محاولة قراءة آخر السجلات (إذا كانت متاحة)
    Write-Host "📄 آخر نشاط في سيرفر Python:" -ForegroundColor Cyan
    Write-Host "   (تحقق من terminal سيرفر Python لرؤية السجلات المفصلة)" -ForegroundColor Gray
} catch {
    Write-Host "⚠️ لا يمكن قراءة السجلات مباشرة" -ForegroundColor Yellow
}

# 4. اختبار الصور المولدة
Write-Host "`n🖼️ اختبار 4: فحص الصور المولدة" -ForegroundColor Yellow

$screenshotsPath = "assets\modules\bugbounty\screenshots"
if (Test-Path $screenshotsPath) {
    $recentImages = Get-ChildItem -Path $screenshotsPath -Recurse -Filter "*.png" | Sort-Object LastWriteTime -Descending | Select-Object -First 10
    
    Write-Host "📸 آخر 10 صور مولدة:" -ForegroundColor Cyan
    foreach ($img in $recentImages) {
        $size = [math]::Round($img.Length / 1KB, 2)
        Write-Host "   $($img.Name) - ${size}KB - $($img.LastWriteTime)" -ForegroundColor White
    }
} else {
    Write-Host "❌ مجلد الصور غير موجود: $screenshotsPath" -ForegroundColor Red
}

# 5. ملخص النتائج
Write-Host "`n📊 ملخص نتائج الاختبار:" -ForegroundColor Green
Write-Host "✅ تم اختبار تدفق البيانات من PowerShell إلى سيرفر Python" -ForegroundColor White
Write-Host "✅ تم اختبار تنوع البيانات لثغرات مختلفة" -ForegroundColor White
Write-Host "✅ تم فحص الصور المولدة" -ForegroundColor White
Write-Host "`n🔍 للمراقبة المباشرة، تحقق من:" -ForegroundColor Yellow
Write-Host "   - terminal سيرفر Python للسجلات المفصلة" -ForegroundColor White
Write-Host "   - مجلد screenshots للصور المولدة" -ForegroundColor White
Write-Host "   - استجابات API للتأكد من تمرير البيانات" -ForegroundColor White

Write-Host "`n🎯 اختبار تدفق البيانات مكتمل!" -ForegroundColor Green
