#!/usr/bin/env python3
"""
اختبار مشكلة 404 في صور "بعد الاستغلال"
"""

import requests
import json
import time
import sys

def test_server_health():
    """اختبار صحة السيرفر"""
    try:
        print("🔍 اختبار صحة السيرفر...")
        response = requests.get("http://localhost:8000/health", timeout=10)
        print(f"✅ السيرفر يعمل: {response.status_code}")
        print(f"📊 الاستجابة: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ السيرفر لا يعمل: {e}")
        return False

def test_after_exploitation_screenshot():
    """اختبار صورة بعد الاستغلال التي تسبب 404"""
    try:
        print("\n🔥 اختبار صورة بعد الاستغلال...")
        
        # بيانات الاختبار
        test_data = {
            "url": "https://httpbin.org/get?id=1",
            "filename": "test_after_exploitation",
            "report_id": "test_404_fix",
            "vulnerability_name": "SQL Injection Test",
            "vulnerability_type": "SQL Injection",
            "stage": "after",
            "payload_data": "' UNION SELECT 1,2,3 --",
            "target_parameter": "id"
        }
        
        print(f"📤 إرسال طلب إلى: http://localhost:8000/v4_website")
        print(f"📋 البيانات: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        # إرسال الطلب
        response = requests.post(
            "http://localhost:8000/v4_website",
            json=test_data,
            timeout=60
        )
        
        print(f"📥 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ نجح الطلب!")
            print(f"📊 النتيجة: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # فحص إذا كانت هناك مشكلة 404 في URL
            if 'target_url' in result:
                target_url = result['target_url']
                print(f"\n🔗 فحص URL المستهدف: {target_url}")
                
                # اختبار URL مباشرة
                try:
                    url_response = requests.get(target_url, timeout=10)
                    print(f"🌐 كود استجابة URL: {url_response.status_code}")
                    
                    if url_response.status_code == 404:
                        print("❌ مشكلة 404 مكتشفة!")
                        print(f"🔧 URL المشكل: {target_url}")
                        return False
                    else:
                        print("✅ URL يعمل بشكل صحيح")
                        return True
                        
                except Exception as url_error:
                    print(f"❌ خطأ في اختبار URL: {url_error}")
                    return False
            
            return True
            
        else:
            print(f"❌ فشل الطلب: {response.status_code}")
            print(f"📄 الاستجابة: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_url_creation_logic():
    """اختبار منطق إنشاء URL"""
    print("\n🧪 اختبار منطق إنشاء URL...")
    
    # محاكاة الدوال المستخدمة في السيرفر
    def create_after_exploitation_url_test(original_url, vulnerability_name, payload_data=None):
        """محاكاة دالة إنشاء URL"""
        print(f"🔧 إنشاء URL للاستغلال:")
        print(f"   - URL الأصلي: {original_url}")
        print(f"   - نوع الثغرة: {vulnerability_name}")
        print(f"   - Payload: {payload_data}")
        
        # الطريقة القديمة (المشكلة)
        if payload_data:
            if '?' in original_url:
                problematic_url = f"{original_url}&exploit=test&vuln={vulnerability_name}"
            else:
                problematic_url = f"{original_url}?exploit=test&vuln={vulnerability_name}"
            print(f"❌ URL المشكل (القديم): {problematic_url}")
        
        # الطريقة الصحيحة (الحل)
        correct_url = original_url  # استخدام URL الأصلي بدون تعديل
        print(f"✅ URL الصحيح (الجديد): {correct_url}")
        
        return correct_url
    
    # اختبار مع URLs مختلفة
    test_urls = [
        "https://httpbin.org/get?id=1",
        "https://httpbin.org/get",
        "https://example.com/page?param=value",
        "https://testphp.vulnweb.com/artists.php?artist=1"
    ]
    
    for url in test_urls:
        print(f"\n🔍 اختبار URL: {url}")
        result_url = create_after_exploitation_url_test(url, "SQL Injection", "' UNION SELECT 1,2,3 --")
        
        # اختبار إذا كان URL صالح
        try:
            response = requests.head(result_url, timeout=5)
            print(f"✅ URL صالح: {response.status_code}")
        except Exception as e:
            print(f"❌ URL غير صالح: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔥" * 50)
    print("🔥 اختبار مشكلة 404 في صور بعد الاستغلال")
    print("🔥" * 50)
    
    # 1. اختبار صحة السيرفر
    if not test_server_health():
        print("❌ السيرفر لا يعمل! تأكد من تشغيله أولاً")
        return
    
    # 2. اختبار منطق إنشاء URL
    test_url_creation_logic()
    
    # 3. اختبار صورة بعد الاستغلال
    print("\n" + "="*50)
    success = test_after_exploitation_screenshot()
    
    # 4. النتيجة النهائية
    print("\n" + "🔥" * 50)
    if success:
        print("✅ الاختبار نجح! لا توجد مشكلة 404")
    else:
        print("❌ الاختبار فشل! مشكلة 404 لا تزال موجودة")
        print("🔧 يجب إصلاح دالة create_after_exploitation_url")
    print("🔥" * 50)

if __name__ == "__main__":
    main()
