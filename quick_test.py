#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للسيرفر والثغرات
"""

import requests
import time
import json

def test_server_health():
    """اختبار صحة السيرفر"""
    print("🔍 اختبار صحة السيرفر...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"✅ Health endpoint: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   📊 الخدمة: {data.get('service', 'غير محدد')}")
            print(f"   📊 الحالة: {data.get('status', 'غير محدد')}")
        return True
    except Exception as e:
        print(f"❌ Health endpoint: خطأ - {str(e)}")
        return False

def test_simple_vulnerability():
    """اختبار ثغرة واحدة بسيطة"""
    print("\n🔍 اختبار ثغرة SQL Injection...")
    
    test_data = {
        "url": "https://httpbin.org/get?id=1",
        "vulnerability_name": "SQL_Injection_Test",
        "vulnerability_type": "sql_injection",
        "report_id": f"quick_test_{int(time.time())}",
        "payload_data": "' OR '1'='1' --",
        "target_parameter": "id"
    }
    
    print(f"📋 إرسال طلب إلى /vulnerability_sequence...")
    print(f"🎯 URL: {test_data['url']}")
    print(f"🔥 Payload: {test_data['payload_data']}")
    
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8000/vulnerability_sequence",
            json=test_data,
            timeout=60  # timeout أطول
        )
        elapsed_time = round(time.time() - start_time, 2)
        
        print(f"✅ الاستجابة: {response.status_code} - {elapsed_time}s")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 النتيجة: {data.get('message', 'لا توجد رسالة')}")
            print(f"📊 المراحل الناجحة: {data.get('successful_stages', 0)}/{data.get('total_stages', 3)}")
            
            # عرض تفاصيل المراحل
            if 'stages_results' in data:
                print(f"\n📋 تفاصيل المراحل:")
                for stage, result in data['stages_results'].items():
                    if result and result.get('success'):
                        print(f"   ✅ {stage}: نجح")
                        if 'screenshot_path' in result:
                            print(f"      📸 الصورة: {result['screenshot_path']}")
                    else:
                        print(f"   ❌ {stage}: فشل")
                        if result and 'error' in result:
                            print(f"      ⚠️ الخطأ: {result['error']}")
            
            return True
        else:
            print(f"⚠️ استجابة غير متوقعة: {response.status_code}")
            print(f"   المحتوى: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الطلب: {str(e)}")
        return False

def test_before_after_endpoint():
    """اختبار endpoint before_after"""
    print("\n🔍 اختبار endpoint /before_after...")
    
    test_data = {
        "url": "https://httpbin.org/get",
        "vulnerability_name": "Quick_Test",
        "vulnerability_type": "sql_injection",
        "report_id": f"before_after_test_{int(time.time())}",
        "payload_data": "test_payload"
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8000/before_after",
            json=test_data,
            timeout=30
        )
        elapsed_time = round(time.time() - start_time, 2)
        
        print(f"✅ الاستجابة: {response.status_code} - {elapsed_time}s")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 النتيجة: {data.get('message', 'لا توجد رسالة')}")
            return True
        else:
            print(f"⚠️ استجابة غير متوقعة: {response.status_code}")
            print(f"   المحتوى: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الطلب: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار السريع للسيرفر...")
    print(f"⏰ الوقت: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # اختبار صحة السيرفر
    if not test_server_health():
        print("❌ السيرفر لا يعمل بشكل صحيح!")
        return
    
    # اختبار ثغرة بسيطة
    print("\n" + "="*50)
    success1 = test_simple_vulnerability()
    
    # اختبار endpoint آخر
    print("\n" + "="*50)
    success2 = test_before_after_endpoint()
    
    # ملخص النتائج
    print("\n" + "="*50)
    print("📊 ملخص النتائج:")
    print(f"   ✅ صحة السيرفر: نجح")
    print(f"   {'✅' if success1 else '❌'} اختبار vulnerability_sequence: {'نجح' if success1 else 'فشل'}")
    print(f"   {'✅' if success2 else '❌'} اختبار before_after: {'نجح' if success2 else 'فشل'}")
    
    if success1 and success2:
        print("\n🎉 جميع الاختبارات نجحت!")
    else:
        print("\n⚠️ بعض الاختبارات فشلت!")

if __name__ == "__main__":
    main()
