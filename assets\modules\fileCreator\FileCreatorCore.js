/**
 * File Creator Core - Advanced File Generation System
 * Creates professional PDF, PowerPoint, EXE files and more
 * Integrated with internet capabilities like ChatGPT Pro
 */

// حماية من التحميل المكرر
if (typeof window.FileCreatorCore !== 'undefined') {
    console.warn('⚠️ FileCreatorCore تم تحميله مسبقاً - تجاهل التحميل المكرر');
} else {

// تصدير فوري للكلاس قبل التعريف
console.log('📁 بدء تحميل FileCreatorCore...');

class FileCreatorCore {
    constructor() {
        this.isActive = false;
        this.internetAccess = true;
        this.creationHistory = [];
        this.capturedContent = null; // لحفظ المحتوى المُلتقط
        this.templates = this.initTemplates();
        this.supportedFormats = [
            'pdf', 'pptx', 'docx', 'xlsx', 'exe', 'html', 'css', 'js',
            'py', 'java', 'cpp', 'zip', 'json', 'xml', 'csv'
        ];
        this.internetTools = this.initInternetTools();
        this.loadJsPDF();
        this.setupContentCapture();
    }

    // Setup content capture system
    setupContentCapture() {
        // مراقبة جميع مناطق الرد المحتملة
        const possibleAreas = ['#response-area', '.response-content', '.assistant-response', '.chat-response', '.message-content'];

        possibleAreas.forEach(selector => {
            const area = document.querySelector(selector);
            if (area) {
                const observer = new MutationObserver((mutations) => {
                    const text = area.textContent || area.innerText;
                    if (text && text.length > 200) { // نص طويل يدل على شرح مفصل
                        // تنظيف النص
                        let cleanText = text
                            .replace(/أهلاً وسهلاً بك![\s\S]*?ثلاثي الأبعاد/g, '')
                            .replace(/يمكنك تنزيل الملف[\s\S]*?sharing\)/g, '')
                            .replace(/فهمت طلبك:[\s\S]*?نبدأ به\?/g, '')
                            .replace(/يمكنني مساعدتك بطرق متعددة:[\s\S]*?نبدأ به\?/g, '')
                            .trim();

                        // حفظ فقط إذا كان شرح حقيقي وليس رد عام
                        if (cleanText.length > 200 &&
                            !cleanText.includes('يمكنني مساعدتك بطرق متعددة') &&
                            !cleanText.includes('ما الذي تفضل أن نبدأ به')) {
                            window.lastAssistantResponse = cleanText;
                            console.log('📥 تم حفظ شرح مفصل من', selector, ':', cleanText.substring(0, 100) + '...');
                        }
                    }
                });

                observer.observe(area, {
                    childList: true,
                    subtree: true,
                    characterData: true
                });
            }
        });

        // مراقبة إضافية للتغييرات في الصفحة
        setInterval(() => {
            possibleAreas.forEach(selector => {
                const area = document.querySelector(selector);
                if (area) {
                    const text = area.textContent || area.innerText;
                    if (text && text.length > 50) {
                        let cleanText = text
                            .replace(/أهلاً وسهلاً بك![\s\S]*?ثلاثي الأبعاد/g, '')
                            .replace(/يمكنك تنزيل الملف[\s\S]*?sharing\)/g, '')
                            .trim();

                        if (cleanText.length > 50 && cleanText !== window.lastAssistantResponse) {
                            window.lastAssistantResponse = cleanText;
                            console.log('📥 تم تحديث رد المساعد:', cleanText.substring(0, 100) + '...');
                        }
                    }
                }
            });
        }, 500); // فحص كل نصف ثانية
    }

    // تحميل مكتبة jsPDF للنظام الأصلي
    loadJsPDF() {
        if (typeof window.jsPDF === 'undefined') {
            console.log('📦 تحميل مكتبة jsPDF للنظام الأصلي...');
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => {
                console.log('✅ تم تحميل مكتبة jsPDF للنظام الأصلي');
                // التأكد من تعيين jsPDF بشكل صحيح
                if (window.jspdf && window.jspdf.jsPDF) {
                    window.jsPDF = window.jspdf.jsPDF;
                    console.log('✅ تم تعيين window.jsPDF بنجاح');
                } else {
                    console.warn('⚠️ مشكلة في تعيين jsPDF');
                }
            };
            script.onerror = () => {
                console.warn('⚠️ فشل في تحميل jsPDF للنظام الأصلي');
            };
            document.head.appendChild(script);
        } else {
            console.log('✅ مكتبة jsPDF متاحة بالفعل');
        }
    }

    // Initialize templates for different file types
    initTemplates() {
        return {
            pdf: {
                report: 'تقرير احترافي',
                presentation: 'عرض تقديمي',
                manual: 'دليل المستخدم',
                invoice: 'فاتورة',
                certificate: 'شهادة',
                resume: 'سيرة ذاتية'
            },
            powerpoint: {
                business: 'عرض أعمال',
                educational: 'عرض تعليمي',
                technical: 'عرض تقني',
                marketing: 'عرض تسويقي',
                scientific: 'عرض علمي'
            },
            exe: {
                utility: 'أداة مساعدة',
                game: 'لعبة بسيطة',
                calculator: 'آلة حاسبة',
                converter: 'محول ملفات',
                organizer: 'منظم ملفات'
            }
        };
    }

    // Initialize internet tools
    initInternetTools() {
        return {
            imageSearch: true,
            dataFetching: true,
            apiAccess: true,
            downloadCapability: true,
            realTimeInfo: true
        };
    }

    // Activate File Creator Mode
    activate() {
        this.isActive = true;
        console.log('📁 File Creator Mode activated');

        // تحديث المتغير العام
        window.fileCreatorActive = true;

        // إظهار تأكيد مرئي فوري
        this.showActivationConfirmation();

        // إضافة رسالة للمحادثة
        if (typeof addMessage === 'function') {
            addMessage('assistant', '✅ تم تفعيل File Creator Mode - جاهز لإنشاء الملفات مع روابط حقيقية مثل ChatGPT');
        }

        if (typeof speakText === 'function') {
            speakText('تم تفعيل File Creator Mode المتقدم. يمكنني الآن إنشاء أي نوع من الملفات مع التكامل الكامل مع الإنترنت. ما الملف الذي تريد إنشاءه؟');
        }
    }

    // Handle voice commands for file creation
    async handleVoiceCommand(command) {
        const lowerCommand = command.toLowerCase();

        // إعدادات طريقة العرض (أوامر صوتية)
        if (lowerCommand.includes('غير طريقة العرض') || lowerCommand.includes('إعدادات الملفات') ||
            lowerCommand.includes('اعدادات العرض') || lowerCommand.includes('طريقة عرض الملفات')) {
            this.showDisplayModeSettings();
            return '⚙️ تم فتح إعدادات طريقة عرض الملفات. يمكنك الاختيار بين: المحادثة، نافذة منبثقة، أو الاثنين معاً.';
        }

        if (lowerCommand.includes('عرض في المحادثة') || lowerCommand.includes('مثل شات جي بي تي') ||
            lowerCommand.includes('في الدردشة') || lowerCommand.includes('chat mode')) {
            this.setDisplayMode('chat');
            return '💬 تم تغيير طريقة العرض إلى: في المحادثة مثل ChatGPT. الملفات ستظهر كروابط تحميل في المحادثة.';
        }

        if (lowerCommand.includes('عرض نافذة منبثقة') || lowerCommand.includes('نافذة منبثقة') ||
            lowerCommand.includes('popup') || lowerCommand.includes('نافذة')) {
            this.setDisplayMode('popup');
            return '🪟 تم تغيير طريقة العرض إلى: نافذة منبثقة. ستظهر نافذة تأكيد عند إنشاء الملفات.';
        }

        if (lowerCommand.includes('عرض الاثنين') || lowerCommand.includes('الطريقتين') ||
            lowerCommand.includes('both') || lowerCommand.includes('معا')) {
            this.setDisplayMode('both');
            return '🔄 تم تغيير طريقة العرض إلى: الاثنين معاً. ستحصل على نافذة منبثقة ورابط في المحادثة.';
        }

        // PDF creation commands
        if (lowerCommand.includes('pdf') || lowerCommand.includes('تقرير')) {
            return await this.handlePDFCreation(command);
        }

        // PowerPoint creation commands
        if (lowerCommand.includes('powerpoint') || lowerCommand.includes('عرض') || lowerCommand.includes('ppt')) {
            return await this.handlePowerPointCreation(command);
        }

        // EXE creation commands
        if (lowerCommand.includes('exe') || lowerCommand.includes('برنامج') || lowerCommand.includes('تطبيق')) {
            return await this.handleEXECreation(command);
        }

        // General file creation
        if (lowerCommand.includes('أنشئ') || lowerCommand.includes('اعمل') || lowerCommand.includes('create')) {
            return await this.handleSpecificFileCreation(command);
        }

        // Internet integration commands
        if (lowerCommand.includes('من الإنترنت') || lowerCommand.includes('ابحث') || lowerCommand.includes('حمل')) {
            return await this.handleInternetIntegration(command);
        }

        return await this.getAIFileCreationResponse(command);
    }

    // معالجة طلبات المستخدم (للتكامل مع المحادثة)
    async processUserRequest(userMessage) {
        console.log('📁 معالجة طلب المستخدم:', userMessage.substring(0, 50));

        try {
            // التحقق من التفعيل باستخدام المتغير العام الموثوق
            const isActive = window.fileCreatorActive === true || this.isActive === true;

            if (!isActive) {
                console.log('⚠️ File Creator غير مفعل');
                console.log('- this.isActive:', this.isActive);
                console.log('- window.fileCreatorActive:', window.fileCreatorActive);
                return 'يرجى تفعيل File Creator Mode أولاً من الأزرار العلوية';
            }

            console.log('✅ File Creator مفعل - بدء معالجة الطلب');

            // تحليل نوع الملف المطلوب
            const lowerMessage = userMessage.toLowerCase();

            if (lowerMessage.includes('pdf') || lowerMessage.includes('تقرير')) {
                return await this.handlePDFCreation(userMessage);
            } else if (lowerMessage.includes('powerpoint') || lowerMessage.includes('عرض') || lowerMessage.includes('ppt')) {
                return await this.handlePowerPointCreation(userMessage);
            } else if (lowerMessage.includes('exe') || lowerMessage.includes('برنامج') || lowerMessage.includes('تطبيق')) {
                return await this.handleEXECreation(userMessage);
            } else {
                // ملف عام - تحديد النوع تلقائياً
                return await this.handleSpecificFileCreation(userMessage);
            }

        } catch (error) {
            console.error('❌ خطأ في معالجة طلب المستخدم:', error);
            return `❌ حدث خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // معالجة إنشاء الملفات العامة (ChatGPT Style)
    async handleGeneralFileCreation(userMessage) {
        console.log('📄 ChatGPT Style: معالجة إنشاء ملف عام:', userMessage.substring(0, 50));

        try {
            // إرسال نص المستخدم مباشرة إلى النموذج (مثل ChatGPT)
            const content = await this.generateFileContent(userMessage, 'general');

            if (!content) {
                return `❌ فشل في إنشاء الملف: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // تحديد نوع الملف من المحتوى أو الطلب
            const detectedType = this.detectFileTypeFromContent(content, userMessage);
            const fileInfo = this.getFileTypeInfo(detectedType);
            const fileType = fileInfo.type;
            const mimeType = fileInfo.mimeType;
            const extension = fileInfo.extension;

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, userMessage);
            const filename = `${topic}${extension}`;

            if (content) {
                // إنشاء الملف مع الحاوية الاحترافية مباشرة
                console.log('📁 إنشاء الملف بالنظام الأصلي:', filename);

                // معالجة خاصة لـ PDF
                if (fileType === 'pdf') {
                    if (content instanceof Blob) {
                        // PDF حقيقي من jsPDF
                        const url = URL.createObjectURL(content);
                        console.log('📊 تفاصيل PDF الحقيقي:', { filename, size: content.size, type: 'application/pdf' });
                        this.createProfessionalDownloadContainer(filename, url, content.size);
                    } else if (content instanceof Promise) {
                        // انتظار إنشاء PDF
                        console.log('⏳ انتظار إنشاء PDF...');
                        const pdfBlob = await content;
                        if (pdfBlob instanceof Blob) {
                            const url = URL.createObjectURL(pdfBlob);
                            console.log('📊 تفاصيل PDF المنتظر:', { filename, size: pdfBlob.size, type: 'application/pdf' });
                            this.createProfessionalDownloadContainer(filename, url, pdfBlob.size);
                        } else {
                            // PDF نصي
                            const blob = new Blob([pdfBlob], { type: 'application/pdf' });
                            const url = URL.createObjectURL(blob);
                            console.log('📊 تفاصيل PDF النصي:', { filename, size: blob.size, type: 'application/pdf' });
                            this.createProfessionalDownloadContainer(filename, url, blob.size);
                        }
                    } else {
                        // PDF نصي
                        const blob = new Blob([content], { type: 'application/pdf' });
                        const url = URL.createObjectURL(blob);
                        console.log('📊 تفاصيل PDF النصي:', { filename, size: blob.size, type: 'application/pdf' });
                        this.createProfessionalDownloadContainer(filename, url, blob.size);
                    }
                } else {
                    // ملفات أخرى
                    const blob = new Blob([content], { type: mimeType });
                    const url = URL.createObjectURL(blob);
                    console.log('📊 تفاصيل الملف الأصلي:', { filename, size: blob.size, type: mimeType });
                    console.log('🔥 استدعاء createProfessionalDownloadContainer مباشرة...');
                    this.createProfessionalDownloadContainer(filename, url, blob.size);
                    console.log('✅ تم استدعاء createProfessionalDownloadContainer');
                }

                // لا ترجع رسالة نصية - فقط الحاوية
                return null;
            } else {
                return '❌ لم يتم توليد محتوى للملف. تأكد من تشغيل النموذج المحلي.';
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء الملف العام:', error);
            return `❌ حدث خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // توليد محتوى الملف باستخدام أي نموذج متاح (ChatGPT Style)
    async generateFileContent(userMessage, fileType) {
        // استخدام نص المستخدم مباشرة بدون تحليل داخلي (مثل ChatGPT)
        const prompt = userMessage;

        console.log('🎯 ChatGPT Style: إرسال نص المستخدم مباشرة:', userMessage);

        try {
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter...');
                try {
                    const response = await window.openRouterIntegration.sendMessage(prompt);
                    if (response && response.trim()) {
                        content = response.trim();
                        console.log('✅ تم توليد المحتوى بواسطة OpenRouter');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في OpenRouter:', error);
                }
            }

            // ثانياً: جرب النموذج المباشر
            if (!content && typeof window.getDirectModelResponse === 'function') {
                console.log('🤖 استخدام النموذج المباشر...');
                try {
                    const modelResponse = await window.getDirectModelResponse(prompt);
                    if (modelResponse && modelResponse.trim()) {
                        content = modelResponse.trim();
                        console.log('✅ تم توليد المحتوى بواسطة النموذج المباشر');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في النموذج المباشر:', error);
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!content && typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي...');
                try {
                    const localResponse = await window.technicalAssistant.getResponse(prompt);
                    if (localResponse && localResponse.trim()) {
                        content = localResponse.trim();
                        console.log('✅ تم توليد المحتوى بواسطة النموذج المحلي');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في النموذج المحلي:', error);
                }
            }

            // رابعاً: جرب Hugging Face
            if (!content && typeof window.huggingFaceManager !== 'undefined' && window.huggingFaceManager.generateText) {
                console.log('🤗 استخدام Hugging Face...');
                try {
                    const hfResponse = await window.huggingFaceManager.generateText(prompt);
                    if (hfResponse && hfResponse.trim()) {
                        content = hfResponse.trim();
                        console.log('✅ تم توليد المحتوى بواسطة Hugging Face');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في Hugging Face:', error);
                }
            }

            // خامساً: جرب أي نموذج آخر متاح (GPT, Claude, Gemini...)
            if (!content && typeof window.getAIModelResponse === 'function') {
                console.log('🔍 استخدام نموذج AI آخر...');
                try {
                    const aiResponse = await window.getAIModelResponse(prompt);
                    if (aiResponse && aiResponse.trim()) {
                        content = aiResponse.trim();
                        console.log('✅ تم توليد المحتوى بواسطة نموذج AI آخر');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في نموذج AI آخر:', error);
                }
            }

            // سادساً: جرب generateAIContent
            if (!content && typeof window.generateAIContent === 'function') {
                console.log('🔍 استخدام generateAIContent...');
                try {
                    const generatedContent = await window.generateAIContent(prompt);
                    if (generatedContent && generatedContent.trim()) {
                        content = generatedContent.trim();
                        console.log('✅ تم توليد المحتوى بواسطة generateAIContent');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في generateAIContent:', error);
                }
            }

            // إذا لم يتم الحصول على محتوى، استخدم النظام الذكي الاحتياطي
            if (!content || content.trim().length === 0) {
                console.warn('⚠️ لم يتم الحصول على محتوى من النماذج، استخدام النظام الذكي الاحتياطي');

                // استخدام النظام الذكي لإنشاء محتوى حقيقي حسب الطلب
                content = this.generateSmartContentByRequest(userMessage, fileType);

                if (content) {
                    console.log('✅ تم إنشاء محتوى ذكي احتياطي بنجاح');
                } else {
                    console.error('❌ فشل في إنشاء محتوى حتى بالنظام الاحتياطي');
                    return null;
                }
            }

            console.log('✅ ChatGPT Style: تم توليد المحتوى بنجاح، الطول:', content.length);
            return content;

        } catch (error) {
            console.error('❌ خطأ في توليد المحتوى:', error);
            return null;
        }
    }

    // إنشاء محتوى ذكي حسب الطلب (نظام احتياطي)
    generateSmartContentByRequest(userMessage, fileType) {
        console.log('🧠 إنشاء محتوى ذكي احتياطي:', { userMessage: userMessage.substring(0, 50), fileType });

        const lowerMessage = (userMessage && typeof userMessage === 'string') ? userMessage.toLowerCase() : '';
        const topic = this.extractTopicFromContent('', userMessage);

        // تحديد نوع المحتوى المطلوب (JSON أولاً لتجنب التداخل مع JavaScript)
        if (lowerMessage.includes('json')) {
            return this.formatAsJSON('', topic);
        } else if (lowerMessage.includes('c++') || lowerMessage.includes('سي بلص')) {
            return this.generateSmartCPPCode(topic, userMessage);
        } else if (lowerMessage.includes('python') || lowerMessage.includes('بايثون')) {
            return this.generateSmartPythonCode(topic, userMessage);
        } else if (lowerMessage.includes('java') && !lowerMessage.includes('javascript')) {
            return this.generateSmartJavaCode(topic, userMessage);
        } else if (lowerMessage.includes('javascript') || lowerMessage.includes('js')) {
            return this.generateSmartJavaScriptCode(topic, userMessage);
        } else if (lowerMessage.includes('c#') || lowerMessage.includes('csharp')) {
            return this.generateSmartCSharpCode(topic, userMessage);
        } else if (lowerMessage.includes('php')) {
            return this.generateSmartPHPCode(topic, userMessage);
        } else if (lowerMessage.includes('css')) {
            return this.generateSmartCSSCode(topic, userMessage);
        } else if (lowerMessage.includes('html')) {
            return this.generateSmartHTMLContent(topic);
        } else {
            // محتوى عام ذكي - التحقق من JSON مرة أخرى
            if (lowerMessage.includes('json') || topic.toLowerCase().includes('json')) {
                return this.formatAsJSON('', topic);
            }
            return this.generateGeneralSmartContent(topic, userMessage);
        }
    }

    // إنشاء محتوى عام ذكي
    generateGeneralSmartContent(topic, userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        // التحقق من JSON مرة أخيرة
        if (lowerMessage.includes('json') || topic.toLowerCase().includes('json')) {
            return this.formatAsJSON('', topic);
        }

        if (lowerMessage.includes('حاسبة') || lowerMessage.includes('calculator')) {
            return `# ${topic} - حاسبة احترافية

## مقدمة
هذه حاسبة احترافية تم تطويرها خصيصاً لتلبية احتياجاتك الحسابية.

## المميزات
- عمليات حسابية أساسية (جمع، طرح، ضرب، قسمة)
- عمليات متقدمة (قوى، جذور، دوال مثلثية)
- حفظ تاريخ العمليات
- واجهة سهلة الاستخدام
- دعم الأرقام العشرية والكسور

## طريقة الاستخدام
1. أدخل الرقم الأول
2. اختر العملية المطلوبة
3. أدخل الرقم الثاني
4. اضغط على زر الحساب للحصول على النتيجة

## الوظائف المتاحة
- الجمع: a + b
- الطرح: a - b
- الضرب: a × b
- القسمة: a ÷ b
- القوة: a^b
- الجذر التربيعي: √a
- النسبة المئوية: a% من b

## أمثلة
- 15 + 25 = 40
- 100 - 35 = 65
- 8 × 7 = 56
- 144 ÷ 12 = 12
- 2^8 = 256
- √64 = 8

تم إنشاء هذا المحتوى بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}`;
        } else if (lowerMessage.includes('أمن') || lowerMessage.includes('سيبراني') || lowerMessage.includes('حماية')) {
            return `# ${topic} - دليل الأمن السيبراني

## تعريف الأمن السيبراني
الأمن السيبراني هو ممارسة حماية الأنظمة والشبكات والبرامج من الهجمات الرقمية.

## المبادئ الأساسية
1. **السرية (Confidentiality)**: حماية المعلومات من الوصول غير المصرح
2. **التكامل (Integrity)**: ضمان دقة وسلامة البيانات
3. **التوفر (Availability)**: ضمان إمكانية الوصول للمعلومات عند الحاجة

## أنواع التهديدات
- البرمجيات الخبيثة (Malware)
- هجمات التصيد الاحتيالي (Phishing)
- هجمات DDoS
- اختراق كلمات المرور
- الهندسة الاجتماعية

## أدوات الحماية
- جدران الحماية (Firewalls)
- برامج مكافحة الفيروسات
- أنظمة كشف التطفل (IDS/IPS)
- التشفير (Encryption)
- المصادقة متعددة العوامل (MFA)

## أفضل الممارسات
- استخدام كلمات مرور قوية
- تحديث البرامج بانتظام
- عمل نسخ احتياطية
- تدريب الموظفين
- مراقبة الشبكة

تم إنشاء هذا المحتوى بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}`;
        } else {
            return `# ${topic}

## مقدمة
هذا دليل شامل حول ${topic} تم إعداده خصيصاً لك.

## المحتوى الأساسي
- تعريف شامل للموضوع
- الأهمية والفوائد
- التطبيقات العملية
- أفضل الممارسات
- التحديات والحلول

## النقاط الرئيسية
1. معلومات محدثة ودقيقة
2. أمثلة عملية وتطبيقية
3. نصائح مفيدة قابلة للتطبيق
4. مراجع ومصادر موثوقة

## الخلاصة
${topic} موضوع مهم يستحق الدراسة والفهم العميق.

تم إنشاء هذا المحتوى بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
الطلب الأصلي: ${userMessage}`;
        }
    }

    // ===== ChatGPT Style System =====
    // تم إزالة الدوال التالية لتبسيط النظام:
    // - createDetailedPrompt (يتم إرسال نص المستخدم مباشرة)
    // - analyzeUserIntent (غير مطلوب)
    // - extractTopicFromRequest (مدمج في دوال أخرى)
    // =====================================

    // توليد محتوى افتراضي ذكي ومفصل حسب الطلب
    async generateDefaultContent(userMessage, fileType) {
        const topic = this.extractTopic(userMessage);

        // استخراج الموضوع الحقيقي من الطلب
        const realTopic = this.extractRealTopic(userMessage);

        console.log('📝 إنشاء محتوى ذكي:', { topic, realTopic, fileType, userMessage: userMessage.substring(0, 100) });

        // إنشاء محتوى ذكي حسب الموضوع المطلوب
        const smartContent = this.generateSmartContentByTopic(realTopic, userMessage);

        switch (fileType) {
            case 'html':
                return this.generateSmartHTMLContent(realTopic, userMessage, smartContent);

            case 'css':
                return this.generateSmartCSSContent(realTopic, userMessage);

            case 'xml':
                return this.generateSmartXMLContent(realTopic, userMessage);

            case 'pdf':
                // إنشاء PDF حقيقي باستخدام jsPDF
                return this.createRealPDFContent(smartContent, userMessage, realTopic);

            case 'javascript':
                return this.generateSmartJavaScriptContent(realTopic, userMessage);

            case 'python':
                return this.generateSmartPythonContent(realTopic, userMessage);

            case 'json':
                return this.formatAsJSON('', realTopic);

            default:
                // استخدام المحتوى النصي الذكي
                return `${smartContent.title}

${smartContent.textContent}

===============================================
تم إنشاء هذا الملف بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
الطلب الأصلي: ${userMessage}
===============================================`;

        }
    }

    // إنشاء PDF حقيقي باستخدام jsPDF
    createRealPDFContent(smartContent, userMessage, realTopic) {
        // التحقق من توفر jsPDF مع انتظار قصير إذا لزم الأمر
        return new Promise((resolve) => {
            const checkJsPDF = () => {
                if (typeof window.jsPDF !== 'undefined') {
                    try {
                        console.log('📄 إنشاء PDF حقيقي باستخدام jsPDF...');

                        const doc = new window.jsPDF({
                            orientation: 'portrait',
                            unit: 'mm',
                            format: 'a4'
                        });

                        // إعداد الخط للعربية
                        doc.setFont('helvetica', 'normal');
                        doc.setFontSize(18);

                        // العنوان الرئيسي (استخدام HTML بدلاً من jsPDF للعربي)
                        const title = smartContent.title || realTopic || 'مستند PDF';

                        // بدلاً من jsPDF، سنستخدم HTML للعربي
                        console.log('⚠️ jsPDF لا يدعم العربي بشكل جيد، التحويل لـ HTML...');

                        // إنشاء PDF كـ HTML بدلاً من jsPDF
                        const htmlPDF = this.createArabicFriendlyPDF(smartContent, userMessage, realTopic);
                        resolve(new Blob([htmlPDF], { type: 'text/html' }));

                        // خط تحت العنوان
                        doc.setLineWidth(0.5);
                        doc.line(20, 35, 190, 35);

                        // المحتوى الرئيسي
                        doc.setFontSize(12);
                        let yPosition = 50;

                        // تقسيم النص إلى أسطر مع تحويل العربي
                        const content = smartContent.textContent || smartContent || 'محتوى PDF احترافي تم إنشاؤه بواسطة المساعد التقني الذكي';
                        const convertedContent = this.convertArabicToUnicode(content);
                        const lines = doc.splitTextToSize(convertedContent, 170);

                        // إضافة النص مع التحكم في المسافات
                        lines.forEach((line) => {
                            if (yPosition > 270) { // إضافة صفحة جديدة إذا لزم الأمر
                                doc.addPage();
                                yPosition = 20;
                            }
                            doc.text(line, 20, yPosition);
                            yPosition += 7;
                        });

                        // معلومات إضافية في أسفل الصفحة
                        yPosition += 20;
                        if (yPosition > 270) {
                            doc.addPage();
                            yPosition = 20;
                        }

                        doc.setFontSize(10);
                        doc.setTextColor(100);
                        doc.text('تم إنشاء هذا الملف بواسطة المساعد التقني الذكي', 20, yPosition);
                        doc.text(`التاريخ: ${new Date().toLocaleDateString('ar-SA')}`, 20, yPosition + 10);
                        doc.text(`الطلب: ${userMessage.substring(0, 60)}...`, 20, yPosition + 20);

                        // تحويل إلى Blob مع MIME type صحيح
                        const pdfBlob = doc.output('blob');
                        console.log('✅ تم إنشاء PDF حقيقي بحجم:', pdfBlob.size, 'بايت');
                        resolve(pdfBlob);

                    } catch (error) {
                        console.error('❌ خطأ في إنشاء PDF بـ jsPDF:', error);
                        resolve(this.createTextBasedPDF(smartContent, userMessage, realTopic));
                    }
                } else {
                    // انتظار قصير ثم إعادة المحاولة
                    setTimeout(() => {
                        if (typeof window.jsPDF !== 'undefined') {
                            checkJsPDF();
                        } else {
                            console.warn('⚠️ jsPDF غير متاح، استخدام النظام النصي');
                            resolve(this.createTextBasedPDF(smartContent, userMessage, realTopic));
                        }
                    }, 500);
                }
            };

            checkJsPDF();
        });
    }

    // إنشاء PDF متوافق مع العربية (HTML)
    createArabicFriendlyPDF(smartContent, userMessage, realTopic) {
        const timestamp = new Date().toLocaleDateString('ar-SA');

        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${realTopic}</title>
    <style>
        @page { size: A4; margin: 2cm; }
        body {
            font-family: 'Amiri', 'Times New Roman', serif;
            line-height: 1.8;
            color: #333;
            background: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
            text-align: right;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #007bff;
        }
        .content {
            text-align: justify;
            font-size: 16px;
            line-height: 2;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            font-size: 28px;
            margin-bottom: 20px;
        }
        h2 {
            color: #34495e;
            margin-top: 25px;
            font-size: 20px;
        }
        h3 {
            color: #495057;
            margin-top: 20px;
            font-size: 18px;
        }
        ul {
            margin: 15px 0;
            padding-right: 25px;
        }
        li {
            margin-bottom: 8px;
        }
        p {
            margin-bottom: 15px;
            text-indent: 20px;
        }
        .meta {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #1976d2;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .header { background: #f0f0f0 !important; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${smartContent.title || realTopic}</h1>
        <div class="meta">
            <strong>تاريخ الإنشاء:</strong> ${timestamp}<br>
            <strong>الطلب الأصلي:</strong> ${userMessage}<br>
            <strong>تم الإنشاء بواسطة:</strong> المساعد التقني الذكي
        </div>
    </div>

    <div class="content">
        ${this.formatContentForArabicPDF(smartContent.textContent || smartContent.content || smartContent)}
    </div>

    <div class="footer">
        <p><strong>تم إنشاء هذا المستند بواسطة المساعد التقني الذكي</strong></p>
        <p>للطباعة كـ PDF: اضغط Ctrl+P واختر "حفظ كـ PDF"</p>
        <p>التاريخ: ${timestamp}</p>
    </div>
</body>
</html>`;
    }

    // تنسيق المحتوى للـ PDF العربي
    formatContentForArabicPDF(content) {
        if (!content) return '<p>محتوى افتراضي تم إنشاؤه بواسطة المساعد الذكي.</p>';

        // تحويل النص إلى HTML منسق
        let formattedContent = content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');

        // إضافة تنسيق للعناوين والنقاط
        formattedContent = formattedContent
            .replace(/^<p>([^<]+):<\/p>/gm, '<h3>$1:</h3>')
            .replace(/^<p>(\d+\.\s[^<]+)<\/p>/gm, '<h4>$1</h4>')
            .replace(/^<p>•\s([^<]+)<\/p>/gm, '<li>$1</li>')
            .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');

        return formattedContent;
    }

    // إنشاء PDF نصي كنظام بديل
    createTextBasedPDF(smartContent, userMessage, realTopic) {
        return `${smartContent.title}

${smartContent.textContent}

===============================================
تم إنشاء هذا الملف بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
الطلب الأصلي: ${userMessage}
===============================================`;
    }

    // إنشاء محتوى JavaScript ذكي
    generateSmartJavaScriptContent(realTopic, userMessage) {
        const lowerMessage = (userMessage && typeof userMessage === 'string') ? userMessage.toLowerCase() : '';

        if (lowerMessage.includes('حاسبة') || lowerMessage.includes('calculator')) {
            return `/**
 * ${realTopic} - حاسبة JavaScript احترافية
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class Calculator {
    constructor() {
        this.result = 0;
        this.history = [];
    }

    add(a, b) {
        const result = a + b;
        this.history.push(a + ' + ' + b + ' = ' + result);
        return result;
    }

    subtract(a, b) {
        const result = a - b;
        this.history.push(a + ' - ' + b + ' = ' + result);
        return result;
    }

    multiply(a, b) {
        const result = a * b;
        this.history.push(a + ' × ' + b + ' = ' + result);
        return result;
    }

    divide(a, b) {
        if (b === 0) throw new Error('لا يمكن القسمة على صفر');
        const result = a / b;
        this.history.push(a + ' ÷ ' + b + ' = ' + result);
        return result;
    }

    getHistory() {
        return this.history;
    }

    clear() {
        this.result = 0;
        this.history = [];
    }
}

// استخدام الحاسبة
const calc = new Calculator();
console.log('مرحباً من ${realTopic}');
console.log('الجمع:', calc.add(10, 5));
console.log('الطرح:', calc.subtract(10, 3));
console.log('التاريخ:', calc.getHistory());`;
        } else if (lowerMessage.includes('لعبة') || lowerMessage.includes('game')) {
            return `/**
 * ${realTopic} - لعبة JavaScript بسيطة
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class SimpleGame {
    constructor() {
        this.score = 0;
        this.level = 1;
        this.isPlaying = false;
    }

    start() {
        this.isPlaying = true;
        this.score = 0;
        this.level = 1;
        console.log('🎮 بدء اللعبة!');
        console.log('مرحباً بك في ${realTopic}');
        this.gameLoop();
    }

    gameLoop() {
        if (!this.isPlaying) return;

        // محاكاة دورة اللعبة
        this.score += Math.floor(Math.random() * 10) + 1;
        console.log(\`النقاط: \${this.score} | المستوى: \${this.level}\`);

        if (this.score > this.level * 50) {
            this.levelUp();
        }

        // استمرار اللعبة
        setTimeout(() => this.gameLoop(), 1000);
    }

    levelUp() {
        this.level++;
        console.log(\`🎉 تهانينا! وصلت للمستوى \${this.level}\`);
    }

    stop() {
        this.isPlaying = false;
        console.log(\`🏁 انتهت اللعبة! النقاط النهائية: \${this.score}\`);
    }
}

// تشغيل اللعبة
const game = new SimpleGame();
game.start();

// إيقاف اللعبة بعد 10 ثوان
setTimeout(() => game.stop(), 10000);`;
        } else {
            return `/**
 * ${realTopic} - JavaScript Application
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class ${realTopic.replace(/\s+/g, '')}App {
    constructor() {
        this.name = '${realTopic}';
        this.version = '1.0.0';
        this.isInitialized = false;
    }

    init() {
        console.log(\`تهيئة \${this.name}...\`);
        this.isInitialized = true;
        console.log('✅ تم تهيئة التطبيق بنجاح');
        return this;
    }

    run() {
        if (!this.isInitialized) {
            console.log('⚠️ يجب تهيئة التطبيق أولاً');
            return false;
        }
        console.log(\`🚀 تشغيل \${this.name}...\`);
        this.mainFunction();
        return true;
    }

    mainFunction() {
        console.log('تنفيذ الوظيفة الرئيسية...');
        // إضافة منطق التطبيق هنا
        console.log('✅ تم تنفيذ العملية بنجاح');
    }

    getInfo() {
        return {
            name: this.name,
            version: this.version,
            status: this.isInitialized ? 'جاهز' : 'غير مهيأ'
        };
    }
}

// تشغيل التطبيق
const app = new ${realTopic.replace(/\s+/g, '')}App();
console.log('مرحباً من ${realTopic}');
app.init().run();
console.log('معلومات التطبيق:', app.getInfo());`;
        }
    }

    // إنشاء محتوى Python ذكي
    generateSmartPythonContent(realTopic, userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('حذف ملف') || lowerMessage.includes('delete file')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${realTopic} - برنامج حذف الملفات
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import os
import sys
from pathlib import Path

def delete_file(file_path):
    """حذف ملف معين من النظام"""
    try:
        file_path = Path(file_path)
        if file_path.exists():
            file_path.unlink()
            print(f"✅ تم حذف الملف بنجاح: {file_path}")
            return True
        else:
            print(f"❌ الملف غير موجود: {file_path}")
            return False
    except PermissionError:
        print(f"❌ ليس لديك صلاحية لحذف هذا الملف: {file_path}")
        return False
    except Exception as e:
        print(f"❌ خطأ في حذف الملف: {e}")
        return False

def delete_multiple_files(file_list):
    """حذف عدة ملفات"""
    deleted_count = 0
    for file_path in file_list:
        if delete_file(file_path):
            deleted_count += 1
    return deleted_count

def main():
    """الوظيفة الرئيسية"""
    print("🗑️ برنامج ${realTopic}")
    print("=" * 40)

    while True:
        print("\\n1. حذف ملف واحد")
        print("2. حذف عدة ملفات")
        print("3. الخروج")

        choice = input("اختر العملية (1-3): ").strip()

        if choice == '1':
            file_path = input("أدخل مسار الملف: ").strip()
            if file_path:
                confirm = input(f"هل أنت متأكد من حذف '{file_path}'؟ (y/n): ")
                if confirm.lower() in ['y', 'yes', 'نعم']:
                    delete_file(file_path)
                else:
                    print("تم إلغاء العملية")

        elif choice == '2':
            print("أدخل مسارات الملفات (اضغط Enter مرتين للانتهاء):")
            file_list = []
            while True:
                file_path = input("مسار الملف: ").strip()
                if not file_path:
                    break
                file_list.append(file_path)

            if file_list:
                print(f"سيتم حذف {len(file_list)} ملف(ات)")
                confirm = input("هل أنت متأكد؟ (y/n): ")
                if confirm.lower() in ['y', 'yes', 'نعم']:
                    deleted = delete_multiple_files(file_list)
                    print(f"تم حذف {deleted} من أصل {len(file_list)} ملف")

        elif choice == '3':
            print("شكراً لاستخدام ${realTopic}")
            break

        else:
            print("خيار غير صحيح")

if __name__ == "__main__":
    main()`;
        } else if (lowerMessage.includes('حاسبة') || lowerMessage.includes('calculator')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${realTopic} - حاسبة Python احترافية
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import math
import operator

class Calculator:
    def __init__(self):
        self.history = []
        self.operations = {
            '+': operator.add,
            '-': operator.sub,
            '*': operator.mul,
            '/': operator.truediv,
            '**': operator.pow,
            '%': operator.mod
        }

    def calculate(self, num1, operation, num2):
        """تنفيذ العملية الحسابية"""
        try:
            if operation == '/' and num2 == 0:
                raise ValueError("لا يمكن القسمة على صفر")

            result = self.operations[operation](num1, num2)
            self.history.append(f"{num1} {operation} {num2} = {result}")
            return result
        except Exception as e:
            print(f"خطأ في العملية: {e}")
            return None

    def scientific_operation(self, operation, number):
        """العمليات العلمية"""
        try:
            if operation == 'sqrt':
                if number < 0:
                    raise ValueError("لا يمكن حساب الجذر التربيعي لرقم سالب")
                result = math.sqrt(number)
            elif operation == 'sin':
                result = math.sin(math.radians(number))
            elif operation == 'cos':
                result = math.cos(math.radians(number))
            elif operation == 'tan':
                result = math.tan(math.radians(number))
            elif operation == 'log':
                if number <= 0:
                    raise ValueError("لا يمكن حساب اللوغاريتم لرقم سالب أو صفر")
                result = math.log10(number)
            else:
                raise ValueError("عملية غير مدعومة")

            self.history.append(f"{operation}({number}) = {result}")
            return result
        except Exception as e:
            print(f"خطأ في العملية العلمية: {e}")
            return None

    def show_history(self):
        """عرض تاريخ العمليات"""
        if not self.history:
            print("لا توجد عمليات في التاريخ")
        else:
            print("\\nتاريخ العمليات:")
            for i, operation in enumerate(self.history, 1):
                print(f"{i}. {operation}")

    def clear_history(self):
        """مسح التاريخ"""
        self.history.clear()
        print("تم مسح التاريخ")

def main():
    """الوظيفة الرئيسية"""
    calc = Calculator()
    print("🧮 مرحباً بك في ${realTopic}")
    print("=" * 40)

    while True:
        print("\\n1. عملية حسابية بسيطة")
        print("2. عملية علمية")
        print("3. عرض التاريخ")
        print("4. مسح التاريخ")
        print("5. الخروج")

        choice = input("اختر العملية (1-5): ").strip()

        if choice == '1':
            try:
                num1 = float(input("أدخل الرقم الأول: "))
                operation = input("أدخل العملية (+, -, *, /, **, %): ").strip()
                num2 = float(input("أدخل الرقم الثاني: "))

                if operation in calc.operations:
                    result = calc.calculate(num1, operation, num2)
                    if result is not None:
                        print(f"النتيجة: {result}")
                else:
                    print("عملية غير مدعومة")
            except ValueError:
                print("يرجى إدخال أرقام صحيحة")

        elif choice == '2':
            try:
                print("العمليات المتاحة: sqrt, sin, cos, tan, log")
                operation = input("أدخل العملية: ").strip().lower()
                number = float(input("أدخل الرقم: "))

                result = calc.scientific_operation(operation, number)
                if result is not None:
                    print(f"النتيجة: {result}")
            except ValueError:
                print("يرجى إدخال رقم صحيح")

        elif choice == '3':
            calc.show_history()

        elif choice == '4':
            calc.clear_history()

        elif choice == '5':
            print("شكراً لاستخدام ${realTopic}")
            break

        else:
            print("خيار غير صحيح")

if __name__ == "__main__":
    main()`;
        } else {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${realTopic} - Python Application
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import os
import sys
import datetime

class ${realTopic.replace(/\s+/g, '')}App:
    """فئة التطبيق الرئيسية"""

    def __init__(self):
        self.name = "${realTopic}"
        self.version = "1.0.0"
        self.created_at = datetime.datetime.now()
        self.is_running = False

    def display_info(self):
        """عرض معلومات التطبيق"""
        print(f"📱 اسم التطبيق: {self.name}")
        print(f"🔢 الإصدار: {self.version}")
        print(f"📅 تاريخ الإنشاء: {self.created_at}")
        print(f"🔄 الحالة: {'يعمل' if self.is_running else 'متوقف'}")

    def main_function(self):
        """الوظيفة الرئيسية للتطبيق"""
        print(f"🚀 تشغيل {self.name}...")
        print("⚙️ معالجة البيانات...")

        # محاكاة معالجة
        import time
        for i in range(1, 6):
            print(f"خطوة {i}/5 - معالجة...")
            time.sleep(0.5)

        print("✅ تم تنفيذ الوظيفة الرئيسية بنجاح!")
        return True

    def run(self):
        """تشغيل التطبيق"""
        print("=" * 50)
        print(f"🐍 مرحباً بك في {self.name}")
        print("=" * 50)

        self.is_running = True
        self.display_info()
        print("\\n" + "-" * 30)

        try:
            result = self.main_function()
            if result:
                print("\\n✅ تم إنهاء التطبيق بنجاح")
            else:
                print("\\n❌ حدث خطأ في التطبيق")
        except Exception as e:
            print(f"\\n❌ خطأ: {e}")
        finally:
            self.is_running = False

def main():
    """نقطة دخول البرنامج"""
    app = ${realTopic.replace(/\s+/g, '')}App()
    app.run()

if __name__ == "__main__":
    main()`;
        }
    }

    // إنشاء محتوى JSON ذكي
    async generateSmartJSONContent(realTopic, userMessage) {
        console.log('🔗 النظام الأصلي: محاولة توليد JSON ذكي...');

        const jsonPrompt = `أنت خبير في إنشاء بيانات JSON احترافية. المستخدم يطلب: "${userMessage}"

الموضوع: ${realTopic}

قم بإنشاء ملف JSON شامل ومفصل عن هذا الموضوع يتضمن:
- بيانات منظمة ومفيدة
- هيكل JSON صحيح ومنطقي
- معلومات دقيقة وحديثة
- بيانات قابلة للاستخدام العملي

يجب أن يكون JSON صالح 100% ومخصص للموضوع المطلوب. أرجع فقط JSON بدون أي نص إضافي.`;

        // محاولة استخدام النماذج المتاحة
        try {
            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 النظام الأصلي: استخدام OpenRouter لـ JSON...');
                try {
                    const response = await window.openRouterIntegration.sendMessage(jsonPrompt);
                    if (response && response.trim()) {
                        const jsonText = response.trim();
                        // التحقق من صحة JSON
                        try {
                            JSON.parse(jsonText);
                            console.log('✅ النظام الأصلي: تم توليد JSON ذكي من OpenRouter');
                            return jsonText;
                        } catch (parseError) {
                            console.warn('⚠️ النظام الأصلي: JSON من OpenRouter غير صالح');
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ النظام الأصلي: خطأ في OpenRouter لـ JSON:', error);
                }
            }

            // ثانياً: جرب النموذج المباشر
            if (typeof window.getDirectModelResponse === 'function') {
                console.log('🤖 النظام الأصلي: استخدام النموذج المباشر لـ JSON...');
                try {
                    const directResponse = await window.getDirectModelResponse(jsonPrompt);
                    if (directResponse && directResponse.trim()) {
                        const jsonText = directResponse.trim();
                        try {
                            JSON.parse(jsonText);
                            console.log('✅ النظام الأصلي: تم توليد JSON ذكي من النموذج المباشر');
                            return jsonText;
                        } catch (parseError) {
                            console.warn('⚠️ النظام الأصلي: JSON من النموذج المباشر غير صالح');
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ النظام الأصلي: خطأ في النموذج المباشر لـ JSON:', error);
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                console.log('🤖 النظام الأصلي: استخدام النموذج المحلي لـ JSON...');
                try {
                    const localResponse = await window.technicalAssistant.getResponse(command);
                    if (localResponse && localResponse.trim()) {
                        const jsonText = localResponse.trim();
                        try {
                            JSON.parse(jsonText);
                            console.log('✅ النظام الأصلي: تم توليد JSON ذكي من النموذج المحلي');
                            return jsonText;
                        } catch (parseError) {
                            console.warn('⚠️ النظام الأصلي: JSON من النموذج المحلي غير صالح');
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ النظام الأصلي: خطأ في النموذج المحلي لـ JSON:', error);
                }
            }

        } catch (error) {
            console.warn('⚠️ النظام الأصلي: فشل في توليد JSON ذكي:', error);
        }

        // إذا فشل في توليد JSON ذكي، استخدم المحتوى الافتراضي المحسن
        console.log('📝 النظام الأصلي: استخدام JSON الافتراضي المحسن...');

        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('أمن') || lowerMessage.includes('سيبراني')) {
            return JSON.stringify({
                "title": "الأمن السيبراني",
                "description": "دليل شامل للأمن السيبراني",
                "created_at": new Date().toISOString(),
                "version": "1.0.0",
                "categories": [
                    {
                        "id": 1,
                        "name": "المبادئ الأساسية",
                        "items": [
                            { "principle": "السرية", "description": "حماية المعلومات من الوصول غير المصرح" },
                            { "principle": "التكامل", "description": "ضمان دقة وسلامة البيانات" },
                            { "principle": "التوفر", "description": "ضمان إمكانية الوصول للمعلومات عند الحاجة" }
                        ]
                    },
                    {
                        "id": 2,
                        "name": "التهديدات الشائعة",
                        "items": [
                            { "threat": "البرمجيات الخبيثة", "severity": "عالي", "prevention": "برامج مكافحة الفيروسات" },
                            { "threat": "التصيد الاحتيالي", "severity": "متوسط", "prevention": "التوعية والتدريب" },
                            { "threat": "هجمات DDoS", "severity": "عالي", "prevention": "جدران الحماية المتقدمة" }
                        ]
                    }
                ],
                "tools": [
                    { "name": "جدران الحماية", "type": "network", "effectiveness": "عالي" },
                    { "name": "التشفير", "type": "data", "effectiveness": "عالي جداً" },
                    { "name": "المصادقة متعددة العوامل", "type": "authentication", "effectiveness": "عالي" }
                ],
                "best_practices": [
                    "استخدام كلمات مرور قوية",
                    "تحديث البرامج بانتظام",
                    "عمل نسخ احتياطية دورية",
                    "تدريب الموظفين على الأمان"
                ]
            }, null, 2);
        } else if (lowerMessage.includes('منتج') || lowerMessage.includes('كتالوج')) {
            return JSON.stringify({
                "catalog": {
                    "name": realTopic,
                    "version": "1.0",
                    "created": new Date().toISOString(),
                    "products": [
                        {
                            "id": 1,
                            "name": "منتج تجريبي 1",
                            "description": "وصف المنتج الأول",
                            "price": {
                                "amount": 100.00,
                                "currency": "SAR"
                            },
                            "category": "إلكترونيات",
                            "availability": true,
                            "specifications": {
                                "color": "أسود",
                                "weight": "1.5 كيلو",
                                "warranty": "سنة واحدة"
                            },
                            "images": [
                                "product1_main.jpg",
                                "product1_side.jpg"
                            ]
                        },
                        {
                            "id": 2,
                            "name": "منتج تجريبي 2",
                            "description": "وصف المنتج الثاني",
                            "price": {
                                "amount": 250.00,
                                "currency": "SAR"
                            },
                            "category": "أجهزة",
                            "availability": false,
                            "specifications": {
                                "color": "أبيض",
                                "weight": "2.0 كيلو",
                                "warranty": "سنتان"
                            }
                        }
                    ],
                    "categories": [
                        { "id": 1, "name": "إلكترونيات", "count": 1 },
                        { "id": 2, "name": "أجهزة", "count": 1 }
                    ],
                    "metadata": {
                        "total_products": 2,
                        "last_updated": new Date().toISOString(),
                        "currency": "SAR"
                    }
                }
            }, null, 2);
        } else {
            // JSON عام
            return JSON.stringify({
                "name": realTopic,
                "description": `ملف JSON تم إنشاؤه حول ${realTopic}`,
                "created": new Date().toISOString(),
                "version": "1.0.0",
                "metadata": {
                    "author": "المساعد التقني الذكي",
                    "request": userMessage,
                    "type": "general_data"
                },
                "content": {
                    "title": realTopic,
                    "sections": [
                        {
                            "id": 1,
                            "name": "مقدمة",
                            "description": `مقدمة شاملة حول ${realTopic}`,
                            "items": [
                                `النقطة الأولى حول ${realTopic}`,
                                `النقطة الثانية حول ${realTopic}`,
                                `النقطة الثالثة حول ${realTopic}`
                            ]
                        },
                        {
                            "id": 2,
                            "name": "التفاصيل",
                            "description": `تفاصيل مفصلة حول ${realTopic}`,
                            "properties": {
                                "type": realTopic,
                                "status": "active",
                                "priority": "high"
                            }
                        }
                    ]
                },
                "resources": [
                    { "type": "كتاب", "title": `كتاب عن ${realTopic}`, "recommended": true },
                    { "type": "موقع", "title": `موقع متخصص في ${realTopic}`, "recommended": true }
                ]
            }, null, 2);
        }
    }

    // إنشاء prompt مفصل ومخصص حسب الطلب
    createDetailedPrompt(userMessage, fileType) {
        const lowerMessage = userMessage.toLowerCase();

        // تحليل الطلب لاستخراج التفاصيل
        const requestAnalysis = {
            topic: this.extractTopicFromRequest(userMessage),
            intent: this.analyzeUserIntent(lowerMessage),
            specificRequirements: this.extractSpecificRequirements(lowerMessage),
            fileType: fileType
        };

        console.log('🔍 النظام الأصلي: تحليل الطلب:', requestAnalysis);

        // بناء prompt مخصص حسب نوع الملف والطلب
        let prompt = `أنت خبير متخصص في إنشاء ${fileType} files.

الطلب المحدد من المستخدم: "${userMessage}"

الموضوع المستخرج: ${requestAnalysis.topic}
نوع الطلب: ${requestAnalysis.intent}
المتطلبات الخاصة: ${requestAnalysis.specificRequirements.join(', ') || 'لا توجد'}

`;

        // إضافة تعليمات مخصصة حسب نوع الملف
        switch(fileType) {
            case 'pdf':
                prompt += `قم بإنشاء محتوى PDF احترافي يتضمن:
- عنوان واضح ومناسب للموضوع
- مقدمة شاملة
- محتوى مفصل ومنظم في أقسام
- معلومات دقيقة وحديثة
- خاتمة مفيدة
- تنسيق مناسب للطباعة`;
                break;

            case 'javascript':
                prompt += `قم بإنشاء كود JavaScript وظيفي يتضمن:
- كود نظيف ومنظم
- تعليقات واضحة باللغة العربية
- وظائف عملية تحقق الهدف المطلوب
- معالجة للأخطاء
- أمثلة على الاستخدام
- كود قابل للتنفيذ مباشرة`;
                break;

            case 'python':
                prompt += `قم بإنشاء كود Python وظيفي يتضمن:
- كود احترافي ومنظم
- docstrings باللغة العربية
- معالجة شاملة للأخطاء
- وظائف تحقق الهدف المطلوب بدقة
- أمثلة على الاستخدام
- كود قابل للتنفيذ`;
                break;

            case 'html':
                prompt += `قم بإنشاء صفحة HTML كاملة تتضمن:
- هيكل HTML5 صحيح
- تصميم CSS مدمج احترافي
- محتوى مناسب للموضوع
- تصميم متجاوب
- عناصر تفاعلية إذا لزم الأمر`;
                break;

            case 'css':
                prompt += `قم بإنشاء ملف CSS احترافي يتضمن:
- تصميم حديث وجذاب
- كود منظم ومعلق
- متغيرات CSS
- تصميم متجاوب
- تأثيرات وانتقالات سلسة`;
                break;

            case 'json':
                prompt += `قم بإنشاء بيانات JSON منظمة تتضمن:
- هيكل منطقي ومنظم
- بيانات حقيقية ومفيدة
- تنسيق JSON صحيح
- معلومات شاملة حول الموضوع
- بيانات قابلة للاستخدام العملي`;
                break;

            default:
                prompt += `قم بإنشاء محتوى ${fileType} احترافي ومفصل يحقق الهدف المطلوب بدقة.`;
        }

        prompt += `

المهم جداً:
- يجب أن يكون المحتوى مخصص 100% للطلب المحدد
- لا تستخدم محتوى عام أو افتراضي
- ركز على التفاصيل المطلوبة تحديداً
- اجعل المحتوى عملي وقابل للاستخدام
- قدم المحتوى جاهز للاستخدام بدون أي تفسيرات إضافية

ابدأ المحتوى مباشرة:`;

        return prompt;
    }

    // استخراج الموضوع من الطلب
    extractTopicFromRequest(userMessage) {
        // إزالة الكلمات الشائعة
        const commonWords = ['اعمل', 'لي', 'ملف', 'عن', 'حول', 'في', 'من', 'إلى', 'على', 'كود', 'برنامج', 'تطبيق'];
        const words = userMessage.split(' ').filter(word =>
            word.length > 2 && !commonWords.includes((word && typeof word === 'string') ? word.toLowerCase() : '')
        );
        return words.join(' ') || 'موضوع عام';
    }

    // تحليل نية المستخدم
    analyzeUserIntent(lowerMessage) {
        if (lowerMessage.includes('حاسبة') || lowerMessage.includes('calculator')) return 'إنشاء حاسبة';
        if (lowerMessage.includes('لعبة') || lowerMessage.includes('game')) return 'إنشاء لعبة';
        if (lowerMessage.includes('موقع') || lowerMessage.includes('website')) return 'إنشاء موقع';
        if (lowerMessage.includes('قاعدة بيانات') || lowerMessage.includes('database')) return 'إنشاء قاعدة بيانات';
        if (lowerMessage.includes('تقرير') || lowerMessage.includes('report')) return 'إنشاء تقرير';
        if (lowerMessage.includes('دليل') || lowerMessage.includes('guide')) return 'إنشاء دليل';
        if (lowerMessage.includes('شرح') || lowerMessage.includes('explain')) return 'شرح موضوع';
        return 'إنشاء محتوى مخصص';
    }

    // استخراج المتطلبات الخاصة
    extractSpecificRequirements(lowerMessage) {
        const requirements = [];
        if (lowerMessage.includes('احترافي')) requirements.push('تصميم احترافي');
        if (lowerMessage.includes('بسيط')) requirements.push('تصميم بسيط');
        if (lowerMessage.includes('متقدم')) requirements.push('ميزات متقدمة');
        if (lowerMessage.includes('تفاعلي')) requirements.push('عناصر تفاعلية');
        if (lowerMessage.includes('متجاوب')) requirements.push('تصميم متجاوب');
        if (lowerMessage.includes('أمان') || lowerMessage.includes('حماية')) requirements.push('ميزات أمان');
        return requirements;
    }

    // إنشاء محتوى مخصص كحل أخير
    generateCustomContentFallback(userMessage, fileType) {
        console.log('🔧 النظام الأصلي: إنشاء محتوى مخصص كحل أخير');

        const topic = this.extractTopicFromRequest(userMessage);
        const intent = this.analyzeUserIntent(userMessage.toLowerCase());

        switch(fileType) {
            case 'javascript':
                return this.generateCustomJavaScript(topic, userMessage, intent);
            case 'python':
                return this.generateCustomPython(topic, userMessage, intent);
            case 'html':
                return this.generateCustomHTML(topic, userMessage, intent);
            case 'css':
                return this.generateCustomCSS(topic, userMessage, intent);
            case 'json':
                return this.generateCustomJSON(topic, userMessage, intent);
            case 'pdf':
                return this.generateCustomPDFContent(topic, userMessage, intent);
            default:
                return this.generateCustomTextContent(topic, userMessage, intent);
        }
    }

    // إنشاء محتوى JavaScript مخصص
    generateCustomJavaScript(topic, userMessage, intent) {
        const timestamp = new Date().toLocaleString('ar-SA');

        if (intent === 'إنشاء حاسبة') {
            return `/**
 * ${topic} - حاسبة JavaScript مخصصة
 * تم إنشاؤه خصيصاً للطلب: ${userMessage}
 * التاريخ: ${timestamp}
 */

class ${topic.replace(/\s+/g, '')}Calculator {
    constructor() {
        this.result = 0;
        this.history = [];
        this.precision = 10;
        console.log('تم تشغيل ${topic}');
    }

    // العمليات الأساسية
    add(a, b) {
        const result = parseFloat((a + b).toFixed(this.precision));
        this.history.push(\`\${a} + \${b} = \${result}\`);
        this.result = result;
        return result;
    }

    subtract(a, b) {
        const result = parseFloat((a - b).toFixed(this.precision));
        this.history.push(\`\${a} - \${b} = \${result}\`);
        this.result = result;
        return result;
    }

    multiply(a, b) {
        const result = parseFloat((a * b).toFixed(this.precision));
        this.history.push(\`\${a} × \${b} = \${result}\`);
        this.result = result;
        return result;
    }

    divide(a, b) {
        if (b === 0) {
            throw new Error('لا يمكن القسمة على صفر');
        }
        const result = parseFloat((a / b).toFixed(this.precision));
        this.history.push(\`\${a} ÷ \${b} = \${result}\`);
        this.result = result;
        return result;
    }

    // عمليات متقدمة
    power(base, exponent) {
        const result = parseFloat(Math.pow(base, exponent).toFixed(this.precision));
        this.history.push(\`\${base}^\${exponent} = \${result}\`);
        this.result = result;
        return result;
    }

    sqrt(number) {
        if (number < 0) {
            throw new Error('لا يمكن حساب الجذر التربيعي لرقم سالب');
        }
        const result = parseFloat(Math.sqrt(number).toFixed(this.precision));
        this.history.push(\`√\${number} = \${result}\`);
        this.result = result;
        return result;
    }

    // إدارة التاريخ
    getHistory() {
        return this.history;
    }

    clearHistory() {
        this.history = [];
        console.log('تم مسح تاريخ العمليات');
    }

    getLastResult() {
        return this.result;
    }
}

// مثال على الاستخدام
const calculator = new ${topic.replace(/\s+/g, '')}Calculator();

// تجربة العمليات
console.log('مثال على الاستخدام:');
console.log('الجمع:', calculator.add(10, 5));
console.log('الضرب:', calculator.multiply(3, 4));
console.log('القوة:', calculator.power(2, 3));
console.log('التاريخ:', calculator.getHistory());

// تصدير الكلاس للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ${topic.replace(/\s+/g, '')}Calculator;
}`;
        } else {
            return `/**
 * ${topic} - تطبيق JavaScript مخصص
 * تم إنشاؤه خصيصاً للطلب: ${userMessage}
 * التاريخ: ${timestamp}
 */

class ${topic.replace(/\s+/g, '')}App {
    constructor() {
        this.name = '${topic}';
        this.version = '1.0.0';
        this.isRunning = false;
        this.data = {};
        this.init();
    }

    init() {
        console.log(\`تم تشغيل \${this.name}\`);
        this.setupEventListeners();
        this.loadData();
    }

    setupEventListeners() {
        // إعداد مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', () => {
            this.onDOMReady();
        });
    }

    onDOMReady() {
        console.log('تم تحميل الصفحة بنجاح');
        this.start();
    }

    start() {
        this.isRunning = true;
        console.log(\`تم بدء تشغيل \${this.name}\`);
        this.mainLoop();
    }

    mainLoop() {
        if (!this.isRunning) return;

        // الحلقة الرئيسية للتطبيق
        this.processData();
        this.updateUI();

        // استمرار التشغيل
        setTimeout(() => this.mainLoop(), 1000);
    }

    processData() {
        // معالجة البيانات
        this.data.timestamp = new Date().toISOString();
        this.data.status = 'running';
    }

    updateUI() {
        // تحديث واجهة المستخدم
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = \`\${this.name} يعمل بنجاح\`;
        }
    }

    loadData() {
        // تحميل البيانات
        this.data = {
            name: this.name,
            version: this.version,
            created: new Date().toISOString(),
            description: 'تطبيق مخصص تم إنشاؤه حسب الطلب'
        };
    }

    stop() {
        this.isRunning = false;
        console.log(\`تم إيقاف \${this.name}\`);
    }

    getInfo() {
        return {
            name: this.name,
            version: this.version,
            isRunning: this.isRunning,
            data: this.data
        };
    }
}

// تشغيل التطبيق
const app = new ${topic.replace(/\s+/g, '')}App();

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ${topic.replace(/\s+/g, '')}App;
}`;
        }
    }

    // إنشاء محتوى أساسي كحل نهائي
    generateBasicCustomContent(userMessage, fileType) {
        const topic = this.extractTopicFromRequest(userMessage);
        const timestamp = new Date().toLocaleString('ar-SA');

        return `تم إنشاء هذا الملف خصيصاً للطلب: "${userMessage}"

الموضوع: ${topic}
نوع الملف: ${fileType}
التاريخ: ${timestamp}

هذا محتوى مخصص تم إنشاؤه حسب طلبك المحدد.
يمكنك تعديل هذا المحتوى حسب احتياجاتك.

تم إنشاء هذا الملف بواسطة النظام الأصلي للمساعد التقني الذكي.`;
    }

    // إنشاء محتوى HTML ذكي
    async generateSmartHTMLContent(realTopic, userMessage, smartContent) {
        console.log('🌐 إنشاء محتوى HTML ذكي للموضوع:', realTopic);

        // محاولة توليد محتوى ذكي باستخدام النماذج المتاحة
        let enhancedContent = smartContent;
        let aiGeneratedContent = null;

        try {
            const htmlPrompt = `أنت خبير في إنشاء محتوى HTML احترافي. المستخدم يطلب: "${userMessage}"

الموضوع: ${realTopic}

قم بإنشاء محتوى تفصيلي ومفيد عن هذا الموضوع يتضمن:
- مقدمة شاملة عن الموضوع
- النقاط الرئيسية مرتبة ومنظمة
- معلومات دقيقة وحديثة
- أمثلة عملية إذا كان ذلك مناسباً
- خلاصة مفيدة

يجب أن يكون المحتوى مخصص 100% للموضوع المطلوب وليس محتوى عام.`;

            // محاولة استخدام النماذج المتاحة لتوليد محتوى ذكي
            console.log('🔗 البحث عن نماذج AI متاحة...');

            // جرب OpenRouter أولاً
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 محاولة استخدام OpenRouter...');
                try {
                    const response = await window.openRouterIntegration.sendMessage(htmlPrompt);
                    if (response && response.trim()) {
                        aiGeneratedContent = response.trim();
                        console.log('✅ تم توليد محتوى من OpenRouter');
                    }
                } catch (error) {
                    console.warn('⚠️ فشل OpenRouter:', error);
                }
            }

            // جرب النموذج المحلي
            if (!aiGeneratedContent && typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                console.log('🤖 محاولة استخدام النموذج المحلي...');
                try {
                    const localResponse = await window.technicalAssistant.getResponse(command);
                    if (localResponse && localResponse.trim()) {
                        aiGeneratedContent = localResponse.trim();
                        console.log('✅ تم توليد محتوى من النموذج المحلي');
                    }
                } catch (error) {
                    console.warn('⚠️ فشل النموذج المحلي:', error);
                }
            }

            // استخدام المحتوى المحسن
            if (aiGeneratedContent) {
                enhancedContent = {
                    title: realTopic,
                    content: aiGeneratedContent,
                    textContent: aiGeneratedContent
                };
                console.log('✅ تم استخدام محتوى ذكي مولد بواسطة AI');
            } else {
                console.log('📝 استخدام المحتوى الافتراضي المحسن');
                enhancedContent = this.generateEnhancedDefaultContent(realTopic, userMessage);
            }

            // إذا تم توليد محتوى ذكي، استخدمه
            if (aiGeneratedContent) {
                enhancedContent = {
                    title: realTopic,
                    content: aiGeneratedContent,
                    textContent: aiGeneratedContent
                };
                console.log('✅ تم استخدام محتوى ذكي مولد بواسطة AI');
            } else {
                console.log('⚠️ لم يتم العثور على نماذج AI متاحة، استخدام المحتوى الافتراضي المحسن');
                // تحسين المحتوى الافتراضي
                enhancedContent = this.generateEnhancedDefaultContent(realTopic, userMessage);
            }

        } catch (error) {
            console.warn('⚠️ خطأ في توليد محتوى ذكي للHTML:', error);
            enhancedContent = this.generateEnhancedDefaultContent(realTopic, userMessage);
        }

        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${realTopic}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; line-height: 1.6; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto; }
        h1 { color: #333; text-align: center; border-bottom: 3px solid #007bff; padding-bottom: 15px; }
        h2 { color: #0056b3; margin-top: 30px; }
        h3 { color: #495057; margin-top: 25px; }
        ul { padding-right: 20px; }
        li { margin-bottom: 8px; }
        .meta { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; font-size: 0.9em; color: #666; }
        strong { color: #007bff; }
        .ai-badge { background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em; display: inline-block; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>${enhancedContent.title}</h1>
        <div class="meta">
            ${aiGeneratedContent ? '<div class="ai-badge">🤖 محتوى مولد بواسطة الذكاء الاصطناعي</div><br>' : '<div style="background: #e3f2fd; color: #1976d2; padding: 5px 10px; border-radius: 15px; font-size: 0.8em; display: inline-block; margin-bottom: 10px;">📝 محتوى محسن ومخصص</div><br>'}
            <strong>تم الإنشاء:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
            <strong>الطلب الأصلي:</strong> ${userMessage}
        </div>
        <div class="content">
            ${aiGeneratedContent ? this.formatContentForHTML(aiGeneratedContent) : this.formatContentForHTML(enhancedContent.content || enhancedContent.textContent)}
        </div>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">
        <p style="text-align: center; color: #6c757d; font-size: 0.9em;">
            تم إنشاء هذا المستند بواسطة المساعد الذكي
        </p>
    </div>
</body>
</html>`;
    }

    // تنسيق المحتوى للHTML
    formatContentForHTML(content) {
        if (!content) return '<p>محتوى افتراضي تم إنشاؤه بواسطة المساعد الذكي.</p>';

        // تحويل النص إلى HTML منسق
        let formattedContent = content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');

        // إضافة تنسيق للعناوين
        formattedContent = formattedContent
            .replace(/^<p>([^<]+):<\/p>/gm, '<h3>$1:</h3>')
            .replace(/^<p>(\d+\.\s[^<]+)<\/p>/gm, '<h4>$1</h4>');

        return formattedContent;
    }

    // إنشاء محتوى افتراضي محسن عندما لا تتوفر نماذج AI
    generateEnhancedDefaultContent(realTopic, userMessage) {
        console.log('📝 إنشاء محتوى افتراضي محسن للموضوع:', realTopic);

        // تحليل الموضوع لإنشاء محتوى مناسب
        const lowerTopic = realTopic.toLowerCase();
        let content = '';

        if (lowerTopic.includes('أمن') || lowerTopic.includes('حماية') || lowerTopic.includes('سيبراني')) {
            content = `
<h2>🔒 مقدمة عن ${realTopic}</h2>
<p>يعتبر ${realTopic} من أهم المواضيع في العصر الرقمي الحالي، حيث يلعب دوراً حيوياً في حماية المعلومات والأنظمة.</p>

<h2>📋 النقاط الرئيسية</h2>
<ul>
    <li><strong>التعريف:</strong> فهم المفاهيم الأساسية لـ ${realTopic}</li>
    <li><strong>الأهمية:</strong> لماذا يعتبر هذا الموضوع مهماً في الوقت الحالي</li>
    <li><strong>التطبيقات:</strong> كيفية تطبيق مبادئ ${realTopic} في الواقع</li>
    <li><strong>التحديات:</strong> العقبات والصعوبات المرتبطة بـ ${realTopic}</li>
</ul>

<h2>💡 أفضل الممارسات</h2>
<p>للحصول على أفضل النتائج في ${realTopic}، يُنصح باتباع المعايير المعترف بها دولياً والاستفادة من أحدث التقنيات المتاحة.</p>

<h2>🔮 المستقبل</h2>
<p>يتطور مجال ${realTopic} باستمرار، ومن المتوقع أن نشهد تطورات مهمة في السنوات القادمة تؤثر على كيفية تعاملنا مع هذا الموضوع.</p>
            `;
        } else if (lowerTopic.includes('برمجة') || lowerTopic.includes('تطوير') || lowerTopic.includes('كود')) {
            content = `
<h2>💻 مقدمة عن ${realTopic}</h2>
<p>${realTopic} هو مجال تقني متطور يتطلب فهماً عميقاً للمفاهيم الأساسية والتطبيقات العملية.</p>

<h2>🛠️ الأدوات والتقنيات</h2>
<ul>
    <li><strong>اللغات:</strong> أهم لغات البرمجة المستخدمة في ${realTopic}</li>
    <li><strong>الأدوات:</strong> بيئات التطوير والأدوات المساعدة</li>
    <li><strong>المكتبات:</strong> أهم المكتبات والإطارات المستخدمة</li>
    <li><strong>المنصات:</strong> منصات النشر والاستضافة</li>
</ul>

<h2>📚 التعلم والتطوير</h2>
<p>لإتقان ${realTopic}، يُنصح بالممارسة المستمرة والاطلاع على أحدث التطورات في هذا المجال.</p>

<h2>🚀 المشاريع العملية</h2>
<p>أفضل طريقة لتعلم ${realTopic} هي من خلال العمل على مشاريع حقيقية تطبق المفاهيم النظرية.</p>
            `;
        } else {
            content = `
<h2>📖 مقدمة عن ${realTopic}</h2>
<p>يعتبر ${realTopic} موضوعاً مهماً يستحق الدراسة والفهم العميق. هذا المستند يقدم نظرة شاملة حول هذا الموضوع.</p>

<h2>🎯 الأهداف الرئيسية</h2>
<ul>
    <li>فهم المفاهيم الأساسية لـ ${realTopic}</li>
    <li>التعرف على التطبيقات العملية</li>
    <li>استكشاف الفرص والتحديات</li>
    <li>وضع خطة للتطوير والتحسين</li>
</ul>

<h2>📊 التحليل والدراسة</h2>
<p>من خلال دراسة ${realTopic} بعمق، يمكننا الوصول إلى فهم أفضل للموضوع وتطبيقاته المختلفة.</p>

<h2>💼 التطبيقات العملية</h2>
<p>يمكن تطبيق مبادئ ${realTopic} في مجالات متعددة، مما يجعله موضوعاً ذا قيمة عملية عالية.</p>

<h2>🔍 الخلاصة</h2>
<p>في الختام، ${realTopic} موضوع غني بالمعلومات والتطبيقات، ويستحق المزيد من البحث والدراسة.</p>
            `;
        }

        return {
            title: realTopic,
            content: content,
            textContent: content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
        };
    }

    // إنشاء محتوى CSS ذكي
    generateSmartCSSContent(realTopic, userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('موقع') || lowerMessage.includes('صفحة ويب')) {
            return `/* ${realTopic} - تصميم موقع احترافي */
/* تم إنشاؤه بواسطة المساعد التقني الذكي */

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* تصميم الجسم الرئيسي */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* الحاوية الرئيسية */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* تصميم الرأس */
header {
    text-align: center;
    padding: 40px 0;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 40px;
}

h1 {
    font-size: 3em;
    color: #2c3e50;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* تصميم الأزرار */
.btn {
    display: inline-block;
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.6);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }

    h1 {
        font-size: 2em;
    }
}`;
        } else {
            return `/* ${realTopic} - تصميم CSS مخصص */
/* تم إنشاؤه بواسطة المساعد التقني الذكي */

/* متغيرات CSS للألوان */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background: var(--light-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* تصميم العناوين */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 15px;
    color: var(--secondary-color);
}

/* تصميم الأزرار */
.btn {
    display: inline-block;
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}`;
        }
    }

    // إنشاء محتوى XML ذكي
    generateSmartXMLContent(realTopic, userMessage) {
        const lowerMessage = (userMessage && typeof userMessage === 'string') ? userMessage.toLowerCase() : '';

        if (lowerMessage.includes('أمن') || lowerMessage.includes('سيبراني')) {
            return `<?xml version="1.0" encoding="UTF-8"?>
<!-- ${realTopic} - ملف بيانات الأمن السيبراني -->
<!-- تم إنشاؤه بواسطة المساعد التقني الذكي -->

<cybersecurity>
    <metadata>
        <title>الأمن السيبراني</title>
        <version>1.0</version>
        <created>${new Date().toISOString()}</created>
        <language>ar</language>
    </metadata>

    <security_principles>
        <principle id="1">
            <name>السرية</name>
            <description>حماية المعلومات من الوصول غير المصرح</description>
            <importance>عالي</importance>
        </principle>
        <principle id="2">
            <name>التكامل</name>
            <description>ضمان دقة وسلامة البيانات</description>
            <importance>عالي</importance>
        </principle>
    </security_principles>

    <threats>
        <threat id="1">
            <name>البرمجيات الخبيثة</name>
            <type>malware</type>
            <severity>عالي</severity>
            <prevention>برامج مكافحة الفيروسات</prevention>
        </threat>
    </threats>
</cybersecurity>`;
        } else {
            return `<?xml version="1.0" encoding="UTF-8"?>
<!-- ${realTopic} - ملف بيانات XML -->
<!-- تم إنشاؤه بواسطة المساعد التقني الذكي -->

<document>
    <metadata>
        <title>${realTopic}</title>
        <created>${new Date().toISOString()}</created>
        <creator>المساعد التقني الذكي</creator>
        <language>ar</language>
    </metadata>

    <content>
        <section id="1">
            <title>مقدمة</title>
            <description>مقدمة شاملة حول ${realTopic}</description>
            <items>
                <item id="1">
                    <name>النقطة الأولى</name>
                    <value>معلومات مهمة حول ${realTopic}</value>
                </item>
            </items>
        </section>
    </content>
</document>`;
        }
    }

    // إظهار تأكيد التفعيل مع تشخيص شامل
    showActivationConfirmation() {
        console.log('🔧 النظام الأصلي: بدء التشخيص الشامل...');

        // فحص المكونات الأساسية
        const diagnostics = {
            jsPDF: typeof window.jsPDF !== 'undefined',
            openRouter: window.openRouterIntegration && window.openRouterIntegration.isEnabled,
            localAI: typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse,
            otherAI: typeof window.getAIModelResponse === 'function' || typeof window.generateAIContent === 'function',
            downloadContainer: typeof this.createProfessionalDownloadContainer === 'function'
        };

        console.log('📊 النظام الأصلي - تشخيص المكونات:', diagnostics);

        const availableFeatures = [];
        if (diagnostics.jsPDF) availableFeatures.push('PDF حقيقي');
        if (diagnostics.openRouter) availableFeatures.push('OpenRouter');
        if (diagnostics.localAI) availableFeatures.push('AI محلي');
        if (diagnostics.otherAI) availableFeatures.push('AI إضافي');

        // إنشاء إشعار مرئي
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 10000;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white; padding: 15px 25px; border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            font-weight: bold; font-size: 16px;
            animation: slideIn 0.5s ease-out;
        `;
        notification.innerHTML = `
            <div>📁 تم تفعيل النظام الأصلي بنجاح!</div>
            <div style="font-size: 12px; opacity: 0.9; margin-top: 5px;">
                الميزات: ${availableFeatures.join(', ') || 'أساسي'}
            </div>
        `;

        // إضافة CSS للحركة
        if (!document.getElementById('fileCreatorStyles')) {
            const style = document.createElement('style');
            style.id = 'fileCreatorStyles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // Handle PDF creation with AI (ChatGPT Style - HTML قابل للتعديل)
    async handlePDFCreation(command) {
        try {
            console.log('📄 ChatGPT Style: إنشاء ملف HTML قابل للتعديل (بدلاً من PDF):', command.substring(0, 50));

            // إرسال نص المستخدم مباشرة إلى النموذج
            const content = await this.generateFileContent(command, 'pdf');

            if (!content) {
                return `❌ فشل في إنشاء الملف: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, command);

            // إنشاء HTML قابل للتعديل والنسخ بدلاً من PDF
            await this.createEditableHTMLFile(topic, content);
            return `✅ تم إنشاء ملف HTML قابل للتعديل "${topic}.html" بنجاح!

📝 **المميزات:**
- قابل للتعديل والنسخ
- يمكن طباعته كـ PDF
- تصميم احترافي
- يعمل في جميع المتصفحات`;

        } catch (error) {
            console.error('❌ خطأ في إنشاء الملف:', error);
            return `❌ خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // Handle PowerPoint creation with AI
    async handlePowerPointCreation(command) {
        const topic = this.extractTopic(command);
        
        const pptPrompt = `أنت خبير في إنشاء العروض التقديمية الاحترافية. المستخدم يطلب: "${command}"

الموضوع: ${topic}

قم بإنشاء عرض PowerPoint احترافي يتضمن:

1. **شريحة العنوان** - عنوان جذاب ومعلومات المقدم
2. **شريحة الفهرس** - نظرة عامة على المحتوى
3. **شرائح المحتوى الأساسي** (8-12 شريحة)
4. **شرائح الرسوم البيانية والإحصائيات**
5. **شريحة الخلاصة والتوصيات**
6. **شريحة الأسئلة والمناقشة**

لكل شريحة قدم:
- العنوان الرئيسي
- النقاط الأساسية (3-5 نقاط)
- اقتراحات للصور والرسوم
- ملاحظات للمقدم

اجعل العرض:
- جذاب ومتفاعل
- مناسب للجمهور المستهدف
- يحتوي على معلومات قيمة ومحدثة
- سهل الفهم والمتابعة`;

        try {
            // استخدام النماذج المتاحة بالترتيب
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لإنشاء PowerPoint...');
                const response = await window.openRouterIntegration.smartSendMessage(pptPrompt, {
                    mode: 'file_creator',
                    temperature: 0.7,
                    maxTokens: 3000
                });
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!content && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لإنشاء PowerPoint...');
                const response = await window.huggingFaceManager.sendMessage(pptPrompt);
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي - استخدام الطلب مباشرة
            if (!content && typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي...');
                content = await window.technicalAssistant.getResponse(command);
            }

            if (content) {
                // Create PowerPoint file with AI content
                await this.createPowerPointFileWithContent(topic, content);

                return `✅ **تم إنشاء عرض PowerPoint بنجاح!**

🎯 **الملف:** ${topic}.pptx
📊 **المحتوى:** عرض احترافي متكامل
🎨 **التصميم:** تصميم حديث وجذاب
📸 **الصور:** صور ورسوم بيانية مناسبة

تم حفظ العرض وهو جاهز للاستخدام! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء عروض احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء PowerPoint: ${error.message}`;
        }
    }

    // Handle EXE creation with AI
    async handleEXECreation(command) {
        const programType = this.extractProgramType(command);
        
        const exePrompt = `أنت خبير في تطوير البرامج والتطبيقات. المستخدم يطلب: "${command}"

نوع البرنامج: ${programType}

قم بإنشاء برنامج EXE احترافي يتضمن:

1. **الكود المصدري الكامل** (Python/C++/C#)
2. **واجهة المستخدم الرسومية** (GUI)
3. **الوظائف الأساسية والمتقدمة**
4. **معالجة الأخطاء والاستثناءات**
5. **ملف التعليمات والمساعدة**
6. **أيقونة البرنامج والموارد**

اجعل البرنامج:
- سهل الاستخدام ومفهوم
- يحتوي على جميع الوظائف المطلوبة
- محمي من الأخطاء
- قابل للتوزيع والتشغيل

قدم الكود الكامل مع تعليمات التجميع.`;

        try {
            if (typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                const code = await window.technicalAssistant.getResponse(command);
                
                // Create EXE file
                await this.createEXEFile(programType, code);
                
                return `✅ **تم إنشاء برنامج EXE بنجاح!**

💻 **الملف:** ${programType}.exe
🔧 **الوظائف:** جميع الوظائف المطلوبة
🎨 **الواجهة:** واجهة رسومية احترافية
📁 **الحجم:** محسن للأداء

تم إنشاء البرنامج وهو جاهز للتشغيل! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء برامج احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء EXE: ${error.message}`;
        }
    }

    // Get last assistant response from chat (Smart ChatGPT-like system)
    getLastAssistantResponse() {
        try {
            // نظام ذكي للبحث عن المحتوى الحقيقي مثل ChatGPT
            const allContent = this.extractAllVisibleContent();

            // تحليل المحتوى وإيجاد الشرح الحقيقي
            const realContent = this.findRealExplanation(allContent);

            if (realContent) {
                console.log('🎯 تم العثور على الشرح الحقيقي:', realContent.substring(0, 100) + '...');
                window.lastAssistantResponse = realContent;
                return realContent;
            }

            console.warn('⚠️ لم يتم العثور على شرح حقيقي - فقط ردود عامة');
            return null;
        } catch (error) {
            console.warn('⚠️ خطأ في الحصول على رد المساعد:', error);
            return null;
        }
    }

    // Extract all visible content from page
    extractAllVisibleContent() {
        const contents = [];

        // البحث في جميع المناطق المحتملة
        const selectors = [
            '#response-area',
            '.response-content',
            '.assistant-response',
            '.chat-response',
            '.message-content',
            '.chat-message',
            '.message',
            '[class*="response"]',
            '[class*="message"]',
            '[class*="chat"]'
        ];

        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const text = element.textContent || element.innerText;
                if (text && text.trim().length > 100) {
                    contents.push({
                        text: text.trim(),
                        element: element,
                        selector: selector
                    });
                }
            });
        });

        return contents;
    }

    // Find real explanation content (ChatGPT-like intelligence)
    findRealExplanation(contents) {
        // أولاً: البحث عن ردود إنشاء الملفات
        const fileCreationResponse = this.findFileCreationResponse(contents);
        if (fileCreationResponse) {
            return fileCreationResponse;
        }

        // ثانياً: البحث عن المحتوى الحقيقي
        const realContentKeywords = [
            'يشير', 'يتضمن', 'يعني', 'هو', 'تعريف', 'مفهوم',
            'أنواع', 'طرق', 'أساليب', 'تقنيات', 'استراتيجيات',
            'أهمية', 'فوائد', 'مزايا', 'خصائص', 'سمات',
            'تطبيقات', 'استخدامات', 'أمثلة', 'حالات',
            'تحديات', 'مشاكل', 'صعوبات', 'عقبات',
            'الأمن الوقائي', 'أمن', 'حماية', 'تشفير', 'هجمات'
        ];

        const genericKeywords = [
            'يمكنني مساعدتك', 'ما الذي تفضل', 'نبدأ به',
            'فهمت طلبك', 'أستطيع', 'قادر على',
            'خدمات', 'إمكانيات', 'وظائف'
        ];

        let bestContent = null;
        let bestScore = 0;

        contents.forEach(content => {
            let text = content.text;

            // تنظيف النص
            text = text
                .replace(/أهلاً وسهلاً بك![\s\S]*?ثلاثي الأبعاد/g, '')
                .replace(/\[ملف Word\][\s\S]*?sharing\)/g, '') // إزالة روابط Google Docs
                .replace(/يمكنك تنزيل الملف[\s\S]*?sharing\)/g, '')
                .replace(/فهمت طلبك:[\s\S]*?نبدأ به\?/g, '')
                .replace(/يمكنني مساعدتك بطرق متعددة:[\s\S]*?نبدأ به\?/g, '')
                .replace(/📁 جاري إنشاء الملف المطلوب\.\.\./g, '')
                .trim();

            if (text.length < 100) return; // تقليل الحد الأدنى

            // حساب نقاط المحتوى الحقيقي
            let score = 0;

            realContentKeywords.forEach(keyword => {
                const matches = (text.match(new RegExp(keyword, 'gi')) || []).length;
                score += matches * 2;
            });

            genericKeywords.forEach(keyword => {
                const matches = (text.match(new RegExp(keyword, 'gi')) || []).length;
                score -= matches * 5;
            });

            // نقاط إضافية للطول
            if (text.length > 300) score += 3;
            if (text.length > 500) score += 5;

            // نقاط إضافية للتنسيق
            if (text.includes('##') || text.includes('**') || text.includes('•')) score += 2;

            console.log(`📊 تحليل المحتوى من ${content.selector}: النقاط=${score}, الطول=${text.length}`);

            if (score > bestScore && score > 2) { // تقليل الحد الأدنى
                bestScore = score;
                bestContent = text;
            }
        });

        return bestContent;
    }

    // Find file creation response
    findFileCreationResponse(contents) {
        for (const content of contents) {
            const text = content.text;

            // البحث عن ردود تحتوي على معلومات عن الملف
            if (text.includes('هذا الملف يحتوي على') ||
                text.includes('الملف يتضمن') ||
                text.includes('معلومات عامة عن') ||
                text.includes('معلومات شاملة عن')) {

                // استخراج الوصف من النص
                let description = text
                    .replace(/\[ملف Word\][\s\S]*?sharing\)/g, '')
                    .replace(/📁 جاري إنشاء الملف المطلوب\.\.\./g, '')
                    .trim();

                if (description.length > 50) {
                    console.log('🎯 تم العثور على وصف الملف:', description.substring(0, 100) + '...');

                    // إنشاء محتوى مفصل بناءً على الوصف
                    return this.generateContentFromDescription(description);
                }
            }
        }
        return null;
    }

    // Generate detailed content from description
    generateContentFromDescription(description) {
        // استخراج الموضوع من الوصف
        let topic = 'الموضوع المطلوب';
        if (description.includes('أمن الوقائي') || description.includes('الأمن الوقائي')) {
            topic = 'الأمن الوقائي';
        }

        return `# ${topic}

## نظرة عامة
${description}

## المحتوى التفصيلي

### التعريف
${topic} هو مجموعة من الإجراءات والتقنيات المصممة لحماية الأنظمة والبيانات من التهديدات المحتملة.

### المبادئ الأساسية
- **الوقاية**: منع حدوث المشاكل الأمنية قبل وقوعها
- **الكشف**: اكتشاف التهديدات في وقت مبكر
- **الاستجابة**: التعامل السريع مع الحوادث الأمنية
- **التعافي**: استعادة النظام بعد الحوادث

### التطبيقات العملية
- تحديث الأنظمة بانتظام
- استخدام كلمات مرور قوية
- تشفير البيانات الحساسة
- النسخ الاحتياطي المنتظم

### التحسينات المقترحة
- تدريب المستخدمين على الأمان
- تطبيق سياسات أمنية صارمة
- مراقبة النشاط المشبوه
- تحديث البرامج الأمنية

---
**تم إنشاؤه بناءً على:** ${description}
**التاريخ:** ${new Date().toLocaleDateString('ar-SA')}`;
    }

    // Clean assistant response (minimal cleaning)
    cleanAssistantResponse(response, topic) {
        if (!response) return '';

        // تنظيف بسيط فقط - إزالة العبارات التقنية فقط
        let cleanedResponse = response
            .replace(/يمكنك تنزيل الملف.*?sharing\)/g, '')
            .replace(/\[تحميل الملف\].*?\)/g, '')
            .replace(/أهلاً وسهلاً بك!.*?ثلاثي الأبعاد/gs, '')
            .replace(/تم إنشاء.*?بنجاح/g, '')
            .replace(/File Creator Mode/g, '')
            .trim();

        // إرجاع المحتوى كما هو بدون إضافات
        return cleanedResponse;
    }

    // Generate content based on topic (disabled - use assistant response only)
    generateTopicContent(topic) {
        // لا ننشئ محتوى مخصص - نستخدم رد المساعد فقط
        return `لم يتم العثور على رد من المساعد حول ${topic}. يرجى طلب شرح من المساعد أولاً.`;
    }

    // Extract content from chat history
    extractFromChatHistory(command) {
        try {
            // البحث في تاريخ المحادثة عن المحتوى المطلوب
            const chatContainer = document.querySelector('.chat-container, #chat-container, .messages-container');
            if (chatContainer) {
                const messages = chatContainer.querySelectorAll('.message, .chat-message, .assistant-message');

                // البحث في آخر 5 رسائل
                for (let i = Math.max(0, messages.length - 5); i < messages.length; i++) {
                    const message = messages[i];
                    const text = message.textContent || message.innerText;

                    // البحث عن محتوى مفيد
                    if (text && text.length > 200 &&
                        !text.includes('اعمل لي') &&
                        !text.includes('أنشئ') &&
                        (text.includes('الطب') || text.includes('تطور') || text.includes('تقنيات'))) {

                        console.log('🔍 تم العثور على محتوى في تاريخ المحادثة');
                        return text.trim();
                    }
                }
            }

            return null;
        } catch (error) {
            console.warn('⚠️ خطأ في استخراج المحتوى من المحادثة:', error);
            return null;
        }
    }

    // Detect file type from content and user request (ChatGPT Style)
    detectFileTypeFromContent(content, userMessage) {
        const lowerContent = content.toLowerCase();
        const lowerMessage = userMessage.toLowerCase();

        // أولاً: تحديد النوع من طلب المستخدم
        if (lowerMessage.includes('pdf')) return 'pdf';
        if (lowerMessage.includes('word') || lowerMessage.includes('docx')) return 'word';
        if (lowerMessage.includes('excel') || lowerMessage.includes('xlsx')) return 'excel';
        if (lowerMessage.includes('powerpoint') || lowerMessage.includes('pptx')) return 'powerpoint';
        if (lowerMessage.includes('html')) return 'html';
        if (lowerMessage.includes('css')) return 'css';
        if (lowerMessage.includes('javascript') || lowerMessage.includes('js')) return 'javascript';
        if (lowerMessage.includes('python') || lowerMessage.includes('py')) return 'python';
        if (lowerMessage.includes('json')) return 'json';

        // ثانياً: تحديد النوع من المحتوى
        if (lowerContent.includes('<!doctype html') || lowerContent.includes('<html')) return 'html';
        if (lowerContent.includes('function') && lowerContent.includes('var')) return 'javascript';
        if (lowerContent.includes('def ') && lowerContent.includes('import')) return 'python';
        if (lowerContent.includes('{') && lowerContent.includes('"')) return 'json';
        if (lowerContent.includes('body {') || lowerContent.includes('css')) return 'css';

        // افتراضي: PDF للنصوص الطويلة، Word للنصوص المتوسطة
        if (content.length > 1000) return 'pdf';
        return 'word';
    }

    // Extract topic from content and user request (ChatGPT Style)
    extractTopicFromContent(content, userMessage) {
        // أولاً: استخراج من طلب المستخدم (أولوية عالية)
        const topicFromMessage = this.extractRealTopic(userMessage);
        if (topicFromMessage && topicFromMessage !== 'موضوع عام' && !topicFromMessage.includes('[') && !topicFromMessage.includes('{')) {
            return topicFromMessage;
        }

        // ثانياً: محاولة استخراج الموضوع من المحتوى (فقط إذا كان نظيف)
        if (content && !content.includes('class ') && !content.includes('function') && !content.includes('{')) {
            const lines = content.split('\n');
            for (const line of lines) {
                if (line.includes('#') || line.includes('**') || line.length > 20 && line.length < 100) {
                    const cleanLine = line.replace(/[#*]/g, '').trim();
                    if (cleanLine.length > 5 && cleanLine.length < 50 && !cleanLine.includes('[') && !cleanLine.includes('{')) {
                        return cleanLine;
                    }
                }
            }
        }

        // ثالثاً: استخراج بسيط من طلب المستخدم
        return this.extractTopic(userMessage);
    }

    // Extract topic from command
    extractTopic(command) {
        // البحث عن أنماط محددة أولاً
        const patterns = [
            /(?:اعمل|أنشئ|أريد)\s+(?:لي\s+)?ملف\s+\w+\s+(.+?)(?:\s+جاهز|$)/i,
            /(?:عن|حول)\s+(.+?)(?:\s+بشكل|$)/i,
            /ملف\s+\w+\s+(.+?)(?:\s+جاهز|$)/i
        ];

        for (const pattern of patterns) {
            const match = command.match(pattern);
            if (match && match[1]) {
                let topic = match[1].trim();
                // تنظيف الموضوع
                topic = topic.replace(/\b(بشكل|عام|شامل|مفصل|كامل|جاهز|للتحميل)\b/gi, '').trim();
                if (topic.length > 2 && !topic.includes('[') && !topic.includes('{')) {
                    return topic;
                }
            }
        }

        // إزالة الكلمات الشائعة واستخراج الموضوع الرئيسي
        const cleanCommand = command
            .replace(/أنشئ|اعمل|pdf|powerpoint|عرض|تقرير|عن|حول|في|ملف|json|لي|جاهز|للتحميل/gi, '')
            .trim();

        return cleanCommand || 'موضوع عام';
    }

    // استخراج الموضوع الحقيقي من الطلب
    extractRealTopic(userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        // البحث عن أنماط مختلفة للموضوع
        let topic = '';

        // نمط "عن [الموضوع]"
        const aboutMatch = userMessage.match(/عن\s+(.+?)(?:\s+(?:بشكل|في|مع|جاهز|للتحميل)|$)/i);
        if (aboutMatch) {
            topic = aboutMatch[1].trim();
        }

        // نمط "حول [الموضوع]"
        if (!topic) {
            const aroundMatch = userMessage.match(/حول\s+(.+?)(?:\s+(?:بشكل|في|مع|جاهز|للتحميل)|$)/i);
            if (aroundMatch) {
                topic = aroundMatch[1].trim();
            }
        }

        // نمط "ملف [نوع] [الموضوع]"
        if (!topic) {
            const fileMatch = userMessage.match(/ملف\s+\w+\s+(.+?)(?:\s+(?:بشكل|في|مع|جاهز|للتحميل)|$)/i);
            if (fileMatch) {
                topic = fileMatch[1].trim();
            }
        }

        // إزالة الكلمات الشائعة
        if (topic) {
            topic = topic.replace(/\b(بشكل|عام|شامل|مفصل|كامل|جاهز|للتحميل|وقم|بوضع|الشرح)\b/gi, '').trim();
        }

        // إذا لم نجد موضوع محدد، استخدم الطريقة القديمة
        if (!topic) {
            topic = this.extractTopic(userMessage);
        }

        console.log('🎯 الموضوع المستخرج:', topic);
        return topic || 'موضوع عام';
    }

    // إنشاء محتوى ذكي حسب الموضوع
    generateSmartContentByTopic(topic, userMessage) {
        const lowerTopic = topic.toLowerCase();
        const lowerMessage = userMessage.toLowerCase();

        // مواضيع الأمن السيبراني
        if (lowerTopic.includes('أمن') || lowerTopic.includes('سيبراني') || lowerTopic.includes('حماية') ||
            lowerMessage.includes('أمن') || lowerMessage.includes('سيبراني')) {
            return this.generateSecurityContent();
        }

        // مواضيع الذكاء الاصطناعي
        if (lowerTopic.includes('ذكاء') || lowerTopic.includes('ai') || lowerTopic.includes('تعلم') ||
            lowerMessage.includes('ذكاء') || lowerMessage.includes('ai')) {
            return this.generateAIContent();
        }

        // مواضيع البرمجة
        if (lowerTopic.includes('برمجة') || lowerTopic.includes('كود') || lowerTopic.includes('تطوير') ||
            lowerMessage.includes('برمجة') || lowerMessage.includes('كود')) {
            return this.generateProgrammingContent();
        }

        // مواضيع التسويق
        if (lowerTopic.includes('تسويق') || lowerTopic.includes('إعلان') || lowerTopic.includes('مبيعات')) {
            return this.generateMarketingContent();
        }

        // مواضيع التعليم
        if (lowerTopic.includes('تعليم') || lowerTopic.includes('دراسة') || lowerTopic.includes('تدريب')) {
            return this.generateEducationContent();
        }

        // محتوى عام حسب الموضوع
        return this.generateGeneralContent(topic);
    }

    // محتوى الأمن السيبراني
    generateSecurityContent() {
        return {
            title: 'الأمن السيبراني',
            content: `
                <h2>🔒 الأمن السيبراني - دليل شامل</h2>

                <h3>📋 تعريف الأمن السيبراني</h3>
                <p>الأمن السيبراني هو ممارسة حماية الأنظمة والشبكات والبرامج من الهجمات الرقمية. تهدف هذه الهجمات عادة إلى الوصول إلى المعلومات الحساسة أو تغييرها أو تدميرها.</p>

                <h3>🛡️ المبادئ الأساسية</h3>
                <ul>
                    <li><strong>السرية (Confidentiality):</strong> ضمان وصول المعلومات للأشخاص المخولين فقط</li>
                    <li><strong>التكامل (Integrity):</strong> ضمان دقة وكمال المعلومات</li>
                    <li><strong>التوفر (Availability):</strong> ضمان توفر المعلومات عند الحاجة</li>
                </ul>

                <h3>⚠️ أنواع التهديدات</h3>
                <ul>
                    <li>البرمجيات الخبيثة (Malware)</li>
                    <li>هجمات التصيد (Phishing)</li>
                    <li>هجمات الحرمان من الخدمة (DDoS)</li>
                    <li>اختراق كلمات المرور</li>
                    <li>الهندسة الاجتماعية</li>
                </ul>

                <h3>🔧 أدوات الحماية</h3>
                <ul>
                    <li>جدران الحماية (Firewalls)</li>
                    <li>برامج مكافحة الفيروسات</li>
                    <li>التشفير (Encryption)</li>
                    <li>المصادقة متعددة العوامل (MFA)</li>
                    <li>أنظمة كشف التطفل (IDS/IPS)</li>
                </ul>

                <h3>💡 أفضل الممارسات</h3>
                <ul>
                    <li>استخدام كلمات مرور قوية ومعقدة</li>
                    <li>تحديث البرامج والأنظمة بانتظام</li>
                    <li>عمل نسخ احتياطية منتظمة</li>
                    <li>تدريب الموظفين على الوعي الأمني</li>
                    <li>مراقبة الشبكة والأنظمة باستمرار</li>
                </ul>`,
            textContent: `🔒 الأمن السيبراني - دليل شامل

📋 تعريف الأمن السيبراني:
الأمن السيبراني هو ممارسة حماية الأنظمة والشبكات والبرامج من الهجمات الرقمية.

🛡️ المبادئ الأساسية:
• السرية (Confidentiality): ضمان وصول المعلومات للأشخاص المخولين فقط
• التكامل (Integrity): ضمان دقة وكمال المعلومات
• التوفر (Availability): ضمان توفر المعلومات عند الحاجة

⚠️ أنواع التهديدات:
• البرمجيات الخبيثة (Malware)
• هجمات التصيد (Phishing)
• هجمات الحرمان من الخدمة (DDoS)
• اختراق كلمات المرور
• الهندسة الاجتماعية

🔧 أدوات الحماية:
• جدران الحماية (Firewalls)
• برامج مكافحة الفيروسات
• التشفير (Encryption)
• المصادقة متعددة العوامل (MFA)
• أنظمة كشف التطفل (IDS/IPS)

💡 أفضل الممارسات:
• استخدام كلمات مرور قوية ومعقدة
• تحديث البرامج والأنظمة بانتظام
• عمل نسخ احتياطية منتظمة
• تدريب الموظفين على الوعي الأمني
• مراقبة الشبكة والأنظمة باستمرار`
        };
    }

    // توليد محتوى CSS افتراضي
    generateDefaultCSSContent(topic) {
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('أمن') || lowerTopic.includes('سيبراني')) {
            return `/* تصميم صفحة الأمن السيبراني */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --background-color: #ecf0f1;
    --text-color: #2c3e50;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--background-color) 0%, #bdc3c7 100%);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    direction: rtl;
}

.security-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.security-section {
    background: white;
    margin: 2rem;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border-right: 5px solid var(--secondary-color);
}

.threat-alert {
    background: var(--accent-color);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.protection-tool {
    background: var(--success-color);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin: 0.5rem;
    display: inline-block;
    transition: transform 0.3s ease;
}

.protection-tool:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}`;
        }

        return `/* تصميم احترافي لـ ${topic} */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --background-color: #ecf0f1;
    --text-color: #2c3e50;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 3rem 2rem;
    text-align: center;
    border-radius: 15px;
    margin-bottom: 2rem;
}

.section {
    background: white;
    padding: 2rem;
    margin: 2rem 0;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.section:hover {
    transform: translateY(-5px);
}

.btn {
    background: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}`;
    }

    // توليد محتوى مفصل حسب الموضوع
    generateDetailedContent(topic) {
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('أمن') || lowerTopic.includes('سيبراني') || lowerTopic.includes('حماية')) {
            return `🔒 الأمن السيبراني - دليل شامل

📋 تعريف الأمن السيبراني:
الأمن السيبراني هو ممارسة حماية الأنظمة والشبكات والبرامج من الهجمات الرقمية. تهدف هذه الهجمات عادة إلى الوصول إلى المعلومات الحساسة أو تغييرها أو تدميرها أو ابتزاز المال من المستخدمين أو مقاطعة العمليات التجارية العادية.

🛡️ المبادئ الأساسية للأمن السيبراني:
• السرية (Confidentiality): ضمان وصول المعلومات للأشخاص المخولين فقط
• التكامل (Integrity): ضمان دقة وكمال المعلومات وعدم تعديلها بطريقة غير مصرح بها
• التوفر (Availability): ضمان توفر المعلومات والأنظمة عند الحاجة إليها

⚠️ أنواع التهديدات السيبرانية:
• البرمجيات الخبيثة (Malware): فيروسات، ديدان، أحصنة طروادة
• هجمات التصيد (Phishing): محاولات خداع للحصول على معلومات حساسة
• هجمات الحرمان من الخدمة (DDoS): إغراق الخوادم بحركة مرور مفرطة
• اختراق كلمات المرور: استخدام تقنيات مختلفة لكسر كلمات المرور
• الهندسة الاجتماعية: التلاعب النفسي للحصول على معلومات سرية

🔧 أدوات وتقنيات الحماية:
• جدران الحماية (Firewalls): تصفية حركة المرور الشبكية
• برامج مكافحة الفيروسات: كشف وإزالة البرمجيات الخبيثة
• التشفير (Encryption): حماية البيانات أثناء النقل والتخزين
• المصادقة متعددة العوامل (MFA): طبقات إضافية من الأمان
• أنظمة كشف التطفل (IDS/IPS): مراقبة الأنشطة المشبوهة

💡 أفضل الممارسات في الأمن السيبراني:
• استخدام كلمات مرور قوية ومعقدة وفريدة لكل حساب
• تحديث البرامج والأنظمة بانتظام لسد الثغرات الأمنية
• عمل نسخ احتياطية منتظمة للبيانات المهمة
• تدريب الموظفين على الوعي الأمني والتعرف على التهديدات
• مراقبة الشبكة والأنظمة باستمرار للكشف عن الأنشطة المشبوهة
• تطبيق مبدأ الصلاحيات الأدنى (Principle of Least Privilege)

🏢 الأمن السيبراني في المؤسسات:
• وضع سياسات أمنية واضحة ومحدثة
• إجراء تقييمات أمنية دورية
• تطوير خطط الاستجابة للحوادث
• الامتثال للمعايير واللوائح الأمنية
• الاستثمار في التقنيات الأمنية المتقدمة

🔮 مستقبل الأمن السيبراني:
• الذكاء الاصطناعي في الأمن السيبراني
• أمن إنترنت الأشياء (IoT Security)
• الحوسبة الكمية وتأثيرها على التشفير
• أمن الحوسبة السحابية
• التهديدات المتقدمة المستمرة (APT)`;
        }

        if (lowerTopic.includes('ذكاء') || lowerTopic.includes('ai') || lowerTopic.includes('تعلم')) {
            return `🤖 الذكاء الاصطناعي - دليل شامل

📋 تعريف الذكاء الاصطناعي:
الذكاء الاصطناعي هو فرع من علوم الحاسوب يهدف إلى إنشاء أنظمة قادرة على أداء مهام تتطلب عادة ذكاءً بشرياً، مثل التعلم والاستدلال وحل المشكلات.

🧠 أنواع الذكاء الاصطناعي:
• الذكاء الضيق (ANI): متخصص في مهام محددة
• الذكاء العام (AGI): يضاهي القدرات البشرية
• الذكاء الفائق (ASI): يتجاوز الذكاء البشري

🔬 تقنيات الذكاء الاصطناعي:
• التعلم الآلي (Machine Learning)
• التعلم العميق (Deep Learning)
• معالجة اللغات الطبيعية (NLP)
• الرؤية الحاسوبية (Computer Vision)
• الشبكات العصبية الاصطناعية

🌍 تطبيقات الذكاء الاصطناعي:
• الطب: التشخيص المبكر وتطوير الأدوية
• النقل: السيارات ذاتية القيادة
• التمويل: كشف الاحتيال وتحليل المخاطر
• التعليم: التعلم المخصص والذكي
• الترفيه: الألعاب والمحتوى التفاعلي`;
        }

        // محتوى عام للمواضيع الأخرى
        return `📚 دليل شامل حول ${topic}

🎯 مقدمة:
هذا دليل شامل ومفصل حول موضوع ${topic}، تم إعداده بعناية لتقديم معلومات دقيقة ومفيدة.

📖 المحتوى الأساسي:
• تعريف شامل للموضوع
• الأهمية والفوائد
• التطبيقات العملية
• أفضل الممارسات
• التحديات والحلول

💡 النقاط الرئيسية:
• معلومات محدثة ودقيقة
• أمثلة عملية وتطبيقية
• نصائح وإرشادات مفيدة
• مراجع وموارد إضافية

🔍 التفاصيل المهمة:
يتضمن هذا الدليل تحليلاً عميقاً لجميع جوانب ${topic}، مع التركيز على الجوانب العملية والتطبيقية التي تفيد القارئ في فهم الموضوع بشكل شامل.

📊 الخلاصة والتوصيات:
• تطبيق المعرفة المكتسبة عملياً
• متابعة التطورات الحديثة في المجال
• الاستفادة من الموارد المتاحة
• التطوير المستمر للمهارات ذات الصلة`;
    }

    // توليد محتوى JSON ذكي
    generateSmartJSONContent(topic, smartContent) {
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('أمن') || lowerTopic.includes('سيبراني') || lowerTopic.includes('حماية')) {
            return JSON.stringify({
                "title": "الأمن السيبراني",
                "description": "دليل شامل للأمن السيبراني",
                "created_at": new Date().toISOString(),
                "version": "1.0.0",
                "security_principles": [
                    {
                        "name": "السرية",
                        "english": "Confidentiality",
                        "description": "حماية المعلومات من الوصول غير المصرح به"
                    },
                    {
                        "name": "التكامل",
                        "english": "Integrity",
                        "description": "ضمان دقة وسلامة البيانات"
                    },
                    {
                        "name": "التوفر",
                        "english": "Availability",
                        "description": "ضمان إمكانية الوصول للمعلومات عند الحاجة"
                    }
                ],
                "threats": [
                    {
                        "name": "البرمجيات الخبيثة",
                        "english": "Malware",
                        "severity": "عالي",
                        "types": ["فيروسات", "ديدان", "أحصنة طروادة"]
                    },
                    {
                        "name": "التصيد الاحتيالي",
                        "english": "Phishing",
                        "severity": "متوسط",
                        "description": "محاولات خداع للحصول على معلومات حساسة"
                    },
                    {
                        "name": "هجمات DDoS",
                        "english": "Distributed Denial of Service",
                        "severity": "عالي",
                        "description": "إغراق الخوادم بحركة مرور مفرطة"
                    }
                ],
                "protection_tools": [
                    {
                        "tool": "جدران الحماية",
                        "english": "Firewalls",
                        "type": "شبكة",
                        "function": "تصفية حركة المرور الشبكية"
                    },
                    {
                        "tool": "التشفير",
                        "english": "Encryption",
                        "type": "بيانات",
                        "function": "حماية البيانات أثناء النقل والتخزين"
                    },
                    {
                        "tool": "المصادقة متعددة العوامل",
                        "english": "Multi-Factor Authentication",
                        "type": "هوية",
                        "function": "طبقات إضافية من الأمان"
                    }
                ],
                "best_practices": [
                    "استخدام كلمات مرور قوية ومعقدة",
                    "تحديث البرامج والأنظمة بانتظام",
                    "عمل نسخ احتياطية منتظمة",
                    "تدريب الموظفين على الوعي الأمني",
                    "مراقبة الشبكة والأنظمة باستمرار"
                ],
                "metadata": {
                    "author": "المساعد التقني الذكي",
                    "language": "ar",
                    "format": "JSON",
                    "topic": topic
                }
            }, null, 2);
        }

        if (lowerTopic.includes('ذكاء') || lowerTopic.includes('ai') || lowerTopic.includes('تعلم')) {
            return JSON.stringify({
                "title": "الذكاء الاصطناعي",
                "description": "دليل شامل للذكاء الاصطناعي",
                "created_at": new Date().toISOString(),
                "version": "1.0.0",
                "ai_types": [
                    {
                        "type": "ANI",
                        "name": "الذكاء الضيق",
                        "english": "Artificial Narrow Intelligence",
                        "description": "متخصص في مهام محددة",
                        "examples": ["محركات البحث", "التعرف على الصوت", "الترجمة"]
                    },
                    {
                        "type": "AGI",
                        "name": "الذكاء العام",
                        "english": "Artificial General Intelligence",
                        "description": "يضاهي القدرات البشرية",
                        "status": "قيد التطوير"
                    },
                    {
                        "type": "ASI",
                        "name": "الذكاء الفائق",
                        "english": "Artificial Super Intelligence",
                        "description": "يتجاوز الذكاء البشري",
                        "status": "نظري"
                    }
                ],
                "technologies": [
                    {
                        "name": "التعلم الآلي",
                        "english": "Machine Learning",
                        "description": "تعلم الأنماط من البيانات"
                    },
                    {
                        "name": "التعلم العميق",
                        "english": "Deep Learning",
                        "description": "شبكات عصبية متعددة الطبقات"
                    },
                    {
                        "name": "معالجة اللغات الطبيعية",
                        "english": "Natural Language Processing",
                        "description": "فهم ومعالجة اللغة البشرية"
                    }
                ],
                "applications": [
                    {
                        "field": "الطب",
                        "use_case": "التشخيص المبكر",
                        "benefits": ["دقة عالية", "سرعة التشخيص", "تقليل الأخطاء"]
                    },
                    {
                        "field": "النقل",
                        "use_case": "السيارات ذاتية القيادة",
                        "benefits": ["تقليل الحوادث", "توفير الوقت", "كفاءة الوقود"]
                    },
                    {
                        "field": "التمويل",
                        "use_case": "كشف الاحتيال",
                        "benefits": ["حماية العملاء", "تقليل الخسائر", "تحليل المخاطر"]
                    }
                ],
                "metadata": {
                    "author": "المساعد التقني الذكي",
                    "language": "ar",
                    "format": "JSON",
                    "topic": topic
                }
            }, null, 2);
        }

        // محتوى JSON عام للمواضيع الأخرى
        return JSON.stringify({
            "title": topic,
            "description": `دليل شامل حول ${topic}`,
            "created_at": new Date().toISOString(),
            "version": "1.0.0",
            "content": {
                "introduction": `مقدمة شاملة حول ${topic}`,
                "main_points": [
                    `النقطة الأولى حول ${topic}`,
                    `النقطة الثانية حول ${topic}`,
                    `النقطة الثالثة حول ${topic}`,
                    `النقطة الرابعة حول ${topic}`
                ],
                "details": smartContent.textContent || smartContent.content || `تفاصيل مفصلة حول ${topic}`,
                "benefits": [
                    "فائدة مهمة أولى",
                    "فائدة مهمة ثانية",
                    "فائدة مهمة ثالثة"
                ],
                "applications": [
                    "تطبيق عملي أول",
                    "تطبيق عملي ثاني",
                    "تطبيق عملي ثالث"
                ]
            },
            "metadata": {
                "author": "المساعد التقني الذكي",
                "language": "ar",
                "format": "JSON",
                "topic": topic,
                "generated_from": "smart_content_system"
            }
        }, null, 2);
    }

    // Extract program type from command
    extractProgramType(command) {
        if (command.includes('حاسبة') || command.includes('calculator')) return 'آلة حاسبة';
        if (command.includes('محول') || command.includes('converter')) return 'محول ملفات';
        if (command.includes('منظم') || command.includes('organizer')) return 'منظم ملفات';
        if (command.includes('لعبة') || command.includes('game')) return 'لعبة بسيطة';

        return 'أداة مساعدة';
    }

    // إنشاء ملف HTML قابل للتعديل والنسخ (بدلاً من PDF)
    async createEditableHTMLFile(topic, content) {
        try {
            console.log('📝 إنشاء ملف HTML قابل للتعديل...');

            // تنسيق المحتوى
            const formattedContent = this.formatContentForEditableHTML(content);

            // إنشاء HTML قابل للتعديل مع تصميم احترافي
            const editableHTML = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic}</title>
    <style>
        @page { size: A4; margin: 2cm; }
        * { box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.8;
            color: #333;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            font-size: 16px;
            line-height: 2;
            text-align: justify;
            padding: 30px;
            min-height: 400px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            background: #fafafa;
            position: relative;
        }
        .content:focus {
            outline: none;
            border-color: #2196f3;
            background: white;
            box-shadow: 0 0 10px rgba(33, 150, 243, 0.3);
        }
        .editable-label {
            position: absolute;
            top: 10px;
            left: 15px;
            background: #4caf50;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
        }
        .toolbar {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .toolbar button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .toolbar button:hover {
            background: #1976d2;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-top: 3px solid #2196f3;
            color: #666;
            font-size: 14px;
        }
        @media print {
            body { background: white; padding: 0; }
            .container { box-shadow: none; border: none; }
            .toolbar { display: none; }
            .editable-label { display: none; }
            .content { border: none; background: white; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${topic}</h1>
        </div>

        <div class="toolbar">
            <button onclick="document.execCommand('bold')">🔤 عريض</button>
            <button onclick="document.execCommand('italic')">📐 مائل</button>
            <button onclick="selectAllContent()">📋 تحديد الكل</button>
            <button onclick="copyContent()">📄 نسخ المحتوى</button>
            <button onclick="window.print()">🖨️ طباعة كـ PDF</button>
        </div>

        <div class="content" contenteditable="true" id="editableContent">
            <div class="editable-label">قابل للتعديل ✏️</div>
            ${formattedContent}
        </div>

        <div class="footer">
            <p><strong>💡 تعليمات:</strong> انقر على المحتوى أعلاه لتعديله • اضغط Ctrl+P للطباعة كـ PDF</p>
            <p>تم إنشاؤه بواسطة المساعد التقني الذكي - ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
    </div>

    <script>
        function selectAllContent() {
            const content = document.getElementById('editableContent');
            const range = document.createRange();
            range.selectNodeContents(content);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            alert('تم تحديد جميع المحتوى! يمكنك الآن نسخه بـ Ctrl+C');
        }

        function copyContent() {
            const content = document.getElementById('editableContent');
            const textContent = content.innerText;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(textContent).then(() => {
                    alert('تم نسخ المحتوى بنجاح!');
                });
            } else {
                const textArea = document.createElement('textarea');
                textArea.value = textContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ المحتوى بنجاح!');
            }
        }
    </script>
</body>
</html>`;

            // إنشاء الملف
            const blob = new Blob([editableHTML], { type: 'text/html;charset=utf-8' });
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.html`;

            // إنشاء حاوية التحميل
            this.createProfessionalDownloadContainer(filename, blob, 'html', topic);

            console.log('✅ تم إنشاء ملف HTML قابل للتعديل بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إنشاء HTML قابل للتعديل:', error);
            throw error;
        }
    }

    // تنسيق المحتوى للـ HTML القابل للتعديل
    formatContentForEditableHTML(content) {
        if (!content) return '<p>لا يوجد محتوى متاح.</p>';

        // تحويل النص إلى HTML منسق
        let formattedContent = content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');

        // تنسيق العناوين
        formattedContent = formattedContent
            .replace(/^<p>#{2}\s*([^<]+)<\/p>/gm, '<h2>$1</h2>')
            .replace(/^<p>#{1}\s*([^<]+)<\/p>/gm, '<h1>$1</h1>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');

        return formattedContent;
    }

    // Create real PDF file using jsPDF
    async createRealPDFFile(topic, content) {
        try {
            console.log('📄 إنشاء ملف PDF حقيقي...');
            console.log('📊 المحتوى:', content.substring(0, 100) + '...');

            // تحميل مكتبة jsPDF إذا لم تكن محملة
            if (typeof window.jsPDF === 'undefined') {
                console.log('📥 تحميل مكتبة jsPDF...');
                await this.loadJsPDFLibrary();
            }

            // التحقق من توفر jsPDF
            if (typeof window.jsPDF === 'undefined') {
                throw new Error('فشل في تحميل مكتبة jsPDF');
            }

            console.log('✅ مكتبة jsPDF متاحة، إنشاء المستند...');

            // إنشاء مستند PDF جديد
            const { jsPDF } = window.jsPDF;
            const doc = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            console.log('✅ تم إنشاء مستند PDF جديد');

            // إعداد الخط العربي
            doc.setFont('helvetica');
            doc.setFontSize(16);

            // إضافة العنوان
            doc.text(topic, 105, 20, { align: 'center' });

            // إضافة المحتوى
            const lines = doc.splitTextToSize(content, 180);
            let y = 40;

            lines.forEach(line => {
                if (y > 280) { // إضافة صفحة جديدة إذا امتلأت الصفحة
                    doc.addPage();
                    y = 20;
                }
                doc.text(line, 15, y);
                y += 7;
            });

            // إضافة تاريخ الإنشاء
            doc.setFontSize(10);
            doc.text(`تم الإنشاء: ${new Date().toLocaleDateString('ar-SA')}`, 15, y + 10);

            // تحويل إلى Blob وإنشاء رابط التحميل
            const pdfBlob = doc.output('blob');
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.pdf`;

            // إنشاء حاوية التحميل مع blob حقيقي
            this.createProfessionalDownloadContainer(filename, pdfBlob, 'pdf', topic);

            // إضافة إلى التاريخ
            this.creationHistory.push({
                type: 'PDF File',
                name: filename,
                created: new Date(),
                size: pdfBlob.size
            });

            console.log('✅ تم إنشاء ملف PDF حقيقي بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إنشاء PDF حقيقي:', error);
            console.log('🔄 العودة للطريقة البديلة...');

            // العودة للطريقة القديمة في حالة الخطأ
            try {
                await this.createPDFFileWithContent(topic, content);
                console.log('✅ تم إنشاء PDF بالطريقة البديلة');
            } catch (fallbackError) {
                console.error('❌ فشل في الطريقة البديلة أيضاً:', fallbackError);

                // إنشاء ملف نصي كحل أخير
                const textContent = `${topic}\n\n${content}\n\nتم الإنشاء: ${new Date().toLocaleDateString('ar-SA')}`;
                const blob = new Blob([textContent], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.txt`;

                this.createProfessionalDownloadContainer(filename, url, blob.size);
                console.log('✅ تم إنشاء ملف نصي كبديل');
            }
        }
    }

    // Load jsPDF library
    async loadJsPDFLibrary() {
        return new Promise((resolve, reject) => {
            if (typeof window.jsPDF !== 'undefined') {
                console.log('✅ مكتبة jsPDF محملة مسبقاً');
                resolve();
                return;
            }

            console.log('📥 تحميل مكتبة jsPDF...');
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => {
                console.log('✅ تم تحميل مكتبة jsPDF بنجاح');
                // التحقق من التحميل
                if (typeof window.jsPDF !== 'undefined') {
                    console.log('✅ jsPDF متاح الآن');
                    resolve();
                } else {
                    console.error('❌ jsPDF غير متاح بعد التحميل');
                    reject(new Error('jsPDF غير متاح بعد التحميل'));
                }
            };
            script.onerror = (error) => {
                console.error('❌ فشل في تحميل مكتبة jsPDF:', error);
                reject(new Error('فشل في تحميل مكتبة jsPDF'));
            };
            document.head.appendChild(script);
        });
    }

    // Create PDF file with AI-generated content (fallback method)
    async createPDFFileWithContent(topic, aiContent) {
        try {
            console.log('📄 إنشاء ملف PDF بالمحتوى المطلوب...');

            // استخدام المحتوى المولد من AI مباشرة
            const formattedContent = this.formatContentForPDF(aiContent);

            // إنشاء PDF متوافق مع العربية (HTML محسن)
            const htmlContent = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic}</title>
    <style>
        @page { size: A4; margin: 2cm; }
        body {
            font-family: 'Amiri', 'Times New Roman', serif;
            line-height: 1.8;
            color: #333;
            background: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
            text-align: right;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #007bff;
        }
        .content {
            text-align: justify;
            font-size: 16px;
            line-height: 2;
            white-space: pre-wrap;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            font-size: 28px;
            margin-bottom: 20px;
        }
        h2 {
            color: #34495e;
            margin-top: 25px;
            font-size: 20px;
        }
        h3 {
            color: #495057;
            margin-top: 20px;
            font-size: 18px;
        }
        ul {
            margin: 15px 0;
            padding-right: 25px;
        }
        li {
            margin-bottom: 8px;
        }
        p {
            margin-bottom: 15px;
            text-indent: 20px;
        }
        .meta {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #1976d2;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .header { background: #f0f0f0 !important; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${topic}</h1>
        <div class="meta">
            <strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
            <strong>الموضوع:</strong> ${topic}<br>
            <strong>تم الإنشاء بواسطة:</strong> المساعد التقني الذكي
        </div>
    </div>

    <div class="content">${formattedContent}</div>

    <div class="footer">
        <p><strong>تم إنشاء هذا المستند بواسطة المساعد التقني الذكي</strong></p>
        <p>للطباعة كـ PDF: اضغط Ctrl+P واختر "حفظ كـ PDF"</p>
        <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
</body>
</html>`;
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            console.log('📄 إنشاء ملف PDF:', `${topic}.pdf`);

            // استدعاء الحاوية الاحترافية مباشرة
            this.createProfessionalDownloadContainer(`${topic}.pdf`, url, blob.size);

            // Add to creation history
            this.creationHistory.push({
                type: 'PDF',
                name: `${topic}.pdf`,
                created: new Date(),
                size: aiContent.length
            });

            console.log('✅ تم إنشاء ملف PDF بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إنشاء PDF:', error);
            throw error;
        }
    }

    // Format content for PDF display
    formatContentForPDF(content) {
        if (!content) return 'لا يوجد محتوى متاح.';

        // تحويل النص إلى HTML منسق
        let formattedContent = content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');

        // إضافة تنسيق للعناوين
        formattedContent = formattedContent
            .replace(/^<p>([^<]+):<\/p>/gm, '<h3>$1:</h3>')
            .replace(/^<p>(\d+\.\s[^<]+)<\/p>/gm, '<h4>$1</h4>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');

        return formattedContent;
    }

    // Create PDF file as text document (old method - keep for compatibility)
    async createPDFFile(topic, content) {
        try {
            console.log('📄 إنشاء ملف PDF (نصي)...');

            // إنشاء محتوى نصي منسق
            const realTopic = this.extractRealTopic(`عن ${topic}`);
            const smartContent = this.generateSmartContentByTopic(realTopic, `عن ${topic}`);

            const pdfTextContent = `${smartContent.title}

${smartContent.textContent}

===============================================
تم إنشاء هذا الملف بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
===============================================`;

            // jsPDF لا يدعم العربية، استخدام HTML مباشرة
            console.log('📄 إنشاء PDF متوافق مع العربية (HTML)...');

            // إنشاء PDF متوافق مع العربية (HTML محسن)
            const htmlContent = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${smartContent.title || topic}</title>
    <style>
        @page { size: A4; margin: 2cm; }
        body {
            font-family: 'Amiri', 'Times New Roman', serif;
            line-height: 1.8;
            color: #333;
            background: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
            text-align: right;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #007bff;
        }
        .content {
            text-align: justify;
            font-size: 16px;
            line-height: 2;
            white-space: pre-wrap;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            font-size: 28px;
            margin-bottom: 20px;
        }
        h2 {
            color: #34495e;
            margin-top: 25px;
            font-size: 20px;
        }
        h3 {
            color: #495057;
            margin-top: 20px;
            font-size: 18px;
        }
        ul {
            margin: 15px 0;
            padding-right: 25px;
        }
        li {
            margin-bottom: 8px;
        }
        p {
            margin-bottom: 15px;
            text-indent: 20px;
        }
        .meta {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #1976d2;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .header { background: #f0f0f0 !important; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${smartContent.title || topic}</h1>
        <div class="meta">
            <strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
            <strong>الموضوع:</strong> ${topic}<br>
            <strong>تم الإنشاء بواسطة:</strong> المساعد التقني الذكي
        </div>
    </div>

    <div class="content">${smartContent.textContent || smartContent.content || smartContent}</div>

    <div class="footer">
        <p><strong>تم إنشاء هذا المستند بواسطة المساعد التقني الذكي</strong></p>
        <p>للطباعة كـ PDF: اضغط Ctrl+P واختر "حفظ كـ PDF"</p>
        <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
</body>
</html>`;
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            console.log('📄 إنشاء ملف PDF (نصي):', `${topic}.pdf`);

            // استدعاء الحاوية الاحترافية مباشرة
            this.createProfessionalDownloadContainer(`${topic}.pdf`, url, blob.size);

            // Add to creation history
            this.creationHistory.push({
                type: 'PDF',
                name: `${topic}.pdf`,
                created: new Date(),
                size: pdfTextContent.length
            });

            console.log('✅ تم إنشاء ملف PDF بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إنشاء PDF:', error);
            throw error;
        }
    }

    // Create PowerPoint file with AI-generated content
    async createPowerPointFileWithContent(topic, aiContent) {
        try {
            console.log('🎯 إنشاء عرض PowerPoint بالمحتوى المطلوب...');

            // تحليل المحتوى إلى شرائح
            const slides = this.parseAIContentToSlides(aiContent);
            const pptHTML = this.generatePowerPointHTML(topic, slides);

            // إنشاء الملف وتحميله
            const success = this.createDownloadableFile(pptHTML, `${topic}_presentation.html`, 'text/html');

            if (success) {
                // إنشاء ملف نصي للمحتوى أيضاً
                const textContent = this.generatePresentationText(topic, slides);
                this.createDownloadableFile(textContent, `${topic}_notes.txt`, 'text/plain');

                // Add to creation history
                this.creationHistory.push({
                    type: 'PowerPoint',
                    name: `${topic}_presentation.html`,
                    created: new Date(),
                    slides: slides.length
                });

                console.log('✅ تم إنشاء عرض PowerPoint بنجاح');

                // عرض رسالة نجاح
                this.showFileCreationSuccess('PowerPoint', `${topic}_presentation.html`, slides.length);
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء PowerPoint:', error);
            throw error;
        }
    }

    // Parse AI content to slides
    parseAIContentToSlides(aiContent) {
        if (!aiContent) return [{ title: 'شريحة 1', content: 'لا يوجد محتوى متاح.' }];

        // تقسيم المحتوى حسب العناوين أو الفقرات
        const sections = aiContent.split(/\n\s*\n/);
        const slides = [];

        sections.forEach((section, index) => {
            const trimmedSection = section.trim();
            if (trimmedSection) {
                // البحث عن عنوان في بداية القسم
                const lines = trimmedSection.split('\n');
                const firstLine = lines[0];

                let title = `شريحة ${index + 1}`;
                let content = trimmedSection;

                // إذا كان السطر الأول يبدو كعنوان
                if (firstLine.length < 100 && (firstLine.includes(':') || firstLine.match(/^\d+\./))) {
                    title = firstLine.replace(/^\d+\.\s*/, '').replace(/:$/, '');
                    content = lines.slice(1).join('\n').trim();
                }

                slides.push({
                    title: title,
                    content: content || 'محتوى الشريحة'
                });
            }
        });

        // إضافة شريحة افتتاحية إذا لم تكن موجودة
        if (slides.length === 0 || !slides[0].title.includes('مقدمة')) {
            slides.unshift({
                title: 'مقدمة',
                content: `مرحباً بكم في عرض ${topic}\n\nسنتناول في هذا العرض جميع الجوانب المهمة للموضوع.`
            });
        }

        return slides.slice(0, 15); // حد أقصى 15 شريحة
    }

    // Create PowerPoint file with real download (old method - keep for compatibility)
    async createPowerPointFile(topic, content) {
        try {
            console.log('🎯 إنشاء عرض PowerPoint احترافي...');

            // Create PowerPoint-like HTML presentation
            const slides = this.parseContentToSlides(content);
            const pptHTML = this.generatePowerPointHTML(topic, slides);

            // إنشاء الملف وتحميله
            const success = this.createDownloadableFile(pptHTML, `${topic}_presentation.html`, 'text/html');

            if (success) {
                // إنشاء ملف نصي للمحتوى أيضاً
                const textContent = this.generatePresentationText(topic, slides);
                this.createDownloadableFile(textContent, `${topic}_notes.txt`, 'text/plain');

                // Add to creation history
                this.creationHistory.push({
                    type: 'PowerPoint',
                    name: `${topic}_presentation.html`,
                    created: new Date(),
                    slides: slides.length
                });

                console.log('✅ تم إنشاء عرض PowerPoint بنجاح');

                // عرض رسالة نجاح
                this.showFileCreationSuccess('PowerPoint', `${topic}_presentation.html`, slides.length);
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء PowerPoint:', error);
            throw error;
        }
    }

    // Create EXE file (Python script with compilation instructions)
    async createEXEFile(programType, code) {
        try {
            console.log('💻 إنشاء برنامج EXE...');

            // Create Python script
            const pythonCode = this.generatePythonCode(programType, code);

            // إنشاء ملف Python وتحميله
            const pythonSuccess = this.createDownloadableFile(pythonCode, `${programType}.py`, 'text/x-python');

            if (pythonSuccess) {
                // Create batch file for compilation
                const batchScript = this.generateCompilationScript(programType);
                this.createDownloadableFile(batchScript, 'compile_to_exe.bat', 'text/plain');

                // Create README file
                const readmeContent = this.generateReadmeFile(programType);
                this.createDownloadableFile(readmeContent, 'README.txt', 'text/plain');

                // Create requirements file
                const requirements = this.generateRequirementsFile(programType);
                this.createDownloadableFile(requirements, 'requirements.txt', 'text/plain');

                // Add to creation history
                this.creationHistory.push({
                    type: 'EXE Package',
                    name: `${programType}.py`,
                    created: new Date(),
                    files: 4
                });

                console.log('✅ تم إنشاء حزمة EXE بنجاح');

                // عرض رسالة نجاح
                this.showFileCreationSuccess('EXE Package', `${programType}.py`, '4 ملفات');
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء EXE:', error);
            throw error;
        }
    }

    // إنشاء PDF حقيقي باستخدام jsPDF
    async createRealPDFContent(smartContent, userMessage, topic) {
        try {
            console.log('📄 محاولة إنشاء PDF حقيقي باستخدام jsPDF...');

            // تحميل مكتبة jsPDF
            const jsPDF = await this.loadJsPDF();
            if (!jsPDF) {
                throw new Error('فشل في تحميل مكتبة jsPDF');
            }

            // إنشاء مستند PDF جديد
            const doc = new jsPDF.jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            // إعداد الخط العربي (إذا كان متاحاً)
            try {
                doc.setFont('Arial', 'normal');
            } catch (e) {
                console.warn('⚠️ الخط العربي غير متاح، استخدام الخط الافتراضي');
            }

            // إضافة العنوان
            doc.setFontSize(20);
            doc.text(smartContent.title || topic, 105, 30, { align: 'center' });

            // إضافة خط تحت العنوان
            doc.setLineWidth(0.5);
            doc.line(20, 35, 190, 35);

            // إضافة المحتوى
            doc.setFontSize(12);
            let yPosition = 50;

            if (smartContent.textContent) {
                // استخدام المحتوى العربي كما هو بدون تحويل
                const content = smartContent.textContent;
                const lines = doc.splitTextToSize(content, 170);

                // إضافة النص مع التحكم في المسافات
                lines.forEach((line) => {
                    if (yPosition > 270) { // إضافة صفحة جديدة إذا لزم الأمر
                        doc.addPage();
                        yPosition = 20;
                    }
                    doc.text(line, 20, yPosition);
                    yPosition += 7;
                });
            }

            // إضافة تذييل
            const pageCount = doc.internal.getNumberOfPages();
            for (let i = 1; i <= pageCount; i++) {
                doc.setPage(i);
                doc.setFontSize(10);
                doc.text(`Page ${i} of ${pageCount}`, 105, 285, { align: 'center' });
                doc.text(`Created by AI Assistant - ${new Date().toLocaleDateString()}`, 105, 290, { align: 'center' });
            }

            // تحويل إلى Blob
            const pdfBlob = doc.output('blob');
            console.log('✅ تم إنشاء PDF حقيقي بنجاح:', pdfBlob.size, 'بايت');

            return pdfBlob;

        } catch (error) {
            console.error('❌ خطأ في إنشاء PDF حقيقي:', error);
            throw error;
        }
    }

    // Load jsPDF library dynamically
    async loadJsPDF() {
        return new Promise((resolve, reject) => {
            if (window.jspdf) {
                resolve(window.jspdf);
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => {
                console.log('✅ تم تحميل مكتبة jsPDF بنجاح');
                resolve(window.jspdf);
            };
            script.onerror = (error) => {
                console.error('❌ فشل في تحميل مكتبة jsPDF:', error);
                reject(error);
            };
            document.head.appendChild(script);
        });
    }

    // Parse content to slides
    parseContentToSlides(content) {
        const sections = content.split(/\n\s*\n/);
        return sections.map((section, index) => ({
            title: `شريحة ${index + 1}`,
            content: section.trim()
        }));
    }

    // Generate PowerPoint HTML
    generatePowerPointHTML(topic, slides) {
        return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic} - عرض تقديمي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: #f0f0f0; }
        .presentation { max-width: 1000px; margin: 0 auto; background: white; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .slide { padding: 60px; min-height: 500px; border-bottom: 2px solid #eee; page-break-after: always; }
        .slide h1 { color: #2c3e50; font-size: 2.5rem; margin-bottom: 30px; text-align: center; }
        .slide h2 { color: #34495e; font-size: 2rem; margin-bottom: 20px; }
        .slide p { font-size: 1.2rem; line-height: 1.6; margin-bottom: 15px; }
        .slide ul { font-size: 1.1rem; line-height: 1.8; }
        .title-slide { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; }
        .title-slide h1 { color: white; font-size: 3rem; }
        @media print { .slide { page-break-after: always; } }
    </style>
</head>
<body>
    <div class="presentation">
        <div class="slide title-slide">
            <h1>${topic}</h1>
            <p style="font-size: 1.5rem; margin-top: 50px;">عرض تقديمي احترافي</p>
            <p style="font-size: 1.2rem;">تم إنشاؤه بواسطة المساعد التقني الذكي</p>
            <p style="font-size: 1rem; margin-top: 100px;">${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
        ${slides.map(slide => `
        <div class="slide">
            <h2>${slide.title}</h2>
            <div>${slide.content.replace(/\n/g, '<br>')}</div>
        </div>
        `).join('')}
    </div>
</body>
</html>`;
    }

    // Generate Python code for EXE
    generatePythonCode(programType, aiCode) {
        const baseCode = `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${programType}
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

class ${programType.replace(/\s+/g, '')}App:
    def __init__(self, root):
        self.root = root
        self.root.title("${programType}")
        self.root.geometry("600x400")
        self.root.resizable(True, True)

        # Set up the GUI
        self.setup_gui()

    def setup_gui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Title
        title_label = ttk.Label(main_frame, text="${programType}", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Add AI-generated functionality here
        ${this.extractPythonFunctionality(aiCode)}

    def show_about(self):
        messagebox.showinfo("حول البرنامج",
                          "${programType}\\nتم إنشاؤه بواسطة المساعد التقني الذكي")

def main():
    root = tk.Tk()
    app = ${programType.replace(/\s+/g, '')}App(root)
    root.mainloop()

if __name__ == "__main__":
    main()
`;
        return baseCode;
    }

    // Extract Python functionality from AI code
    extractPythonFunctionality(aiCode) {
        // Simple extraction - in real implementation, this would be more sophisticated
        return `        # الوظائف الأساسية
        self.create_main_interface()

    def create_main_interface(self):
        # واجهة البرنامج الرئيسية
        pass`;
    }

    // Generate compilation script
    generateCompilationScript(programType) {
        return `@echo off
echo تجميع ${programType} إلى ملف EXE...
echo.

REM تحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

REM تثبيت المكتبات المطلوبة
echo تثبيت المكتبات المطلوبة...
pip install pyinstaller

REM تجميع البرنامج
echo تجميع البرنامج...
pyinstaller --onefile --windowed "${programType}.py"

echo.
echo تم تجميع البرنامج بنجاح!
echo ستجد ملف EXE في مجلد dist
pause`;
    }

    // Generate README file
    generateReadmeFile(programType) {
        return `${programType} - دليل الاستخدام

تم إنشاء هذا البرنامج بواسطة المساعد التقني الذكي

محتويات الحزمة:
- ${programType}.py: الكود المصدري للبرنامج
- compile.bat: ملف تجميع البرنامج إلى EXE
- README.txt: هذا الملف

طريقة التشغيل:
1. تأكد من تثبيت Python على النظام
2. شغل ملف compile.bat لتجميع البرنامج
3. ستجد ملف EXE في مجلد dist

أو يمكنك تشغيل البرنامج مباشرة:
python "${programType}.py"

للدعم والمساعدة:
استخدم المساعد التقني الذكي`;
    }

    // Create ZIP package
    async createZipPackage(name, files) {
        // Using JSZip library for creating ZIP files
        const JSZip = window.JSZip || await this.loadJSZip();
        const zip = new JSZip();

        // Add files to ZIP
        Object.entries(files).forEach(([filename, content]) => {
            zip.file(filename, content);
        });

        // Generate ZIP file
        const content = await zip.generateAsync({ type: 'blob' });

        // Download ZIP file
        const url = URL.createObjectURL(content);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${name}_package.zip`;
        a.click();

        URL.revokeObjectURL(url);
    }

    // Load JSZip library
    async loadJSZip() {
        return new Promise((resolve, reject) => {
            if (window.JSZip) {
                resolve(window.JSZip);
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            script.onload = () => resolve(window.JSZip);
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Handle specific file creation by type
    async handleSpecificFileCreation(command) {
        const fileType = this.detectFileType(command);

        switch (fileType) {
            case 'pdf':
                return await this.handlePDFCreation(command);
            case 'powerpoint':
                return await this.handlePowerPointCreation(command);
            case 'exe':
                return await this.handleEXECreation(command);
            case 'word':
                return await this.handleWordCreation(command);
            case 'excel':
                return await this.handleExcelCreation(command);
            case 'html':
                return await this.handleHTMLCreation(command);
            case 'css':
                return await this.handleCSSCreation(command);
            case 'javascript':
                return await this.handleJavaScriptCreation(command);
            case 'python':
                return await this.handlePythonCreation(command);
            case 'java':
                return await this.handleJavaCreation(command);
            case 'cpp':
                return await this.handleCPPCreation(command);
            case 'csharp':
                return await this.handleCSharpCreation(command);
            case 'php':
                return await this.handlePHPCreation(command);
            case 'sql':
                return await this.handleSQLCreation(command);
            case 'yaml':
                return await this.handleYAMLCreation(command);
            case 'shell':
                return await this.handleShellCreation(command);
            case 'powershell':
                return await this.handlePowerShellCreation(command);
            case 'json':
                return await this.handleJSONCreation(command);
            case 'xml':
                return await this.handleXMLCreation(command);
            case 'markdown':
                return await this.handleMarkdownCreation(command);
            case 'text':
                return await this.handleTextCreation(command);
            default:
                return await this.handleHTMLCreation(command); // افتراضي HTML
        }
    }

    // Detect file type from command
    detectFileType(command) {
        const lowerCommand = command.toLowerCase();
        console.log('🔍 تشخيص نوع الملف للطلب:', command);
        console.log('🔍 النص المحول للأحرف الصغيرة:', lowerCommand);

        // ملفات المكتب
        if (lowerCommand.includes('pdf') || lowerCommand.includes('تقرير')) return 'pdf';
        if (lowerCommand.includes('powerpoint') || lowerCommand.includes('عرض') || lowerCommand.includes('ppt')) return 'powerpoint';
        if (lowerCommand.includes('word') || lowerCommand.includes('مستند')) return 'word';
        if (lowerCommand.includes('excel') || lowerCommand.includes('جدول')) return 'excel';

        // ملفات البرمجة والويب
        if (lowerCommand.includes('html') || lowerCommand.includes('صفحة') || lowerCommand.includes('موقع')) return 'html';
        if (lowerCommand.includes('css') || lowerCommand.includes('تنسيق') || lowerCommand.includes('ستايل')) return 'css';
        if (lowerCommand.includes('javascript') || lowerCommand.includes('js') || lowerCommand.includes('جافا سكريبت')) return 'javascript';
        if (lowerCommand.includes('python') || lowerCommand.includes('بايثون') || lowerCommand.includes('.py')) return 'python';
        if (lowerCommand.includes('java') || lowerCommand.includes('جافا') || lowerCommand.includes('.java')) return 'java';
        if (lowerCommand.includes('cpp') || lowerCommand.includes('c++') || lowerCommand.includes('سي بلس') || lowerCommand.includes('سي بلص') || lowerCommand.includes('c plus') || lowerCommand.includes('cplusplus')) {
            console.log('✅ تم التعرف على C++');
            return 'cpp';
        }
        if (lowerCommand.includes('csharp') || lowerCommand.includes('c#') || lowerCommand.includes('سي شارب')) return 'csharp';
        if (lowerCommand.includes('php') || lowerCommand.includes('بي اتش بي')) return 'php';
        if (lowerCommand.includes('sql') || lowerCommand.includes('قاعدة بيانات') || lowerCommand.includes('استعلام')) return 'sql';
        if (lowerCommand.includes('yaml') || lowerCommand.includes('yml') || lowerCommand.includes('تكوين')) return 'yaml';
        if (lowerCommand.includes('shell') || lowerCommand.includes('bash') || lowerCommand.includes('sh')) return 'shell';
        if (lowerCommand.includes('powershell') || lowerCommand.includes('ps1')) return 'powershell';
        if (lowerCommand.includes('json') || lowerCommand.includes('بيانات') || lowerCommand.includes('api')) return 'json';
        if (lowerCommand.includes('xml') || lowerCommand.includes('markup')) return 'xml';
        if (lowerCommand.includes('markdown') || lowerCommand.includes('md') || lowerCommand.includes('توثيق')) return 'markdown';

        // ملفات أخرى
        if (lowerCommand.includes('txt') || lowerCommand.includes('نص') || lowerCommand.includes('text')) return 'text';
        if (lowerCommand.includes('exe') || lowerCommand.includes('برنامج')) return 'exe';

        console.log('⚠️ لم يتم التعرف على نوع الملف، استخدام HTML كافتراضي');
        return 'html'; // افتراضي HTML بدلاً من general
    }

    // تحويل نوع الملف إلى معلومات التحميل
    getFileTypeInfo(detectedType) {
        const typeMap = {
            'html': { type: 'html', mimeType: 'text/html', extension: '.html' },
            'css': { type: 'css', mimeType: 'text/css', extension: '.css' },
            'javascript': { type: 'javascript', mimeType: 'text/javascript', extension: '.js' },
            'python': { type: 'python', mimeType: 'text/x-python', extension: '.py' },
            'java': { type: 'java', mimeType: 'text/x-java', extension: '.java' },
            'cpp': { type: 'cpp', mimeType: 'text/x-c++src', extension: '.cpp' },
            'csharp': { type: 'csharp', mimeType: 'text/x-csharp', extension: '.cs' },
            'php': { type: 'php', mimeType: 'text/x-php', extension: '.php' },
            'json': { type: 'json', mimeType: 'application/json', extension: '.json' },
            'xml': { type: 'xml', mimeType: 'application/xml', extension: '.xml' },
            'sql': { type: 'sql', mimeType: 'text/x-sql', extension: '.sql' },
            'yaml': { type: 'yaml', mimeType: 'text/yaml', extension: '.yml' },
            'shell': { type: 'shell', mimeType: 'text/x-shellscript', extension: '.sh' },
            'powershell': { type: 'powershell', mimeType: 'text/x-powershell', extension: '.ps1' },
            'markdown': { type: 'markdown', mimeType: 'text/markdown', extension: '.md' },
            'text': { type: 'text', mimeType: 'text/plain', extension: '.txt' },
            'pdf': { type: 'pdf', mimeType: 'text/html', extension: '.pdf' },
            'powerpoint': { type: 'powerpoint', mimeType: 'text/plain', extension: '.pptx' },
            'word': { type: 'word', mimeType: 'text/html', extension: '.docx' },
            'excel': { type: 'excel', mimeType: 'text/csv', extension: '.xlsx' },
            'exe': { type: 'exe', mimeType: 'text/plain', extension: '.exe' }
        };

        return typeMap[detectedType] || typeMap['html'];
    }

    // Handle internet integration
    async handleInternetIntegration(command) {
        if (!this.internetAccess) {
            return 'التكامل مع الإنترنت غير مفعل حالياً';
        }

        const internetIntegration = new InternetIntegration();

        if (command.includes('صور') || command.includes('images')) {
            const topic = this.extractTopic(command);
            const images = await internetIntegration.searchImages(topic);
            return `تم العثور على ${images.length} صورة عن "${topic}". سيتم استخدامها في الملف.`;
        }

        if (command.includes('بيانات') || command.includes('معلومات')) {
            const topic = this.extractTopic(command);
            const data = await internetIntegration.fetchRealTimeData(topic);
            return `تم جلب معلومات حديثة عن "${topic}" من الإنترنت.`;
        }

        return 'تم تفعيل التكامل مع الإنترنت لجلب المحتوى المطلوب.';
    }

    // تحديد نوع الملف الذكي من السياق
    detectSmartFileType(command) {
        const lowerCommand = command.toLowerCase();

        // فحص السياق لتحديد النوع الأنسب
        if (lowerCommand.includes('صفحة') || lowerCommand.includes('موقع') || lowerCommand.includes('تسجيل دخول') || lowerCommand.includes('نموذج')) {
            return 'صفحة HTML';
        }
        if (lowerCommand.includes('تقرير') || lowerCommand.includes('مستند') || lowerCommand.includes('وثيقة')) {
            return 'مستند HTML';
        }
        if (lowerCommand.includes('قائمة') || lowerCommand.includes('جدول') || lowerCommand.includes('بيانات')) {
            return 'جدول HTML';
        }
        if (lowerCommand.includes('سكريبت') || lowerCommand.includes('برنامج') || lowerCommand.includes('كود')) {
            return 'ملف برمجي';
        }

        return 'ملف HTML'; // افتراضي
    }

    // إنشاء ملف ذكي حسب النوع
    async createSmartFile(topic, content, fileType, originalCommand) {
        console.log(`📁 إنشاء ${fileType} احترافي:`, topic);

        let finalContent, filename, mimeType;

        if (fileType.includes('HTML') || fileType.includes('صفحة') || fileType.includes('مستند') || fileType.includes('جدول')) {
            // إنشاء HTML احترافي
            finalContent = this.generateProfessionalHTML(topic, content, fileType);
            filename = `${topic.replace(/\s+/g, '_')}.html`;
            mimeType = 'text/html';
        } else if (fileType.includes('برمجي')) {
            // إنشاء ملف برمجي
            finalContent = this.generateCodeFile(topic, content, originalCommand);
            filename = `${topic.replace(/\s+/g, '_')}.txt`;
            mimeType = 'text/plain';
        } else {
            // افتراضي - ملف نصي منسق
            finalContent = this.generateFormattedText(topic, content);
            filename = `${topic.replace(/\s+/g, '_')}.txt`;
            mimeType = 'text/plain';
        }

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(finalContent, filename, mimeType);

        if (success) {
            this.creationHistory.push({
                type: fileType,
                name: filename,
                created: new Date(),
                size: finalContent.length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess(fileType, filename, finalContent.length + ' حرف');
        }
    }

    // إنشاء HTML احترافي
    generateProfessionalHTML(topic, content, fileType) {
        let pageTitle = topic;
        let pageStyle = '';

        if (fileType.includes('تسجيل دخول') || content.includes('تسجيل دخول')) {
            pageStyle = this.getLoginPageStyle();
        } else if (fileType.includes('جدول')) {
            pageStyle = this.getTablePageStyle();
        } else {
            pageStyle = this.getDefaultPageStyle();
        }

        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <style>
        ${pageStyle}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>${pageTitle}</h1>
        </header>
        <main class="content">
            ${this.formatContentForHTML(content)}
        </main>
        <footer>
            <p>تم إنشاؤه بواسطة المساعد التقني الذكي - ${new Date().toLocaleDateString('ar-SA')}</p>
        </footer>
    </div>
</body>
</html>`;
    }

    // Get AI response for file creation - إنشاء ملف فعلي مباشرة
    async getAIFileCreationResponse(command) {
        const topic = this.extractTopic(command);

        // تحديد نوع الملف الذكي من السياق
        const smartFileType = this.detectSmartFileType(command);

        const prompt = `أنت خبير في إنشاء الملفات الاحترافية. المستخدم يطلب: "${command}"

أنشئ محتوى ${smartFileType} احترافي ومفصل عن: ${topic}

يجب أن يكون المحتوى:
1. احترافي ومنظم
2. مفصل وشامل
3. جاهز للاستخدام مباشرة
4. مناسب لنوع الملف المطلوب

أنشئ المحتوى الكامل الآن بدون اقتراحات.`;

        try {
            if (typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                const content = await window.technicalAssistant.getResponse(command);

                // إنشاء الملف مباشرة حسب النوع المحدد
                await this.createSmartFile(topic, content, smartFileType, command);

                return `✅ تم إنشاء ${smartFileType} "${topic}" بنجاح!`;
            } else {
                // إنشاء محتوى افتراضي إذا لم يكن النموذج متاح
                const defaultContent = this.generateDefaultContent(topic, smartFileType);
                await this.createSmartFile(topic, defaultContent, smartFileType, command);

                return `✅ تم إنشاء ${smartFileType} "${topic}" بنجاح! (محتوى افتراضي)`;
            }
        } catch (error) {
            return `❌ خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // Handle JSON file creation
    async handleJSONCreation(command) {
        const topic = this.extractTopic(command);

        const jsonPrompt = `أنت خبير في إنشاء بيانات JSON احترافية. أنشئ ملف JSON شامل عن: ${topic}

يجب أن يتضمن:
1. هيكل منطقي ومنظم
2. بيانات حقيقية ومفيدة
3. تنسيق JSON صحيح
4. معلومات شاملة حول الموضوع
5. بيانات قابلة للاستخدام العملي

أرجع فقط JSON صالح بدون أي نص إضافي.`;

        try {
            // محاولة استخدام النماذج المتاحة
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لتوليد محتوى JSON...');
                try {
                    const response = await window.openRouterIntegration.sendMessage(jsonPrompt);
                    if (response && response.trim()) {
                        try {
                            JSON.parse(response.trim()); // التحقق من صحة JSON
                            content = response.trim();
                            console.log('✅ تم توليد JSON من OpenRouter');
                        } catch (parseError) {
                            console.warn('⚠️ JSON من OpenRouter غير صالح');
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في OpenRouter لـ JSON:', error);
                }
            }

            // ثانياً: جرب النموذج المباشر
            if (!content && typeof window.getDirectModelResponse === 'function') {
                console.log('🤖 استخدام النموذج المباشر لتوليد محتوى JSON...');
                try {
                    const directResponse = await window.getDirectModelResponse(jsonPrompt);
                    if (directResponse && directResponse.trim()) {
                        try {
                            JSON.parse(directResponse.trim());
                            content = directResponse.trim();
                            console.log('✅ تم توليد JSON من النموذج المباشر');
                        } catch (parseError) {
                            console.warn('⚠️ JSON من النموذج المباشر غير صالح');
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في النموذج المباشر لـ JSON:', error);
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!content && typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لتوليد محتوى JSON...');
                try {
                    const localResponse = await window.technicalAssistant.getResponse(jsonPrompt);
                    if (localResponse && localResponse.trim()) {
                        try {
                            JSON.parse(localResponse.trim());
                            content = localResponse.trim();
                            console.log('✅ تم توليد JSON من النموذج المحلي');
                        } catch (parseError) {
                            console.warn('⚠️ JSON من النموذج المحلي غير صالح، استخدام النظام الذكي...');
                            // استخدام النظام الذكي كبديل
                            content = this.formatAsJSON('', topic);
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في النموذج المحلي لـ JSON:', error);
                    // استخدام النظام الذكي كبديل
                    content = this.formatAsJSON('', topic);
                }
            }

            // إذا لم يتم توليد محتوى، استخدم النظام الذكي
            if (!content) {
                console.log('🧠 استخدام النظام الذكي لإنشاء JSON...');
                content = this.formatAsJSON('', topic);
                console.log('✅ تم إنشاء JSON باستخدام النظام الذكي');
            }

            await this.createJSONFileWithContent(topic, content);
            return `✅ تم إنشاء ملف JSON "${topic}.json" بنجاح!`;

        } catch (error) {
            return `❌ خطأ في إنشاء ملف JSON: ${error.message}`;
        }
    }

    // Handle Word document creation (ChatGPT Style)
    async handleWordCreation(command) {
        try {
            console.log('📄 ChatGPT Style: إنشاء مستند Word:', command.substring(0, 50));

            // إرسال نص المستخدم مباشرة إلى النموذج (مثل ChatGPT)
            const content = await this.generateFileContent(command, 'word');

            if (!content) {
                return `❌ فشل في إنشاء مستند Word: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, command);

            await this.createWordFileWithContent(topic, content);
            return `✅ تم إنشاء مستند Word "${topic}.docx" بنجاح!`;

        } catch (error) {
            return `❌ خطأ في إنشاء مستند Word: ${error.message}`;
        }
    }

    // Handle Excel creation
    async handleExcelCreation(command) {
        const topic = this.extractTopic(command);

        const excelPrompt = `أنت خبير في إنشاء جداول البيانات. أنشئ جدول Excel احترافي عن: ${topic}

يجب أن يتضمن:
1. أوراق عمل متعددة
2. بيانات منظمة ومفيدة
3. رسوم بيانية
4. معادلات وحسابات
5. تنسيق احترافي

قدم هيكل الجدول والبيانات المطلوبة.`;

        try {
            if (typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                const content = await window.technicalAssistant.getResponse(command);
                await this.createExcelFile(topic, content);
                return `✅ تم إنشاء جدول Excel "${topic}.xlsx" بنجاح!`;
            } else {
                return 'النموذج المحلي غير متاح لإنشاء جداول Excel احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء جدول Excel: ${error.message}`;
        }
    }

    // Handle CSS creation (ChatGPT Style)
    async handleCSSCreation(command) {
        try {
            console.log('🎨 ChatGPT Style: إنشاء ملف CSS:', command.substring(0, 50));

            // إرسال نص المستخدم مباشرة إلى النموذج
            const content = await this.generateFileContent(command, 'css');

            if (!content) {
                return `❌ فشل في إنشاء ملف CSS: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, command);

            // تنسيق المحتوى كـ CSS
            const formattedContent = this.formatAsCSS(content, topic);

            // إنشاء الملف مع حاوية التحميل
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.css`;
            const blob = new Blob([formattedContent], { type: 'text/css' });
            const url = URL.createObjectURL(blob);

            // إنشاء حاوية التحميل الاحترافية
            this.createProfessionalDownloadContainer(filename, url, blob.size);

            // إضافة إلى التاريخ
            this.creationHistory.push({
                type: 'CSS File',
                name: filename,
                created: new Date(),
                size: blob.size
            });

            return `✅ تم إنشاء ملف CSS "${filename}" بنجاح!`;

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف CSS:', error);
            return `❌ خطأ في إنشاء ملف CSS: ${error.message}`;
        }
    }

    // Handle JavaScript creation (ChatGPT Style)
    async handleJavaScriptCreation(command) {
        try {
            console.log('🟨 ChatGPT Style: إنشاء ملف JavaScript:', command.substring(0, 50));

            // إرسال نص المستخدم مباشرة إلى النموذج
            const content = await this.generateFileContent(command, 'javascript');

            if (!content) {
                return `❌ فشل في إنشاء ملف JavaScript: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, command);

            // تنسيق المحتوى كـ JavaScript
            const formattedContent = this.formatAsJavaScript(content, topic);

            // إنشاء الملف مع حاوية التحميل
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.js`;
            const blob = new Blob([formattedContent], { type: 'text/javascript' });
            const url = URL.createObjectURL(blob);

            // إنشاء حاوية التحميل الاحترافية
            this.createProfessionalDownloadContainer(filename, url, blob.size);

            // إضافة إلى التاريخ
            this.creationHistory.push({
                type: 'JavaScript File',
                name: filename,
                created: new Date(),
                size: blob.size
            });

            return `✅ تم إنشاء ملف JavaScript "${filename}" بنجاح!`;

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف JavaScript:', error);
            return `❌ خطأ في إنشاء ملف JavaScript: ${error.message}`;
        }
    }

    // Handle Java creation (ChatGPT Style)
    async handleJavaCreation(command) {
        try {
            console.log('☕ ChatGPT Style: إنشاء ملف Java:', command.substring(0, 50));

            // إرسال نص المستخدم مباشرة إلى النموذج
            const content = await this.generateFileContent(command, 'java');

            if (!content) {
                return `❌ فشل في إنشاء ملف Java: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, command);

            // تنسيق المحتوى كـ Java
            const formattedContent = this.formatAsJava(content, topic);

            // إنشاء الملف مع حاوية التحميل
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.java`;
            const blob = new Blob([formattedContent], { type: 'text/x-java-source' });
            const url = URL.createObjectURL(blob);

            // إنشاء حاوية التحميل الاحترافية
            this.createProfessionalDownloadContainer(filename, url, blob.size);

            // إضافة إلى التاريخ
            this.creationHistory.push({
                type: 'Java File',
                name: filename,
                created: new Date(),
                size: blob.size
            });

            return `✅ تم إنشاء ملف Java "${filename}" بنجاح!`;

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف Java:', error);
            return `❌ خطأ في إنشاء ملف Java: ${error.message}`;
        }
    }

    // Handle C# creation (ChatGPT Style)
    async handleCSharpCreation(command) {
        try {
            console.log('🔷 ChatGPT Style: إنشاء ملف C#:', command.substring(0, 50));

            // إرسال نص المستخدم مباشرة إلى النموذج
            const content = await this.generateFileContent(command, 'csharp');

            if (!content) {
                return `❌ فشل في إنشاء ملف C#: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, command);

            // تنسيق المحتوى كـ C#
            const formattedContent = this.formatAsCSharp(content, topic);

            // إنشاء الملف مع حاوية التحميل
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.cs`;
            const blob = new Blob([formattedContent], { type: 'text/x-csharp' });
            const url = URL.createObjectURL(blob);

            // إنشاء حاوية التحميل الاحترافية
            this.createProfessionalDownloadContainer(filename, url, blob.size);

            // إضافة إلى التاريخ
            this.creationHistory.push({
                type: 'C# File',
                name: filename,
                created: new Date(),
                size: blob.size
            });

            return `✅ تم إنشاء ملف C# "${filename}" بنجاح!`;

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف C#:', error);
            return `❌ خطأ في إنشاء ملف C#: ${error.message}`;
        }
    }

    // Handle PHP creation (ChatGPT Style)
    async handlePHPCreation(command) {
        try {
            console.log('🐘 ChatGPT Style: إنشاء ملف PHP:', command.substring(0, 50));

            // إرسال نص المستخدم مباشرة إلى النموذج
            const content = await this.generateFileContent(command, 'php');

            if (!content) {
                return `❌ فشل في إنشاء ملف PHP: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, command);

            // تنسيق المحتوى كـ PHP
            const formattedContent = this.formatAsPHP(content, topic);

            // إنشاء الملف مع حاوية التحميل
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.php`;
            const blob = new Blob([formattedContent], { type: 'text/x-php' });
            const url = URL.createObjectURL(blob);

            // إنشاء حاوية التحميل الاحترافية
            this.createProfessionalDownloadContainer(filename, url, blob.size);

            // إضافة إلى التاريخ
            this.creationHistory.push({
                type: 'PHP File',
                name: filename,
                created: new Date(),
                size: blob.size
            });

            return `✅ تم إنشاء ملف PHP "${filename}" بنجاح!`;

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف PHP:', error);
            return `❌ خطأ في إنشاء ملف PHP: ${error.message}`;
        }
    }

    // Handle C++ creation (ChatGPT Style)
    async handleCPPCreation(command) {
        try {
            console.log('⚡ ChatGPT Style: إنشاء ملف C++:', command.substring(0, 50));

            // إرسال نص المستخدم مباشرة إلى النموذج
            const content = await this.generateFileContent(command, 'cpp');

            if (!content) {
                return `❌ فشل في إنشاء ملف C++: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, command);

            // تنسيق المحتوى كـ C++
            const formattedContent = this.formatAsCPP(content, topic);

            // إنشاء الملف مع حاوية التحميل
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.cpp`;
            const blob = new Blob([formattedContent], { type: 'text/x-c++src' });
            const url = URL.createObjectURL(blob);

            // إنشاء حاوية التحميل الاحترافية
            this.createProfessionalDownloadContainer(filename, url, blob.size);

            // إضافة إلى التاريخ
            this.creationHistory.push({
                type: 'C++ File',
                name: filename,
                created: new Date(),
                size: blob.size
            });

            return `✅ تم إنشاء ملف C++ "${filename}" بنجاح!`;

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف C++:', error);
            return `❌ خطأ في إنشاء ملف C++: ${error.message}`;
        }
    }

    // Handle Python creation (ChatGPT Style)
    async handlePythonCreation(command) {
        try {
            console.log('🐍 ChatGPT Style: إنشاء ملف Python:', command.substring(0, 50));

            // إرسال نص المستخدم مباشرة إلى النموذج
            const content = await this.generateFileContent(command, 'python');

            if (!content) {
                return `❌ فشل في إنشاء ملف Python: لم يتم الحصول على محتوى من النماذج المتاحة.

💡 تأكد من:
- تشغيل أحد النماذج المتاحة (OpenRouter، النموذج المحلي، إلخ)
- صياغة الطلب بوضوح`;
            }

            // استخراج الموضوع من المحتوى أو الطلب
            const topic = this.extractTopicFromContent(content, command);

            // تنسيق المحتوى كـ Python
            const formattedContent = this.formatAsPython(content, topic);

            // إنشاء الملف مع حاوية التحميل
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.py`;
            const blob = new Blob([formattedContent], { type: 'text/x-python' });
            const url = URL.createObjectURL(blob);

            // إنشاء حاوية التحميل الاحترافية
            this.createProfessionalDownloadContainer(filename, url, blob.size);

            // إضافة إلى التاريخ
            this.creationHistory.push({
                type: 'Python File',
                name: filename,
                created: new Date(),
                size: blob.size
            });

            return `✅ تم إنشاء ملف Python "${filename}" بنجاح!`;

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف Python:', error);
            return `❌ خطأ في إنشاء ملف Python: ${error.message}`;
        }
    }

    // Handle HTML creation (يدعم جميع أنواع الملفات)
    async handleHTMLCreation(command) {
        const topic = this.extractTopic(command);
        const detectedType = this.detectFileType(command);
        const fileInfo = this.getFileTypeInfo(detectedType);

        // إنشاء prompt مخصص حسب نوع الملف
        let customPrompt = this.generateCustomPrompt(command, topic, detectedType);

        try {
            let content = '';

            // محاولة الحصول على المحتوى من النماذج المتاحة
            if (typeof window.technicalAssistant !== 'undefined' && window.technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي...');
                try {
                    content = await window.technicalAssistant.getResponse(command); // استخدام الطلب مباشرة
                    console.log('✅ تم الحصول على المحتوى من النموذج المحلي');
                } catch (error) {
                    console.warn('⚠️ خطأ في النموذج المحلي:', error);
                }
            }

            // إذا لم يتم الحصول على محتوى، استخدم محتوى افتراضي بسيط
            if (!content || content.trim() === '') {
                console.log('⚠️ لم يتم الحصول على محتوى من النماذج');
                content = `محتوى ${this.getFileTypeName(detectedType)} عن ${topic}

لم يتم توليد محتوى مخصص من النماذج المتاحة.
الطلب الأصلي: ${command}

تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}`;
            }

            // إنشاء الملف بالمحتوى المولد
            await this.createSmartFileWithContainer(topic, content, detectedType, fileInfo);
            return `✅ تم إنشاء ${this.getFileTypeName(detectedType)} "${topic}${fileInfo.extension}" بنجاح!`;

        } catch (error) {
            return `❌ خطأ في إنشاء الملف: ${error.message}`;
        }
    }

    // إنشاء prompt مخصص حسب نوع الملف
    generateCustomPrompt(command, topic, fileType) {
        const prompts = {
            'html': `أنت خبير في تطوير الويب. أنشئ صفحة HTML احترافية عن: ${topic}
                    يجب أن تتضمن: هيكل HTML5 صحيح، تصميم CSS مدمج جميل، محتوى تفاعلي، تصميم متجاوب.`,

            'css': `أنت خبير في تصميم الويب. أنشئ ملف CSS احترافي لموضوع: ${topic}
                   يجب أن يتضمن: تصميم حديث، ألوان متناسقة، تأثيرات جميلة، تصميم متجاوب.`,

            'javascript': `أنت خبير في JavaScript. أنشئ سكريبت احترافي عن: ${topic}
                          يجب أن يتضمن: كود نظيف، وظائف متقدمة، تعليقات واضحة، أفضل الممارسات.`,

            'python': `أنت خبير في Python. أنشئ برنامج Python احترافي عن: ${topic}
                      يجب أن يتضمن: كود نظيف، وظائف متقدمة، تعليقات واضحة، معالجة الأخطاء.`,

            'java': `أنت خبير في Java. أنشئ برنامج Java احترافي عن: ${topic}
                    يجب أن يتضمن: كود منظم، classes مناسبة، تعليقات واضحة، أفضل الممارسات.`,

            'json': `أنت خبير في هياكل البيانات. أنشئ ملف JSON احترافي عن: ${topic}
                    يجب أن يتضمن: هيكل منظم، بيانات مفيدة، تنسيق صحيح.`,

            'pdf': `أنت خبير في إنشاء التقارير. أنشئ تقرير PDF احترافي عن: ${topic}
                   يجب أن يتضمن: مقدمة، محتوى مفصل، خلاصة، تنسيق احترافي.`,

            'text': `أنت كاتب محترف. أنشئ نص احترافي عن: ${topic}
                    يجب أن يتضمن: محتوى مفيد، تنظيم واضح، معلومات دقيقة.`
        };

        return prompts[fileType] || prompts['html'];
    }

    // إنشاء ملف ذكي مع الحاوية
    async createSmartFileWithContainer(topic, content, fileType, fileInfo) {
        console.log(`📁 إنشاء ${fileType} احترافي:`, topic);

        // تنسيق المحتوى حسب نوع الملف
        const formattedContent = this.formatContentByType(content, fileType, topic);

        // تحديد اسم الملف
        const filename = `${topic.replace(/\s+/g, '_')}${fileInfo.extension}`;

        // إنشاء الملف وتحميله مع الحاوية الاحترافية
        const success = this.createDownloadableFile(formattedContent, filename, fileInfo.mimeType);

        if (success) {
            this.creationHistory.push({
                type: fileType,
                name: filename,
                created: new Date(),
                size: formattedContent.length
            });

            console.log(`✅ تم إنشاء ${fileType}: ${filename}`);
        }
    }

    // تنسيق المحتوى حسب نوع الملف
    formatContentByType(content, fileType, topic) {
        switch (fileType) {
            case 'html':
                return this.cleanAndFormatHTML(content);

            case 'css':
                return this.formatAsCSS(content, topic);

            case 'javascript':
                return this.formatAsJavaScript(content, topic);

            case 'python':
                return this.formatAsPython(content, topic);

            case 'java':
                return this.formatAsJava(content, topic);

            case 'cpp':
                return this.formatAsCPP(content, topic);

            case 'csharp':
                return this.formatAsCSharp(content, topic);

            case 'php':
                return this.formatAsPHP(content, topic);

            case 'sql':
                return this.formatAsSQL(content, topic);

            case 'yaml':
                return this.formatAsYAML(content, topic);

            case 'shell':
                return this.formatAsShell(content, topic);

            case 'powershell':
                return this.formatAsPowerShell(content, topic);

            case 'xml':
                return this.formatAsXML(content, topic);

            case 'json':
                return this.formatAsJSON(content, topic);

            case 'pdf':
                return this.formatAsPDF(content, topic);

            default:
                return this.formatAsText(content, topic);
        }
    }

    // تنسيق CSS
    formatAsCSS(content, topic) {
        // التحقق من وجود كود CSS حقيقي
        if (content.includes('{') || content.includes('}') || content.includes(':') || content.includes('body') || content.includes('color') || content.includes('font')) {
            // المحتوى يحتوي على كود CSS حقيقي
            return `/*
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}

/* نهاية الملف */`;
        } else {
            // المحتوى نص عام، إنشاء كود CSS ذكي
            return this.generateSmartCSSCode(topic, content);
        }
    }

    // إنشاء كود CSS ذكي حسب الموضوع
    generateSmartCSSCode(topic, description) {
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('أمن') || lowerTopic.includes('سيبراني') || lowerTopic.includes('حماية')) {
            return `/*
 * ${topic} - تصميم موقع الأمن السيبراني
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    line-height: 1.6;
    direction: rtl;
}

/* الحاوية الرئيسية */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* العنوان الرئيسي */
.main-header {
    text-align: center;
    padding: 40px 0;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.main-header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    color: #00d4ff;
}

.main-header .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    color: #b8e6ff;
}

/* أقسام المحتوى */
.security-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.security-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.3);
}

.security-section h2 {
    color: #00d4ff;
    font-size: 2rem;
    margin-bottom: 20px;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 10px;
}

/* شبكة التهديدات */
.threats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.threat-item {
    background: linear-gradient(45deg, #ff4757, #ff6b7a);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease;
}

.threat-item:hover {
    transform: scale(1.05);
}

.threat-item h3 {
    margin-bottom: 10px;
    font-size: 1.3rem;
}

/* أدوات الحماية */
.protection-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
}

.tool-badge {
    background: linear-gradient(45deg, #2ed573, #7bed9f);
    color: #2c2c2c;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.tool-badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(46, 213, 115, 0.4);
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    justify-content: center;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #ffa726, #ff9800);
    color: white;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* تأثيرات الأمان */
.security-alert {
    background: linear-gradient(45deg, #ff4757, #ff3838);
    border: 2px solid #ff6b7a;
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 71, 87, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 71, 87, 0); }
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .main-header h1 {
        font-size: 2rem;
    }

    .threats-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .protection-tools {
        justify-content: center;
    }
}

/* تأثيرات إضافية */
.glow-effect {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(0, 212, 255, 0.5); }
    to { box-shadow: 0 0 30px rgba(0, 212, 255, 0.8); }
}`;
        } else if (lowerTopic.includes('ذكاء') || lowerTopic.includes('ai') || lowerTopic.includes('تعلم')) {
            return `/*
 * ${topic} - تصميم موقع الذكاء الاصطناعي
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    line-height: 1.6;
    direction: rtl;
    overflow-x: hidden;
}

/* الحاوية الرئيسية */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
}

/* تأثير الخلفية المتحركة */
.ai-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* العنوان الرئيسي */
.ai-header {
    text-align: center;
    padding: 50px 0;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 20px;
    margin-bottom: 40px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-header h1 {
    font-size: 3.5rem;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(90deg); }
}

.ai-header .tagline {
    font-size: 1.3rem;
    opacity: 0.9;
    color: #b8e6ff;
}

/* أقسام الذكاء الاصطناعي */
.ai-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 35px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ai-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.ai-section:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
}

.ai-section h2 {
    color: #4ecdc4;
    font-size: 2.2rem;
    margin-bottom: 25px;
    position: relative;
}

/* شبكة أنواع الذكاء الاصطناعي */
.ai-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 25px;
}

.ai-type-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.ai-type-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    border-radius: 15px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ai-type-card:hover::before {
    opacity: 1;
}

.ai-type-card:hover {
    transform: scale(1.05) rotateY(5deg);
}

.ai-type-card h3 {
    margin-bottom: 15px;
    font-size: 1.4rem;
    color: #ffffff;
}

/* تطبيقات الذكاء الاصطناعي */
.ai-applications {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 25px;
}

.app-chip {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: #ffffff;
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: bold;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.app-chip:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 10px 25px rgba(78, 205, 196, 0.4);
    border-color: #ffffff;
}

/* أزرار تفاعلية */
.ai-controls {
    display: flex;
    gap: 20px;
    margin-top: 40px;
    justify-content: center;
    flex-wrap: wrap;
}

.ai-btn {
    padding: 15px 35px;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.ai-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.ai-btn:hover::before {
    left: 100%;
}

.ai-btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
}

.ai-btn-secondary {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
}

.ai-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* تأثيرات خاصة */
.neural-network {
    position: relative;
    background: radial-gradient(circle, rgba(78, 205, 196, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    width: 200px;
    height: 200px;
    margin: 20px auto;
    animation: pulse-ai 3s ease-in-out infinite;
}

@keyframes pulse-ai {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(78, 205, 196, 0.7);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(78, 205, 196, 0);
    }
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .ai-header h1 {
        font-size: 2.5rem;
    }

    .ai-types-grid {
        grid-template-columns: 1fr;
    }

    .ai-controls {
        flex-direction: column;
        align-items: center;
    }

    .ai-applications {
        justify-content: center;
    }
}`;
        } else {
            // تصميم عام حسب الموضوع
            return `/*
 * ${topic} - تصميم احترافي
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: #2d3436;
    line-height: 1.6;
    direction: rtl;
}

/* الحاوية الرئيسية */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* العنوان الرئيسي */
.main-header {
    text-align: center;
    padding: 40px 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.main-header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    color: #2d3436;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.main-header .subtitle {
    font-size: 1.2rem;
    color: #636e72;
}

/* أقسام المحتوى */
.content-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.content-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.content-section h2 {
    color: #0984e3;
    font-size: 2rem;
    margin-bottom: 20px;
    border-bottom: 2px solid #74b9ff;
    padding-bottom: 10px;
}

/* شبكة العناصر */
.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.item-card {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease;
}

.item-card:hover {
    transform: scale(1.05);
}

.item-card h3 {
    margin-bottom: 10px;
    font-size: 1.3rem;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(45deg, #0984e3, #74b9ff);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #00b894, #00cec9);
    color: white;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .main-header h1 {
        font-size: 2rem;
    }

    .items-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
}

/* تأثيرات إضافية */
.highlight {
    background: linear-gradient(45deg, #fdcb6e, #e17055);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
    text-align: center;
    font-weight: bold;
}

.fade-in {
    animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}`;
        }
    }

    // تنسيق JavaScript
    formatAsJavaScript(content, topic) {
        // التحقق من أن الطلب ليس JSON
        if (topic.toLowerCase().includes('json')) {
            return this.formatAsJSON(content, topic);
        }

        // التحقق من وجود كود JavaScript حقيقي
        if (content.includes('function') || content.includes('const ') || content.includes('let ') || content.includes('var ') || content.includes('console.log')) {
            // المحتوى يحتوي على كود JavaScript حقيقي
            return `/**
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}

console.log("تم تحميل ${topic} بنجاح!");`;
        } else {
            // المحتوى نص عام، إنشاء كود JavaScript ذكي
            return this.generateSmartJavaScriptCode(topic, content);
        }
    }

    // إنشاء كود JavaScript ذكي حسب الموضوع
    generateSmartJavaScriptCode(topic, description) {
        // التحقق الشامل من JSON
        const lowerTopic = topic.toLowerCase();
        const lowerDescription = (description || '').toLowerCase();

        if (lowerTopic.includes('json') || lowerDescription.includes('json') ||
            lowerTopic.includes('ملف json') || lowerDescription.includes('ملف json')) {
            console.log('🔄 تحويل من JavaScript إلى JSON:', topic);
            return this.formatAsJSON('', topic);
        }

        if (lowerTopic.includes('حاسبة') || lowerTopic.includes('calculator')) {
            return `/**
 * ${topic} - حاسبة احترافية
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

class Calculator {
    constructor() {
        this.history = [];
        this.display = null;
        this.init();
    }

    init() {
        console.log('🔢 مرحباً بك في ${topic}');
        this.createInterface();
    }

    createInterface() {
        // إنشاء واجهة الحاسبة
        const calculatorHTML = \`
            <div id="calculator" style="
                max-width: 300px;
                margin: 20px auto;
                padding: 20px;
                border: 2px solid #007bff;
                border-radius: 10px;
                background: #f8f9fa;
                font-family: Arial, sans-serif;
            ">
                <h2 style="text-align: center; color: #007bff;">🔢 ${topic}</h2>
                <input type="text" id="display" readonly style="
                    width: 100%;
                    height: 50px;
                    font-size: 24px;
                    text-align: right;
                    margin-bottom: 10px;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    padding: 0 10px;
                ">
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                    <button onclick="calculator.clear()" style="grid-column: span 2; padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #dc3545; color: white; cursor: pointer;">مسح</button>
                    <button onclick="calculator.deleteLast()" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #ffc107; color: black; cursor: pointer;">⌫</button>
                    <button onclick="calculator.appendToDisplay('/')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #17a2b8; color: white; cursor: pointer;">÷</button>

                    <button onclick="calculator.appendToDisplay('7')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">7</button>
                    <button onclick="calculator.appendToDisplay('8')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">8</button>
                    <button onclick="calculator.appendToDisplay('9')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">9</button>
                    <button onclick="calculator.appendToDisplay('*')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #17a2b8; color: white; cursor: pointer;">×</button>

                    <button onclick="calculator.appendToDisplay('4')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">4</button>
                    <button onclick="calculator.appendToDisplay('5')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">5</button>
                    <button onclick="calculator.appendToDisplay('6')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">6</button>
                    <button onclick="calculator.appendToDisplay('-')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #17a2b8; color: white; cursor: pointer;">-</button>

                    <button onclick="calculator.appendToDisplay('1')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">1</button>
                    <button onclick="calculator.appendToDisplay('2')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">2</button>
                    <button onclick="calculator.appendToDisplay('3')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">3</button>
                    <button onclick="calculator.appendToDisplay('+')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #17a2b8; color: white; cursor: pointer;">+</button>

                    <button onclick="calculator.appendToDisplay('0')" style="grid-column: span 2; padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">0</button>
                    <button onclick="calculator.appendToDisplay('.')" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">.</button>
                    <button onclick="calculator.calculate()" style="padding: 15px; font-size: 16px; border: none; border-radius: 5px; background: #28a745; color: white; cursor: pointer;">=</button>
                </div>
                <div style="margin-top: 10px;">
                    <button onclick="calculator.showHistory()" style="width: 48%; padding: 10px; margin-right: 4%; border: none; border-radius: 5px; background: #007bff; color: white; cursor: pointer;">التاريخ</button>
                    <button onclick="calculator.clearHistory()" style="width: 48%; padding: 10px; border: none; border-radius: 5px; background: #6c757d; color: white; cursor: pointer;">مسح التاريخ</button>
                </div>
                <div id="history" style="margin-top: 10px; max-height: 100px; overflow-y: auto; font-size: 12px; color: #666;"></div>
            </div>
        \`;

        // إضافة الواجهة إلى الصفحة
        if (document.body) {
            document.body.insertAdjacentHTML('beforeend', calculatorHTML);
            this.display = document.getElementById('display');
        }
    }

    appendToDisplay(value) {
        if (this.display) {
            this.display.value += value;
        }
    }

    clear() {
        if (this.display) {
            this.display.value = '';
        }
    }

    deleteLast() {
        if (this.display && this.display.value.length > 0) {
            this.display.value = this.display.value.slice(0, -1);
        }
    }

    calculate() {
        try {
            if (this.display && this.display.value) {
                const expression = this.display.value;
                const result = eval(expression.replace(/×/g, '*').replace(/÷/g, '/'));

                // إضافة إلى التاريخ
                this.history.push(\`\${expression} = \${result}\`);

                this.display.value = result;
                console.log(\`حساب: \${expression} = \${result}\`);
            }
        } catch (error) {
            this.display.value = 'خطأ';
            console.error('خطأ في الحساب:', error);
            setTimeout(() => {
                this.clear();
            }, 1000);
        }
    }

    showHistory() {
        const historyDiv = document.getElementById('history');
        if (historyDiv) {
            if (this.history.length === 0) {
                historyDiv.innerHTML = '<p>لا يوجد تاريخ</p>';
            } else {
                historyDiv.innerHTML = '<strong>التاريخ:</strong><br>' +
                    this.history.slice(-5).map(item => \`<div>\${item}</div>\`).join('');
            }
        }
    }

    clearHistory() {
        this.history = [];
        const historyDiv = document.getElementById('history');
        if (historyDiv) {
            historyDiv.innerHTML = '<p>تم مسح التاريخ</p>';
        }
        console.log('تم مسح تاريخ العمليات');
    }
}

// إنشاء مثيل الحاسبة
const calculator = new Calculator();

// تصدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Calculator;
}

console.log("تم تحميل ${topic} بنجاح!");`;
        } else if (lowerTopic.includes('لعبة') || lowerTopic.includes('game')) {
            return `/**
 * ${topic} - لعبة تخمين الرقم
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

class NumberGuessingGame {
    constructor() {
        this.minNum = 1;
        this.maxNum = 100;
        this.secretNumber = this.generateRandomNumber();
        this.attempts = 0;
        this.maxAttempts = 7;
        this.gameActive = true;
        this.init();
    }

    init() {
        console.log('🎮 مرحباً بك في ${topic}!');
        this.createGameInterface();
    }

    generateRandomNumber() {
        return Math.floor(Math.random() * (this.maxNum - this.minNum + 1)) + this.minNum;
    }

    createGameInterface() {
        const gameHTML = \`
            <div id="guessing-game" style="
                max-width: 400px;
                margin: 20px auto;
                padding: 20px;
                border: 2px solid #28a745;
                border-radius: 10px;
                background: #f8f9fa;
                font-family: Arial, sans-serif;
                text-align: center;
            ">
                <h2 style="color: #28a745;">🎮 ${topic}</h2>
                <p>خمن الرقم بين \${this.minNum} و \${this.maxNum}</p>
                <p>لديك \${this.maxAttempts} محاولات</p>

                <div style="margin: 20px 0;">
                    <input type="number" id="guess-input" min="\${this.minNum}" max="\${this.maxNum}"
                           placeholder="أدخل تخمينك" style="
                        width: 200px;
                        height: 40px;
                        font-size: 18px;
                        text-align: center;
                        border: 2px solid #ccc;
                        border-radius: 5px;
                        margin-bottom: 10px;
                    ">
                    <br>
                    <button onclick="game.makeGuess()" style="
                        padding: 10px 20px;
                        font-size: 16px;
                        border: none;
                        border-radius: 5px;
                        background: #28a745;
                        color: white;
                        cursor: pointer;
                        margin: 5px;
                    ">تخمين</button>
                    <button onclick="game.newGame()" style="
                        padding: 10px 20px;
                        font-size: 16px;
                        border: none;
                        border-radius: 5px;
                        background: #007bff;
                        color: white;
                        cursor: pointer;
                        margin: 5px;
                    ">لعبة جديدة</button>
                </div>

                <div id="game-message" style="
                    min-height: 50px;
                    padding: 10px;
                    margin: 10px 0;
                    border-radius: 5px;
                    font-weight: bold;
                "></div>

                <div id="attempts-counter" style="color: #666;">
                    المحاولات المتبقية: \${this.maxAttempts}
                </div>

                <div id="game-history" style="
                    margin-top: 20px;
                    max-height: 150px;
                    overflow-y: auto;
                    font-size: 14px;
                    color: #666;
                "></div>
            </div>
        \`;

        if (document.body) {
            document.body.insertAdjacentHTML('beforeend', gameHTML);

            // إضافة مستمع للضغط على Enter
            const input = document.getElementById('guess-input');
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.makeGuess();
                    }
                });
            }
        }
    }

    makeGuess() {
        if (!this.gameActive) return;

        const input = document.getElementById('guess-input');
        const messageDiv = document.getElementById('game-message');
        const attemptsDiv = document.getElementById('attempts-counter');
        const historyDiv = document.getElementById('game-history');

        if (!input || !messageDiv) return;

        const guess = parseInt(input.value);

        if (isNaN(guess) || guess < this.minNum || guess > this.maxNum) {
            this.showMessage(\`يرجى إدخال رقم بين \${this.minNum} و \${this.maxNum}\`, 'warning');
            return;
        }

        this.attempts++;
        const remaining = this.maxAttempts - this.attempts;

        // إضافة إلى التاريخ
        const historyItem = \`المحاولة \${this.attempts}: \${guess}\`;
        if (historyDiv) {
            historyDiv.innerHTML += \`<div>\${historyItem}</div>\`;
            historyDiv.scrollTop = historyDiv.scrollHeight;
        }

        if (guess === this.secretNumber) {
            this.showMessage(\`🎉 مبروك! لقد خمنت الرقم \${this.secretNumber} في \${this.attempts} محاولة!\`, 'success');
            this.gameActive = false;
            console.log(\`فوز! الرقم كان \${this.secretNumber}\`);
        } else if (this.attempts >= this.maxAttempts) {
            this.showMessage(\`💔 انتهت المحاولات! الرقم كان \${this.secretNumber}\`, 'danger');
            this.gameActive = false;
            console.log(\`خسارة! الرقم كان \${this.secretNumber}\`);
        } else {
            const hint = guess < this.secretNumber ? '📈 الرقم أكبر!' : '📉 الرقم أصغر!';
            this.showMessage(hint, 'info');
            console.log(\`تخمين: \${guess}, الرقم \${guess < this.secretNumber ? 'أكبر' : 'أصغر'}\`);
        }

        if (attemptsDiv) {
            attemptsDiv.textContent = \`المحاولات المتبقية: \${remaining}\`;
        }

        input.value = '';
        input.focus();
    }

    showMessage(message, type) {
        const messageDiv = document.getElementById('game-message');
        if (messageDiv) {
            const colors = {
                success: '#d4edda',
                danger: '#f8d7da',
                warning: '#fff3cd',
                info: '#d1ecf1'
            };
            messageDiv.textContent = message;
            messageDiv.style.backgroundColor = colors[type] || colors.info;
        }
    }

    newGame() {
        this.secretNumber = this.generateRandomNumber();
        this.attempts = 0;
        this.gameActive = true;

        const messageDiv = document.getElementById('game-message');
        const attemptsDiv = document.getElementById('attempts-counter');
        const historyDiv = document.getElementById('game-history');
        const input = document.getElementById('guess-input');

        if (messageDiv) messageDiv.textContent = '';
        if (attemptsDiv) attemptsDiv.textContent = \`المحاولات المتبقية: \${this.maxAttempts}\`;
        if (historyDiv) historyDiv.innerHTML = '';
        if (input) {
            input.value = '';
            input.focus();
        }

        console.log('🎮 لعبة جديدة بدأت!');
    }
}

// إنشاء مثيل اللعبة
const game = new NumberGuessingGame();

// تصدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NumberGuessingGame;
}

console.log("تم تحميل ${topic} بنجاح!");`;
        } else {
            // التحقق من أن الموضوع ليس JSON
            if (topic.toLowerCase().includes('json')) {
                // إذا كان JSON، استخدم formatAsJSON
                return this.formatAsJSON('', topic);
            }

            // كود عام حسب الموضوع
            return `/**
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

class ${topic.replace(/[^a-zA-Z0-9]/g, '')} {
    constructor() {
        this.name = "${topic}";
        this.createdAt = "${new Date().toLocaleDateString('ar-SA')}";
        this.init();
    }

    init() {
        console.log(\`🚀 تم إنشاء \${this.name} بنجاح!\`);
        this.displayInfo();
        this.process();
    }

    process() {
        console.log(\`جاري معالجة \${this.name}...\`);

        // إضافة الكود المطلوب هنا
        try {
            // منطق المعالجة الأساسي
            const result = this.performMainTask();

            if (result) {
                console.log(\`✅ تم تشغيل \${this.name} بنجاح!\`);
                this.onSuccess();
            } else {
                console.log(\`❌ فشل في تشغيل \${this.name}\`);
                this.onError();
            }

            return result;
        } catch (error) {
            console.error(\`خطأ في \${this.name}:\`, error);
            this.onError(error);
            return false;
        }
    }

    performMainTask() {
        // المهمة الرئيسية - يمكن تخصيصها حسب الحاجة
        console.log(\`تنفيذ المهمة الرئيسية لـ \${this.name}\`);

        // محاكاة معالجة
        const success = Math.random() > 0.1; // 90% نجاح
        return success;
    }

    displayInfo() {
        console.log(\`
📋 معلومات \${this.name}:
- الاسم: \${this.name}
- تاريخ الإنشاء: \${this.createdAt}
- الحالة: نشط
- النوع: JavaScript Application
        \`);
    }

    onSuccess() {
        console.log(\`🎉 تم إكمال \${this.name} بنجاح!\`);

        // يمكن إضافة إجراءات إضافية عند النجاح
        this.logActivity('success', 'تم إكمال المهمة بنجاح');
    }

    onError(error = null) {
        console.log(\`⚠️ حدث خطأ في \${this.name}\`);

        if (error) {
            console.error('تفاصيل الخطأ:', error.message);
        }

        // يمكن إضافة إجراءات إضافية عند الخطأ
        this.logActivity('error', error ? error.message : 'خطأ غير محدد');
    }

    logActivity(type, message) {
        const timestamp = new Date().toLocaleString('ar-SA');
        console.log(\`[\${timestamp}] \${type.toUpperCase()}: \${message}\`);
    }

    // دالة مساعدة للتشغيل اليدوي
    run() {
        console.log(\`🔄 إعادة تشغيل \${this.name}...\`);
        return this.process();
    }

    // دالة للحصول على معلومات الحالة
    getStatus() {
        return {
            name: this.name,
            createdAt: this.createdAt,
            isActive: true,
            lastRun: new Date().toISOString()
        };
    }
}

// إنشاء مثيل التطبيق
const app = new ${topic.replace(/[^a-zA-Z0-9]/g, '')}();

// تصدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ${topic.replace(/[^a-zA-Z0-9]/g, '')};
}

// جعل التطبيق متاح عالمياً
if (typeof window !== 'undefined') {
    window.${topic.replace(/[^a-zA-Z0-9]/g, '')} = ${topic.replace(/[^a-zA-Z0-9]/g, '')};
    window.app = app;
}

console.log("تم تحميل ${topic} بنجاح!");`;
        }
    }

    // تنسيق Python
    formatAsPython(content, topic) {
        // التحقق من وجود كود Python حقيقي
        if (content.includes('def ') || content.includes('import ') || content.includes('class ') || content.includes('print(')) {
            // المحتوى يحتوي على كود Python حقيقي
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
"""

${content}

if __name__ == "__main__":
    print("${topic} - تم التشغيل بنجاح!")`;
        } else {
            // المحتوى نص عام، إنشاء كود Python ذكي
            return this.generateSmartPythonCode(topic, content);
        }
    }

    // إنشاء كود Python ذكي حسب الموضوع
    generateSmartPythonCode(topic, description) {
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('حاسبة') || lowerTopic.includes('calculator')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic} - حاسبة احترافية
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
"""

import math

class Calculator:
    """حاسبة احترافية تدعم العمليات الأساسية والمتقدمة"""

    def __init__(self):
        self.history = []

    def add(self, a, b):
        """جمع رقمين"""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result

    def subtract(self, a, b):
        """طرح رقمين"""
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result

    def multiply(self, a, b):
        """ضرب رقمين"""
        result = a * b
        self.history.append(f"{a} × {b} = {result}")
        return result

    def divide(self, a, b):
        """قسمة رقمين"""
        if b == 0:
            raise ValueError("لا يمكن القسمة على صفر")
        result = a / b
        self.history.append(f"{a} ÷ {b} = {result}")
        return result

    def power(self, base, exponent):
        """رفع رقم لقوة"""
        result = base ** exponent
        self.history.append(f"{base}^{exponent} = {result}")
        return result

    def square_root(self, number):
        """الجذر التربيعي"""
        if number < 0:
            raise ValueError("لا يمكن حساب الجذر التربيعي لرقم سالب")
        result = math.sqrt(number)
        self.history.append(f"√{number} = {result}")
        return result

    def show_history(self):
        """عرض تاريخ العمليات"""
        print("تاريخ العمليات:")
        for operation in self.history:
            print(f"  {operation}")

    def clear_history(self):
        """مسح تاريخ العمليات"""
        self.history.clear()
        print("تم مسح تاريخ العمليات")

def main():
    """الدالة الرئيسية للحاسبة"""
    calc = Calculator()

    print("🔢 مرحباً بك في ${topic}")
    print("العمليات المتاحة:")
    print("1. الجمع (+)")
    print("2. الطرح (-)")
    print("3. الضرب (*)")
    print("4. القسمة (/)")
    print("5. القوة (**)")
    print("6. الجذر التربيعي (√)")
    print("7. عرض التاريخ")
    print("8. مسح التاريخ")
    print("0. خروج")

    while True:
        try:
            choice = input("\\nاختر العملية (0-8): ")

            if choice == '0':
                print("شكراً لاستخدام الحاسبة!")
                break
            elif choice == '1':
                a = float(input("الرقم الأول: "))
                b = float(input("الرقم الثاني: "))
                print(f"النتيجة: {calc.add(a, b)}")
            elif choice == '2':
                a = float(input("الرقم الأول: "))
                b = float(input("الرقم الثاني: "))
                print(f"النتيجة: {calc.subtract(a, b)}")
            elif choice == '3':
                a = float(input("الرقم الأول: "))
                b = float(input("الرقم الثاني: "))
                print(f"النتيجة: {calc.multiply(a, b)}")
            elif choice == '4':
                a = float(input("الرقم الأول: "))
                b = float(input("الرقم الثاني: "))
                print(f"النتيجة: {calc.divide(a, b)}")
            elif choice == '5':
                base = float(input("الأساس: "))
                exp = float(input("الأس: "))
                print(f"النتيجة: {calc.power(base, exp)}")
            elif choice == '6':
                num = float(input("الرقم: "))
                print(f"النتيجة: {calc.square_root(num)}")
            elif choice == '7':
                calc.show_history()
            elif choice == '8':
                calc.clear_history()
            else:
                print("اختيار غير صحيح!")

        except ValueError as e:
            print(f"خطأ: {e}")
        except Exception as e:
            print(f"حدث خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()`;
        } else if (lowerTopic.includes('لعبة') || lowerTopic.includes('game')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic} - لعبة تخمين الرقم
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
"""

import random

class NumberGuessingGame:
    """لعبة تخمين الرقم"""

    def __init__(self, min_num=1, max_num=100):
        self.min_num = min_num
        self.max_num = max_num
        self.secret_number = random.randint(min_num, max_num)
        self.attempts = 0
        self.max_attempts = 7

    def play(self):
        """بدء اللعبة"""
        print(f"🎮 مرحباً بك في ${topic}!")
        print(f"خمن الرقم بين {self.min_num} و {self.max_num}")
        print(f"لديك {self.max_attempts} محاولات")

        while self.attempts < self.max_attempts:
            try:
                guess = int(input(f"\\nالمحاولة {self.attempts + 1}: "))
                self.attempts += 1

                if guess == self.secret_number:
                    print(f"🎉 مبروك! لقد خمنت الرقم {self.secret_number} في {self.attempts} محاولة!")
                    return True
                elif guess < self.secret_number:
                    print("📈 الرقم أكبر!")
                else:
                    print("📉 الرقم أصغر!")

                remaining = self.max_attempts - self.attempts
                if remaining > 0:
                    print(f"المحاولات المتبقية: {remaining}")

            except ValueError:
                print("يرجى إدخال رقم صحيح!")
                self.attempts -= 1

        print(f"💔 انتهت المحاولات! الرقم كان {self.secret_number}")
        return False

    def play_again(self):
        """لعب مرة أخرى"""
        self.secret_number = random.randint(self.min_num, self.max_num)
        self.attempts = 0
        return self.play()

def main():
    """الدالة الرئيسية"""
    game = NumberGuessingGame()

    while True:
        game.play()

        play_again = input("\\nهل تريد اللعب مرة أخرى؟ (y/n): ").lower()
        if play_again not in ['y', 'yes', 'نعم']:
            print("شكراً للعب!")
            break

        game = NumberGuessingGame()

if __name__ == "__main__":
    main()`;
        } else {
            // كود عام حسب الموضوع
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
"""

class ${topic.replace(/[^a-zA-Z0-9]/g, '')}:
    """فئة ${topic}"""

    def __init__(self):
        """تهيئة ${topic}"""
        self.name = "${topic}"
        self.created_at = "${new Date().toLocaleDateString('ar-SA')}"
        print(f"تم إنشاء {self.name} بنجاح!")

    def process(self):
        """معالجة ${topic}"""
        print(f"جاري معالجة {self.name}...")
        # إضافة الكود المطلوب هنا
        return True

    def display_info(self):
        """عرض معلومات ${topic}"""
        print(f"الاسم: {self.name}")
        print(f"تاريخ الإنشاء: {self.created_at}")

    def run(self):
        """تشغيل ${topic}"""
        print(f"🚀 بدء تشغيل {self.name}")
        self.display_info()
        result = self.process()
        if result:
            print(f"✅ تم تشغيل {self.name} بنجاح!")
        else:
            print(f"❌ فشل في تشغيل {self.name}")
        return result

def main():
    """الدالة الرئيسية"""
    app = ${topic.replace(/[^a-zA-Z0-9]/g, '')}()
    app.run()

if __name__ == "__main__":
    main()`;
        }
    }

    // تنسيق Java
    formatAsJava(content, topic) {
        // التحقق من وجود كود Java حقيقي
        if (content.includes('class ') || content.includes('public ') || content.includes('import ') || content.includes('System.out.println')) {
            // المحتوى يحتوي على كود Java حقيقي
            return `/**
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}`;
        } else {
            // المحتوى نص عام، إنشاء كود Java ذكي
            return this.generateSmartJavaCode(topic, content);
        }
    }

    // إنشاء كود Java ذكي حسب الموضوع
    generateSmartJavaCode(topic, description) {
        const className = topic.replace(/[^a-zA-Z0-9]/g, '');
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('حاسبة') || lowerTopic.includes('calculator')) {
            return `/**
 * ${topic} - حاسبة احترافية
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

import java.util.Scanner;
import java.util.ArrayList;
import java.util.List;

public class Calculator {
    private List<String> history;
    private Scanner scanner;

    public Calculator() {
        this.history = new ArrayList<>();
        this.scanner = new Scanner(System.in);
    }

    public double add(double a, double b) {
        double result = a + b;
        history.add(a + " + " + b + " = " + result);
        return result;
    }

    public double subtract(double a, double b) {
        double result = a - b;
        history.add(a + " - " + b + " = " + result);
        return result;
    }

    public double multiply(double a, double b) {
        double result = a * b;
        history.add(a + " × " + b + " = " + result);
        return result;
    }

    public double divide(double a, double b) {
        if (b == 0) {
            throw new ArithmeticException("لا يمكن القسمة على صفر");
        }
        double result = a / b;
        history.add(a + " ÷ " + b + " = " + result);
        return result;
    }

    public double power(double base, double exponent) {
        double result = Math.pow(base, exponent);
        history.add(base + "^" + exponent + " = " + result);
        return result;
    }

    public void showHistory() {
        System.out.println("تاريخ العمليات:");
        for (String operation : history) {
            System.out.println("  " + operation);
        }
    }

    public void clearHistory() {
        history.clear();
        System.out.println("تم مسح تاريخ العمليات");
    }

    public void run() {
        System.out.println("🔢 مرحباً بك في ${topic}");
        System.out.println("العمليات المتاحة:");
        System.out.println("1. الجمع (+)");
        System.out.println("2. الطرح (-)");
        System.out.println("3. الضرب (*)");
        System.out.println("4. القسمة (/)");
        System.out.println("5. القوة (**)");
        System.out.println("6. عرض التاريخ");
        System.out.println("7. مسح التاريخ");
        System.out.println("0. خروج");

        while (true) {
            try {
                System.out.print("\\nاختر العملية (0-7): ");
                int choice = scanner.nextInt();

                if (choice == 0) {
                    System.out.println("شكراً لاستخدام الحاسبة!");
                    break;
                } else if (choice >= 1 && choice <= 5) {
                    System.out.print("الرقم الأول: ");
                    double a = scanner.nextDouble();
                    System.out.print("الرقم الثاني: ");
                    double b = scanner.nextDouble();

                    double result = 0;
                    switch (choice) {
                        case 1: result = add(a, b); break;
                        case 2: result = subtract(a, b); break;
                        case 3: result = multiply(a, b); break;
                        case 4: result = divide(a, b); break;
                        case 5: result = power(a, b); break;
                    }
                    System.out.println("النتيجة: " + result);
                } else if (choice == 6) {
                    showHistory();
                } else if (choice == 7) {
                    clearHistory();
                } else {
                    System.out.println("اختيار غير صحيح!");
                }
            } catch (Exception e) {
                System.out.println("خطأ: " + e.getMessage());
                scanner.nextLine(); // تنظيف المدخل
            }
        }
    }

    public static void main(String[] args) {
        Calculator calc = new Calculator();
        calc.run();
    }
}`;
        } else {
            // كود عام حسب الموضوع
            return `/**
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

public class ${className} {
    private String name;
    private String createdAt;

    public ${className}() {
        this.name = "${topic}";
        this.createdAt = "${new Date().toLocaleDateString('ar-SA')}";
        System.out.println("تم إنشاء " + this.name + " بنجاح!");
    }

    public boolean process() {
        System.out.println("جاري معالجة " + this.name + "...");
        // إضافة الكود المطلوب هنا
        return true;
    }

    public void displayInfo() {
        System.out.println("الاسم: " + this.name);
        System.out.println("تاريخ الإنشاء: " + this.createdAt);
    }

    public boolean run() {
        System.out.println("🚀 بدء تشغيل " + this.name);
        displayInfo();
        boolean result = process();
        if (result) {
            System.out.println("✅ تم تشغيل " + this.name + " بنجاح!");
        } else {
            System.out.println("❌ فشل في تشغيل " + this.name);
        }
        return result;
    }

    public static void main(String[] args) {
        ${className} app = new ${className}();
        app.run();
    }
}`;
        }
    }

    // تنسيق JSON بسيط مثل رد المساعد
    formatAsJSON(content, topic) {
        // إذا كان هناك محتوى من المساعد، استخدمه مباشرة
        if (content && content.trim() && !content.includes('class ') && !content.includes('function')) {
            // المحتوى من المساعد - حوله لـ JSON بسيط
            return JSON.stringify({
                "title": topic,
                "content": content.trim(),
                "created_at": new Date().toISOString(),
                "created_by": "المساعد التقني الذكي",
                "type": "assistant_response"
            }, null, 2);
        }

        // التحقق من وجود JSON حقيقي
        if (content && content.includes('{') && content.includes('}') && content.includes('"')) {
            try {
                const parsed = JSON.parse(content);
                return JSON.stringify(parsed, null, 2);
            } catch (e) {
                console.log('المحتوى ليس JSON صالح، إنشاء JSON بسيط...');
            }
        }

        // إنشاء JSON بسيط مثل رد المساعد
        const lowerTopic = topic.toLowerCase();
        let simpleData = {};

        if (lowerTopic.includes('أمن') || lowerTopic.includes('سيبراني') || lowerTopic.includes('حماية')) {
            simpleData = {
                "title": topic,
                "description": "دليل شامل حول الأمن السيبراني",
                "created_at": new Date().toISOString(),
                "created_by": "المساعد التقني الذكي",
                "content": {
                    "introduction": "الأمن السيبراني هو ممارسة حماية الأنظمة والشبكات والبرامج من الهجمات الرقمية",
                    "main_principles": [
                        "السرية: حماية المعلومات من الوصول غير المصرح",
                        "التكامل: ضمان دقة وسلامة البيانات",
                        "التوفر: ضمان إمكانية الوصول للمعلومات عند الحاجة"
                    ],
                    "common_threats": [
                        "البرمجيات الخبيثة والفيروسات",
                        "هجمات التصيد الاحتيالي",
                        "هجمات الحرمان من الخدمة",
                        "اختراق كلمات المرور"
                    ],
                    "protection_methods": [
                        "استخدام جدران الحماية",
                        "تحديث البرامج بانتظام",
                        "استخدام كلمات مرور قوية",
                        "عمل نسخ احتياطية منتظمة"
                    ]
                }
            };
        } else if (lowerTopic.includes('ذكاء') || lowerTopic.includes('ai') || lowerTopic.includes('تعلم')) {
            simpleData = {
                "title": topic,
                "description": "دليل شامل حول الذكاء الاصطناعي",
                "created_at": new Date().toISOString(),
                "created_by": "المساعد التقني الذكي",
                "content": {
                    "introduction": "الذكاء الاصطناعي هو فرع من علوم الحاسوب يهدف إلى إنشاء أنظمة ذكية",
                    "types": [
                        "الذكاء الضيق: متخصص في مهام محددة",
                        "الذكاء العام: يضاهي القدرات البشرية",
                        "الذكاء الفائق: يتجاوز الذكاء البشري"
                    ],
                    "applications": [
                        "الطب: التشخيص المبكر",
                        "النقل: السيارات ذاتية القيادة",
                        "التمويل: كشف الاحتيال",
                        "التعليم: التعلم المخصص"
                    ],
                    "technologies": [
                        "التعلم الآلي",
                        "التعلم العميق",
                        "معالجة اللغات الطبيعية",
                        "الرؤية الحاسوبية"
                    ]
                }
            };
        } else {
            // موضوع عام - إنشاء JSON بسيط
            simpleData = {
                "title": topic,
                "description": `دليل شامل حول ${topic}`,
                "created_at": new Date().toISOString(),
                "created_by": "المساعد التقني الذكي",
                "content": {
                    "introduction": `نظرة عامة شاملة حول ${topic}`,
                    "main_points": [
                        `النقطة الأولى حول ${topic}`,
                        `النقطة الثانية حول ${topic}`,
                        `النقطة الثالثة حول ${topic}`
                    ],
                    "benefits": [
                        `الفائدة الأولى من ${topic}`,
                        `الفائدة الثانية من ${topic}`
                    ],
                    "applications": [
                        `التطبيق الأول لـ ${topic}`,
                        `التطبيق الثاني لـ ${topic}`
                    ],
                    "conclusion": `خلاصة شاملة حول ${topic}`
                }
            };
        }

        return JSON.stringify(simpleData, null, 2);
    }

    // تنسيق PDF (كـ HTML)
    formatAsPDF(content, topic) {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic}</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            line-height: 1.8;
            background: white;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .content {
            text-align: justify;
            font-size: 16px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        @media print {
            body { margin: 0; padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${topic}</h1>
        <p>تقرير احترافي - ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
    <div class="content">
        ${content.replace(/\n/g, '</p><p>')}
    </div>
    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد التقني الذكي</p>
        <p>File Creator Mode - ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
</body>
</html>`;
    }

    // تنسيق C++
    formatAsCPP(content, topic) {
        // التحقق من وجود كود C++ حقيقي
        if (content.includes('#include') || content.includes('int main') || content.includes('cout') || content.includes('class ') || content.includes('using namespace')) {
            // المحتوى يحتوي على كود C++ حقيقي
            return `/*
 * ${topic} - C++ Program
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}

// نهاية البرنامج`;
        } else {
            // المحتوى نص عام، إنشاء كود C++ ذكي
            return this.generateSmartCPPCode(topic, content);
        }
    }

    // إنشاء كود C++ ذكي حسب الموضوع
    generateSmartCPPCode(topic, description) {
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('حاسبة') || lowerTopic.includes('calculator')) {
            return `/*
 * ${topic} - حاسبة احترافية
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

#include <iostream>
#include <vector>
#include <string>
#include <iomanip>
#include <cmath>

using namespace std;

class Calculator {
private:
    vector<string> history;

public:
    Calculator() {
        cout << "🔢 مرحباً بك في ${topic}" << endl;
    }

    double add(double a, double b) {
        double result = a + b;
        string operation = to_string(a) + " + " + to_string(b) + " = " + to_string(result);
        history.push_back(operation);
        return result;
    }

    double subtract(double a, double b) {
        double result = a - b;
        string operation = to_string(a) + " - " + to_string(b) + " = " + to_string(result);
        history.push_back(operation);
        return result;
    }

    double multiply(double a, double b) {
        double result = a * b;
        string operation = to_string(a) + " × " + to_string(b) + " = " + to_string(result);
        history.push_back(operation);
        return result;
    }

    double divide(double a, double b) {
        if (b == 0) {
            cout << "خطأ: لا يمكن القسمة على صفر!" << endl;
            return 0;
        }
        double result = a / b;
        string operation = to_string(a) + " ÷ " + to_string(b) + " = " + to_string(result);
        history.push_back(operation);
        return result;
    }

    double power(double base, double exponent) {
        double result = pow(base, exponent);
        string operation = to_string(base) + "^" + to_string(exponent) + " = " + to_string(result);
        history.push_back(operation);
        return result;
    }

    double squareRoot(double number) {
        if (number < 0) {
            cout << "خطأ: لا يمكن حساب الجذر التربيعي لرقم سالب!" << endl;
            return 0;
        }
        double result = sqrt(number);
        string operation = "√" + to_string(number) + " = " + to_string(result);
        history.push_back(operation);
        return result;
    }

    void showHistory() {
        cout << "\\n📋 تاريخ العمليات:" << endl;
        if (history.empty()) {
            cout << "لا توجد عمليات سابقة" << endl;
        } else {
            for (size_t i = 0; i < history.size(); ++i) {
                cout << (i + 1) << ". " << history[i] << endl;
            }
        }
    }

    void clearHistory() {
        history.clear();
        cout << "تم مسح تاريخ العمليات" << endl;
    }

    void showMenu() {
        cout << "\\n=== ${topic} ===" << endl;
        cout << "1. الجمع (+)" << endl;
        cout << "2. الطرح (-)" << endl;
        cout << "3. الضرب (×)" << endl;
        cout << "4. القسمة (÷)" << endl;
        cout << "5. القوة (^)" << endl;
        cout << "6. الجذر التربيعي (√)" << endl;
        cout << "7. عرض التاريخ" << endl;
        cout << "8. مسح التاريخ" << endl;
        cout << "0. خروج" << endl;
        cout << "اختر العملية: ";
    }

    void run() {
        int choice;
        double a, b, result;

        while (true) {
            showMenu();
            cin >> choice;

            if (choice == 0) {
                cout << "شكراً لاستخدام الحاسبة!" << endl;
                break;
            }

            switch (choice) {
                case 1:
                    cout << "الرقم الأول: ";
                    cin >> a;
                    cout << "الرقم الثاني: ";
                    cin >> b;
                    result = add(a, b);
                    cout << "النتيجة: " << fixed << setprecision(2) << result << endl;
                    break;

                case 2:
                    cout << "الرقم الأول: ";
                    cin >> a;
                    cout << "الرقم الثاني: ";
                    cin >> b;
                    result = subtract(a, b);
                    cout << "النتيجة: " << fixed << setprecision(2) << result << endl;
                    break;

                case 3:
                    cout << "الرقم الأول: ";
                    cin >> a;
                    cout << "الرقم الثاني: ";
                    cin >> b;
                    result = multiply(a, b);
                    cout << "النتيجة: " << fixed << setprecision(2) << result << endl;
                    break;

                case 4:
                    cout << "الرقم الأول: ";
                    cin >> a;
                    cout << "الرقم الثاني: ";
                    cin >> b;
                    result = divide(a, b);
                    if (b != 0) {
                        cout << "النتيجة: " << fixed << setprecision(2) << result << endl;
                    }
                    break;

                case 5:
                    cout << "الأساس: ";
                    cin >> a;
                    cout << "الأس: ";
                    cin >> b;
                    result = power(a, b);
                    cout << "النتيجة: " << fixed << setprecision(2) << result << endl;
                    break;

                case 6:
                    cout << "الرقم: ";
                    cin >> a;
                    result = squareRoot(a);
                    if (a >= 0) {
                        cout << "النتيجة: " << fixed << setprecision(2) << result << endl;
                    }
                    break;

                case 7:
                    showHistory();
                    break;

                case 8:
                    clearHistory();
                    break;

                default:
                    cout << "اختيار غير صحيح!" << endl;
                    break;
            }
        }
    }
};

int main() {
    Calculator calc;
    calc.run();
    return 0;
}`;
        } else {
            // كود عام حسب الموضوع
            return `/*
 * ${topic} - C++ Program
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

#include <iostream>
#include <string>
#include <vector>

using namespace std;

class ${topic.replace(/[^a-zA-Z0-9]/g, '')} {
private:
    string name;
    string createdAt;

public:
    ${topic.replace(/[^a-zA-Z0-9]/g, '')}() {
        name = "${topic}";
        createdAt = "${new Date().toLocaleDateString('ar-SA')}";
        cout << "تم إنشاء " << name << " بنجاح!" << endl;
    }

    bool process() {
        cout << "جاري معالجة " << name << "..." << endl;

        // إضافة الكود المطلوب هنا

        return true;
    }

    void displayInfo() {
        cout << "الاسم: " << name << endl;
        cout << "تاريخ الإنشاء: " << createdAt << endl;
    }

    bool run() {
        cout << "🚀 بدء تشغيل " << name << endl;
        displayInfo();

        bool result = process();

        if (result) {
            cout << "✅ تم تشغيل " << name << " بنجاح!" << endl;
        } else {
            cout << "❌ فشل في تشغيل " << name << endl;
        }

        return result;
    }
};

int main() {
    ${topic.replace(/[^a-zA-Z0-9]/g, '')} app;
    app.run();

    cout << "اضغط Enter للخروج...";
    cin.get();

    return 0;
}`;
        }
    }

    // تنسيق C#
    formatAsCSharp(content, topic) {
        // التحقق من وجود كود C# حقيقي
        if (content.includes('using ') || content.includes('class ') || content.includes('Console.') || content.includes('namespace ') || content.includes('static void Main')) {
            // المحتوى يحتوي على كود C# حقيقي
            return `/*
 * ${topic} - C# Program
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}`;
        } else {
            // المحتوى نص عام، إنشاء كود C# ذكي
            return this.generateSmartCSharpCode(topic, content);
        }
    }

    // إنشاء كود C# ذكي حسب الموضوع
    generateSmartCSharpCode(topic, description) {
        const className = topic.replace(/[^a-zA-Z0-9]/g, '');
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('حاسبة') || lowerTopic.includes('calculator')) {
            return `/*
 * ${topic} - حاسبة احترافية
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

using System;
using System.Collections.Generic;

namespace CalculatorApp
{
    public class Calculator
    {
        private List<string> history;

        public Calculator()
        {
            history = new List<string>();
            Console.WriteLine("🔢 مرحباً بك في ${topic}");
        }

        public double Add(double a, double b)
        {
            double result = a + b;
            string operation = $"{a} + {b} = {result}";
            history.Add(operation);
            return result;
        }

        public double Subtract(double a, double b)
        {
            double result = a - b;
            string operation = $"{a} - {b} = {result}";
            history.Add(operation);
            return result;
        }

        public double Multiply(double a, double b)
        {
            double result = a * b;
            string operation = $"{a} × {b} = {result}";
            history.Add(operation);
            return result;
        }

        public double Divide(double a, double b)
        {
            if (b == 0)
            {
                Console.WriteLine("خطأ: لا يمكن القسمة على صفر!");
                return 0;
            }
            double result = a / b;
            string operation = $"{a} ÷ {b} = {result}";
            history.Add(operation);
            return result;
        }

        public double Power(double baseNum, double exponent)
        {
            double result = Math.Pow(baseNum, exponent);
            string operation = $"{baseNum}^{exponent} = {result}";
            history.Add(operation);
            return result;
        }

        public double SquareRoot(double number)
        {
            if (number < 0)
            {
                Console.WriteLine("خطأ: لا يمكن حساب الجذر التربيعي لرقم سالب!");
                return 0;
            }
            double result = Math.Sqrt(number);
            string operation = $"√{number} = {result}";
            history.Add(operation);
            return result;
        }

        public void ShowHistory()
        {
            Console.WriteLine("\\n📋 تاريخ العمليات:");
            if (history.Count == 0)
            {
                Console.WriteLine("لا توجد عمليات سابقة");
            }
            else
            {
                for (int i = 0; i < history.Count; i++)
                {
                    Console.WriteLine($"{i + 1}. {history[i]}");
                }
            }
        }

        public void ClearHistory()
        {
            history.Clear();
            Console.WriteLine("تم مسح تاريخ العمليات");
        }

        public void ShowMenu()
        {
            Console.WriteLine("\\n=== ${topic} ===");
            Console.WriteLine("1. الجمع (+)");
            Console.WriteLine("2. الطرح (-)");
            Console.WriteLine("3. الضرب (×)");
            Console.WriteLine("4. القسمة (÷)");
            Console.WriteLine("5. القوة (^)");
            Console.WriteLine("6. الجذر التربيعي (√)");
            Console.WriteLine("7. عرض التاريخ");
            Console.WriteLine("8. مسح التاريخ");
            Console.WriteLine("0. خروج");
            Console.Write("اختر العملية: ");
        }

        public void Run()
        {
            int choice;
            double a, b, result;

            while (true)
            {
                ShowMenu();

                if (!int.TryParse(Console.ReadLine(), out choice))
                {
                    Console.WriteLine("يرجى إدخال رقم صحيح!");
                    continue;
                }

                if (choice == 0)
                {
                    Console.WriteLine("شكراً لاستخدام الحاسبة!");
                    break;
                }

                try
                {
                    switch (choice)
                    {
                        case 1:
                            Console.Write("الرقم الأول: ");
                            a = Convert.ToDouble(Console.ReadLine());
                            Console.Write("الرقم الثاني: ");
                            b = Convert.ToDouble(Console.ReadLine());
                            result = Add(a, b);
                            Console.WriteLine($"النتيجة: {result:F2}");
                            break;

                        case 2:
                            Console.Write("الرقم الأول: ");
                            a = Convert.ToDouble(Console.ReadLine());
                            Console.Write("الرقم الثاني: ");
                            b = Convert.ToDouble(Console.ReadLine());
                            result = Subtract(a, b);
                            Console.WriteLine($"النتيجة: {result:F2}");
                            break;

                        case 3:
                            Console.Write("الرقم الأول: ");
                            a = Convert.ToDouble(Console.ReadLine());
                            Console.Write("الرقم الثاني: ");
                            b = Convert.ToDouble(Console.ReadLine());
                            result = Multiply(a, b);
                            Console.WriteLine($"النتيجة: {result:F2}");
                            break;

                        case 4:
                            Console.Write("الرقم الأول: ");
                            a = Convert.ToDouble(Console.ReadLine());
                            Console.Write("الرقم الثاني: ");
                            b = Convert.ToDouble(Console.ReadLine());
                            result = Divide(a, b);
                            if (b != 0)
                            {
                                Console.WriteLine($"النتيجة: {result:F2}");
                            }
                            break;

                        case 5:
                            Console.Write("الأساس: ");
                            a = Convert.ToDouble(Console.ReadLine());
                            Console.Write("الأس: ");
                            b = Convert.ToDouble(Console.ReadLine());
                            result = Power(a, b);
                            Console.WriteLine($"النتيجة: {result:F2}");
                            break;

                        case 6:
                            Console.Write("الرقم: ");
                            a = Convert.ToDouble(Console.ReadLine());
                            result = SquareRoot(a);
                            if (a >= 0)
                            {
                                Console.WriteLine($"النتيجة: {result:F2}");
                            }
                            break;

                        case 7:
                            ShowHistory();
                            break;

                        case 8:
                            ClearHistory();
                            break;

                        default:
                            Console.WriteLine("اختيار غير صحيح!");
                            break;
                    }
                }
                catch (FormatException)
                {
                    Console.WriteLine("خطأ: يرجى إدخال رقم صحيح!");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"حدث خطأ: {ex.Message}");
                }
            }
        }
    }

    class Program
    {
        static void Main(string[] args)
        {
            Calculator calc = new Calculator();
            calc.Run();
        }
    }
}`;
        } else {
            // كود عام حسب الموضوع
            return `/*
 * ${topic} - C# Program
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

using System;

namespace ${className}App
{
    public class ${className}
    {
        private string name;
        private string createdAt;

        public ${className}()
        {
            name = "${topic}";
            createdAt = "${new Date().toLocaleDateString('ar-SA')}";
            Console.WriteLine($"تم إنشاء {name} بنجاح!");
        }

        public bool Process()
        {
            Console.WriteLine($"جاري معالجة {name}...");

            // إضافة الكود المطلوب هنا

            return true;
        }

        public void DisplayInfo()
        {
            Console.WriteLine($"الاسم: {name}");
            Console.WriteLine($"تاريخ الإنشاء: {createdAt}");
        }

        public bool Run()
        {
            Console.WriteLine($"🚀 بدء تشغيل {name}");
            DisplayInfo();

            bool result = Process();

            if (result)
            {
                Console.WriteLine($"✅ تم تشغيل {name} بنجاح!");
            }
            else
            {
                Console.WriteLine($"❌ فشل في تشغيل {name}");
            }

            return result;
        }
    }

    class Program
    {
        static void Main(string[] args)
        {
            ${className} app = new ${className}();
            app.Run();

            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}`;
        }
    }

    // تنسيق PHP
    formatAsPHP(content, topic) {
        // التحقق من وجود كود PHP حقيقي
        if (content.includes('<?php') || content.includes('echo ') || content.includes('function ') || content.includes('class ') || content.includes('$')) {
            // المحتوى يحتوي على كود PHP حقيقي
            return `<?php
/*
 * ${topic} - PHP Script
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

${content}

?>`;
        } else {
            // المحتوى نص عام، إنشاء كود PHP ذكي
            return this.generateSmartPHPCode(topic, content);
        }
    }

    // إنشاء كود PHP ذكي حسب الموضوع
    generateSmartPHPCode(topic, description) {
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('حاسبة') || lowerTopic.includes('calculator')) {
            return `<?php
/*
 * ${topic} - حاسبة احترافية
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

class Calculator {
    private $history = [];

    public function __construct() {
        echo "🔢 مرحباً بك في ${topic}\\n";
    }

    public function add($a, $b) {
        $result = $a + $b;
        $operation = "$a + $b = $result";
        $this->history[] = $operation;
        return $result;
    }

    public function subtract($a, $b) {
        $result = $a - $b;
        $operation = "$a - $b = $result";
        $this->history[] = $operation;
        return $result;
    }

    public function multiply($a, $b) {
        $result = $a * $b;
        $operation = "$a × $b = $result";
        $this->history[] = $operation;
        return $result;
    }

    public function divide($a, $b) {
        if ($b == 0) {
            echo "خطأ: لا يمكن القسمة على صفر!\\n";
            return 0;
        }
        $result = $a / $b;
        $operation = "$a ÷ $b = $result";
        $this->history[] = $operation;
        return $result;
    }

    public function power($base, $exponent) {
        $result = pow($base, $exponent);
        $operation = "$base^$exponent = $result";
        $this->history[] = $operation;
        return $result;
    }

    public function squareRoot($number) {
        if ($number < 0) {
            echo "خطأ: لا يمكن حساب الجذر التربيعي لرقم سالب!\\n";
            return 0;
        }
        $result = sqrt($number);
        $operation = "√$number = $result";
        $this->history[] = $operation;
        return $result;
    }

    public function showHistory() {
        echo "\\n📋 تاريخ العمليات:\\n";
        if (empty($this->history)) {
            echo "لا توجد عمليات سابقة\\n";
        } else {
            foreach ($this->history as $index => $operation) {
                echo ($index + 1) . ". $operation\\n";
            }
        }
    }

    public function clearHistory() {
        $this->history = [];
        echo "تم مسح تاريخ العمليات\\n";
    }

    public function showMenu() {
        echo "\\n=== ${topic} ===\\n";
        echo "1. الجمع (+)\\n";
        echo "2. الطرح (-)\\n";
        echo "3. الضرب (×)\\n";
        echo "4. القسمة (÷)\\n";
        echo "5. القوة (^)\\n";
        echo "6. الجذر التربيعي (√)\\n";
        echo "7. عرض التاريخ\\n";
        echo "8. مسح التاريخ\\n";
        echo "0. خروج\\n";
        echo "اختر العملية: ";
    }

    public function getInput() {
        return trim(fgets(STDIN));
    }

    public function run() {
        while (true) {
            $this->showMenu();
            $choice = (int)$this->getInput();

            if ($choice == 0) {
                echo "شكراً لاستخدام الحاسبة!\\n";
                break;
            }

            switch ($choice) {
                case 1:
                    echo "الرقم الأول: ";
                    $a = (float)$this->getInput();
                    echo "الرقم الثاني: ";
                    $b = (float)$this->getInput();
                    $result = $this->add($a, $b);
                    echo "النتيجة: " . number_format($result, 2) . "\\n";
                    break;

                case 2:
                    echo "الرقم الأول: ";
                    $a = (float)$this->getInput();
                    echo "الرقم الثاني: ";
                    $b = (float)$this->getInput();
                    $result = $this->subtract($a, $b);
                    echo "النتيجة: " . number_format($result, 2) . "\\n";
                    break;

                case 3:
                    echo "الرقم الأول: ";
                    $a = (float)$this->getInput();
                    echo "الرقم الثاني: ";
                    $b = (float)$this->getInput();
                    $result = $this->multiply($a, $b);
                    echo "النتيجة: " . number_format($result, 2) . "\\n";
                    break;

                case 4:
                    echo "الرقم الأول: ";
                    $a = (float)$this->getInput();
                    echo "الرقم الثاني: ";
                    $b = (float)$this->getInput();
                    $result = $this->divide($a, $b);
                    if ($b != 0) {
                        echo "النتيجة: " . number_format($result, 2) . "\\n";
                    }
                    break;

                case 5:
                    echo "الأساس: ";
                    $a = (float)$this->getInput();
                    echo "الأس: ";
                    $b = (float)$this->getInput();
                    $result = $this->power($a, $b);
                    echo "النتيجة: " . number_format($result, 2) . "\\n";
                    break;

                case 6:
                    echo "الرقم: ";
                    $a = (float)$this->getInput();
                    $result = $this->squareRoot($a);
                    if ($a >= 0) {
                        echo "النتيجة: " . number_format($result, 2) . "\\n";
                    }
                    break;

                case 7:
                    $this->showHistory();
                    break;

                case 8:
                    $this->clearHistory();
                    break;

                default:
                    echo "اختيار غير صحيح!\\n";
                    break;
            }
        }
    }
}

// تشغيل الحاسبة
$calc = new Calculator();
$calc->run();

?>`;
        } else {
            // كود عام حسب الموضوع
            const className = topic.replace(/[^a-zA-Z0-9]/g, '');
            return `<?php
/*
 * ${topic} - PHP Script
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 * التاريخ: ${new Date().toLocaleDateString('ar-SA')}
 */

class ${className} {
    private $name;
    private $createdAt;

    public function __construct() {
        $this->name = "${topic}";
        $this->createdAt = "${new Date().toLocaleDateString('ar-SA')}";
        echo "تم إنشاء " . $this->name . " بنجاح!\\n";
    }

    public function process() {
        echo "جاري معالجة " . $this->name . "...\\n";

        // إضافة الكود المطلوب هنا

        return true;
    }

    public function displayInfo() {
        echo "الاسم: " . $this->name . "\\n";
        echo "تاريخ الإنشاء: " . $this->createdAt . "\\n";
    }

    public function run() {
        echo "🚀 بدء تشغيل " . $this->name . "\\n";
        $this->displayInfo();

        $result = $this->process();

        if ($result) {
            echo "✅ تم تشغيل " . $this->name . " بنجاح!\\n";
        } else {
            echo "❌ فشل في تشغيل " . $this->name . "\\n";
        }

        return $result;
    }
}

// تشغيل التطبيق
$app = new ${className}();
$app->run();

?>`;
        }
    }

    // تنسيق SQL
    formatAsSQL(content, topic) {
        return `-- ${topic} - SQL Script
-- تم إنشاؤه بواسطة المساعد التقني الذكي
-- التاريخ: ${new Date().toLocaleDateString('ar-SA')}

${content}

-- نهاية السكريبت`;
    }

    // تنسيق YAML
    formatAsYAML(content, topic) {
        return `# ${topic} - YAML Configuration
# تم إنشاؤه بواسطة المساعد التقني الذكي
# التاريخ: ${new Date().toLocaleDateString('ar-SA')}

${content}`;
    }

    // تنسيق Shell Script
    formatAsShell(content, topic) {
        return `#!/bin/bash
# ${topic} - Shell Script
# تم إنشاؤه بواسطة المساعد التقني الذكي
# التاريخ: ${new Date().toLocaleDateString('ar-SA')}

${content}

# نهاية السكريبت`;
    }

    // تنسيق PowerShell
    formatAsPowerShell(content, topic) {
        return `# ${topic} - PowerShell Script
# تم إنشاؤه بواسطة المساعد التقني الذكي
# التاريخ: ${new Date().toLocaleDateString('ar-SA')}

${content}

# نهاية السكريبت`;
    }

    // تنسيق XML
    formatAsXML(content, topic) {
        return `<?xml version="1.0" encoding="UTF-8"?>
<!-- ${topic} - XML Data File -->
<!-- تم إنشاؤه بواسطة المساعد التقني الذكي -->
<!-- التاريخ: ${new Date().toLocaleDateString('ar-SA')} -->

${content}`;
    }

    // تنسيق نص عادي
    formatAsText(content, topic) {
        return `${topic}
${'='.repeat(topic.length)}

${content}

---
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}`;
    }

    // الحصول على اسم نوع الملف
    getFileTypeName(fileType) {
        const names = {
            'html': 'صفحة HTML',
            'css': 'ملف CSS',
            'javascript': 'سكريبت JavaScript',
            'python': 'برنامج Python',
            'java': 'برنامج Java',
            'json': 'ملف JSON',
            'pdf': 'تقرير PDF',
            'text': 'ملف نصي'
        };
        return names[fileType] || 'ملف';
    }

    // إنشاء محتوى افتراضي حسب النوع
    generateDefaultContentByType(topic, fileType) {
        const defaultContents = {
            'html': this.generateSmartHTMLContent(topic),
            'css': `/* تنسيق ${topic} */\nbody { font-family: Arial, sans-serif; }`,
            'javascript': this.generateSmartJavaScriptContent(topic),
            'python': this.generateSmartPythonContent(topic),
            'java': `// ${topic}\npublic class Main {\n    public static void main(String[] args) {\n        System.out.println("مرحباً من ${topic}");\n    }\n}`,
            'cpp': `// ${topic}\n#include <iostream>\nusing namespace std;\n\nint main() {\n    cout << "مرحباً من ${topic}" << endl;\n    return 0;\n}`,
            'csharp': `// ${topic}\nusing System;\n\nclass Program {\n    static void Main() {\n        Console.WriteLine("مرحباً من ${topic}");\n    }\n}`,
            'php': `<?php\n// ${topic}\necho "مرحباً من ${topic}";\n?>`,
            'sql': `-- ${topic}\nSELECT '${topic}' as message;`,
            'yaml': `# ${topic}\ntitle: "${topic}"\ndescription: "محتوى افتراضي"`,
            'shell': `#!/bin/bash\n# ${topic}\necho "مرحباً من ${topic}"`,
            'powershell': `# ${topic}\nWrite-Host "مرحباً من ${topic}"`,
            'xml': `<?xml version="1.0"?>\n<root>\n  <title>${topic}</title>\n</root>`,
            'json': this.formatAsJSON('', topic),
            'pdf': `هذا تقرير افتراضي عن ${topic}.\n\nيحتوي على معلومات أساسية ومفيدة.`,
            'text': `${topic}\n\nهذا محتوى نصي افتراضي عن ${topic}.`
        };
        return defaultContents[fileType] || defaultContents['text'];
    }

    // إنشاء محتوى HTML ذكي
    generateSmartHTMLContent(topic) {
        const lowerTopic = topic.toLowerCase();

        if (lowerTopic.includes('أمن') || lowerTopic.includes('سيبراني') || lowerTopic.includes('حماية')) {
            return `<div class="cybersecurity-content">
                <h1>🔒 الأمن السيبراني - دليل شامل</h1>

                <div class="section">
                    <h2>📋 تعريف الأمن السيبراني</h2>
                    <p>الأمن السيبراني هو ممارسة حماية الأنظمة والشبكات والبرامج من الهجمات الرقمية. تهدف هذه الهجمات عادة إلى الوصول إلى المعلومات الحساسة أو تغييرها أو تدميرها أو ابتزاز المال من المستخدمين أو مقاطعة العمليات التجارية العادية.</p>
                </div>

                <div class="section">
                    <h2>🛡️ المبادئ الأساسية للأمن السيبراني</h2>
                    <ul class="principles-list">
                        <li><strong>السرية (Confidentiality):</strong> ضمان وصول المعلومات للأشخاص المخولين فقط</li>
                        <li><strong>التكامل (Integrity):</strong> ضمان دقة وكمال المعلومات وعدم تعديلها بطريقة غير مصرح بها</li>
                        <li><strong>التوفر (Availability):</strong> ضمان توفر المعلومات والأنظمة عند الحاجة إليها</li>
                    </ul>
                </div>

                <div class="section">
                    <h2>⚠️ أنواع التهديدات السيبرانية</h2>
                    <div class="threats-grid">
                        <div class="threat-item">
                            <h3>البرمجيات الخبيثة (Malware)</h3>
                            <p>فيروسات، ديدان، أحصنة طروادة، وبرامج التجسس</p>
                        </div>
                        <div class="threat-item">
                            <h3>هجمات التصيد (Phishing)</h3>
                            <p>محاولات خداع للحصول على معلومات حساسة</p>
                        </div>
                        <div class="threat-item">
                            <h3>هجمات DDoS</h3>
                            <p>إغراق الخوادم بحركة مرور مفرطة</p>
                        </div>
                        <div class="threat-item">
                            <h3>اختراق كلمات المرور</h3>
                            <p>استخدام تقنيات مختلفة لكسر كلمات المرور</p>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>🔧 أدوات وتقنيات الحماية</h2>
                    <div class="tools-list">
                        <div class="tool-category">
                            <h3>حماية الشبكة</h3>
                            <ul>
                                <li>جدران الحماية (Firewalls)</li>
                                <li>أنظمة كشف التطفل (IDS/IPS)</li>
                                <li>شبكات VPN الآمنة</li>
                            </ul>
                        </div>
                        <div class="tool-category">
                            <h3>حماية البيانات</h3>
                            <ul>
                                <li>التشفير (Encryption)</li>
                                <li>النسخ الاحتياطية الآمنة</li>
                                <li>إدارة المفاتيح</li>
                            </ul>
                        </div>
                        <div class="tool-category">
                            <h3>إدارة الهوية</h3>
                            <ul>
                                <li>المصادقة متعددة العوامل (MFA)</li>
                                <li>إدارة الصلاحيات</li>
                                <li>مراقبة الوصول</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>💡 أفضل الممارسات</h2>
                    <div class="best-practices">
                        <div class="practice-item">
                            <h4>للأفراد:</h4>
                            <ul>
                                <li>استخدام كلمات مرور قوية ومعقدة</li>
                                <li>تحديث البرامج والأنظمة بانتظام</li>
                                <li>الحذر من الروابط والمرفقات المشبوهة</li>
                                <li>استخدام برامج مكافحة الفيروسات</li>
                            </ul>
                        </div>
                        <div class="practice-item">
                            <h4>للمؤسسات:</h4>
                            <ul>
                                <li>وضع سياسات أمنية واضحة</li>
                                <li>تدريب الموظفين على الوعي الأمني</li>
                                <li>إجراء تقييمات أمنية دورية</li>
                                <li>تطوير خطط الاستجابة للحوادث</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>🔮 مستقبل الأمن السيبراني</h2>
                    <p>يشهد مجال الأمن السيبراني تطوراً مستمراً مع ظهور تقنيات جديدة مثل:</p>
                    <ul>
                        <li>الذكاء الاصطناعي في الأمن السيبراني</li>
                        <li>أمن إنترنت الأشياء (IoT Security)</li>
                        <li>الحوسبة الكمية وتأثيرها على التشفير</li>
                        <li>أمن الحوسبة السحابية</li>
                        <li>التهديدات المتقدمة المستمرة (APT)</li>
                    </ul>
                </div>
            </div>`;
        }

        if (lowerTopic.includes('ذكاء') || lowerTopic.includes('ai') || lowerTopic.includes('تعلم')) {
            return `<div class="ai-content">
                <h1>🤖 الذكاء الاصطناعي - دليل شامل</h1>

                <div class="section">
                    <h2>📋 تعريف الذكاء الاصطناعي</h2>
                    <p>الذكاء الاصطناعي هو فرع من علوم الحاسوب يهدف إلى إنشاء أنظمة قادرة على أداء مهام تتطلب عادة ذكاءً بشرياً، مثل التعلم والاستدلال وحل المشكلات.</p>
                </div>

                <div class="section">
                    <h2>🧠 أنواع الذكاء الاصطناعي</h2>
                    <div class="ai-types">
                        <div class="ai-type">
                            <h3>الذكاء الضيق (ANI)</h3>
                            <p>متخصص في مهام محددة مثل محركات البحث والتعرف على الصوت</p>
                        </div>
                        <div class="ai-type">
                            <h3>الذكاء العام (AGI)</h3>
                            <p>يضاهي القدرات البشرية في جميع المجالات المعرفية</p>
                        </div>
                        <div class="ai-type">
                            <h3>الذكاء الفائق (ASI)</h3>
                            <p>يتجاوز الذكاء البشري في جميع الجوانب</p>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>🔬 تقنيات الذكاء الاصطناعي</h2>
                    <ul>
                        <li><strong>التعلم الآلي (Machine Learning):</strong> تعلم الأنماط من البيانات</li>
                        <li><strong>التعلم العميق (Deep Learning):</strong> شبكات عصبية متعددة الطبقات</li>
                        <li><strong>معالجة اللغات الطبيعية (NLP):</strong> فهم ومعالجة اللغة البشرية</li>
                        <li><strong>الرؤية الحاسوبية:</strong> تحليل وفهم الصور والفيديو</li>
                    </ul>
                </div>

                <div class="section">
                    <h2>🌍 تطبيقات الذكاء الاصطناعي</h2>
                    <div class="applications-grid">
                        <div class="app-item">
                            <h3>الطب</h3>
                            <p>التشخيص المبكر، تطوير الأدوية، الجراحة الروبوتية</p>
                        </div>
                        <div class="app-item">
                            <h3>النقل</h3>
                            <p>السيارات ذاتية القيادة، تحسين المرور، الطيران الآلي</p>
                        </div>
                        <div class="app-item">
                            <h3>التمويل</h3>
                            <p>كشف الاحتيال، تحليل المخاطر، التداول الآلي</p>
                        </div>
                        <div class="app-item">
                            <h3>التعليم</h3>
                            <p>التعلم المخصص، التقييم الذكي، المساعدين الافتراضيين</p>
                        </div>
                    </div>
                </div>
            </div>`;
        }

        // محتوى HTML عام للمواضيع الأخرى
        return `<div class="general-content">
            <h1>📚 ${topic} - دليل شامل</h1>

            <div class="section">
                <h2>🎯 مقدمة</h2>
                <p>هذا دليل شامل ومفصل حول موضوع <strong>${topic}</strong>، تم إعداده بعناية لتقديم معلومات دقيقة ومفيدة.</p>
            </div>

            <div class="section">
                <h2>📖 المحتوى الأساسي</h2>
                <ul>
                    <li>تعريف شامل للموضوع</li>
                    <li>الأهمية والفوائد</li>
                    <li>التطبيقات العملية</li>
                    <li>أفضل الممارسات</li>
                    <li>التحديات والحلول</li>
                </ul>
            </div>

            <div class="section">
                <h2>💡 النقاط الرئيسية</h2>
                <div class="key-points">
                    <div class="point">
                        <h3>معلومات محدثة</h3>
                        <p>محتوى دقيق ومحدث يواكب أحدث التطورات في المجال</p>
                    </div>
                    <div class="point">
                        <h3>أمثلة عملية</h3>
                        <p>تطبيقات وأمثلة واقعية تساعد في فهم الموضوع</p>
                    </div>
                    <div class="point">
                        <h3>نصائح مفيدة</h3>
                        <p>إرشادات عملية قابلة للتطبيق المباشر</p>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🔍 التفاصيل المهمة</h2>
                <p>يتضمن هذا الدليل تحليلاً عميقاً لجميع جوانب <strong>${topic}</strong>، مع التركيز على الجوانب العملية والتطبيقية التي تفيد القارئ في فهم الموضوع بشكل شامل.</p>
            </div>

            <div class="section">
                <h2>📊 الخلاصة والتوصيات</h2>
                <ul>
                    <li>تطبيق المعرفة المكتسبة عملياً</li>
                    <li>متابعة التطورات الحديثة في المجال</li>
                    <li>الاستفادة من الموارد المتاحة</li>
                    <li>التطوير المستمر للمهارات ذات الصلة</li>
                </ul>
            </div>
        </div>`;
    }

    // إنشاء محتوى JSON ذكي
    generateSmartJSONContent(topic) {
        if (topic.toLowerCase().includes('أمن') || topic.toLowerCase().includes('سيبراني')) {
            return JSON.stringify({
                "title": "الأمن السيبراني",
                "description": "دليل شامل للأمن السيبراني",
                "created_at": new Date().toISOString(),
                "security_principles": [
                    { "name": "السرية", "description": "حماية المعلومات من الوصول غير المصرح" },
                    { "name": "التكامل", "description": "ضمان دقة وسلامة البيانات" },
                    { "name": "التوفر", "description": "ضمان إمكانية الوصول للمعلومات عند الحاجة" }
                ],
                "threats": [
                    { "name": "البرمجيات الخبيثة", "severity": "عالي" },
                    { "name": "التصيد الاحتيالي", "severity": "متوسط" },
                    { "name": "هجمات DDoS", "severity": "عالي" }
                ]
            }, null, 2);
        } else if (topic.toLowerCase().includes('ذكاء') || topic.toLowerCase().includes('ai')) {
            return JSON.stringify({
                "title": "الذكاء الاصطناعي",
                "description": "دليل شامل للذكاء الاصطناعي",
                "created_at": new Date().toISOString(),
                "ai_types": [
                    { "type": "ANI", "name": "الذكاء الضيق" },
                    { "type": "AGI", "name": "الذكاء العام" },
                    { "type": "ASI", "name": "الذكاء الفائق" }
                ]
            }, null, 2);
        } else {
            return JSON.stringify({
                "title": topic,
                "description": `دليل شامل حول ${topic}`,
                "created_at": new Date().toISOString(),
                "content": {
                    "introduction": `مقدمة شاملة حول ${topic}`,
                    "main_points": [
                        `النقطة الأولى حول ${topic}`,
                        `النقطة الثانية حول ${topic}`
                    ]
                }
            }, null, 2);
        }
    }

    // إنشاء محتوى JavaScript ذكي
    generateSmartJavaScriptContent(topic) {
        if (topic.toLowerCase().includes('حاسبة') || topic.toLowerCase().includes('calculator')) {
            return `/**
 * حاسبة JavaScript احترافية
 * الموضوع: ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class Calculator {
    constructor() {
        this.result = 0;
        this.history = [];
    }

    add(a, b) {
        const result = a + b;
        this.history.push(\`\${a} + \${b} = \${result}\`);
        return result;
    }

    subtract(a, b) {
        const result = a - b;
        this.history.push(\`\${a} - \${b} = \${result}\`);
        return result;
    }

    multiply(a, b) {
        const result = a * b;
        this.history.push(\`\${a} × \${b} = \${result}\`);
        return result;
    }

    divide(a, b) {
        if (b === 0) throw new Error('لا يمكن القسمة على صفر');
        const result = a / b;
        this.history.push(\`\${a} ÷ \${b} = \${result}\`);
        return result;
    }

    showHistory() {
        console.log('تاريخ العمليات:', this.history);
    }
}

// استخدام الحاسبة
const calc = new Calculator();
console.log('مرحباً من ${topic}');
console.log('الجمع:', calc.add(10, 5));
console.log('الطرح:', calc.subtract(10, 3));`;
        } else {
            return `/**
 * ${topic} - JavaScript Application
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

class ${topic.replace(/\s+/g, '')}App {
    constructor() {
        this.name = '${topic}';
        this.version = '1.0.0';
        this.isInitialized = false;
    }

    init() {
        console.log(\`تهيئة \${this.name}...\`);
        this.isInitialized = true;
        console.log('تم تهيئة التطبيق بنجاح');
        return this;
    }

    run() {
        if (!this.isInitialized) {
            console.log('يجب تهيئة التطبيق أولاً');
            return false;
        }
        console.log(\`تشغيل \${this.name}...\`);
        this.mainFunction();
        return true;
    }

    mainFunction() {
        console.log('تنفيذ الوظيفة الرئيسية...');
        // إضافة منطق التطبيق هنا
    }
}

// تشغيل التطبيق
const app = new ${topic.replace(/\s+/g, '')}App();
app.init().run();`;
        }
    }

    // إنشاء محتوى Python ذكي
    generateSmartPythonContent(topic) {
        if (topic.toLowerCase().includes('حذف ملف')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج Python لحذف ملف معين
الموضوع: ${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import os
import sys

def delete_file(file_path):
    """حذف ملف معين من النظام"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"✅ تم حذف الملف بنجاح: {file_path}")
            return True
        else:
            print(f"❌ الملف غير موجود: {file_path}")
            return False
    except PermissionError:
        print(f"❌ ليس لديك صلاحية لحذف هذا الملف: {file_path}")
        return False
    except Exception as e:
        print(f"❌ خطأ في حذف الملف: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("🗑️ برنامج حذف الملفات")
    print("=" * 30)

    file_path = input("أدخل مسار الملف المراد حذفه: ")

    if not file_path.strip():
        print("❌ يجب إدخال مسار الملف")
        return

    confirm = input(f"هل أنت متأكد من حذف الملف '{file_path}'؟ (y/n): ")

    if confirm.lower() in ['y', 'yes', 'نعم']:
        delete_file(file_path)
    else:
        print("تم إلغاء العملية")

if __name__ == "__main__":
    main()`;
        } else {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic} - برنامج Python
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import os
import sys
import datetime

class ${topic.replace(/\s+/g, '')}App:
    """فئة التطبيق الرئيسية"""

    def __init__(self):
        self.name = "${topic}"
        self.version = "1.0.0"
        self.created_at = datetime.datetime.now()

    def display_info(self):
        """عرض معلومات التطبيق"""
        print(f"📱 اسم التطبيق: {self.name}")
        print(f"🔢 الإصدار: {self.version}")
        print(f"📅 تاريخ الإنشاء: {self.created_at}")

    def main_function(self):
        """الوظيفة الرئيسية للتطبيق"""
        print(f"🚀 تشغيل {self.name}...")
        print("✅ تم تشغيل التطبيق بنجاح!")
        return True

    def run(self):
        """تشغيل التطبيق"""
        print("=" * 50)
        print(f"🐍 مرحباً بك في {self.name}")
        print("=" * 50)

        self.display_info()
        print("\\n" + "-" * 30)

        try:
            result = self.main_function()
            if result:
                print("\\n✅ تم إنهاء التطبيق بنجاح")
            else:
                print("\\n❌ حدث خطأ في التطبيق")
        except Exception as e:
            print(f"\\n❌ خطأ: {e}")

def main():
    """نقطة دخول البرنامج"""
    app = ${topic.replace(/\s+/g, '')}App()
    app.run()

if __name__ == "__main__":
    main()`;
        }
    }

    // إنشاء تحميل تجريبي لإظهار الحاوية الاحترافية
    createTestDownload(userRequest) {
        // تحليل نوع الملف من الطلب
        const fileType = this.detectFileType(userRequest);
        const topic = this.extractTopic(userRequest);
        const fileInfo = this.getFileTypeInfo(fileType);

        // إنشاء محتوى تجريبي
        const content = this.generateDefaultContentByType(topic, fileType);
        const filename = `${topic.replace(/\s+/g, '_')}${fileInfo.extension}`;

        // إنشاء الملف والحاوية الاحترافية
        const blob = new Blob([content], { type: fileInfo.mimeType });
        const url = URL.createObjectURL(blob);

        // إظهار الحاوية الاحترافية مباشرة
        this.createProfessionalDownloadContainer(filename, url, blob.size);
    }

    // إضافة رسالة تشخيص للمحادثة
    addDiagnosticMessage(message) {
        // البحث عن وظيفة إضافة الرسائل
        if (typeof window.addMessageToChat === 'function') {
            window.addMessageToChat('assistant', message);
        } else if (typeof addMessage === 'function') {
            addMessage('assistant', message);
        } else {
            // إضافة مباشرة للمحادثة إذا وجدت
            const chatContainer = document.getElementById('chatContainer') ||
                                 document.querySelector('.chat-container') ||
                                 document.querySelector('#chat-messages');

            if (chatContainer) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant';
                messageDiv.style.cssText = `
                    margin: 10px 0;
                    padding: 10px 15px;
                    background: #f0f8ff;
                    border-radius: 10px;
                    border-left: 4px solid #007bff;
                    font-family: monospace;
                    font-size: 12px;
                    color: #333;
                `;
                messageDiv.innerHTML = message.replace(/\n/g, '<br>');
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }
    }

    // Create JSON file with AI-generated content
    async createJSONFileWithContent(topic, aiContent) {
        console.log('📋 إنشاء ملف JSON بالمحتوى المطلوب...');

        try {
            // تحويل المحتوى إلى JSON صالح
            let jsonContent;

            try {
                // محاولة تحليل المحتوى كـ JSON إذا كان صالح
                jsonContent = JSON.parse(aiContent);
            } catch {
                // إذا لم يكن JSON صالح، إنشاء JSON من النص
                jsonContent = {
                    "title": topic,
                    "content": aiContent,
                    "created_at": new Date().toISOString(),
                    "type": "AI Generated Content",
                    "language": "ar",
                    "metadata": {
                        "topic": topic,
                        "generated_by": "Technical Assistant AI",
                        "format": "JSON"
                    }
                };
            }

            const formattedJSON = JSON.stringify(jsonContent, null, 2);
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_data.json`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(formattedJSON, filename, 'application/json');

            if (success) {
                this.creationHistory.push({
                    type: 'JSON File',
                    name: filename,
                    created: new Date(),
                    size: formattedJSON.length
                });

                console.log('✅ تم إنشاء ملف JSON بنجاح');
                return true;
            } else {
                console.error('❌ فشل في إنشاء ملف JSON');
                return false;
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف JSON:', error);
            return false;
        }
    }

    // Create JSON file with real download (old method - keep for compatibility)
    async createJSONFile(topic, content) {
        console.log('📄 إنشاء ملف JSON احترافي...');

        try {
            // إنشاء اسم الملف
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_data.json`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(content, filename, 'application/json');

            if (success) {
                // إضافة إلى التاريخ
                this.creationHistory.push({
                    type: 'JSON File',
                    name: filename,
                    created: new Date(),
                    size: content.length
                });

                console.log('✅ تم إنشاء ملف JSON بنجاح');
                return true;
            } else {
                console.error('❌ فشل في إنشاء ملف JSON');
                return false;
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف JSON:', error);
            return false;
        }
    }

    // Create CSS file with AI-generated content
    async createCSSFileWithContent(topic, aiContent) {
        console.log('🎨 إنشاء ملف CSS بالمحتوى المطلوب...');

        try {
            // تنسيق المحتوى كـ CSS
            const formattedContent = this.formatAsCSS(aiContent, topic);
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_styles.css`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(formattedContent, filename, 'text/css');

            if (success) {
                this.creationHistory.push({
                    type: 'CSS File',
                    name: filename,
                    created: new Date(),
                    size: formattedContent.length
                });

                console.log('✅ تم إنشاء ملف CSS بنجاح');
                return true;
            } else {
                console.error('❌ فشل في إنشاء ملف CSS');
                return false;
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف CSS:', error);
            return false;
        }
    }

    // Create CSS file with real download (old method - keep for compatibility)
    async createCSSFile(topic, content) {
        console.log('🎨 إنشاء ملف CSS احترافي...');

        try {
            // تنسيق المحتوى كـ CSS
            const formattedContent = this.formatAsCSS(content, topic);
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_styles.css`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(formattedContent, filename, 'text/css');

            if (success) {
                this.creationHistory.push({
                    type: 'CSS File',
                    name: filename,
                    created: new Date(),
                    size: formattedContent.length
                });

                console.log('✅ تم إنشاء ملف CSS بنجاح');
                return true;
            } else {
                console.error('❌ فشل في إنشاء ملف CSS');
                return false;
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف CSS:', error);
            return false;
        }
    }

    // Create JavaScript file with AI-generated content
    async createJavaScriptFileWithContent(topic, aiContent) {
        console.log('⚡ إنشاء ملف JavaScript بالمحتوى المطلوب...');

        try {
            // تنسيق المحتوى كـ JavaScript
            const formattedContent = this.formatAsJavaScript(aiContent, topic);
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_script.js`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(formattedContent, filename, 'text/javascript');

            if (success) {
                this.creationHistory.push({
                    type: 'JavaScript File',
                    name: filename,
                    created: new Date(),
                    size: formattedContent.length
                });

                console.log('✅ تم إنشاء ملف JavaScript بنجاح');
                return true;
            } else {
                console.error('❌ فشل في إنشاء ملف JavaScript');
                return false;
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف JavaScript:', error);
            return false;
        }
    }

    // Create JavaScript file with real download (old method - keep for compatibility)
    async createJavaScriptFile(topic, content) {
        console.log('⚡ إنشاء ملف JavaScript احترافي...');

        try {
            // تنسيق المحتوى كـ JavaScript
            const formattedContent = this.formatAsJavaScript(content, topic);
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_script.js`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(formattedContent, filename, 'text/javascript');

            if (success) {
                this.creationHistory.push({
                    type: 'JavaScript File',
                    name: filename,
                    created: new Date(),
                    size: formattedContent.length
                });

                console.log('✅ تم إنشاء ملف JavaScript بنجاح');
                return true;
            } else {
                console.error('❌ فشل في إنشاء ملف JavaScript');
                return false;
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف JavaScript:', error);
            return false;
        }
    }

    // Create Python file with AI-generated content
    async createPythonFileWithContent(topic, aiContent) {
        console.log('🐍 إنشاء ملف Python بالمحتوى المطلوب...');

        try {
            // تنسيق المحتوى كـ Python
            const formattedContent = this.formatAsPython(aiContent, topic);
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_program.py`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(formattedContent, filename, 'text/x-python');

            if (success) {
                this.creationHistory.push({
                    type: 'Python File',
                    name: filename,
                    created: new Date(),
                    size: formattedContent.length
                });

                console.log('✅ تم إنشاء ملف Python بنجاح');
                return true;
            } else {
                console.error('❌ فشل في إنشاء ملف Python');
                return false;
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف Python:', error);
            return false;
        }
    }

    // Create Python file with real download (old method - keep for compatibility)
    async createPythonFile(topic, content) {
        console.log('🐍 إنشاء ملف Python احترافي...');

        try {
            // تنسيق المحتوى كـ Python
            const formattedContent = this.formatAsPython(content, topic);
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_program.py`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(formattedContent, filename, 'text/x-python');

            if (success) {
                this.creationHistory.push({
                    type: 'Python File',
                    name: filename,
                    created: new Date(),
                    size: formattedContent.length
                });

                console.log('✅ تم إنشاء ملف Python بنجاح');
                return true;
            } else {
                console.error('❌ فشل في إنشاء ملف Python');
                return false;
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف Python:', error);
            return false;
        }
    }

    // Create Word file with AI-generated content
    async createWordFileWithContent(topic, aiContent) {
        console.log('📄 إنشاء مستند Word بالمحتوى المطلوب...');

        // Create HTML version of Word document using AI content
        const wordHTML = this.generateWordHTMLWithContent(topic, aiContent);

        try {
            // إنشاء اسم الملف
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_document.docx`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(wordHTML, filename, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');

            if (success) {
                // إضافة إلى التاريخ
                this.creationHistory.push({
                    type: 'Word Document',
                    name: filename,
                    created: new Date(),
                    size: wordHTML.length
                });

                console.log('✅ تم إنشاء مستند Word بنجاح');
            } else {
                console.error('❌ فشل في إنشاء مستند Word');
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء مستند Word:', error);
        }
    }

    // Generate Word HTML with AI content
    generateWordHTMLWithContent(topic, aiContent) {
        const formattedContent = this.formatContentForHTML(aiContent);

        return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${topic}</title>
    <style>
        body { font-family: 'Times New Roman', serif; max-width: 800px; margin: 0 auto; padding: 40px; line-height: 1.6; }
        h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        p { text-align: justify; margin-bottom: 15px; }
        .header { text-align: center; margin-bottom: 40px; }
        .footer { text-align: center; margin-top: 40px; font-size: 12px; color: #7f8c8d; }
        @media print { body { margin: 0; padding: 20px; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>${topic}</h1>
        <p>مستند احترافي - ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
    <div class="content">
        ${formattedContent}
    </div>
    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد التقني الذكي - File Creator Mode</p>
    </div>
</body>
</html>`;
    }

    // Format content for HTML display
    formatContentForHTML(content) {
        if (!content) return '<p>لا يوجد محتوى متاح.</p>';

        // تحويل النص إلى HTML منسق
        let formattedContent = content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');

        // إضافة تنسيق للعناوين
        formattedContent = formattedContent
            .replace(/^<p>([^<]+):<\/p>/gm, '<h3>$1:</h3>')
            .replace(/^<p>(\d+\.\s[^<]+)<\/p>/gm, '<h4>$1</h4>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');

        return formattedContent;
    }

    // Create Word file with real download (old method - keep for compatibility)
    async createWordFile(topic, content) {
        console.log('📄 إنشاء مستند Word احترافي...');

        // Create HTML version of Word document
        const wordHTML = this.generateWordHTML(topic, content);

        try {
            // إنشاء اسم الملف
            const filename = `${topic.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_document.docx`;

            // استخدام createDownloadableFile لإنشاء الملف
            const success = this.createDownloadableFile(wordHTML, filename, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');

            if (success) {
                // إضافة إلى التاريخ
                this.creationHistory.push({
                    type: 'Word Document',
                    name: filename,
                    created: new Date(),
                    size: wordHTML.length
                });

                console.log('✅ تم إنشاء مستند Word بنجاح');
            } else {
                console.error('❌ فشل في إنشاء مستند Word');
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء مستند Word:', error);
        }
    }

    // Create Excel file with real download
    async createExcelFile(topic, content) {
        console.log('📊 إنشاء جدول Excel احترافي...');

        // Create CSV version of Excel data
        const csvData = this.generateCSVData(topic, content);

        // إنشاء الملف وتحميله
        try {
            const filename = `${topic}_spreadsheet.xlsx`;
            const blob = new Blob([csvData], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

            // استخدام createProfessionalDownloadContainer لإنشاء حاوية التحميل
            this.createProfessionalDownloadContainer(filename, blob, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

            this.creationHistory.push({
                type: 'Excel Spreadsheet',
                name: filename,
                created: new Date(),
                rows: csvData.split('\n').length
            });

            console.log('✅ تم إنشاء Excel مع حاوية التحميل');

        } catch (error) {
            console.error('❌ خطأ في إنشاء Excel:', error);
        }
    }

    // Create HTML file with real download
    async createHTMLFile(topic, content) {
        console.log('🌐 إنشاء صفحة HTML احترافية...');

        // تنظيف وتحسين كود HTML
        const cleanHTML = this.cleanAndFormatHTML(content);

        // إنشاء الملف وتحميله
        const success = this.createDownloadableFile(cleanHTML, `${topic}.html`, 'text/html');

        if (success) {
            this.creationHistory.push({
                type: 'HTML Page',
                name: `${topic}.html`,
                created: new Date(),
                size: cleanHTML.length
            });

            // عرض رسالة نجاح
            this.showFileCreationSuccess('HTML Page', `${topic}.html`, cleanHTML.length + ' حرف');
        }
    }

    // تنسيق المحتوى للـ HTML
    formatContentForHTML(content) {
        if (!content) return '<p>محتوى افتراضي تم إنشاؤه بواسطة المساعد الذكي.</p>';

        // تحويل النص إلى HTML منسق
        let formattedContent = content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');

        // إضافة تنسيق للعناوين
        formattedContent = formattedContent
            .replace(/^<p>([^<]+):<\/p>/gm, '<h3>$1:</h3>')
            .replace(/^<p>(\d+\.\s[^<]+)<\/p>/gm, '<h4>$1</h4>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');

        return formattedContent;
    }

    // تنظيف وتحسين كود HTML
    cleanAndFormatHTML(content) {
        // إذا كان المحتوى يحتوي على HTML كامل، استخدمه كما هو
        if (content.includes('<!DOCTYPE') || content.includes('<html')) {
            return content;
        }

        // إذا لم يكن كذلك، أنشئ هيكل HTML كامل
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة احترافية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .content {
            font-size: 16px;
            color: #333;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            ${this.formatContentForHTML(content)}
        </div>
        <div class="footer">
            <p>تم إنشاؤه بواسطة المساعد التقني الذكي - ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
    </div>
</body>
</html>`;
    }

    // Generate Word HTML
    generateWordHTML(topic, content) {
        return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${topic}</title>
    <style>
        body { font-family: 'Times New Roman', serif; max-width: 800px; margin: 0 auto; padding: 40px; line-height: 1.6; }
        h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        p { text-align: justify; margin-bottom: 15px; }
        .header { text-align: center; margin-bottom: 40px; }
        .footer { text-align: center; margin-top: 40px; font-size: 12px; color: #7f8c8d; }
        @media print { body { margin: 0; padding: 20px; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>${topic}</h1>
        <p>مستند احترافي - ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
    <div class="content">
        ${content.replace(/\n/g, '</p><p>')}
    </div>
    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد التقني الذكي - File Creator Mode</p>
    </div>
</body>
</html>`;
    }

    // Generate CSV data
    generateCSVData(topic, content) {
        const timestamp = new Date().toLocaleDateString('ar-SA');

        // تحليل المحتوى لإنشاء بيانات مفيدة
        const lines = content.split('\n').filter(line => line.trim());
        let csvData = `العنوان,القيمة,التاريخ,الملاحظات\n`;

        // إضافة بيانات أساسية
        csvData += `${topic},البيانات الرئيسية,${timestamp},تم إنشاؤها بواسطة AI\n`;

        // إضافة بيانات من المحتوى
        lines.slice(0, 10).forEach((line, index) => {
            const cleanLine = line.replace(/[,]/g, '؛').substring(0, 50);
            csvData += `عنصر ${index + 1},${cleanLine},${timestamp},من المحتوى الأصلي\n`;
        });

        // إضافة إحصائيات
        csvData += `إجمالي الأسطر,${lines.length},${timestamp},إحصائية\n`;
        csvData += `عدد الكلمات,${content.split(' ').length},${timestamp},إحصائية\n`;
        csvData += `عدد الأحرف,${content.length},${timestamp},إحصائية\n`;

        return csvData;
    }

    // إنشاء جدول HTML من CSV
    generateExcelHTML(topic, csvData) {
        const lines = csvData.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',');
        const rows = lines.slice(1);

        let tableHTML = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جدول: ${topic}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px; background: #f5f5f5;
        }
        .container {
            background: white; padding: 30px; border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50; text-align: center; margin-bottom: 30px;
            border-bottom: 3px solid #3498db; padding-bottom: 15px;
        }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 15px; text-align: center;
            font-weight: bold; border: 1px solid #ddd;
        }
        td {
            padding: 12px; border: 1px solid #ddd; text-align: center;
            transition: background-color 0.3s ease;
        }
        tr:nth-child(even) { background-color: #f8f9fa; }
        tr:hover { background-color: #e3f2fd; }
        .footer {
            text-align: center; margin-top: 30px; color: #666;
            font-size: 14px; border-top: 1px solid #eee; padding-top: 20px;
        }
        @media print {
            body { margin: 0; background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 ${topic}</h1>
        <table>
            <thead>
                <tr>`;

        // إضافة العناوين
        headers.forEach(header => {
            tableHTML += `<th>${header.trim()}</th>`;
        });

        tableHTML += `</tr>
            </thead>
            <tbody>`;

        // إضافة الصفوف
        rows.forEach(row => {
            if (row.trim()) {
                const cells = row.split(',');
                tableHTML += '<tr>';
                cells.forEach(cell => {
                    tableHTML += `<td>${cell.trim()}</td>`;
                });
                tableHTML += '</tr>';
            }
        });

        tableHTML += `</tbody>
        </table>
        <div class="footer">
            <p>تم إنشاء هذا الجدول بواسطة المساعد التقني الذكي</p>
            <p>تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}</p>
        </div>
    </div>
</body>
</html>`;

        return tableHTML;
    }

    // إنشاء حاوية التحميل الاحترافية (ChatGPT Style)
    createProfessionalDownloadContainer(filename, downloadUrl, fileSize) {
        console.log('🎨 ChatGPT Style: إنشاء حاوية التحميل:', filename);
        console.log('📊 معلومات الملف:', { filename, fileSize, urlType: downloadUrl.startsWith('blob:') ? 'Blob محلي' : 'رابط خارجي' });

        // جعل الوظيفة متاحة عالمياً
        if (!window.createProfessionalDownloadContainer) {
            window.createProfessionalDownloadContainer = this.createProfessionalDownloadContainer.bind(this);
            console.log('✅ تم ربط createProfessionalDownloadContainer بالنطاق العام');
        }

        // إزالة أي حاوية سابقة
        const existingContainer = document.getElementById('professionalDownloadContainer');
        if (existingContainer) {
            existingContainer.remove();
        }

        // إنشاء الحاوية الرئيسية (متناسبة مع المحادثة)
        const downloadContainer = document.createElement('div');
        downloadContainer.id = 'professionalDownloadContainer';
        downloadContainer.className = 'message-content';
        downloadContainer.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 100%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            animation: slideInScale 0.4s ease-out;
            margin: 0;
        `;

        // إضافة CSS للأنيميشن
        if (!document.getElementById('downloadContainerStyles')) {
            const styles = document.createElement('style');
            styles.id = 'downloadContainerStyles';
            styles.textContent = `
                @keyframes slideInScale {
                    from {
                        opacity: 0;
                        transform: translate(-50%, -50%) scale(0.8);
                    }
                    to {
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                    }
                }
                @keyframes pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                }
                .download-btn:hover {
                    animation: pulse 0.6s ease-in-out;
                }
            `;
            document.head.appendChild(styles);
        }

        // تحديد أيقونة ولون الملف
        const fileIcon = this.getFileIcon(filename);
        const fileExtension = filename.split('.').pop().toUpperCase();
        const fileSizeFormatted = this.formatFileSize(fileSize);
        const fileColor = this.getFileColor(fileExtension);

        // محتوى الحاوية
        downloadContainer.innerHTML = `
            <!-- Header -->
            <div style="
                background: rgba(255,255,255,0.1);
                padding: 20px;
                text-align: center;
                border-bottom: 1px solid rgba(255,255,255,0.2);
            ">
                <div style="font-size: 3em; margin-bottom: 10px;">${fileIcon}</div>
                <h2 style="margin: 0; font-size: 1.5em; font-weight: 600;">
                    ✅ تم إنشاء الملف بنجاح!
                </h2>
                <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 0.9em;">
                    جاهز للتحميل الآن
                </p>
            </div>

            <!-- File Info -->
            <div style="padding: 25px;">
                <div style="
                    background: rgba(255,255,255,0.1);
                    border-radius: 15px;
                    padding: 20px;
                    margin-bottom: 20px;
                    border: 2px solid ${fileColor};
                ">
                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                        <div style="
                            font-size: 2.5em;
                            background: ${fileColor};
                            width: 60px;
                            height: 60px;
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">${fileIcon}</div>
                        <div style="flex: 1;">
                            <div style="font-weight: bold; font-size: 1.1em; margin-bottom: 5px;">
                                ${filename}
                            </div>
                            <div style="opacity: 0.8; font-size: 0.9em;">
                                ${fileExtension} • ${fileSizeFormatted} • تم إنشاؤه بالذكاء الاصطناعي
                            </div>
                        </div>
                    </div>

                    <!-- Download Button -->
                    <a href="${downloadUrl}"
                       download="${filename}"
                       class="download-btn"
                       style="
                           display: flex;
                           align-items: center;
                           justify-content: center;
                           gap: 12px;
                           background: linear-gradient(45deg, #4CAF50, #45a049);
                           color: white;
                           text-decoration: none;
                           padding: 15px 25px;
                           border-radius: 12px;
                           font-size: 1.1em;
                           font-weight: 600;
                           transition: all 0.3s ease;
                           border: none;
                           cursor: pointer;
                           box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
                       "
                       onmouseover="this.style.background='linear-gradient(45deg, #45a049, #388e3c)'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(76, 175, 80, 0.6)'"
                       onmouseout="this.style.background='linear-gradient(45deg, #4CAF50, #45a049)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(76, 175, 80, 0.4)'"
                       onclick="console.log('📥 تحميل الملف:', '${filename}');">
                        <span style="font-size: 1.3em;">📥</span>
                        <span>تحميل ${filename}</span>
                    </a>
                </div>

                <!-- Actions -->
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 0.9em;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        ❌ إغلاق
                    </button>

                    <button onclick="navigator.clipboard.writeText('${downloadUrl}').then(() => alert('✅ تم نسخ الرابط!')).catch(() => alert('❌ فشل النسخ'))" style="
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 0.9em;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        📋 نسخ الرابط
                    </button>
                </div>

                <!-- Footer -->
                <div style="
                    text-align: center;
                    margin-top: 20px;
                    padding-top: 15px;
                    border-top: 1px solid rgba(255,255,255,0.2);
                    font-size: 0.8em;
                    opacity: 0.7;
                ">
                    💡 رابط محلي 100% - لا رفع خارجي • تحميل مباشر مثل ChatGPT
                </div>
            </div>
        `;

        // البحث عن حاوي المحادثة الصحيح من index.html
        const chatContainer = document.getElementById('chatContainer') ||
                             document.querySelector('.chat-container') ||
                             document.querySelector('#chat-messages') ||
                             document.querySelector('.messages-container');

        if (chatContainer) {
            console.log('✅ تم العثور على حاوي المحادثة:', chatContainer.id || chatContainer.className);

            // إضافة الحاوية كرسالة في المحادثة
            const messageWrapper = document.createElement('div');
            messageWrapper.className = 'message assistant';
            messageWrapper.style.cssText = `
                margin: 15px 0;
                display: flex;
                align-items: flex-start;
                gap: 10px;
            `;

            // أيقونة المساعد
            const avatar = document.createElement('div');
            avatar.className = 'avatar';
            avatar.innerHTML = '<i class="fas fa-robot"></i>';
            avatar.style.cssText = `
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #4CAF50;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 14px;
                flex-shrink: 0;
            `;

            // إضافة الحاوية للرسالة
            messageWrapper.appendChild(avatar);
            messageWrapper.appendChild(downloadContainer);

            // إضافة للمحادثة
            chatContainer.appendChild(messageWrapper);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            console.log('✅ تم إضافة الحاوية كرسالة في المحادثة');
        } else {
            console.log('⚠️ لم يتم العثور على حاوي المحادثة، إضافة للـ body');
            document.body.appendChild(downloadContainer);
        }

        // تشخيص نجاح الإضافة
        const addedContainer = document.getElementById('professionalDownloadContainer');
        if (addedContainer) {
            const rect = addedContainer.getBoundingClientRect();
            const isVisible = addedContainer.offsetWidth > 0 && addedContainer.offsetHeight > 0;
            console.log('✅ تم إنشاء الحاوية بنجاح!');
            console.log('📍 الموقع:', Math.round(rect.left), Math.round(rect.top));
            console.log('📏 الحجم:', Math.round(rect.width), 'x', Math.round(rect.height));
            console.log('👁️ مرئية:', isVisible ? 'نعم' : 'لا');
        } else {
            console.error('❌ فشل في إضافة الحاوية للصفحة!');
        }

        // إغلاق تلقائي بعد 30 ثانية
        setTimeout(() => {
            if (downloadContainer.parentNode) {
                downloadContainer.style.animation = 'slideInScale 0.3s ease-in reverse';
                setTimeout(() => {
                    if (downloadContainer.parentNode) {
                        downloadContainer.remove();
                        this.addDiagnosticMessage("⏰ تم إغلاق الحاوية تلقائياً بعد 30 ثانية");
                    }
                }, 300);
            }
        }, 30000);

        // إغلاق بالضغط على Escape
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                downloadContainer.remove();
                document.removeEventListener('keydown', handleEscape);
                this.addDiagnosticMessage("⌨️ تم إغلاق الحاوية بـ Escape");
            }
        };
        document.addEventListener('keydown', handleEscape);
    }

    // الحصول على لون الملف حسب النوع
    getFileColor(extension) {
        const colorMap = {
            'PDF': '#e74c3c',
            'DOC': '#3498db', 'DOCX': '#3498db',
            'XLS': '#27ae60', 'XLSX': '#27ae60', 'CSV': '#27ae60',
            'PPT': '#f39c12', 'PPTX': '#f39c12',
            'HTML': '#e67e22', 'HTM': '#e67e22',
            'CSS': '#9b59b6',
            'JS': '#f1c40f', 'JAVASCRIPT': '#f1c40f',
            'PY': '#3498db', 'PYTHON': '#3498db',
            'JAVA': '#e74c3c',
            'CPP': '#34495e', 'C': '#34495e',
            'TXT': '#95a5a6',
            'JSON': '#e67e22',
            'XML': '#e67e22',
            'ZIP': '#8e44ad',
            'EXE': '#2c3e50'
        };
        return colorMap[extension] || '#667eea';
    }

    // تنسيق المحتوى لـ HTML
    formatContentForHTML(content) {
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');
    }

    // إنشاء ملف برمجي
    generateCodeFile(topic, content, originalCommand) {
        const lowerCommand = originalCommand.toLowerCase();

        if (lowerCommand.includes('python') || lowerCommand.includes('بايثون')) {
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

${content}

if __name__ == "__main__":
    print("${topic}")`;
        } else if (lowerCommand.includes('javascript') || lowerCommand.includes('js')) {
            return `/**
 * ${topic}
 * تم إنشاؤه بواسطة المساعد التقني الذكي
 */

${content}

console.log("${topic}");`;
        } else {
            return `/*
${topic}
تم إنشاؤه بواسطة المساعد التقني الذكي
*/

${content}`;
        }
    }

    // إنشاء نص منسق
    generateFormattedText(topic, content) {
        return `${topic}
${'='.repeat(topic.length)}

${content}

---
تم إنشاؤه بواسطة المساعد التقني الذكي
التاريخ: ${new Date().toLocaleDateString('ar-SA')}`;
    }

    // إنشاء محتوى افتراضي
    generateDefaultContent(topic, fileType) {
        return `هذا محتوى افتراضي عن ${topic}.

تم إنشاء هذا ${fileType} بواسطة المساعد التقني الذكي.

يمكنك تعديل هذا المحتوى حسب احتياجاتك.

المميزات:
• محتوى منظم ومرتب
• تصميم احترافي
• جاهز للاستخدام
• قابل للتخصيص

للمزيد من المعلومات، يرجى الرجوع إلى الوثائق الرسمية.`;
    }

    // أنماط CSS للصفحات المختلفة
    getDefaultPageStyle() {
        return `
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        header {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .content {
            padding: 40px;
            font-size: 16px;
            color: #333;
        }
        footer {
            background: #34495e;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: auto;
        }
        p {
            margin-bottom: 15px;
        }
        `;
    }

    getLoginPageStyle() {
        return `
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        header h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #2980b9;
        }
        `;
    }

    getTablePageStyle() {
        return `
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: right;
            border: 1px solid #ddd;
        }
        th {
            background: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        `;
    }

    // Deactivate File Creator Mode
    deactivate() {
        this.isActive = false;
        console.log('📁 File Creator Mode deactivated');

        // تحديث المتغير العام
        window.fileCreatorActive = false;

        // إضافة رسالة للمحادثة
        if (typeof addMessage === 'function') {
            addMessage('assistant', '⏹️ تم إيقاف File Creator Mode');
        }

        if (typeof speakText === 'function') {
            speakText('تم إلغاء تفعيل File Creator Mode.');
        }
    }

    // Get creation history
    getCreationHistory() {
        return this.creationHistory;
    }

    // Clear creation history
    clearHistory() {
        this.creationHistory = [];
        console.log('🗑️ تم مسح سجل إنشاء الملفات');
    }

    // ===========================================
    // دوال التحميل الحقيقي للملفات (مثل ChatGPT)
    // ===========================================

    // إنشاء ملف قابل للتحميل مع رابط تفاعلي (مثل ChatGPT) - 100% محلي
    createDownloadableFile(content, filename, type = 'text/plain', skipContainer = false) {
        try {
            // إنشاء Blob من المحتوى (100% محلي - لا مواقع خارجية)
            const blob = new Blob([content], { type: type });
            const url = URL.createObjectURL(blob);

            // إضافة رابط تحميل تفاعلي في المحادثة فقط إذا لم يتم تخطي الحاوية
            if (!skipContainer) {
                this.addDownloadLinkToChat(filename, url, blob.size);
            }

            return true;

        } catch (error) {
            return false;
        }
    }

    // إضافة رابط تحميل تفاعلي للمحادثة (مثل ChatGPT)
    addDownloadLinkToChat(filename, downloadUrl, fileSize) {
        try {
            // إنشاء حاوية التحميل الاحترافية دائماً
            this.createProfessionalDownloadContainer(filename, downloadUrl, fileSize);

            // البحث عن حاوي المحادثة كبديل
            let chatContainer = document.getElementById('chatContainer') ||
                               document.querySelector('.chat-container') ||
                               document.querySelector('#chat-messages') ||
                               document.querySelector('.messages-container') ||
                               document.querySelector('[class*="chat"]') ||
                               document.querySelector('[id*="chat"]');

            // إضافة رسالة بسيطة للمحادثة (الحاوية الاحترافية تظهر منفصلة)
            if (chatContainer) {
                const downloadMessage = document.createElement('div');
                downloadMessage.className = 'message assistant';
                downloadMessage.style.cssText = `
                    margin: 10px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 12px;
                    border-left: 4px solid #4CAF50;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                `;

                const fileIcon = this.getFileIcon(filename);
                const fileExtension = filename.split('.').pop().toUpperCase();
                const fileSizeFormatted = this.formatFileSize(fileSize);

                downloadMessage.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <span style="font-size: 24px;">${fileIcon}</span>
                        <div>
                            <div style="font-weight: bold; color: #2E7D32; margin-bottom: 5px;">
                                ✅ تم إنشاء الملف بنجاح!
                            </div>
                            <div style="color: #666; font-size: 14px;">
                                ${filename} (${fileExtension} • ${fileSizeFormatted})
                            </div>
                            <div style="color: #888; font-size: 12px; margin-top: 3px;">
                                💡 تم فتح نافذة التحميل - يمكنك تحميل الملف الآن
                            </div>
                        </div>
                    </div>
                `;

                chatContainer.appendChild(downloadMessage);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

        } catch (error) {
            // تحميل مباشر كبديل
            this.directDownload(downloadUrl, filename);
        }
    }

    // إضافة رابط تحميل عبر addMessage (مثل ChatGPT)
    addDownloadLinkViaAddMessage(filename, downloadUrl, fileSize) {
        const fileIcon = this.getFileIcon(filename);
        const fileExtension = filename.split('.').pop().toUpperCase();
        const fileSizeFormatted = this.formatFileSize(fileSize);

        // إنشاء HTML للرابط التفاعلي (مثل ChatGPT)
        const downloadHTML = `
            <div style="background: #f8f9fa; border-radius: 12px; padding: 15px; margin: 10px 0; border-left: 4px solid #4CAF50; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <div style="margin-bottom: 12px;">
                    <strong style="color: #2E7D32; font-size: 16px;">${fileIcon} تم إنشاء الملف:</strong>
                </div>

                <div style="background: white; border-radius: 8px; padding: 15px; border: 1px solid #e0e0e0; margin-bottom: 10px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                        <span style="font-size: 28px;">${fileIcon}</span>
                        <div style="flex: 1;">
                            <div style="font-weight: bold; color: #333; font-size: 16px; margin-bottom: 4px;">${filename}</div>
                            <div style="color: #666; font-size: 13px;">${fileExtension} • ${fileSizeFormatted} • تم إنشاؤه بالذكاء الاصطناعي</div>
                        </div>
                    </div>

                    <a href="${downloadUrl}"
                       download="${filename}"
                       style="
                           display: inline-flex;
                           align-items: center;
                           justify-content: center;
                           gap: 8px;
                           background: #4CAF50;
                           color: white;
                           text-decoration: none;
                           padding: 12px 20px;
                           border-radius: 8px;
                           font-size: 14px;
                           font-weight: 600;
                           transition: all 0.3s ease;
                           border: none;
                           cursor: pointer;
                           width: 100%;
                           box-sizing: border-box;
                           box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
                       "
                       onmouseover="this.style.background='#45a049'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 8px rgba(76, 175, 80, 0.4)'"
                       onmouseout="this.style.background='#4CAF50'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(76, 175, 80, 0.3)'"
                       onclick="console.log('📥 تحميل الملف:', '${filename}');">
                        <span style="font-size: 16px;">📥</span>
                        <span>تحميل ${filename}</span>
                    </a>
                </div>

                <div style="font-size: 12px; color: #666; text-align: center;">
                    💡 <strong>رابط محلي 100%</strong> - لا رفع خارجي • تحميل مباشر مثل ChatGPT
                </div>
            </div>
        `;

        // إضافة الرابط للمحادثة بطرق متعددة
        if (typeof addMessage === 'function') {
            addMessage('assistant', downloadHTML);
            console.log('✅ تم إضافة رابط التحميل المحلي عبر addMessage');
        } else if (chatContainer) {
            // إضافة مباشرة للحاوي
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = downloadHTML;
            chatContainer.appendChild(messageDiv);
            console.log('✅ تم إضافة رابط التحميل المحلي مباشرة للحاوي');
        } else {
            console.warn('⚠️ لا يمكن عرض الرابط، استخدام التحميل المباشر');
            this.directDownload(downloadUrl, filename);
        }

        // تأكيد أن الرابط محلي وليس خارجي
        if (downloadUrl.startsWith('blob:')) {
            console.log('✅ تأكيد: الرابط محلي 100% - لا مواقع خارجية');
        } else {
            console.error('❌ تحذير: الرابط ليس محلي!', downloadUrl);
        }
    }

    // إنشاء حاوية رسائل مؤقتة (مثل ChatGPT)
    createTemporaryChatContainer() {
        // البحث عن مكان مناسب لإضافة الحاوية
        const targetContainer = document.querySelector('main') ||
                               document.querySelector('.main-content') ||
                               document.querySelector('#app') ||
                               document.querySelector('.app') ||
                               document.body;

        // إنشاء حاوية الرسائل المؤقتة
        const tempChatContainer = document.createElement('div');
        tempChatContainer.id = 'temp-chat-container';
        tempChatContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 420px;
            max-height: 600px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border: 1px solid #e0e0e0;
            z-index: 10000;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            animation: slideIn 0.3s ease-out;
        `;

        // إضافة CSS للأنيميشن
        if (!document.getElementById('temp-chat-styles')) {
            const styles = document.createElement('style');
            styles.id = 'temp-chat-styles';
            styles.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }

        // إضافة عنوان للحاوية
        const header = document.createElement('div');
        header.style.cssText = `
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 16px 20px;
            font-weight: 600;
            text-align: center;
            position: relative;
            border-radius: 16px 16px 0 0;
        `;
        header.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                <span style="font-size: 20px;">📁</span>
                <span>الملفات المُنشأة</span>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" style="
                position: absolute;
                top: 12px;
                left: 12px;
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                width: 28px;
                height: 28px;
                border-radius: 50%;
                cursor: pointer;
                font-size: 16px;
                font-weight: bold;
                transition: all 0.2s;
            " onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">×</button>
        `;

        // إضافة منطقة الرسائل
        const messagesArea = document.createElement('div');
        messagesArea.style.cssText = `
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
            background: #fafafa;
        `;

        tempChatContainer.appendChild(header);
        tempChatContainer.appendChild(messagesArea);
        targetContainer.appendChild(tempChatContainer);

        console.log('✅ تم إنشاء حاوية رسائل مؤقتة احترافية');
        return messagesArea; // إرجاع منطقة الرسائل وليس الحاوية الكاملة
    }

    // وظيفة تحميل الملف المحسنة (مثل ChatGPT)
    downloadFile(url, filename) {
        console.log('📥 بدء تحميل الملف:', filename);

        try {
            // التحقق من صحة الرابط
            if (!url || !filename) {
                console.error('❌ رابط أو اسم الملف غير صحيح');
                alert('خطأ: رابط أو اسم الملف غير صحيح');
                return false;
            }

            // إنشاء رابط تحميل مخفي
            const downloadLink = document.createElement('a');
            downloadLink.href = url;
            downloadLink.download = filename;
            downloadLink.style.display = 'none';
            downloadLink.target = '_blank'; // فتح في نافذة جديدة كبديل

            // إضافة للصفحة وتفعيل التحميل
            document.body.appendChild(downloadLink);
            downloadLink.click();

            // تنظيف الرابط بعد التحميل
            setTimeout(() => {
                if (downloadLink.parentNode) {
                    document.body.removeChild(downloadLink);
                }
                console.log('✅ تم تحميل الملف بنجاح:', filename);
            }, 100);

            return true;

        } catch (error) {
            console.error('❌ خطأ في تحميل الملف:', error);
            alert('حدث خطأ في تحميل الملف. يرجى المحاولة مرة أخرى.');
            return false;
        }
    }

    // تحميل مباشر (بديل)
    directDownload(url, filename) {
        console.log('📥 تحميل مباشر للملف:', filename);

        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = filename;
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        setTimeout(() => {
            document.body.removeChild(downloadLink);
            URL.revokeObjectURL(url);
            console.log('✅ تم التحميل المباشر بنجاح:', filename);
        }, 100);
    }

    // الحصول على أيقونة الملف حسب النوع
    getFileIcon(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        const iconMap = {
            'pdf': '📄',
            'doc': '📝', 'docx': '📝',
            'xls': '📊', 'xlsx': '📊', 'csv': '📊',
            'ppt': '🎯', 'pptx': '🎯',
            'html': '🌐', 'htm': '🌐',
            'css': '🎨',
            'js': '⚡', 'javascript': '⚡',
            'py': '🐍', 'python': '🐍',
            'java': '☕',
            'cpp': '⚙️', 'c': '⚙️',
            'txt': '📄',
            'json': '📋',
            'xml': '📋',
            'zip': '📦',
            'exe': '💻'
        };
        return iconMap[extension] || '📄';
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // إنشاء محتوى PDF احترافي
    generateProfessionalPDFContent(topic, content) {
        const timestamp = new Date().toLocaleString('ar-SA');

        return `
        <div class="header">
            <div class="title">${topic}</div>
            <div class="subtitle">تقرير احترافي - ${timestamp}</div>
        </div>

        <div class="content">
            <div class="section">
                <h2>📋 المحتوى الرئيسي</h2>
                <div>${this.formatContentForPDF(content)}</div>
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة المساعد التقني الذكي</p>
            <p>تاريخ الإنشاء: ${timestamp}</p>
        </div>
        `;
    }

    // تنسيق المحتوى للـ PDF
    formatContentForPDF(content) {
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');
    }

    // إنشاء نسخة نصية
    generateTextVersion(topic, content) {
        const timestamp = new Date().toLocaleString('ar-SA');

        return `${topic}
${'='.repeat(topic.length)}

تاريخ الإنشاء: ${timestamp}
تم إنشاؤه بواسطة: المساعد التقني الذكي

المحتوى:
${'-'.repeat(50)}

${content}

${'-'.repeat(50)}
انتهى التقرير`;
    }

    // إنشاء نص العرض التقديمي
    generatePresentationText(topic, slides) {
        const timestamp = new Date().toLocaleString('ar-SA');

        let text = `عرض تقديمي: ${topic}\n`;
        text += `تاريخ الإنشاء: ${timestamp}\n`;
        text += `عدد الشرائح: ${slides.length}\n\n`;
        text += '='.repeat(50) + '\n\n';

        slides.forEach((slide, index) => {
            text += `شريحة ${index + 1}: ${slide.title}\n`;
            text += '-'.repeat(30) + '\n';
            text += `${slide.content}\n\n`;
        });

        return text;
    }

    // عرض رسالة نجاح إنشاء الملف (مع خيارات متعددة)
    showFileCreationSuccess(fileType, filename, size) {
        // تحديد طريقة العرض حسب إعدادات المستخدم
        const displayMode = this.getDisplayMode();

        switch(displayMode) {
            case 'popup':
                this.showPopupSuccess(fileType, filename, size);
                break;
            case 'chat':
                // الرابط التفاعلي يتم إضافته بواسطة addDownloadLinkToChat
                this.showNotification(`✅ تم إنشاء ${fileType} بنجاح!`, 'success');
                break;
            case 'both':
                this.showPopupSuccess(fileType, filename, size);
                this.showNotification(`✅ تم إنشاء ${fileType} بنجاح!`, 'success');
                break;
            default:
                // افتراضي: في المحادثة
                this.showNotification(`✅ تم إنشاء ${fileType} بنجاح!`, 'success');
        }

        console.log(`✅ تم إنشاء ${fileType}: ${filename} (${size})`);
    }

    // الحصول على طريقة العرض المفضلة
    getDisplayMode() {
        // التحقق من إعدادات المستخدم المحفوظة
        const savedMode = localStorage.getItem('fileDisplayMode');
        if (savedMode) {
            return savedMode;
        }

        // افتراضي: في المحادثة (مثل ChatGPT)
        return 'chat';
    }

    // تغيير طريقة العرض
    setDisplayMode(mode) {
        const validModes = ['popup', 'chat', 'both'];
        if (validModes.includes(mode)) {
            localStorage.setItem('fileDisplayMode', mode);
            this.showNotification(`✅ تم تغيير طريقة العرض إلى: ${this.getDisplayModeText(mode)}`, 'info');
            console.log(`📋 تم تغيير طريقة عرض الملفات إلى: ${mode}`);

            // تحديث زر طريقة العرض
            this.updateDisplayModeButton();
        } else {
            console.warn('⚠️ طريقة عرض غير صحيحة:', mode);
        }
    }

    // الحصول على نص طريقة العرض
    getDisplayModeText(mode) {
        const modeTexts = {
            'popup': 'نافذة منبثقة',
            'chat': 'في المحادثة',
            'both': 'الاثنين معاً'
        };
        return modeTexts[mode] || 'غير محدد';
    }

    // عرض نافذة منبثقة للنجاح
    showPopupSuccess(fileType, filename, size) {
        // إنشاء نافذة معاينة
        const successWindow = document.createElement('div');
        successWindow.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 500px; z-index: 10000; text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            border: 2px solid #28a745;
        `;

        successWindow.innerHTML = `
            <div style="color: #28a745; font-size: 48px; margin-bottom: 20px;">✅</div>
            <h2 style="color: #28a745; margin: 0 0 15px 0;">تم إنشاء الملف بنجاح!</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <p style="margin: 5px 0;"><strong>نوع الملف:</strong> ${fileType}</p>
                <p style="margin: 5px 0;"><strong>اسم الملف:</strong> ${filename}</p>
                <p style="margin: 5px 0;"><strong>الحجم:</strong> ${typeof size === 'number' ? size + ' حرف' : size}</p>
            </div>
            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: #007bff; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">إغلاق</button>
                <button onclick="window.fileCreatorInstance.showDisplayModeSettings(); this.parentElement.parentElement.remove();" style="
                    background: #6c757d; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">⚙️ الإعدادات</button>
            </div>
        `;

        document.body.appendChild(successWindow);

        // إزالة النافذة تلقائياً بعد 10 ثوانٍ
        setTimeout(() => {
            if (successWindow.parentElement) {
                successWindow.remove();
            }
        }, 10000);
    }

    // عرض إعدادات طريقة العرض
    showDisplayModeSettings() {
        const currentMode = this.getDisplayMode();

        const settingsWindow = document.createElement('div');
        settingsWindow.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 400px; z-index: 10001; text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            border: 2px solid #007bff;
        `;

        settingsWindow.innerHTML = `
            <div style="color: #007bff; font-size: 36px; margin-bottom: 20px;">⚙️</div>
            <h2 style="color: #007bff; margin: 0 0 15px 0;">إعدادات عرض الملفات</h2>
            <p style="margin-bottom: 20px; color: #666;">اختر طريقة عرض الملفات المفضلة لديك:</p>

            <div style="text-align: right; margin: 20px 0;">
                <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 8px; background: ${currentMode === 'chat' ? '#e3f2fd' : '#f8f9fa'};">
                    <input type="radio" name="displayMode" value="chat" ${currentMode === 'chat' ? 'checked' : ''} style="margin-left: 10px;">
                    💬 في المحادثة (مثل ChatGPT)
                </label>
                <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 8px; background: ${currentMode === 'popup' ? '#e3f2fd' : '#f8f9fa'};">
                    <input type="radio" name="displayMode" value="popup" ${currentMode === 'popup' ? 'checked' : ''} style="margin-left: 10px;">
                    🪟 نافذة منبثقة
                </label>
                <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 8px; background: ${currentMode === 'both' ? '#e3f2fd' : '#f8f9fa'};">
                    <input type="radio" name="displayMode" value="both" ${currentMode === 'both' ? 'checked' : ''} style="margin-left: 10px;">
                    🔄 الاثنين معاً
                </label>
            </div>

            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                <button onclick="window.fileCreatorInstance.saveDisplayModeSettings(this.parentElement.parentElement); this.parentElement.parentElement.remove();" style="
                    background: #28a745; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">✅ حفظ</button>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: #6c757d; color: white; border: none; padding: 12px 24px;
                    border-radius: 8px; cursor: pointer; font-size: 16px;
                ">إلغاء</button>
            </div>
        `;

        document.body.appendChild(settingsWindow);
    }

    // حفظ إعدادات طريقة العرض
    saveDisplayModeSettings(settingsWindow) {
        const selectedMode = settingsWindow.querySelector('input[name="displayMode"]:checked');
        if (selectedMode) {
            this.setDisplayMode(selectedMode.value);
        }
    }

    // إنشاء ملف requirements.txt
    generateRequirementsFile(programType) {
        let requirements = `# متطلبات البرنامج: ${programType}
# تم إنشاؤه بواسطة المساعد التقني الذكي

# المكتبات الأساسية
tkinter  # واجهة المستخدم الرسومية (مدمجة مع Python)

# مكتبات إضافية حسب نوع البرنامج
`;

        if (programType.includes('حاسبة') || programType.includes('calculator')) {
            requirements += `math  # العمليات الرياضية (مدمجة)
decimal  # حسابات دقيقة (مدمجة)
`;
        } else if (programType.includes('محول') || programType.includes('converter')) {
            requirements += `os  # التعامل مع الملفات (مدمجة)
shutil  # نسخ ونقل الملفات (مدمجة)
`;
        } else if (programType.includes('منظم') || programType.includes('organizer')) {
            requirements += `os  # التعامل مع الملفات (مدمجة)
datetime  # التاريخ والوقت (مدمجة)
pathlib  # مسارات الملفات (مدمجة)
`;
        }

        requirements += `
# لتحويل إلى EXE:
# pip install pyinstaller
# أو
# pip install auto-py-to-exe

# تعليمات التحويل:
# pyinstaller --onefile --windowed ${programType}.py
# أو استخدم auto-py-to-exe للواجهة الرسومية
`;

        return requirements;
    }

    // إضافة زر إعدادات طريقة العرض في الواجهة
    addDisplayModeButton() {
        try {
            // البحث عن مكان مناسب لإضافة الزر
            const controlsContainer = document.querySelector('.controls') ||
                                    document.querySelector('.buttons-container') ||
                                    document.querySelector('.chat-controls') ||
                                    document.getElementById('controls');

            if (controlsContainer) {
                // التحقق من عدم وجود الزر مسبقاً
                if (!document.getElementById('displayModeButton')) {
                    const displayModeButton = document.createElement('button');
                    displayModeButton.id = 'displayModeButton';
                    displayModeButton.innerHTML = '⚙️ طريقة العرض';
                    displayModeButton.title = `طريقة العرض الحالية: ${this.getDisplayModeText(this.getDisplayMode())}`;
                    displayModeButton.style.cssText = `
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        margin: 5px;
                        transition: all 0.3s;
                    `;

                    displayModeButton.addEventListener('click', () => {
                        this.showDisplayModeSettings();
                    });

                    displayModeButton.addEventListener('mouseenter', function() {
                        this.style.background = '#5a6268';
                        this.style.transform = 'translateY(-1px)';
                    });

                    displayModeButton.addEventListener('mouseleave', function() {
                        this.style.background = '#6c757d';
                        this.style.transform = 'translateY(0)';
                    });

                    controlsContainer.appendChild(displayModeButton);
                    console.log('✅ تم إضافة زر إعدادات طريقة العرض');
                }
            } else {
                console.log('⚠️ لم يتم العثور على حاوي الأزرار لإضافة زر إعدادات العرض');
            }
        } catch (error) {
            console.error('❌ خطأ في إضافة زر إعدادات العرض:', error);
        }
    }

    // تحديث نص زر طريقة العرض
    updateDisplayModeButton() {
        const button = document.getElementById('displayModeButton');
        if (button) {
            button.title = `طريقة العرض الحالية: ${this.getDisplayModeText(this.getDisplayMode())}`;
        }
    }

    // إنشاء ملف مع محتوى مولد من الذكاء الاصطناعي
    async createFileWithContent(fileInfo, content) {
        console.log('📁 إنشاء ملف مع محتوى مولد:', fileInfo.filename);

        try {
            // تحديد نوع MIME
            const mimeType = this.getMimeTypeForFile(fileInfo.extension);

            console.log(`📄 إنشاء ملف: ${fileInfo.filename} (${mimeType})`);
            console.log(`📊 حجم المحتوى: ${content.length} حرف`);

            // إنشاء الملف مع رابط حقيقي
            const success = this.createDownloadableFile(content, fileInfo.filename, mimeType);

            if (success) {
                // إضافة للتاريخ
                this.creationHistory.push({
                    type: fileInfo.type.toUpperCase(),
                    name: fileInfo.filename,
                    created: new Date(),
                    size: content.length,
                    aiGenerated: true
                });

                // عرض رسالة نجاح حسب طريقة العرض المختارة
                this.showFileCreationSuccess(fileInfo.type.toUpperCase(), fileInfo.filename, content.length);

                console.log('✅ تم إنشاء الملف والرابط بنجاح');

                return true; // فقط إرجاع نجاح العملية بدون رسالة
            } else {
                throw new Error('فشل في إنشاء الملف');
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء الملف:', error);
            throw error;
        }
    }

    // الحصول على نوع MIME للملف
    getMimeTypeForFile(extension) {
        const mimeTypes = {
            'txt': 'text/plain',
            'html': 'text/html',
            'css': 'text/css',
            'js': 'text/javascript',
            'json': 'application/json',
            'xml': 'application/xml',
            'py': 'text/x-python',
            'java': 'text/x-java-source',
            'cpp': 'text/x-c++src',
            'c': 'text/x-csrc',
            'csv': 'text/csv',
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        };
        return mimeTypes[(extension && typeof extension === 'string') ? extension.toLowerCase() : ''] || 'text/plain';
    }
}

// تصدير الكلاس للاستخدام في المتصفح أو Node.js
if (typeof window !== 'undefined') {
    window.FileCreatorCore = FileCreatorCore;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = FileCreatorCore;
}

// تصدير واحد فقط
if (typeof window !== 'undefined') {
    window.FileCreatorCore = FileCreatorCore;
    console.log('✅ تم تصدير window.FileCreatorCore بنجاح');
    console.log('🔍 نوع الكلاس:', typeof window.FileCreatorCore);
    console.log('🔍 اسم الكلاس:', window.FileCreatorCore.name);
} else {
    console.error('❌ window غير متاح للتصدير');
}
// إنشاء مثيل عام بعد التصدير
console.log('🔧 إنشاء مثيل عام...');
try {
    if (typeof window !== 'undefined' && window.FileCreatorCore) {
        window.fileCreatorInstance = new window.FileCreatorCore();
        console.log('✅ تم إنشاء window.fileCreatorInstance بنجاح');
        console.log('🔍 نوع fileCreatorInstance:', typeof window.fileCreatorInstance);

        // ربط الحاوية الأصلية بالنطاق العام
        if (window.fileCreatorInstance.createProfessionalDownloadContainer) {
            window.createProfessionalDownloadContainer = window.fileCreatorInstance.createProfessionalDownloadContainer.bind(window.fileCreatorInstance);
            console.log('✅ تم ربط createProfessionalDownloadContainer بالنطاق العام');

            // التأكد من الربط
            console.log('🔍 فحص الربط العالمي:');
            console.log('- window.createProfessionalDownloadContainer:', typeof window.createProfessionalDownloadContainer);
            console.log('- قابل للاستدعاء:', typeof window.createProfessionalDownloadContainer === 'function');
        }
    } else {
        console.error('❌ لا يمكن إنشاء المثيل - window.FileCreatorCore غير متاح');
    }
} catch (error) {
    console.error('❌ فشل في إنشاء fileCreatorInstance:', error);
}

// تصدير الكلاس للنطاق العام
window.FileCreatorCore = FileCreatorCore;

// ===== ChatGPT Style System Activated =====
// النظام الآن يعمل مثل ChatGPT تماماً:
// ✅ إرسال نص المستخدم مباشرة إلى النماذج
// ✅ عدم تحليل داخلي أو توليد برومبت
// ✅ استخدام استجابة النموذج كما هي
// ✅ دعم جميع أنواع الملفات
// ✅ حاويات تحميل احترافية مثل ChatGPT
// ✅ روابط blob حقيقية قابلة للتنزيل
// ==========================================
console.log('✅ تم تصدير FileCreatorCore للنطاق العام');
console.log('🔍 فحص التصدير:', typeof window.FileCreatorCore);

// إضافة وظيفة toggleFileCreatorMode للكلاس
FileCreatorCore.toggleFileCreatorMode = function() {
    console.log("✅ تم استدعاء toggleFileCreatorMode من FileCreatorCore");

    // تبديل حالة التفعيل
    if (!window.fileCreatorInstance) {
        // إنشاء مثيل جديد
        window.fileCreatorInstance = new FileCreatorCore();
        console.log("✅ تم إنشاء مثيل FileCreator جديد");
    }

    // تبديل الحالة
    const isActive = window.fileCreatorActive || false;
    window.fileCreatorActive = !isActive;

    if (window.fileCreatorActive) {
        // تفعيل
        window.fileCreatorInstance.activate();
        alert("✅ تم تفعيل File Creator Mode!");

        // تحديث الزر
        const btn = document.getElementById('fileCreatorBtn');
        if (btn) {
            btn.innerHTML = '<i class="fas fa-file-check"></i><span>إيقاف File Creator</span>';
            btn.style.background = '#3498db';
            btn.classList.add('active');
        }
    } else {
        // إيقاف
        if (window.fileCreatorInstance.deactivate) {
            window.fileCreatorInstance.deactivate();
        }
        alert("⏹️ تم إيقاف File Creator Mode");

        // تحديث الزر
        const btn = document.getElementById('fileCreatorBtn');
        if (btn) {
            btn.innerHTML = '<i class="fas fa-file-plus"></i><span>File Creator</span>';
            btn.style.background = '#2c3e50';
            btn.classList.remove('active');
        }
    }
};

console.log('✅ تم إضافة toggleFileCreatorMode إلى FileCreatorCore');
console.log('📁 FileCreatorCore جاهز للاستخدام!');

} // إغلاق حماية التحميل المكرر