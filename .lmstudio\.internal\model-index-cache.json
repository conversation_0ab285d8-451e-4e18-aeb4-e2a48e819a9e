{"models": [{"domain": "embedding", "contextLength": 2048, "indexedModelIdentifier": "nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "displayName": "Nomic Embed Text v1.5", "containingDirSubpath": "LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF", "containingDirMtime": 1749057307883, "containingDirAbsolutePath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF", "sourceDirectoryType": "user", "format": "gguf", "metadata": {"gguf": {"arch": "nomic-bert", "name": "nomic-embed-text-v1.5", "embeddingLength": 768, "numAttentionHeads": 12, "parameters": "", "bosToken": "[CLS]", "eosToken": "[SEP]", "contextLength": 2048, "numLayers": 12, "version": 2}}, "allFiles": [{"filename": "nomic-embed-text-v1.5.Q4_K_M.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "relPath": "LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "sizeBytes": 84106624}], "concreteModelDirAbsolutePath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF", "entryPoint": {"filename": "nomic-embed-text-v1.5.Q4_K_M.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "relPath": "LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "sizeBytes": 84106624}, "trainedForToolUse": false, "quant": {"bits": 4, "name": "Q4_K_M"}, "arch": "nomic-bert", "params": null, "sizeBytes": 84106624, "selfFiles": [{"filename": "nomic-embed-text-v1.5.Q4_K_M.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "relPath": "LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "sizeBytes": 84106624}], "altIndexedModelIdentifier": "nomic-ai/nomic-embed-text-v1.5-GGUF", "user": "nomic-ai", "model": "nomic-embed-text-v1.5-GGUF", "file": "nomic-embed-text-v1.5.Q4_K_M.gguf", "defaultIdentifier": "text-embedding-nomic-embed-text-v1.5", "autoIdentifiers": ["text-embedding-nomic-embed-text-v1.5", "nomic-ai/text-embedding-nomic-embed-text-v1.5", "text-embedding-nomic-embed-text-v1.5@q4_k_m", "nomic-ai/text-embedding-nomic-embed-text-v1.5@q4_k_m", "nomic-embed-text-v1.5", "nomic-ai/nomic-embed-text-v1.5", "nomic-embed-text-v1.5@q4_k_m", "nomic-ai/nomic-embed-text-v1.5@q4_k_m", "nomic-ai/nomic-embed-text-v1.5-gguf/nomic-embed-text-v1.5.q4_k_m.gguf", "nomic-ai/nomic-embed-text-v1.5-gguf"]}, {"domain": "llm", "contextLength": 16384, "indexedModelIdentifier": "gguff/deepseek-coder-6.7b-instruct/deepseek-coder-6.7b-instruct.Q8_0.gguf", "displayName": "Deepseek Coder 6.7B Instruct", "containingDirSubpath": "gguff/deepseek-coder-6.7b-instruct", "containingDirMtime": 1749055646102, "containingDirAbsolutePath": "E:/المساعد التقني ai/gguff/deepseek-coder-6.7b-instruct", "sourceDirectoryType": "user", "format": "gguf", "metadata": {"gguf": {"arch": "llama", "name": "deepseek-ai_deepseek-coder-6.7b-instruct", "embeddingLength": 4096, "numAttentionHeads": 32, "numKeyValueHeads": 32, "parameters": "8B", "bosToken": "<｜begin▁of▁sentence｜>", "eosToken": "<|EOT|>", "contextLength": 16384, "numLayers": 32, "version": 2}}, "allFiles": [{"filename": "deepseek-coder-6.7b-instruct.Q8_0.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/gguff/deepseek-coder-6.7b-instruct/deepseek-coder-6.7b-instruct.Q8_0.gguf", "relPath": "gguff/deepseek-coder-6.7b-instruct/deepseek-coder-6.7b-instruct.Q8_0.gguf", "sizeBytes": 7163879648}], "concreteModelDirAbsolutePath": "E:/المساعد التقني ai/gguff/deepseek-coder-6.7b-instruct", "entryPoint": {"filename": "deepseek-coder-6.7b-instruct.Q8_0.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/gguff/deepseek-coder-6.7b-instruct/deepseek-coder-6.7b-instruct.Q8_0.gguf", "relPath": "gguff/deepseek-coder-6.7b-instruct/deepseek-coder-6.7b-instruct.Q8_0.gguf", "sizeBytes": 7163879648}, "trainedForToolUse": false, "quant": {"bits": 8, "name": "Q8_0"}, "arch": "llama", "params": "8B", "sizeBytes": 7163879648, "selfFiles": [{"filename": "deepseek-coder-6.7b-instruct.Q8_0.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/gguff/deepseek-coder-6.7b-instruct/deepseek-coder-6.7b-instruct.Q8_0.gguf", "relPath": "gguff/deepseek-coder-6.7b-instruct/deepseek-coder-6.7b-instruct.Q8_0.gguf", "sizeBytes": 7163879648}], "altIndexedModelIdentifier": "gguff/deepseek-coder-6.7b-instruct", "user": "gguff", "model": "deepseek-coder-6.7b-instruct", "file": "deepseek-coder-6.7b-instruct.Q8_0.gguf", "defaultIdentifier": "deepseek-coder-6.7b-instruct", "autoIdentifiers": ["deepseek-coder-6.7b-instruct", "gguff/deepseek-coder-6.7b-instruct", "deepseek-coder-6.7b-instruct@q8_0", "gguff/deepseek-coder-6.7b-instruct@q8_0", "gguff/deepseek-coder-6.7b-instruct/deepseek-coder-6.7b-instruct.q8_0.gguf", "gguff/deepseek-coder-6.7b-instruct"]}], "conflicts": [{"identifier": "nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "models": [{"domain": "embedding", "contextLength": 2048, "indexedModelIdentifier": "nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "displayName": "Nomic Embed Text v1.5", "containingDirSubpath": "LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF", "containingDirMtime": 1749057307883, "containingDirAbsolutePath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF", "sourceDirectoryType": "user", "format": "gguf", "metadata": {"gguf": {"arch": "nomic-bert", "name": "nomic-embed-text-v1.5", "embeddingLength": 768, "numAttentionHeads": 12, "parameters": "", "bosToken": "[CLS]", "eosToken": "[SEP]", "contextLength": 2048, "numLayers": 12, "version": 2}}, "allFiles": [{"filename": "nomic-embed-text-v1.5.Q4_K_M.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "relPath": "LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "sizeBytes": 84106624}], "concreteModelDirAbsolutePath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF", "entryPoint": {"filename": "nomic-embed-text-v1.5.Q4_K_M.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "relPath": "LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "sizeBytes": 84106624}, "trainedForToolUse": false, "quant": {"bits": 4, "name": "Q4_K_M"}, "arch": "nomic-bert", "params": null, "sizeBytes": 84106624, "selfFiles": [{"filename": "nomic-embed-text-v1.5.Q4_K_M.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "relPath": "LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "sizeBytes": 84106624}], "altIndexedModelIdentifier": "nomic-ai/nomic-embed-text-v1.5-GGUF", "user": "nomic-ai", "model": "nomic-embed-text-v1.5-GGUF", "file": "nomic-embed-text-v1.5.Q4_K_M.gguf", "defaultIdentifier": "text-embedding-nomic-embed-text-v1.5", "autoIdentifiers": ["text-embedding-nomic-embed-text-v1.5", "nomic-ai/text-embedding-nomic-embed-text-v1.5", "text-embedding-nomic-embed-text-v1.5@q4_k_m", "nomic-ai/text-embedding-nomic-embed-text-v1.5@q4_k_m", "nomic-embed-text-v1.5", "nomic-ai/nomic-embed-text-v1.5", "nomic-embed-text-v1.5@q4_k_m", "nomic-ai/nomic-embed-text-v1.5@q4_k_m", "nomic-ai/nomic-embed-text-v1.5-gguf/nomic-embed-text-v1.5.q4_k_m.gguf", "nomic-ai/nomic-embed-text-v1.5-gguf"]}, {"domain": "embedding", "contextLength": 2048, "indexedModelIdentifier": "nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "displayName": "Nomic Embed Text v1.5", "containingDirSubpath": "nomic-ai/nomic-embed-text-v1.5-GGUF", "containingDirMtime": 1749057307883, "containingDirAbsolutePath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF", "sourceDirectoryType": "bundled", "format": "gguf", "metadata": {"gguf": {"arch": "nomic-bert", "name": "nomic-embed-text-v1.5", "embeddingLength": 768, "numAttentionHeads": 12, "parameters": "", "bosToken": "[CLS]", "eosToken": "[SEP]", "contextLength": 2048, "numLayers": 12, "version": 2}}, "allFiles": [{"filename": "nomic-embed-text-v1.5.Q4_K_M.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "relPath": "nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "sizeBytes": 84106624}], "concreteModelDirAbsolutePath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF", "entryPoint": {"filename": "nomic-embed-text-v1.5.Q4_K_M.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "relPath": "nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "sizeBytes": 84106624}, "trainedForToolUse": false, "quant": {"bits": 4, "name": "Q4_K_M"}, "arch": "nomic-bert", "params": null, "sizeBytes": 84106624, "selfFiles": [{"filename": "nomic-embed-text-v1.5.Q4_K_M.gguf", "ext": "gguf", "absPath": "E:/المساعد التقني ai/LM Studio/resources/app/.webpack/bin/bundled-models/nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "relPath": "nomic-ai/nomic-embed-text-v1.5-GGUF/nomic-embed-text-v1.5.Q4_K_M.gguf", "sizeBytes": 84106624}], "altIndexedModelIdentifier": "nomic-ai/nomic-embed-text-v1.5-GGUF", "user": "nomic-ai", "model": "nomic-embed-text-v1.5-GGUF", "file": "nomic-embed-text-v1.5.Q4_K_M.gguf", "defaultIdentifier": "", "autoIdentifiers": ["text-embedding-nomic-embed-text-v1.5", "nomic-ai/text-embedding-nomic-embed-text-v1.5", "text-embedding-nomic-embed-text-v1.5@q4_k_m", "nomic-ai/text-embedding-nomic-embed-text-v1.5@q4_k_m", "nomic-embed-text-v1.5", "nomic-ai/nomic-embed-text-v1.5", "nomic-embed-text-v1.5@q4_k_m", "nomic-ai/nomic-embed-text-v1.5@q4_k_m", "nomic-ai/nomic-embed-text-v1.5-gguf/nomic-embed-text-v1.5.q4_k_m.gguf", "nomic-ai/nomic-embed-text-v1.5-gguf"]}]}], "unclassifiedFileAbsPaths": ["E:/المساعد التقني ai/deepseek-coder-6.7b-instruct.Q8_0.gguf", "E:/المساعد التقني ai/LM Studio/chrome_100_percent.pak", "E:/المساعد التقني ai/LM Studio/chrome_200_percent.pak", "E:/المساعد التقني ai/LM Studio/d3dcompiler_47.dll", "E:/المساعد التقني ai/LM Studio/ffmpeg.dll", "E:/المساعد التقني ai/LM Studio/icudtl.dat", "E:/المساعد التقني ai/LM Studio/libEGL.dll", "E:/المساعد التقني ai/LM Studio/libGLESv2.dll", "E:/المساعد التقني ai/LM Studio/LICENSE.electron.txt", "E:/المساعد التقني ai/LM Studio/LICENSES.chromium.html", "E:/المساعد التقني ai/LM Studio/LM Studio.exe", "E:/المساعد التقني ai/LM Studio/resources.pak", "E:/المساعد التقني ai/LM Studio/snapshot_blob.bin", "E:/المساعد التقني ai/LM Studio/Uninstall LM Studio.exe", "E:/المساعد التقني ai/LM Studio/v8_context_snapshot.bin", "E:/المساعد التقني ai/LM Studio/vk_swiftshader.dll", "E:/المساعد التقني ai/LM Studio/vk_swiftshader_icd.json", "E:/المساعد التقني ai/LM Studio/vulkan-1.dll"], "unresolvedVirtualModels": [], "badModels": [], "errors": []}