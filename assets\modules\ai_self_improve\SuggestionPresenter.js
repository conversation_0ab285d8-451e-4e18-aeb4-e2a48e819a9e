/**
 * AI Self-Improvement Suggestion Presenter
 * Advanced GUI for presenting and managing AI improvement suggestions
 */

class SuggestionPresenter {
    constructor() {
        this.isVisible = false;
        this.currentSuggestions = [];
        this.selectedSuggestion = null;
        this.improvementHistory = this.loadHistory();
    }

    // Show improvement suggestions interface
    show(suggestions = []) {
        if (this.isVisible) {
            this.updateSuggestions(suggestions);
            return;
        }

        this.currentSuggestions = suggestions;
        this.createSuggestionPanel();
        this.isVisible = true;
    }

    // Hide suggestions panel
    hide() {
        const panel = document.getElementById('aiImprovementPanel');
        if (panel) {
            panel.remove();
        }
        this.isVisible = false;
    }

    // Create the main suggestions panel
    createSuggestionPanel() {
        const panel = document.createElement('div');
        panel.id = 'aiImprovementPanel';
        panel.innerHTML = this.generatePanelHTML();
        panel.style.cssText = this.getPanelStyles();

        document.body.appendChild(panel);
        this.attachEventListeners();
        this.loadSuggestions();
    }

    // Generate panel HTML
    generatePanelHTML() {
        return `
            <div class="ai-improvement-content">
                <div class="ai-improvement-header">
                    <h3><i class="fas fa-robot"></i> التحسين الذاتي بالذكاء الاصطناعي</h3>
                    <div class="header-controls">
                        <button class="scan-btn" onclick="aiSelfImprove.startScan()">
                            <i class="fas fa-search"></i> فحص جديد
                        </button>
                        <button class="close-btn" onclick="suggestionPresenter.hide()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <div class="ai-improvement-body">
                    <!-- Status Section -->
                    <div class="status-section">
                        <div class="status-item">
                            <i class="fas fa-eye"></i>
                            <span>الفرص المكتشفة: <strong id="opportunityCount">0</strong></span>
                        </div>
                        <div class="status-item">
                            <i class="fas fa-robot"></i>
                            <span>الذكاء المتاح: <strong id="availableAI">جاري التحقق...</strong></span>
                        </div>
                        <div class="status-item">
                            <i class="fas fa-history"></i>
                            <span>التحسينات المطبقة: <strong id="appliedCount">${this.improvementHistory.length}</strong></span>
                        </div>
                    </div>

                    <!-- Suggestions List -->
                    <div class="suggestions-container">
                        <div class="suggestions-header">
                            <h4><i class="fas fa-lightbulb"></i> اقتراحات التحسين</h4>
                            <div class="filter-controls">
                                <select id="severityFilter" class="filter-select">
                                    <option value="all">جميع المستويات</option>
                                    <option value="critical">حرج</option>
                                    <option value="high">عالي</option>
                                    <option value="medium">متوسط</option>
                                    <option value="low">منخفض</option>
                                </select>
                                <select id="typeFilter" class="filter-select">
                                    <option value="all">جميع الأنواع</option>
                                    <option value="security">أمان</option>
                                    <option value="performance">أداء</option>
                                    <option value="code_quality">جودة الكود</option>
                                    <option value="bug_potential">أخطاء محتملة</option>
                                </select>
                            </div>
                        </div>
                        <div class="suggestions-list" id="suggestionsList">
                            <!-- Suggestions will be loaded here -->
                        </div>
                    </div>

                    <!-- Suggestion Details -->
                    <div class="suggestion-details" id="suggestionDetails" style="display: none;">
                        <div class="details-header">
                            <h4><i class="fas fa-info-circle"></i> تفاصيل الاقتراح</h4>
                            <div class="ai-request-buttons">
                                <button class="request-ai-btn" onclick="suggestionPresenter.requestAIImprovement()">
                                    <i class="fas fa-robot"></i> اختر نظام ذكاء اصطناعي
                                </button>
                                <button class="quick-request-btn" onclick="suggestionPresenter.quickAIRequest()">
                                    <i class="fas fa-bolt"></i> طلب سريع
                                </button>
                            </div>
                        </div>
                        <div class="details-content" id="detailsContent">
                            <!-- Details will be loaded here -->
                        </div>
                    </div>

                    <!-- AI Response Section -->
                    <div class="ai-response-section" id="aiResponseSection" style="display: none;">
                        <div class="response-header">
                            <h4><i class="fas fa-brain"></i> رد الذكاء الاصطناعي</h4>
                            <div class="ai-info">
                                <span id="aiAgentName">GPT-4</span>
                                <span class="confidence" id="aiConfidence">95%</span>
                            </div>
                        </div>
                        <div class="response-content">
                            <div class="code-comparison">
                                <div class="code-before">
                                    <h5><i class="fas fa-code"></i> الكود الحالي</h5>
                                    <pre><code id="currentCode"></code></pre>
                                </div>
                                <div class="code-after">
                                    <h5><i class="fas fa-magic"></i> الكود المحسن</h5>
                                    <pre><code id="improvedCode"></code></pre>
                                </div>
                            </div>
                            <div class="explanation">
                                <h5><i class="fas fa-comment-alt"></i> شرح التحسين</h5>
                                <div id="improvementExplanation"></div>
                            </div>
                            <div class="action-buttons">
                                <button class="accept-btn" onclick="suggestionPresenter.acceptSuggestion()">
                                    <i class="fas fa-check"></i> قبول التحسين
                                </button>
                                <button class="reject-btn" onclick="suggestionPresenter.rejectSuggestion()">
                                    <i class="fas fa-times"></i> رفض
                                </button>
                                <button class="modify-btn" onclick="suggestionPresenter.modifySuggestion()">
                                    <i class="fas fa-edit"></i> تعديل يدوي
                                </button>
                                <button class="test-btn" onclick="suggestionPresenter.testSuggestion()">
                                    <i class="fas fa-flask"></i> اختبار
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- History Section -->
                    <div class="history-section">
                        <div class="history-header">
                            <h4><i class="fas fa-history"></i> تاريخ التحسينات</h4>
                            <button class="clear-history-btn" onclick="suggestionPresenter.clearHistory()">
                                <i class="fas fa-trash"></i> مسح التاريخ
                            </button>
                        </div>
                        <div class="history-list" id="historyList">
                            <!-- History items will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Get panel styles
    getPanelStyles() {
        return `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;
    }

    // Attach event listeners
    attachEventListeners() {
        // Filter controls
        document.getElementById('severityFilter').addEventListener('change', () => {
            this.filterSuggestions();
        });

        document.getElementById('typeFilter').addEventListener('change', () => {
            this.filterSuggestions();
        });

        // Close on background click
        document.getElementById('aiImprovementPanel').addEventListener('click', (e) => {
            if (e.target.id === 'aiImprovementPanel') {
                this.hide();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (!this.isVisible) return;

            if (e.key === 'Escape') {
                this.hide();
            } else if (e.key === 'Enter' && e.ctrlKey) {
                this.acceptSuggestion();
            }
        });
    }

    // Load suggestions into the list
    loadSuggestions() {
        const listContainer = document.getElementById('suggestionsList');
        
        if (this.currentSuggestions.length === 0) {
            listContainer.innerHTML = `
                <div class="no-suggestions">
                    <i class="fas fa-check-circle"></i>
                    <h3>لا توجد اقتراحات حالياً</h3>
                    <p>الكود يبدو في حالة جيدة! انقر "فحص جديد" للبحث عن فرص تحسين.</p>
                </div>
            `;
            return;
        }

        listContainer.innerHTML = this.currentSuggestions.map((suggestion, index) => 
            this.generateSuggestionItem(suggestion, index)
        ).join('');

        // Update count
        document.getElementById('opportunityCount').textContent = this.currentSuggestions.length;
    }

    // Generate suggestion item HTML
    generateSuggestionItem(suggestion, index) {
        const severityClass = this.getSeverityClass(suggestion.severity);
        const typeIcon = this.getTypeIcon(suggestion.type);

        return `
            <div class="suggestion-item ${severityClass}" onclick="suggestionPresenter.selectSuggestion(${index})">
                <div class="suggestion-header">
                    <div class="suggestion-info">
                        <i class="${typeIcon}"></i>
                        <span class="suggestion-type">${this.getTypeLabel(suggestion.type)}</span>
                        <span class="suggestion-severity">${this.getSeverityLabel(suggestion.severity)}</span>
                    </div>
                    <div class="suggestion-file">
                        <i class="fas fa-file-code"></i>
                        ${suggestion.file_path}:${suggestion.line_numbers.join(',')}
                    </div>
                </div>
                <div class="suggestion-description">
                    ${suggestion.description}
                </div>
                <div class="suggestion-preview">
                    <code>${this.truncateCode(suggestion.current_code)}</code>
                </div>
            </div>
        `;
    }

    // Select a suggestion for detailed view
    selectSuggestion(index) {
        this.selectedSuggestion = this.currentSuggestions[index];
        this.showSuggestionDetails();

        // Update selection visual
        document.querySelectorAll('.suggestion-item').forEach((item, i) => {
            item.classList.toggle('selected', i === index);
        });
    }

    // Show detailed view of selected suggestion
    showSuggestionDetails() {
        if (!this.selectedSuggestion) return;

        const detailsSection = document.getElementById('suggestionDetails');
        const detailsContent = document.getElementById('detailsContent');

        detailsContent.innerHTML = `
            <div class="detail-item">
                <label><i class="fas fa-tag"></i> النوع:</label>
                <span>${this.getTypeLabel(this.selectedSuggestion.type)}</span>
            </div>
            <div class="detail-item">
                <label><i class="fas fa-exclamation-triangle"></i> الخطورة:</label>
                <span class="${this.getSeverityClass(this.selectedSuggestion.severity)}">
                    ${this.getSeverityLabel(this.selectedSuggestion.severity)}
                </span>
            </div>
            <div class="detail-item">
                <label><i class="fas fa-file-code"></i> الملف:</label>
                <span>${this.selectedSuggestion.file_path}</span>
            </div>
            <div class="detail-item">
                <label><i class="fas fa-list-ol"></i> الأسطر:</label>
                <span>${this.selectedSuggestion.line_numbers.join(', ')}</span>
            </div>
            <div class="detail-item full-width">
                <label><i class="fas fa-info-circle"></i> الوصف:</label>
                <p>${this.selectedSuggestion.description}</p>
            </div>
            <div class="detail-item full-width">
                <label><i class="fas fa-code"></i> الكود الحالي:</label>
                <pre><code>${this.selectedSuggestion.current_code}</code></pre>
            </div>
            <div class="detail-item full-width">
                <label><i class="fas fa-context"></i> السياق:</label>
                <pre><code>${this.selectedSuggestion.context}</code></pre>
            </div>
        `;

        detailsSection.style.display = 'block';
    }

    // Request AI improvement for selected suggestion (with agent selection)
    async requestAIImprovement() {
        if (!this.selectedSuggestion) {
            this.showMessage('يرجى اختيار اقتراح أولاً', 'warning');
            return;
        }

        this.showMessage('اختر نظام الذكاء الاصطناعي الذي تريد الاستعانة به...', 'info');

        try {
            if (window.aiSelfImprove) {
                // Use the flexible AI selection system
                const response = await aiSelfImprove.requestAIImprovement(
                    this.selectedSuggestion,
                    null,
                    true // User-directed selection
                );

                if (response && !response.error) {
                    this.displayAIResponse(response);
                } else if (response && response.error) {
                    this.showMessage(`خطأ: ${response.error}`, 'error');
                }
            } else {
                // Fallback to basic service
                const response = await this.callAIService(this.selectedSuggestion);
                this.displayAIResponse(response);
            }
        } catch (error) {
            console.error('Error requesting AI improvement:', error);
            this.showMessage('خطأ في طلب التحسين من الذكاء الاصطناعي', 'error');
        }
    }

    // Quick AI request using preferred agent
    async quickAIRequest() {
        if (!this.selectedSuggestion) {
            this.showMessage('يرجى اختيار اقتراح أولاً', 'warning');
            return;
        }

        this.showMessage('جاري طلب التحسين السريع...', 'info');

        try {
            if (window.aiSelfImprove) {
                const response = await aiSelfImprove.requestAIImprovement(
                    this.selectedSuggestion,
                    null,
                    false // Use preferred agent
                );

                if (response && !response.error) {
                    this.displayAIResponse(response);
                } else if (response && response.error) {
                    this.showMessage(`خطأ: ${response.error}`, 'error');
                }
            } else {
                // Fallback
                const response = await this.callAIService(this.selectedSuggestion);
                this.displayAIResponse(response);
            }
        } catch (error) {
            console.error('Error in quick AI request:', error);
            this.showMessage('خطأ في الطلب السريع', 'error');
        }
    }

    // Call AI service (placeholder for actual implementation)
    async callAIService(suggestion) {
        // This would interface with the Python backend
        // For now, return a mock response
        return {
            agent_name: 'Augment AI',
            suggested_code: `// كود محسن\n${suggestion.current_code.replace('var ', 'const ')}`,
            explanation: 'تم تحسين الكود باستخدام const بدلاً من var لتحسين الأمان والأداء',
            confidence: 0.95
        };
    }

    // Display AI response
    displayAIResponse(response) {
        const responseSection = document.getElementById('aiResponseSection');
        
        document.getElementById('aiAgentName').textContent = response.agent_name;
        document.getElementById('aiConfidence').textContent = `${Math.round(response.confidence * 100)}%`;
        document.getElementById('currentCode').textContent = this.selectedSuggestion.current_code;
        document.getElementById('improvedCode').textContent = response.suggested_code;
        document.getElementById('improvementExplanation').textContent = response.explanation;

        responseSection.style.display = 'block';
        responseSection.scrollIntoView({ behavior: 'smooth' });
    }

    // Accept AI suggestion
    acceptSuggestion() {
        if (!this.selectedSuggestion) return;

        // Add to history
        this.addToHistory({
            suggestion: this.selectedSuggestion,
            action: 'accepted',
            timestamp: new Date().toISOString()
        });

        this.showMessage('تم قبول التحسين وتطبيقه', 'success');
        this.hide();
    }

    // Reject AI suggestion
    rejectSuggestion() {
        if (!this.selectedSuggestion) return;

        this.addToHistory({
            suggestion: this.selectedSuggestion,
            action: 'rejected',
            timestamp: new Date().toISOString()
        });

        this.showMessage('تم رفض الاقتراح', 'info');
        this.hideAIResponse();
    }

    // Modify suggestion manually
    modifySuggestion() {
        // Open code editor for manual modification
        this.showMessage('فتح محرر الكود للتعديل اليدوي...', 'info');
    }

    // Test suggestion
    testSuggestion() {
        this.showMessage('جاري اختبار التحسين...', 'info');
        // Implement testing logic
    }

    // Hide AI response section
    hideAIResponse() {
        document.getElementById('aiResponseSection').style.display = 'none';
    }

    // Filter suggestions
    filterSuggestions() {
        const severityFilter = document.getElementById('severityFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;

        const filtered = this.currentSuggestions.filter(suggestion => {
            const severityMatch = severityFilter === 'all' || suggestion.severity === severityFilter;
            const typeMatch = typeFilter === 'all' || suggestion.type === typeFilter;
            return severityMatch && typeMatch;
        });

        // Update display with filtered results
        this.displayFilteredSuggestions(filtered);
    }

    // Display filtered suggestions
    displayFilteredSuggestions(suggestions) {
        const listContainer = document.getElementById('suggestionsList');
        listContainer.innerHTML = suggestions.map((suggestion, index) => 
            this.generateSuggestionItem(suggestion, index)
        ).join('');
    }

    // Utility methods
    getSeverityClass(severity) {
        const classes = {
            'critical': 'severity-critical',
            'high': 'severity-high',
            'medium': 'severity-medium',
            'low': 'severity-low'
        };
        return classes[severity] || 'severity-medium';
    }

    getSeverityLabel(severity) {
        const labels = {
            'critical': 'حرج',
            'high': 'عالي',
            'medium': 'متوسط',
            'low': 'منخفض'
        };
        return labels[severity] || severity;
    }

    getTypeIcon(type) {
        const icons = {
            'security': 'fas fa-shield-alt',
            'performance': 'fas fa-tachometer-alt',
            'code_quality': 'fas fa-code',
            'bug_potential': 'fas fa-bug',
            'accessibility': 'fas fa-universal-access'
        };
        return icons[type] || 'fas fa-lightbulb';
    }

    getTypeLabel(type) {
        const labels = {
            'security': 'أمان',
            'performance': 'أداء',
            'code_quality': 'جودة الكود',
            'bug_potential': 'أخطاء محتملة',
            'accessibility': 'إمكانية الوصول'
        };
        return labels[type] || type;
    }

    truncateCode(code) {
        return code.length > 100 ? code.substring(0, 100) + '...' : code;
    }

    // History management
    addToHistory(item) {
        this.improvementHistory.push(item);
        this.saveHistory();
        this.updateHistoryDisplay();
    }

    loadHistory() {
        try {
            return JSON.parse(localStorage.getItem('aiImprovementHistory') || '[]');
        } catch {
            return [];
        }
    }

    saveHistory() {
        localStorage.setItem('aiImprovementHistory', JSON.stringify(this.improvementHistory));
    }

    clearHistory() {
        this.improvementHistory = [];
        this.saveHistory();
        this.updateHistoryDisplay();
        this.showMessage('تم مسح تاريخ التحسينات', 'info');
    }

    updateHistoryDisplay() {
        document.getElementById('appliedCount').textContent = this.improvementHistory.length;
    }

    // Show message
    showMessage(text, type = 'info') {
        const message = document.createElement('div');
        message.className = `ai-message ai-message-${type}`;
        message.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'}"></i>
            <span>${text}</span>
        `;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 5000);
    }
}

// Create global instance
const suggestionPresenter = new SuggestionPresenter();

// Export for global use
if (typeof window !== 'undefined') {
    window.suggestionPresenter = suggestionPresenter;
    window.SuggestionPresenter = SuggestionPresenter;
}
