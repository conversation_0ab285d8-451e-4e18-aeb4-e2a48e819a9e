#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ثغرات محددة لمقارنة محتوى قسم REAL SERVER RESPONSE
"""

import requests
import json
import time
import os

def test_specific_vulnerabilities():
    """اختبار ثغرات محددة لرؤية الفرق في قسم REAL SERVER RESPONSE"""
    
    print("🔥 اختبار ثغرات محددة لمقارنة محتوى قسم REAL SERVER RESPONSE...")
    print("🌐 السيرفر يجب أن يعمل على: http://localhost:8000")
    
    # التحقق من تشغيل السيرفر
    try:
        response = requests.get("http://localhost:8000", timeout=5)
        print("✅ السيرفر يعمل ويستجيب")
    except requests.exceptions.RequestException as e:
        print(f"❌ السيرفر لا يعمل: {e}")
        return
    
    # اختبار ثغرات محددة - مثا<PERSON>QL vs XSS كما ذكرت
    specific_tests = [
        {
            "name": "SQL Injection Test",
            "type": "SQL Injection", 
            "url": "https://httpbin.org/get?id=1",
            "payload": "' OR 1=1 --",
            "test_id": "sql_test"
        },
        {
            "name": "XSS Test",
            "type": "Cross-Site Scripting",
            "url": "https://httpbin.org/get?search=test", 
            "payload": "<script>alert('XSS')</script>",
            "test_id": "xss_test"
        },
        {
            "name": "Command Injection Test",
            "type": "Command Injection",
            "url": "https://httpbin.org/get?cmd=whoami",
            "payload": "; cat /etc/passwd",
            "test_id": "cmd_test"
        },
        {
            "name": "Information Disclosure Test",
            "type": "Information Disclosure",
            "url": "https://httpbin.org/json",
            "payload": "debug=true",
            "test_id": "info_test"
        }
    ]
    
    print(f"\n🎯 اختبار {len(specific_tests)} ثغرات محددة...")
    
    results = []
    
    for i, test in enumerate(specific_tests):
        print(f"\n📸 اختبار {i+1}: {test['name']}")
        print(f"   🎯 النوع: {test['type']}")
        print(f"   💉 Payload: {test['payload']}")
        print(f"   🔗 URL: {test['url']}")
        
        test_data = {
            "url": test['url'],
            "vulnerability_name": test['name'],
            "vulnerability_type": test['type'],
            "payload": test['payload'],
            "report_id": f"comparison_{test['test_id']}",
            "stage": "after"
        }
        
        try:
            print("   📤 إرسال طلب إلى السيرفر...")
            start_time = time.time()
            
            response = requests.post(
                "http://localhost:8000/capture",
                json=test_data,
                timeout=90
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"   📥 استجابة السيرفر: {response.status_code} (استغرق {duration:.1f}s)")
            
            if response.status_code == 200:
                result = response.json()
                print("   ✅ نجح الطلب!")
                
                if result.get('success'):
                    image_path = result.get('screenshot_path')
                    if image_path and os.path.exists(image_path):
                        file_size = os.path.getsize(image_path)
                        print(f"   📁 مسار الصورة: {os.path.basename(image_path)}")
                        print(f"   📊 حجم الملف: {file_size:,} bytes ({file_size/1024:.1f} KB)")
                        
                        # فتح الصورة للفحص البصري
                        import webbrowser
                        webbrowser.open(f"file:///{os.path.abspath(image_path)}")
                        print(f"   🌐 تم فتح الصورة للفحص: {test['name']}")
                        
                        results.append({
                            'test': test['name'],
                            'type': test['type'],
                            'success': True,
                            'image_path': image_path,
                            'file_size': file_size,
                            'duration': duration,
                            'payload': test['payload'],
                            'url': test['url']
                        })
                        
                        # انتظار للفحص البصري
                        print("   ⏳ انتظار 10 ثواني للفحص البصري...")
                        time.sleep(10)
                        
                    else:
                        print("   ❌ ملف الصورة غير موجود")
                        results.append({
                            'test': test['name'],
                            'type': test['type'],
                            'success': False,
                            'error': 'ملف الصورة غير موجود'
                        })
                else:
                    error_msg = result.get('error', 'خطأ غير محدد')
                    print(f"   ❌ فشل الطلب: {error_msg}")
                    results.append({
                        'test': test['name'],
                        'type': test['type'],
                        'success': False,
                        'error': error_msg
                    })
            else:
                print(f"   ❌ خطأ في السيرفر: {response.status_code}")
                print(f"   📝 الرسالة: {response.text[:200]}...")
                results.append({
                    'test': test['name'],
                    'type': test['type'],
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                })
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ خطأ في الاتصال: {e}")
            results.append({
                'test': test['name'],
                'type': test['type'],
                'success': False,
                'error': f'خطأ اتصال: {e}'
            })
        except Exception as e:
            print(f"   ❌ خطأ عام: {e}")
            results.append({
                'test': test['name'],
                'type': test['type'],
                'success': False,
                'error': f'خطأ عام: {e}'
            })
    
    # عرض النتائج للمقارنة
    print(f"\n" + "="*80)
    print("🔍 مقارنة نتائج الثغرات المختلفة:")
    print("="*80)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ اختبارات ناجحة: {len(successful_tests)}/{len(results)}")
    print(f"❌ اختبارات فاشلة: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        print(f"\n📸 الصور المُنشأة للمقارنة:")
        
        for result in successful_tests:
            print(f"\n🔥 {result['test']}")
            print(f"   📁 الملف: {os.path.basename(result['image_path'])}")
            print(f"   📊 الحجم: {result['file_size']:,} bytes ({result['file_size']/1024:.1f} KB)")
            print(f"   ⏱️ وقت المعالجة: {result['duration']:.1f} ثانية")
            print(f"   🎯 النوع: {result['type']}")
            print(f"   💉 Payload: {result['payload']}")
            print(f"   🔗 URL: {result['url']}")
    
    if failed_tests:
        print(f"\n❌ الاختبارات الفاشلة:")
        for result in failed_tests:
            print(f"   ❌ {result['test']}: {result.get('error', 'خطأ غير محدد')}")
    
    print(f"\n🔍 تعليمات الفحص البصري:")
    print(f"   1. افتح كل صورة وابحث عن قسم '🔥🔥🔥 REAL SERVER RESPONSE 🔥🔥🔥'")
    print(f"   2. تحقق من محتوى القسم:")
    print(f"      ✅ هل يعرض JSON response كامل؟")
    print(f"      ✅ هل يعرض HTTP headers؟")
    print(f"      ✅ هل يعرض معلومات الطلب؟")
    print(f"      ❌ أم يعرض 'No server response data available'؟")
    print(f"   3. قارن بين الثغرات المختلفة")
    print(f"   4. حدد أي ثغرات تعرض استجابة كاملة وأيها لا تعرض")
    
    print(f"\n📂 مجلد الصور: assets/modules/bugbounty/screenshots/")
    print(f"🔍 ابحث عن المجلدات: comparison_sql_test, comparison_xss_test, comparison_cmd_test, comparison_info_test")

if __name__ == "__main__":
    test_specific_vulnerabilities()
