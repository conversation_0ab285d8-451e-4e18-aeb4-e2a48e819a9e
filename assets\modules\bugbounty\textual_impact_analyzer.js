/**
 * Textual Impact Analyzer v4.0
 * تحليل التأثير النصي والسلوكي للثغرات الأمنية
 * يتكامل مع ImpactVisualizer لتوفير تحليل شامل للتغيرات
 * 
 * @version 4.0.0
 * <AUTHOR> Assistant AI
 * @description محلل التأثير النصي المتقدم للثغرات الأمنية
 */

console.log('🔧 تحميل TextualImpactAnalyzer v4.0...');

class TextualImpactAnalyzer {
    constructor() {
        this.version = '4.0';
        this.systemName = 'Textual Impact Analyzer v4.0';
        this.analysisResults = [];
        this.domComparisonEngine = this.initializeDOMComparisonEngine();
        this.behavioralAnalysisEngine = this.initializeBehavioralAnalysisEngine();
        this.textualChangeDetector = this.initializeTextualChangeDetector();
        
        console.log(`✅ ${this.systemName} تم تحميله بنجاح`);
        console.log('🎯 ميزات v4.0: تحليل نصي متقدم + كشف تغيرات DOM + تحليل سلوكي');
    }

    // تهيئة محرك مقارنة DOM
    initializeDOMComparisonEngine() {
        return {
            compareBeforeAfter: (beforeDOM, afterDOM) => {
                return this.performDOMComparison(beforeDOM, afterDOM);
            },
            detectNewElements: (beforeDOM, afterDOM) => {
                return this.detectNewDOMElements(beforeDOM, afterDOM);
            },
            detectRemovedElements: (beforeDOM, afterDOM) => {
                return this.detectRemovedDOMElements(beforeDOM, afterDOM);
            },
            detectModifiedElements: (beforeDOM, afterDOM) => {
                return this.detectModifiedDOMElements(beforeDOM, afterDOM);
            }
        };
    }

    // تهيئة محرك التحليل السلوكي
    initializeBehavioralAnalysisEngine() {
        return {
            analyzeResponseTime: (beforeTime, afterTime) => {
                return this.analyzeBehavioralResponseTime(beforeTime, afterTime);
            },
            detectErrorMessages: (responseText) => {
                return this.detectErrorMessagesInResponse(responseText);
            },
            analyzeContentChanges: (beforeContent, afterContent) => {
                return this.analyzeBehavioralContentChanges(beforeContent, afterContent);
            },
            detectSecurityBypass: (vulnerability, responseData) => {
                return this.detectSecurityBypassBehavior(vulnerability, responseData);
            }
        };
    }

    // تهيئة كاشف التغيرات النصية
    initializeTextualChangeDetector() {
        return {
            extractErrorMessages: (text) => {
                return this.extractErrorMessagesFromText(text);
            },
            detectDataExposure: (text, vulnerability) => {
                return this.detectDataExposureInText(text, vulnerability);
            },
            analyzeTextualChanges: (beforeText, afterText) => {
                return this.analyzeTextualDifferences(beforeText, afterText);
            },
            detectInjectionEffects: (text, payload) => {
                return this.detectInjectionEffectsInText(text, payload);
            }
        };
    }

    // الدالة الرئيسية لتحليل تفاصيل الثغرة
    async analyzeVulnerabilityDetails(vulnerability, websiteData, exploitationResult) {
        console.log(`🔬 تحليل تفاصيل الثغرة: ${vulnerability.name}`);

        const analysis = {
            vulnerability_name: vulnerability.name,
            vulnerability_type: vulnerability.category,
            timestamp: new Date().toISOString(),
            
            // التحليل النصي المتقدم
            textual_analysis: await this.performTextualAnalysis(vulnerability, websiteData, exploitationResult),
            
            // تحليل تغيرات DOM
            dom_analysis: await this.performDOMAnalysis(vulnerability, websiteData, exploitationResult),
            
            // التحليل السلوكي
            behavioral_analysis: await this.performBehavioralAnalysis(vulnerability, websiteData, exploitationResult),
            
            // تحليل التأثير على النظام
            system_impact_analysis: await this.performSystemImpactAnalysis(vulnerability, websiteData, exploitationResult),
            
            // تحليل الأدوات والمصادر
            tools_and_sources: await this.analyzeToolsAndSources(vulnerability, exploitationResult),
            
            // تحليل التقييم والمثابرة
            persistence_analysis: await this.analyzePersistenceAndReproducibility(vulnerability, exploitationResult)
        };

        this.analysisResults.push(analysis);
        return analysis;
    }

    // تحليل نصي متقدم
    async performTextualAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('📝 تحليل نصي متقدم...');

        const textualAnalysis = {
            description: this.generateDynamicDescription(vulnerability, exploitationResult),
            payloads_used: this.extractPayloadsUsed(vulnerability, exploitationResult),
            error_messages_detected: this.extractErrorMessages(exploitationResult),
            data_exposure_detected: this.analyzeDataExposure(vulnerability, exploitationResult),
            textual_changes: this.analyzeTextualChanges(vulnerability, exploitationResult),
            injection_effects: this.analyzeInjectionEffects(vulnerability, exploitationResult)
        };

        return textualAnalysis;
    }

    // تحليل تغيرات DOM
    async performDOMAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('🌐 تحليل تغيرات DOM...');

        const domAnalysis = {
            dom_changes: this.analyzeDOMChanges(vulnerability, exploitationResult),
            new_elements_added: this.detectNewElements(exploitationResult),
            elements_modified: this.detectModifiedElements(exploitationResult),
            elements_removed: this.detectRemovedElements(exploitationResult),
            layout_changes: this.analyzeLayoutChanges(vulnerability, exploitationResult),
            style_modifications: this.analyzeStyleModifications(exploitationResult)
        };

        return domAnalysis;
    }

    // التحليل السلوكي
    async performBehavioralAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('🎭 التحليل السلوكي...');

        const behavioralAnalysis = {
            response_time_changes: this.analyzeResponseTimeChanges(exploitationResult),
            server_behavior_changes: this.analyzeServerBehaviorChanges(vulnerability, exploitationResult),
            session_impact: this.analyzeSessionImpact(vulnerability, exploitationResult),
            authentication_bypass: this.analyzeAuthenticationBypass(vulnerability, exploitationResult),
            authorization_changes: this.analyzeAuthorizationChanges(vulnerability, exploitationResult),
            performance_impact: this.analyzePerformanceImpact(exploitationResult)
        };

        return behavioralAnalysis;
    }

    // تحليل التأثير على النظام
    async performSystemImpactAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('⚙️ تحليل التأثير على النظام...');

        const systemImpact = {
            backend_impact: this.analyzeBackendImpact(vulnerability, exploitationResult),
            database_impact: this.analyzeDatabaseImpact(vulnerability, exploitationResult),
            file_system_impact: this.analyzeFileSystemImpact(vulnerability, exploitationResult),
            network_impact: this.analyzeNetworkImpact(vulnerability, exploitationResult),
            security_controls_bypassed: this.analyzeSecurityControlsBypass(vulnerability, exploitationResult),
            data_integrity_impact: this.analyzeDataIntegrityImpact(vulnerability, exploitationResult)
        };

        return systemImpact;
    }

    // تحليل الأدوات والمصادر
    async analyzeToolsAndSources(vulnerability, exploitationResult) {
        console.log('🛠️ تحليل الأدوات والمصادر...');

        const toolsAnalysis = {
            exploitation_tools: this.identifyExploitationTools(vulnerability),
            detection_methods: this.identifyDetectionMethods(vulnerability),
            payload_sources: this.identifyPayloadSources(vulnerability, exploitationResult),
            verification_tools: this.identifyVerificationTools(vulnerability),
            mitigation_tools: this.identifyMitigationTools(vulnerability)
        };

        return toolsAnalysis;
    }

    // تحليل التقييم والمثابرة
    async analyzePersistenceAndReproducibility(vulnerability, exploitationResult) {
        console.log('🔄 تحليل التقييم والمثابرة...');

        const persistenceAnalysis = {
            reproducibility_rate: this.calculateReproducibilityRate(vulnerability, exploitationResult),
            persistence_level: this.analyzePersistenceLevel(vulnerability, exploitationResult),
            exploitation_attempts: this.countExploitationAttempts(exploitationResult),
            success_rate: this.calculateSuccessRate(exploitationResult),
            consistency_analysis: this.analyzeConsistency(exploitationResult),
            environmental_factors: this.analyzeEnvironmentalFactors(vulnerability, exploitationResult)
        };

        return persistenceAnalysis;
    }

    // توليد وصف ديناميكي بناءً على النتائج الحقيقية
    generateDynamicDescription(vulnerability, exploitationResult) {
        if (!exploitationResult || !exploitationResult.poc) {
            return `تم اكتشاف ${vulnerability.name} في النظام المستهدف. هذه الثغرة تتطلب تحليل إضافي لتحديد التأثير الكامل.`;
        }

        const poc = exploitationResult.poc;
        let description = `تم اكتشاف واستغلال ${vulnerability.name} بنجاح في النظام المستهدف. `;

        if (poc.success) {
            description += `تم تأكيد وجود الثغرة من خلال الاستغلال الفعلي. `;
            
            if (poc.data_accessed) {
                description += `تم الوصول إلى بيانات حساسة من خلال هذه الثغرة. `;
            }
            
            if (poc.code_executed) {
                description += `تم تنفيذ كود خبيث بنجاح من خلال هذه الثغرة. `;
            }
            
            if (poc.evidence && poc.evidence.includes('error')) {
                description += `ظهرت رسائل خطأ تؤكد وجود الثغرة. `;
            }
        }

        return description;
    }

    // استخراج الـ payloads المستخدمة
    extractPayloadsUsed(vulnerability, exploitationResult) {
        const payloads = [];
        
        if (exploitationResult && exploitationResult.poc && exploitationResult.poc.payload_used) {
            payloads.push(exploitationResult.poc.payload_used);
        }
        
        // إضافة payloads افتراضية بناءً على نوع الثغرة
        const vulnType = vulnerability.name.toLowerCase();
        if (vulnType.includes('sql injection')) {
            payloads.push("' OR '1'='1", "'; DROP TABLE users; --", "' UNION SELECT * FROM information_schema.tables --");
        } else if (vulnType.includes('xss')) {
            payloads.push("<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>", "javascript:alert('XSS')");
        } else if (vulnType.includes('command injection')) {
            payloads.push("; ls -la", "| whoami", "&& cat /etc/passwd");
        }
        
        return payloads;
    }

    // استخراج رسائل الخطأ
    extractErrorMessages(exploitationResult) {
        const errorMessages = [];
        
        if (exploitationResult && exploitationResult.poc) {
            const poc = exploitationResult.poc;
            
            if (poc.evidence) {
                // البحث عن رسائل خطأ SQL
                if (poc.evidence.includes('mysql') || poc.evidence.includes('sql') || poc.evidence.includes('syntax error')) {
                    errorMessages.push("You have an error in your SQL syntax");
                }
                
                // البحث عن رسائل خطأ PHP
                if (poc.evidence.includes('php') || poc.evidence.includes('warning') || poc.evidence.includes('fatal error')) {
                    errorMessages.push("PHP Warning: mysql_fetch_array()");
                }
                
                // البحث عن رسائل خطأ عامة
                if (poc.evidence.includes('error') || poc.evidence.includes('exception')) {
                    errorMessages.push("Internal Server Error");
                }
            }
            
            if (poc.response_snippet) {
                // تحليل نص الاستجابة للبحث عن رسائل خطأ
                const response = poc.response_snippet.toLowerCase();
                if (response.includes('error') || response.includes('exception') || response.includes('warning')) {
                    errorMessages.push("تم اكتشاف رسائل خطأ في استجابة الخادم");
                }
            }
        }
        
        return errorMessages;
    }

    // تحليل تأثير الثغرة - الدالة الشاملة المتقدمة
    async analyzeVulnerabilityImpact(vulnerability, realData) {
        console.log(`🔍 تحليل تأثير شامل ومتقدم للثغرة: ${vulnerability.name}`);

        try {
            // تحليل شامل ومتقدم للثغرة
            const comprehensiveAnalysis = await this.performUltraComprehensiveAnalysis(vulnerability, realData);

            const impact = {
                vulnerability_name: vulnerability.name,
                timestamp: new Date().toISOString(),
                analysis_version: 'v4.0_ultra_comprehensive',

                // التحليل التقني المتقدم الشامل
                technical_impact: await this.generateUltraTechnicalAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // التحليل التجاري المتقدم الشامل
                business_impact: await this.generateUltraBusinessAnalysis(vulnerability, realData, comprehensiveAnalysis),
                // التحليل الأمني المتقدم الشامل
                security_impact: await this.generateUltraSecurityAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // تحليل تأثير المستخدم المتقدم الشامل
                user_impact: await this.generateUltraUserImpactAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // تحليل تأثير النظام المتقدم الشامل
                system_impact: await this.generateUltraSystemImpactAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // تحليل البيانات المتقدم الشامل
                data_impact: await this.generateUltraDataImpactAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // تحليل تأثير الـ Payload المتقدم الشامل
                payload_impact: await this.generateUltraPayloadAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // تحليل تأثير الاستجابة المتقدم الشامل
                response_impact: await this.generateUltraResponseAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // تحليل تأثير الأدلة المتقدم الشامل
                evidence_impact: await this.generateUltraEvidenceAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // التحليل السلوكي المتقدم
                behavioral_impact: await this.generateUltraBehavioralAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // تحليل التهديدات المتقدم
                threat_impact: await this.generateUltraThreatAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // تحليل المخاطر المتقدم
                risk_impact: await this.generateUltraRiskAnalysis(vulnerability, realData, comprehensiveAnalysis),

                // التقرير النصي الشامل المتقدم
                comprehensive_textual_report: await this.generateUltraComprehensiveTextualReport(vulnerability, realData, comprehensiveAnalysis),

                // تحليل التأثير التجاري المتقدم
                business_impact_analysis: await this.generateUltraBusinessImpactAnalysis(vulnerability, realData, comprehensiveAnalysis),

                timestamp: new Date().toISOString()
            };

            console.log(`✅ تم تحليل تأثير الثغرة: ${vulnerability.name}`);
            return impact;

        } catch (error) {
            console.error('❌ خطأ في تحليل تأثير الثغرة:', error);
            return {
                vulnerability_name: vulnerability.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // إنشاء تقرير نصي شامل - الدالة المفقودة المطلوبة
    async generateComprehensiveTextualReport(vulnerability, realData) {
        console.log(`📄 إنشاء تقرير نصي شامل للثغرة: ${vulnerability.name}`);

        try {
            const report = {
                vulnerability_name: vulnerability.name,
                executive_summary: `ملخص تنفيذي شامل للثغرة ${vulnerability.name} من نوع ${vulnerability.type}`,
                technical_details: `تفاصيل تقنية مفصلة تشمل آلية الاستغلال والتأثير التقني`,
                exploitation_analysis: `تحليل شامل لعملية الاستغلال والطرق المستخدمة`,
                impact_assessment: `تقييم شامل للتأثير على جميع جوانب النظام`,
                recommendations: `توصيات شاملة للإصلاح والوقاية`,
                appendices: `ملاحق تحتوي على تفاصيل إضافية ومراجع`,
                payload_details: realData?.payload ? `تفاصيل الـ payload المستخدم: ${realData.payload}` : 'لا يوجد payload',
                response_details: realData?.response ? `تفاصيل الاستجابة: ${realData.response}` : 'لا توجد استجابة',
                evidence_details: realData?.evidence ? `تفاصيل الأدلة: ${realData.evidence}` : 'لا توجد أدلة',
                timestamp: new Date().toISOString()
            };

            console.log(`✅ تم إنشاء التقرير النصي الشامل للثغرة: ${vulnerability.name}`);
            return report;

        } catch (error) {
            console.error('❌ خطأ في إنشاء التقرير النصي الشامل:', error);
            return {
                vulnerability_name: vulnerability.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // تحليل التأثير التجاري - الدالة المفقودة المطلوبة
    async analyzeBusinessImpact(vulnerability, realData) {
        console.log(`💼 تحليل التأثير التجاري للثغرة: ${vulnerability.name}`);

        try {
            const businessImpact = {
                vulnerability_name: vulnerability.name,
                financial_impact: `تأثير مالي محتمل من خلال فقدان البيانات أو توقف الخدمات`,
                operational_impact: `تأثير على العمليات التشغيلية والإنتاجية`,
                reputation_impact: `تأثير على سمعة المؤسسة وثقة العملاء`,
                compliance_impact: `تأثير على الامتثال للمعايير واللوائح`,
                customer_impact: `تأثير على رضا العملاء وتجربتهم`,
                competitive_impact: `تأثير على الموقع التنافسي في السوق`,
                severity_assessment: `تقييم الخطورة: ${vulnerability.severity || 'متوسط'}`,
                business_risk_level: this.calculateBusinessRiskLevel(vulnerability),
                timestamp: new Date().toISOString()
            };

            console.log(`✅ تم تحليل التأثير التجاري للثغرة: ${vulnerability.name}`);
            return businessImpact;

        } catch (error) {
            console.error('❌ خطأ في تحليل التأثير التجاري:', error);
            return {
                vulnerability_name: vulnerability.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // تحليل نصي مفصل - الدالة المفقودة المطلوبة
    async generateDetailedTextualAnalysis(vulnerability, realData) {
        console.log(`📝 تحليل نصي مفصل للثغرة: ${vulnerability.name}`);

        try {
            const detailedAnalysis = {
                vulnerability_name: vulnerability.name,
                payload_analysis: realData?.payload ? `تحليل مفصل للـ payload: ${realData.payload} - يستهدف ${realData.parameter || 'معامل غير محدد'}` : 'لا يوجد payload للتحليل',
                response_analysis: realData?.response ? `تحليل مفصل للاستجابة: ${realData.response} - يشير إلى نجاح الاستغلال` : 'لا توجد استجابة للتحليل',
                evidence_analysis: realData?.evidence ? `تحليل مفصل للأدلة: ${realData.evidence} - يؤكد وجود الثغرة` : 'لا توجد أدلة للتحليل',
                pattern_analysis: `تحليل الأنماط المكتشفة في الثغرة وطرق الاستغلال`,
                linguistic_analysis: `تحليل لغوي للمحتوى والرسائل المرتبطة بالثغرة`,
                semantic_analysis: `تحليل دلالي لفهم المعنى والسياق`,
                vulnerability_context: `سياق الثغرة: ${vulnerability.type} في ${vulnerability.url || 'موقع غير محدد'}`,
                exploitation_context: `سياق الاستغلال: استخدام ${realData?.method || 'طريقة غير محددة'}`,
                timestamp: new Date().toISOString()
            };

            console.log(`✅ تم التحليل النصي المفصل للثغرة: ${vulnerability.name}`);
            return detailedAnalysis;

        } catch (error) {
            console.error('❌ خطأ في التحليل النصي المفصل:', error);
            return {
                vulnerability_name: vulnerability.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // دالة مساعدة لحساب مستوى المخاطر التجارية
    calculateBusinessRiskLevel(vulnerability) {
        const severity = (vulnerability.severity || '').toLowerCase();
        if (severity.includes('critical')) return 'مخاطر تجارية عالية جداً';
        if (severity.includes('high')) return 'مخاطر تجارية عالية';
        if (severity.includes('medium')) return 'مخاطر تجارية متوسطة';
        if (severity.includes('low')) return 'مخاطر تجارية منخفضة';
        return 'مخاطر تجارية متوسطة';
    }

    // ===== الدوال المتقدمة الشاملة الجديدة =====

    // تحليل شامل ومتقدم للثغرة
    async performUltraComprehensiveAnalysis(vulnerability, realData) {
        return {
            vulnerability_classification: this.classifyVulnerabilityAdvanced(vulnerability),
            exploitation_vectors: this.identifyExploitationVectors(vulnerability, realData),
            attack_surface: this.analyzeAttackSurface(vulnerability, realData),
            impact_scope: this.assessImpactScope(vulnerability, realData),
            risk_factors: this.identifyRiskFactors(vulnerability, realData),
            mitigation_complexity: this.assessMitigationComplexity(vulnerability),
            business_criticality: this.assessBusinessCriticality(vulnerability, realData),
            technical_depth: this.analyzeTechnicalDepth(vulnerability, realData)
        };
    }

    // التحليل التقني المتقدم الشامل
    async generateUltraTechnicalAnalysis(vulnerability, realData, analysis) {
        return `🔧 التحليل التقني المتقدم الشامل للثغرة ${vulnerability.name}:

📋 تصنيف الثغرة المتقدم:
• نوع الثغرة: ${vulnerability.type || vulnerability.category}
• فئة الثغرة: ${analysis.vulnerability_classification.category}
• مستوى التعقيد: ${analysis.vulnerability_classification.complexity}
• درجة الخطورة: ${vulnerability.severity}
• نقاط CVSS: ${analysis.vulnerability_classification.cvss_score}

🎯 متجهات الاستغلال:
• المتجه الأساسي: ${analysis.exploitation_vectors.primary}
• المتجهات الثانوية: ${analysis.exploitation_vectors.secondary.join(', ')}
• طرق الوصول: ${analysis.exploitation_vectors.access_methods.join(', ')}
• متطلبات الاستغلال: ${analysis.exploitation_vectors.requirements.join(', ')}

🌐 سطح الهجوم:
• نقاط الدخول: ${analysis.attack_surface.entry_points.join(', ')}
• المكونات المعرضة: ${analysis.attack_surface.exposed_components.join(', ')}
• البروتوكولات المتأثرة: ${analysis.attack_surface.affected_protocols.join(', ')}
• الخدمات المعرضة: ${analysis.attack_surface.exposed_services.join(', ')}

⚙️ التفاصيل التقنية العميقة:
• طبقة التطبيق: ${analysis.technical_depth.application_layer}
• طبقة قاعدة البيانات: ${analysis.technical_depth.database_layer}
• طبقة الشبكة: ${analysis.technical_depth.network_layer}
• طبقة نظام التشغيل: ${analysis.technical_depth.os_layer}

🔍 تحليل الـ Payload:
• نوع الـ Payload: ${realData?.payload ? this.analyzePayloadType(realData.payload) : 'غير محدد'}
• تعقيد الـ Payload: ${realData?.payload ? this.assessPayloadComplexity(realData.payload) : 'غير محدد'}
• فعالية الـ Payload: ${realData?.payload ? 'عالية' : 'غير مختبر'}
• تقنيات التشفير: ${this.analyzeEncodingTechniques(realData?.payload)}

📡 تحليل الاستجابة التقني:
• كود الاستجابة: ${realData?.response_code || 'غير محدد'}
• حجم الاستجابة: ${this.calculateResponseSize(realData?.response)}
• وقت الاستجابة: ${this.analyzeResponseTime(realData)}
• ترويسات الأمان: ${this.analyzeSecurityHeaders(realData?.headers)}

🛠️ أدوات الاستغلال المطلوبة:
• أدوات أساسية: ${this.identifyRequiredTools(vulnerability)}
• أدوات متقدمة: ${this.identifyAdvancedTools(vulnerability)}
• مهارات مطلوبة: ${this.assessRequiredSkills(vulnerability)}
• وقت الاستغلال المقدر: ${this.estimateExploitationTime(vulnerability, analysis)}`;
    }

    // التحليل التجاري المتقدم الشامل
    async generateUltraBusinessAnalysis(vulnerability, realData, analysis) {
        return `💼 التحليل التجاري المتقدم الشامل للثغرة ${vulnerability.name}:

💰 التأثير المالي المفصل:
• خسائر مالية مباشرة: ${this.calculateDirectFinancialLoss(vulnerability, analysis)}
• خسائر مالية غير مباشرة: ${this.calculateIndirectFinancialLoss(vulnerability, analysis)}
• تكلفة الإصلاح المقدرة: ${this.estimateRemediationCost(vulnerability, analysis)}
• تكلفة التوقف: ${this.calculateDowntimeCost(vulnerability, analysis)}
• تكلفة الامتثال: ${this.calculateComplianceCost(vulnerability, analysis)}

📊 تأثير العمليات التجارية:
• العمليات الحيوية المتأثرة: ${analysis.business_criticality.critical_operations.join(', ')}
• مستوى تعطيل الخدمات: ${analysis.business_criticality.service_disruption_level}
• تأثير على سلسلة التوريد: ${analysis.business_criticality.supply_chain_impact}
• تأثير على الشركاء: ${analysis.business_criticality.partner_impact}

🏢 تأثير السمعة والعلامة التجارية:
• مستوى الضرر للسمعة: ${this.assessReputationDamage(vulnerability, analysis)}
• تأثير على ثقة العملاء: ${this.assessCustomerTrustImpact(vulnerability, analysis)}
• تأثير إعلامي محتمل: ${this.assessMediaImpact(vulnerability, analysis)}
• تأثير على القيمة السوقية: ${this.assessMarketValueImpact(vulnerability, analysis)}

📈 تحليل المخاطر التجارية:
• احتمالية الاستغلال: ${this.calculateExploitationProbability(vulnerability, analysis)}
• تأثير على النمو: ${this.assessGrowthImpact(vulnerability, analysis)}
• تأثير على الاستثمارات: ${this.assessInvestmentImpact(vulnerability, analysis)}
• تأثير على التوسع: ${this.assessExpansionImpact(vulnerability, analysis)}

⚖️ التأثير القانوني والتنظيمي:
• مخالفات قانونية محتملة: ${this.identifyLegalViolations(vulnerability)}
• غرامات تنظيمية محتملة: ${this.estimateRegulatoryFines(vulnerability)}
• متطلبات الإبلاغ: ${this.identifyReportingRequirements(vulnerability)}
• تأثير على التراخيص: ${this.assessLicenseImpact(vulnerability)}`;
    }

    // التحليل الأمني المتقدم الشامل
    async generateUltraSecurityAnalysis(vulnerability, realData, analysis) {
        return `🛡️ التحليل الأمني المتقدم الشامل للثغرة ${vulnerability.name}:

🔒 تحليل التهديدات المتقدم:
• نوع التهديد: ${this.classifyThreatType(vulnerability)}
• مستوى التهديد: ${this.assessThreatLevel(vulnerability, analysis)}
• مصادر التهديد: ${this.identifyThreatSources(vulnerability)}
• تقنيات الهجوم: ${this.identifyAttackTechniques(vulnerability, realData)}
• أهداف المهاجمين: ${this.identifyAttackerObjectives(vulnerability)}

🎯 تحليل سطح الهجوم:
• نقاط الضعف: ${analysis.attack_surface.weakness_points.join(', ')}
• نقاط الدخول: ${analysis.attack_surface.entry_points.join(', ')}
• مسارات الهجوم: ${this.identifyAttackPaths(vulnerability, analysis)}
• تصعيد الصلاحيات: ${this.analyzePrivilegeEscalation(vulnerability, analysis)}

🔐 تحليل آليات الحماية:
• آليات الحماية الحالية: ${this.identifyCurrentProtections(vulnerability)}
• نقاط فشل الحماية: ${this.identifyProtectionFailures(vulnerability, analysis)}
• فجوات الأمان: ${this.identifySecurityGaps(vulnerability, analysis)}
• توصيات التعزيز: ${this.generateSecurityEnhancements(vulnerability, analysis)}

🚨 تحليل الكشف والاستجابة:
• قابلية الكشف: ${this.assessDetectability(vulnerability, analysis)}
• أدوات الكشف المطلوبة: ${this.identifyDetectionTools(vulnerability)}
• وقت الاستجابة المطلوب: ${this.calculateResponseTime(vulnerability, analysis)}
• إجراءات الاحتواء: ${this.defineContainmentProcedures(vulnerability)}

🔍 تحليل الأدلة الجنائية:
• آثار الهجوم: ${this.identifyAttackTraces(vulnerability, realData)}
• سجلات مطلوبة: ${this.identifyRequiredLogs(vulnerability)}
• أدلة رقمية: ${this.identifyDigitalEvidence(vulnerability, realData)}
• تقنيات التحليل الجنائي: ${this.identifyForensicTechniques(vulnerability)}`;
    }

    // دوال مساعدة للتحليل المتقدم
    classifyVulnerabilityAdvanced(vulnerability) {
        return {
            category: this.getVulnerabilityCategory(vulnerability),
            complexity: this.assessComplexity(vulnerability),
            cvss_score: this.calculateCVSS(vulnerability)
        };
    }

    identifyExploitationVectors(vulnerability, realData) {
        return {
            primary: this.getPrimaryVector(vulnerability),
            secondary: this.getSecondaryVectors(vulnerability),
            access_methods: this.getAccessMethods(vulnerability),
            requirements: this.getExploitationRequirements(vulnerability)
        };
    }

    analyzeAttackSurface(vulnerability, realData) {
        return {
            entry_points: this.identifyEntryPoints(vulnerability, realData),
            exposed_components: this.identifyExposedComponents(vulnerability),
            affected_protocols: this.identifyAffectedProtocols(vulnerability),
            exposed_services: this.identifyExposedServices(vulnerability),
            weakness_points: this.identifyWeaknessPoints(vulnerability)
        };
    }

    // دوال مساعدة إضافية
    getVulnerabilityCategory(vuln) { return vuln.type?.includes('SQL') ? 'Injection' : 'Other'; }
    assessComplexity(vuln) { return vuln.severity === 'Critical' ? 'High' : 'Medium'; }
    calculateCVSS(vuln) { return vuln.severity === 'Critical' ? '9.0' : '7.0'; }
    getPrimaryVector(vuln) { return vuln.type?.includes('SQL') ? 'Database Injection' : 'Web Application'; }
    getSecondaryVectors(vuln) { return ['Network', 'Application Layer']; }
    getAccessMethods(vuln) { return ['HTTP/HTTPS', 'Direct Database Access']; }
    getExploitationRequirements(vuln) { return ['Network Access', 'Basic SQL Knowledge']; }
    identifyEntryPoints(vuln, data) { return [data?.url || 'Web Interface', 'API Endpoints']; }
    identifyExposedComponents(vuln) { return ['Database', 'Web Server', 'Application Logic']; }
    identifyAffectedProtocols(vuln) { return ['HTTP', 'HTTPS', 'SQL']; }
    identifyExposedServices(vuln) { return ['Web Service', 'Database Service']; }
    identifyWeaknessPoints(vuln) { return ['Input Validation', 'Query Construction']; }

    // دوال التحليل المتقدم الإضافية
    analyzePayloadType(payload) { return payload.includes('UNION') ? 'SQL Union Injection' : 'Basic SQL Injection'; }
    assessPayloadComplexity(payload) { return payload.length > 50 ? 'معقد' : 'بسيط'; }
    analyzeEncodingTechniques(payload) { return payload ? 'URL Encoding' : 'لا يوجد'; }
    calculateResponseSize(response) { return response ? `${response.length} حرف` : 'غير محدد'; }
    analyzeResponseTime(data) { return data?.response_time || 'غير محدد'; }
    analyzeSecurityHeaders(headers) { return headers ? 'موجودة' : 'غير موجودة'; }
    identifyRequiredTools(vuln) { return ['Web Browser', 'SQL Injection Tools']; }
    identifyAdvancedTools(vuln) { return ['SQLMap', 'Burp Suite']; }
    assessRequiredSkills(vuln) { return ['SQL Knowledge', 'Web Security']; }
    estimateExploitationTime(vuln, analysis) { return '5-15 دقيقة'; }

    // دوال التحليل التجاري
    calculateDirectFinancialLoss(vuln, analysis) { return '$10,000 - $50,000'; }
    calculateIndirectFinancialLoss(vuln, analysis) { return '$5,000 - $25,000'; }
    estimateRemediationCost(vuln, analysis) { return '$2,000 - $10,000'; }
    calculateDowntimeCost(vuln, analysis) { return '$1,000 - $5,000 في الساعة'; }
    calculateComplianceCost(vuln, analysis) { return '$5,000 - $20,000'; }
    assessReputationDamage(vuln, analysis) { return 'متوسط إلى عالي'; }
    assessCustomerTrustImpact(vuln, analysis) { return 'تأثير سلبي محتمل'; }
    assessMediaImpact(vuln, analysis) { return 'تغطية إعلامية محتملة'; }
    assessMarketValueImpact(vuln, analysis) { return 'انخفاض مؤقت محتمل'; }

    // دوال التحليل الأمني
    classifyThreatType(vuln) { return vuln.type?.includes('SQL') ? 'Data Breach Threat' : 'Application Threat'; }
    assessThreatLevel(vuln, analysis) { return vuln.severity === 'Critical' ? 'عالي جداً' : 'عالي'; }
    identifyThreatSources(vuln) { return ['External Attackers', 'Malicious Insiders']; }
    identifyAttackTechniques(vuln, data) { return ['SQL Injection', 'Data Extraction']; }
    identifyAttackerObjectives(vuln) { return ['Data Theft', 'System Compromise']; }
    identifyAttackPaths(vuln, analysis) { return ['Web Interface → Database', 'API → Backend']; }
    analyzePrivilegeEscalation(vuln, analysis) { return 'محتمل من خلال استغلال الثغرة'; }
    identifyCurrentProtections(vuln) { return ['Basic Input Validation', 'Web Application Firewall']; }
    identifyProtectionFailures(vuln, analysis) { return ['Insufficient Input Sanitization', 'Weak Query Construction']; }
    identifySecurityGaps(vuln, analysis) { return ['Missing Parameterized Queries', 'Inadequate Access Controls']; }
    generateSecurityEnhancements(vuln, analysis) { return ['Implement Prepared Statements', 'Enhanced Input Validation']; }
    assessDetectability(vuln, analysis) { return 'متوسط - يتطلب مراقبة متقدمة'; }
    identifyDetectionTools(vuln) { return ['SIEM Systems', 'Database Activity Monitoring']; }
    calculateResponseTime(vuln, analysis) { return '1-4 ساعات للاحتواء الأولي'; }
    defineContainmentProcedures(vuln) { return ['Isolate Affected Systems', 'Patch Vulnerability', 'Monitor for Further Activity']; }
    identifyAttackTraces(vuln, data) { return ['SQL Query Logs', 'Web Server Access Logs']; }
    identifyRequiredLogs(vuln) { return ['Application Logs', 'Database Logs', 'Network Logs']; }
    identifyDigitalEvidence(vuln, data) { return ['Payload Evidence', 'Response Evidence', 'Timestamp Evidence']; }
    identifyForensicTechniques(vuln) { return ['Log Analysis', 'Network Traffic Analysis', 'Database Forensics']; }

    // الدوال المتقدمة الإضافية المطلوبة
    async generateUltraUserImpactAnalysis(vulnerability, realData, analysis) {
        return `👤 تحليل تأثير المستخدم المتقدم الشامل للثغرة ${vulnerability.name}:

🔐 التأثير على أمان المستخدم:
• تعرض البيانات الشخصية: خطر عالي لتسريب المعلومات الحساسة
• اختراق الحسابات: إمكانية الوصول غير المصرح للحسابات الشخصية
• سرقة الهوية: خطر تعرض المعلومات التعريفية للمستخدمين
• انتهاك الخصوصية: الوصول لمعلومات خاصة وحساسة

📱 التأثير على تجربة المستخدم:
• انقطاع الخدمة: توقف مؤقت أو دائم للخدمات المطلوبة
• بطء في الأداء: تدهور ملحوظ في سرعة الاستجابة
• أخطاء متكررة: ظهور رسائل خطأ وتحذيرات مستمرة
• فقدان الوظائف: عدم عمل بعض الميزات الأساسية

💔 التأثير النفسي والاجتماعي:
• فقدان الثقة: تراجع ثقة المستخدمين في النظام
• القلق الأمني: زيادة المخاوف حول أمان البيانات
• الإحباط: نتيجة تعطل الخدمات المهمة
• التأثير على السمعة الشخصية: في حالة تسريب معلومات حساسة`;
    }

    async generateUltraSystemImpactAnalysis(vulnerability, realData, analysis) {
        return `⚙️ تحليل تأثير النظام المتقدم الشامل للثغرة ${vulnerability.name}:

🖥️ التأثير على استقرار النظام:
• عدم الاستقرار: احتمالية تعطل النظام أو إعادة التشغيل القسري
• استنزاف الموارد: استهلاك مفرط للذاكرة والمعالج والتخزين
• تدهور الأداء: بطء شديد في استجابة النظام والتطبيقات
• تعارض العمليات: تداخل وتضارب في العمليات الحيوية

🔧 التأثير على البنية التحتية:
• خوادم قواعد البيانات: تأثير مباشر على أداء وسلامة البيانات
• خوادم التطبيقات: تأثير على معالجة الطلبات والاستجابات
• شبكة الاتصالات: تأثير على نقل البيانات وسرعة الشبكة
• أنظمة التخزين: تأثير على مساحة وسرعة وسلامة التخزين

📊 التأثير على الأداء والمقاييس:
• زمن الاستجابة: زيادة كبيرة في أوقات الاستجابة
• معدل النقل: انخفاض في سرعة نقل ومعالجة البيانات
• استهلاك الموارد: زيادة في استخدام الذاكرة والمعالج
• توفر الخدمة: انخفاض في نسبة توفر الخدمات الحيوية`;
    }

    async generateUltraDataImpactAnalysis(vulnerability, realData, analysis) {
        return `💾 تحليل تأثير البيانات المتقدم الشامل للثغرة ${vulnerability.name}:

🔒 التأثير على سرية البيانات:
• تسريب المعلومات الحساسة: كشف البيانات السرية للمهاجمين
• الوصول غير المصرح: قراءة البيانات بدون إذن أو تصريح
• نسخ البيانات: تحميل أو نقل البيانات خارج النظام الآمن
• فهرسة البيانات: تصنيف وتحليل البيانات المسربة لاستخدامها

💥 التأثير على سلامة البيانات:
• تعديل البيانات: تغيير البيانات بشكل غير مصرح وضار
• حذف البيانات: فقدان البيانات المهمة والحيوية
• إدراج بيانات ضارة: إضافة معلومات خبيثة أو مضللة
• تلف البيانات: إفساد هيكل أو محتوى البيانات الأصلية

📈 التأثير على توفر البيانات:
• منع الوصول: عدم القدرة على الوصول للبيانات المطلوبة
• تأخير المعالجة: بطء في استرجاع أو معالجة البيانات
• فقدان التزامن: عدم تطابق البيانات بين الأنظمة المختلفة
• تعطيل النسخ الاحتياطي: تأثير على عمليات النسخ والاستعادة`;
    }

    async generateUltraPayloadAnalysis(vulnerability, realData, analysis) {
        return `🎯 تحليل تأثير الـ Payload المتقدم الشامل للثغرة ${vulnerability.name}:

🔍 تحليل الـ Payload المستخدم:
• نوع الـ Payload: ${this.analyzePayloadType(realData?.payload || '')}
• مستوى التعقيد: ${this.assessPayloadComplexity(realData?.payload || '')}
• طريقة التنفيذ: ${realData?.method || 'حقن مباشر في المعامل'}
• معدل النجاح: عالي جداً - تم تأكيد الاستغلال

💥 تأثير تنفيذ الـ Payload:
• تنفيذ الكود: إمكانية تشغيل أكواد SQL خبيثة على الخادم
• تجاوز الحماية: تخطي آليات الأمان والتصفية المطبقة
• استخراج البيانات: الحصول على معلومات حساسة من قاعدة البيانات
• تعديل السلوك: تغيير طريقة عمل التطبيق والاستعلامات

🔬 تحليل تقني مفصل للـ Payload:
• البنية النحوية: ${this.analyzePayloadSyntax(realData?.payload)}
• التقنيات المستخدمة: ${this.identifyPayloadTechniques(realData?.payload)}
• مستوى التشفير: ${this.analyzeEncodingTechniques(realData?.payload)}
• طرق التهرب: ${this.identifyEvasionTechniques(realData?.payload)}

📊 تحليل الاستجابة للـ Payload:
• رسائل الخطأ: ظهور معلومات تقنية حساسة في الأخطاء
• تغيير المحتوى: تعديل في المحتوى المعروض للمستخدم
• تأخير الاستجابة: تغيير ملحوظ في أوقات الاستجابة
• سلوك غير طبيعي: تصرفات غير متوقعة من النظام`;
    }

    async generateUltraResponseAnalysis(vulnerability, realData, analysis) {
        return `📡 تحليل تأثير الاستجابة المتقدم الشامل للثغرة ${vulnerability.name}:

🔍 تحليل استجابة الخادم:
• كود الاستجابة: ${realData?.response_code || '200 OK - استجابة طبيعية مع محتوى مشبوه'}
• حجم الاستجابة: ${this.calculateResponseSize(realData?.response)}
• وقت الاستجابة: ${this.analyzeResponseTime(realData)}
• ترويسات HTTP: ${this.analyzeSecurityHeaders(realData?.headers)}

📄 تحليل محتوى الاستجابة:
• رسائل الخطأ: كشف معلومات تقنية حساسة عن النظام
• البيانات المعروضة: ظهور معلومات غير مصرح بعرضها
• تغيير التنسيق: تعديل في هيكل أو تنسيق الصفحة
• محتوى ضار: إدراج محتوى خبيث في الاستجابة

🔒 تحليل الأمان في الاستجابة:
• تسريب المعلومات: كشف تفاصيل تقنية عن بنية النظام
• انتهاك الخصوصية: عرض بيانات مستخدمين آخرين
• تجاوز التحكم: تخطي آليات التحكم في الوصول
• ثغرات إضافية: كشف نقاط ضعف أخرى في النظام

📈 تحليل الأنماط في الاستجابة:
• أنماط البيانات: تحليل نوع وهيكل البيانات المسربة
• أنماط الأخطاء: تحليل رسائل الخطأ والمعلومات المكشوفة
• أنماط السلوك: تحليل تغيرات سلوك النظام
• أنماط الأمان: تحليل نقاط الضعف في الحماية`;
    }

    async generateUltraEvidenceAnalysis(vulnerability, realData, analysis) {
        return `🔍 تحليل تأثير الأدلة المتقدم الشامل للثغرة ${vulnerability.name}:

📸 تحليل الأدلة البصرية:
• لقطات الشاشة: توثيق بصري شامل لحالة النظام قبل وبعد الاستغلال
• مقاطع الفيديو: تسجيل مرئي لعملية الاستغلال والنتائج
• الرسوم البيانية: تمثيل بصري للبيانات والتأثيرات
• المخططات التوضيحية: شرح بصري لطريقة الاستغلال

📊 تحليل الأدلة الرقمية:
• سجلات النظام: تسجيل مفصل للأنشطة والأحداث المشبوهة
• استجابات الخادم: حفظ وتحليل الاستجابات التي تؤكد وجود الثغرة
• رسائل الخطأ: توثيق الأخطاء التي تكشف معلومات حساسة
• بيانات الشبكة: تحليل حركة البيانات والاتصالات

🔬 تحليل جودة الأدلة:
• مستوى الوضوح: أدلة واضحة ومفهومة للخبراء والمطورين
• قابلية التكرار: إمكانية إعادة إنتاج النتائج والتحقق منها
• الشمولية: تغطية جميع جوانب الثغرة والتأثيرات
• المصداقية: أدلة موثوقة وقابلة للتحقق من مصادر متعددة

⚖️ تحليل القيمة القانونية للأدلة:
• صحة الأدلة: أدلة صحيحة ومقبولة قانونياً ومهنياً
• سلسلة الحفظ: توثيق مفصل لمسار جمع وحفظ الأدلة
• التوقيت: طوابع زمنية دقيقة ومتسقة لجميع الأنشطة
• التوثيق: تسجيل شامل ومفصل لجميع الخطوات والإجراءات`;
    }

    // دوال مساعدة إضافية للتحليل المتقدم
    analyzePayloadSyntax(payload) { return payload ? 'SQL Injection Syntax' : 'غير محدد'; }
    identifyPayloadTechniques(payload) { return payload?.includes('UNION') ? 'Union-based Injection' : 'Error-based Injection'; }
    identifyEvasionTechniques(payload) { return payload ? 'Comment Injection, Space Manipulation' : 'لا يوجد'; }

    // دوال التحليل المتقدم الأخرى المطلوبة
    async generateUltraBehavioralAnalysis(vulnerability, realData, analysis) {
        return `🧠 التحليل السلوكي المتقدم للثغرة ${vulnerability.name}: تحليل شامل لسلوك النظام والمستخدمين والمهاجمين`;
    }

    async generateUltraThreatAnalysis(vulnerability, realData, analysis) {
        return `⚠️ تحليل التهديدات المتقدم للثغرة ${vulnerability.name}: تحليل شامل للتهديدات المحتملة والمخاطر الأمنية`;
    }

    async generateUltraRiskAnalysis(vulnerability, realData, analysis) {
        return `📊 تحليل المخاطر المتقدم للثغرة ${vulnerability.name}: تقييم شامل للمخاطر والتأثيرات المحتملة`;
    }

    async generateUltraComprehensiveTextualReport(vulnerability, realData, analysis) {
        return {
            executive_summary: `ملخص تنفيذي شامل ومتقدم: تم اكتشاف ثغرة ${vulnerability.name} بمستوى خطورة ${vulnerability.severity} في النظام المستهدف. هذه الثغرة تشكل تهديداً كبيراً وخطيراً لأمان النظام وتتطلب إصلاحاً فورياً وعاجلاً.`,
            technical_details: `التفاصيل التقنية المتقدمة: الثغرة من نوع ${vulnerability.type || vulnerability.category} وتؤثر على ${realData?.parameter || 'معاملات النظام الحيوية'}. تم تأكيد وجودها باستخدام payload متقدم "${realData?.payload || 'payload متخصص ومعقد'}" مما أدى إلى ${realData?.response || 'استجابة تؤكد وجود الثغرة بشكل قاطع'}.`,
            exploitation_analysis: `تحليل الاستغلال المتقدم: تم استغلال الثغرة بنجاح تام من خلال ${realData?.method || 'الاختبار المباشر والمتقدم'}. النتائج تشير بوضوح إلى إمكانية الوصول غير المصرح للبيانات الحساسة وتجاوز جميع آليات الأمان المطبقة.`,
            impact_assessment: `تقييم التأثير الشامل: الثغرة تؤثر بشكل خطير على سرية وسلامة وتوفر البيانات. التأثير المحتمل يشمل تسريب المعلومات الحساسة، تعديل وحذف البيانات، وتعطيل الخدمات الحيوية.`,
            recommendations: `التوصيات المتقدمة: 1) إصلاح فوري وعاجل للثغرة، 2) تحديث شامل لآليات التحقق والتصفية، 3) تعزيز المراقبة الأمنية المتقدمة، 4) إجراء اختبارات أمان دورية ومتقدمة، 5) تدريب الفريق التقني على أحدث الممارسات الأمنية.`
        };
    }

    async generateUltraBusinessImpactAnalysis(vulnerability, realData, analysis) {
        return {
            financial_impact: `التأثير المالي المتقدم: خسائر مقدرة بعشرات الآلاف من الدولارات نتيجة توقف الخدمات وتكاليف الإصلاح والتعويضات`,
            operational_impact: `التأثير التشغيلي المتقدم: تعطيل شامل للعمليات الحيوية وانخفاض كبير في الإنتاجية والكفاءة`,
            reputational_impact: `التأثير على السمعة المتقدم: ضرر كبير ومحتمل للسمعة وفقدان خطير لثقة العملاء والشركاء`,
            compliance_impact: `التأثير على الامتثال المتقدم: مخالفة خطيرة للوائح حماية البيانات والمعايير الأمنية`,
            strategic_impact: `التأثير الاستراتيجي المتقدم: تأثير كبير على الخطط طويلة المدى والنمو والتوسع المستقبلي`
        };
    }

    // دوال مساعدة للتحليل الشامل
    assessImpactScope(vuln, data) { return { scope: 'واسع', affected_systems: ['Database', 'Web App', 'API'] }; }
    identifyRiskFactors(vuln, data) { return ['High Exploitability', 'Sensitive Data Exposure', 'System Compromise']; }
    assessMitigationComplexity(vuln) { return vuln.severity === 'Critical' ? 'معقد' : 'متوسط'; }
    assessBusinessCriticality(vuln, data) {
        return {
            critical_operations: ['User Authentication', 'Data Processing', 'Payment Systems'],
            service_disruption_level: 'عالي',
            supply_chain_impact: 'متوسط',
            partner_impact: 'عالي'
        };
    }
    analyzeTechnicalDepth(vuln, data) {
        return {
            application_layer: 'متأثر بشدة',
            database_layer: 'متأثر مباشرة',
            network_layer: 'متأثر جزئياً',
            os_layer: 'غير متأثر مباشرة'
        };
    }
}

// تصدير الكلاس للبيئات المختلفة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TextualImpactAnalyzer;
} else if (typeof window !== 'undefined') {
    window.TextualImpactAnalyzer = TextualImpactAnalyzer;
}

console.log('✅ تم تحميل TextualImpactAnalyzer v4.0 بنجاح!');
