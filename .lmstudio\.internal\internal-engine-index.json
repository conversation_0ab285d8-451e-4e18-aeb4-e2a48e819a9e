{"json": [{"libPaths": {"libLmStudioPath": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\llama.cpp-win-x86_64-avx2-1.33.0\\liblmstudio_bindings.node", "engineLibPath": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\llama.cpp-win-x86_64-avx2-1.33.0\\llm_engine.node"}, "manifest": {"name": "llama.cpp-win-x86_64-avx2", "version": "1.33.0", "domains": ["llm", "embedding"], "engine": "llama.cpp", "target_libraries": [{"name": "llm_engine.node", "type": "llm_engine", "version": "0.1.2"}, {"name": "liblmstudio_bindings.node", "type": "liblmstudio", "version": "0.2.26"}], "platform": "win", "cpu": {"architecture": "x86_64", "instruction_set_extensions": ["AVX2"]}, "supported_model_formats": ["gguf"], "vendor_lib_package_names": ["win-llama-cpu-x86-vendor-v1"], "manifest_version": "4", "extension_type": "engine", "minimum_lmstudio_version": null}, "displayData": [["en", {"langKey": "en", "displayName": "CPU llama.cpp (Windows)", "description": "CPU-only llama.cpp engine", "releaseNotes": []}]], "hasGpuAcceleration": false, "dirPath": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\llama.cpp-win-x86_64-avx2-1.33.0", "backendCompatibility": {"status": "Compatible"}, "hardwareSurveyResult": {"gpuSurveyResult": {"result": {"code": "NoDevicesFound", "message": "No gpus found without acceleration backend compilation!"}, "gpuInfo": []}, "cpuSurveyResult": {"result": {"code": "Success", "message": ""}, "cpuInfo": {"name": "", "architecture": "x86_64", "supportedInstructionSetExtensions": ["AVX", "AVX2"]}}}, "amphibianPath": null, "envVars": {"PATH": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\vendor\\win-llama-cpu-x86-vendor-v1"}, "memoryInfo": {"ramCapacity": 17009516544, "vramCapacity": 0, "totalMemory": 17009516544}, "visibleDevicesConfig": null}, {"libPaths": {"libLmStudioPath": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\llama.cpp-win-x86_64-nvidia-cuda-avx2-1.33.0\\liblmstudio_bindings_cuda.node", "engineLibPath": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\llama.cpp-win-x86_64-nvidia-cuda-avx2-1.33.0\\llm_engine_cuda.node"}, "manifest": {"name": "llama.cpp-win-x86_64-nvidia-cuda-avx2", "version": "1.33.0", "domains": ["llm", "embedding"], "engine": "llama.cpp", "target_libraries": [{"name": "llm_engine_cuda.node", "type": "llm_engine", "version": "0.1.2"}, {"name": "liblmstudio_bindings_cuda.node", "type": "liblmstudio", "version": "0.2.26"}], "platform": "win", "cpu": {"architecture": "x86_64", "instruction_set_extensions": ["AVX2"]}, "gpu": {"make": "Nvidia", "framework": "CUDA"}, "supported_model_formats": ["gguf"], "vendor_lib_package_names": ["win-llama-cuda-vendor-v1"], "manifest_version": "4", "extension_type": "engine", "minimum_lmstudio_version": null}, "displayData": [["en", {"langKey": "en", "displayName": "CUDA llama.cpp (Windows)", "description": "Nvidia CUDA accelerated llama.cpp engine", "releaseNotes": []}]], "hasGpuAcceleration": true, "dirPath": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\llama.cpp-win-x86_64-nvidia-cuda-avx2-1.33.0", "backendCompatibility": {"status": "Compatible"}, "hardwareSurveyResult": {"gpuSurveyResult": {"result": {"code": "Success", "message": ""}, "gpuInfo": [{"name": "NVIDIA GeForce MX250", "deviceId": 0, "totalMemoryCapacityBytes": 2147352576, "dedicatedMemoryCapacityBytes": 2147352576, "integrationType": "Discrete", "detectionPlatform": "CUDA", "detectionPlatformVersion": "", "otherInfo": {"deviceUUID": "f3d41e1374958bf0ef8adbd4349c71cd", "computeCapability": "6.1", "driverVersion": "12070"}}]}, "cpuSurveyResult": {"result": {"code": "Success", "message": ""}, "cpuInfo": {"name": "", "architecture": "x86_64", "supportedInstructionSetExtensions": ["AVX", "AVX2"]}}}, "amphibianPath": null, "envVars": {"PATH": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\vendor\\win-llama-cuda-vendor-v1"}, "memoryInfo": {"ramCapacity": 17009516544, "vramCapacity": 2147352576, "totalMemory": 19156869120}, "visibleDevicesConfig": null}, {"libPaths": {"libLmStudioPath": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\llama.cpp-win-x86_64-vulkan-avx2-1.33.0\\liblmstudio_bindings_vulkan.node", "engineLibPath": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\llama.cpp-win-x86_64-vulkan-avx2-1.33.0\\llm_engine_vulkan.node"}, "manifest": {"name": "llama.cpp-win-x86_64-vulkan-avx2", "version": "1.33.0", "domains": ["llm", "embedding"], "engine": "llama.cpp", "target_libraries": [{"name": "llm_engine_vulkan.node", "type": "llm_engine", "version": "0.1.2"}, {"name": "liblmstudio_bindings_vulkan.node", "type": "liblmstudio", "version": "0.2.26"}], "platform": "win", "cpu": {"architecture": "x86_64", "instruction_set_extensions": ["AVX2"]}, "gpu": {"framework": "Vulkan"}, "supported_model_formats": ["gguf"], "vendor_lib_package_names": ["win-llama-vulkan-vendor-v1"], "manifest_version": "4", "extension_type": "engine", "minimum_lmstudio_version": null}, "displayData": [["en", {"langKey": "en", "displayName": "Vulkan llama.cpp (Windows)", "description": "Vulkan accelerated llama.cpp engine", "releaseNotes": []}]], "hasGpuAcceleration": true, "dirPath": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\llama.cpp-win-x86_64-vulkan-avx2-1.33.0", "backendCompatibility": {"status": "Compatible"}, "hardwareSurveyResult": {"gpuSurveyResult": {"result": {"code": "Success", "message": ""}, "gpuInfo": [{"name": "Intel(R) UHD Graphics", "deviceId": 0, "totalMemoryCapacityBytes": 8504758272, "dedicatedMemoryCapacityBytes": 8504758272, "integrationType": "Integrated", "detectionPlatform": "Vulkan", "detectionPlatformVersion": "1.3.283", "otherInfo": {"deviceLUIDValid": "true", "deviceLUID": "ea20010000000000", "deviceUUID": "8680419b020000000000000000000000", "driverID": "5", "driverName": "Intel Corporation", "driverInfo": "Intel driver", "vendorID": "32902"}}, {"name": "NVIDIA GeForce MX250", "deviceId": 1, "totalMemoryCapacityBytes": 10793205760, "dedicatedMemoryCapacityBytes": 2288447488, "integrationType": "Discrete", "detectionPlatform": "Vulkan", "detectionPlatformVersion": "1.3.283", "otherInfo": {"deviceLUIDValid": "true", "cudaComputeCapability": "6.1", "deviceLUID": "7a24010000000000", "deviceUUID": "f3d41e1374958bf0ef8adbd4349c71cd", "driverID": "4", "driverName": "NVIDIA", "driverInfo": "566.24", "vendorID": "4318"}}]}, "cpuSurveyResult": {"result": {"code": "Success", "message": ""}, "cpuInfo": {"name": "", "architecture": "x86_64", "supportedInstructionSetExtensions": ["AVX", "AVX2"]}}}, "amphibianPath": null, "envVars": {"PATH": "C:\\Users\\<USER>\\.lmstudio\\extensions\\backends\\vendor\\win-llama-vulkan-vendor-v1", "GGML_VK_VISIBLE_DEVICES": "1"}, "memoryInfo": {"ramCapacity": 17009516544, "vramCapacity": 2288447488, "totalMemory": 19297964032}, "visibleDevicesConfig": {"visibleDevices": [1], "changesOrder": true}}], "meta": {"values": {"0.manifest.minimum_lmstudio_version": ["undefined"], "0.displayData": ["map"], "0.amphibianPath": ["undefined"], "0.visibleDevicesConfig": ["undefined"], "1.manifest.minimum_lmstudio_version": ["undefined"], "1.displayData": ["map"], "1.amphibianPath": ["undefined"], "1.visibleDevicesConfig": ["undefined"], "2.manifest.minimum_lmstudio_version": ["undefined"], "2.displayData": ["map"], "2.amphibianPath": ["undefined"]}}}