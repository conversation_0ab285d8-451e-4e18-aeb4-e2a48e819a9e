#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لقسم REAL SERVER RESPONSE
"""

import sys
import asyncio
import os
from pathlib import Path

# إضافة المسار للوحدات
sys.path.append('.')

from assets.modules.bugbounty.screenshot_service import ScreenshotService

async def test_simple_real_response():
    """اختبار مبسط لقسم REAL SERVER RESPONSE"""
    
    print("🔥 اختبار مبسط لقسم REAL SERVER RESPONSE...")
    
    service = ScreenshotService()
    
    try:
        # اختبار مع ثغرة SQL Injection بسيطة
        print("📸 التقاط صورة after مع SQL Injection...")
        
        result = await service.capture_with_playwright(
            url='https://httpbin.org/get?test=1',
            filename='simple_sql_test',
            stage='after',
            report_id='simple_test',
            vulnerability_name='SQL Injection',
            payload_data="' UNION SELECT 1,2,3 --",
            vulnerability_type='SQL Injection',
            v4_data={
                'response': 'MySQL Error detected',
                'server_response': 'Database error occurred'
            },
            v4_real_data={
                'actual_response_content': 'MySQL Error: You have an error in your SQL syntax near UNION SELECT',
                'response_data': 'Error 1064: SQL syntax error',
                'full_response_content': 'Complete database error response with details'
            }
        )
        
        if result and result.get('success'):
            image_path = result.get('path')
            print(f"✅ تم التقاط الصورة بنجاح: {image_path}")
            print(f"📊 حجم الملف: {result.get('file_size', 0)} bytes")
            
            # التحقق من وجود الملف
            if os.path.exists(image_path):
                print("✅ ملف الصورة موجود")
                
                # حفظ نسخة مصغرة للفحص
                try:
                    from PIL import Image
                    img = Image.open(image_path)
                    thumbnail_path = image_path.replace('.png', '_simple_thumbnail.png')
                    img.thumbnail((800, 600))
                    img.save(thumbnail_path)
                    print(f"💾 تم حفظ نسخة مصغرة: {thumbnail_path}")
                    print("📁 افتح الصورة للتحقق من وجود قسم 'REAL SERVER RESPONSE'")
                    
                    # فتح الصورة في المتصفح للفحص
                    import webbrowser
                    webbrowser.open(f"file:///{os.path.abspath(image_path)}")
                    
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة الصورة: {e}")
                    
            else:
                print("❌ ملف الصورة غير موجود")
        else:
            print("❌ فشل في التقاط الصورة")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await service.cleanup()
        print("🔒 تم تنظيف الموارد")

if __name__ == "__main__":
    asyncio.run(test_simple_real_response())
