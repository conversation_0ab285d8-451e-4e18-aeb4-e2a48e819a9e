// اختبار النظام الجديد لاستخراج الثغرات
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testNewSystem() {
    console.log('🔍 اختبار النظام الجديد لاستخراج الثغرات...');

    const core = new BugBountyCore();
    
    // محاكاة البرومبت الكامل
    const testPrompt = `
🧠 المهمة: أنت خبير Bug Bounty محترف متخصص في اكتشاف الثغرات الأمنية الحقيقية.

1. **ثغرات الحقن (Injection Vulnerabilities) - التحليل المتقدم:**
   - SQL Injection (Union-based, Boolean-based, Time-based, Error-based)
   - XSS (Reflected, Stored, DOM-based, Mutation XSS)
   - Command Injection (OS, Code, LDAP, XPath, Template)
   - NoSQL Injection (MongoDB, CouchDB, Redis)
   - LDAP Injection (Authentication bypass, Data extraction)
   - XPath Injection (XML data extraction)
   - Template Injection (Server-side, Client-side)
   - Code Injection (PHP, Python, Java, .NET)
   - OS Command Injection (Windows, Linux, macOS)
   - Header Injection (HTTP Response Splitting)

2. **ثغرات المصادقة والتخويل - التحليل الشامل:**
   - Authentication Bypass (Weak passwords, Default credentials)
   - Session Management (Session fixation, hijacking, weak tokens)
   - Privilege Escalation (Horizontal, Vertical)
   - JWT Vulnerabilities (Weak secrets, algorithm confusion)
   - OAuth/SAML Misconfigurations
   - Multi-factor Authentication Bypass
   - Password Reset Vulnerabilities
   - Account Takeover Techniques
   - Brute Force Attacks
   - Credential Stuffing

3. **ثغرات منطق الأعمال - فحص متعمق:**
   - IDOR (Insecure Direct Object References)
   - Race Conditions (Payment, Registration, File upload)
   - Business Logic Bypass (Workflow manipulation)
   - Price Manipulation (Negative values, Currency bypass)
   - Rate Limiting Issues
   - Workflow Manipulation
   - State Machine Vulnerabilities
   - Time-based Logic Flaws
   - Concurrency Issues
   - Data Validation Bypass

4. **ثغرات الشبكة والبنية - تحليل شامل:**
   - SSRF (Server-Side Request Forgery)
   - Open Redirects (Header injection, Parameter manipulation)
   - CORS Misconfigurations (Wildcard origins, Credential exposure)
   - Subdomain Takeover (DNS, CDN, Cloud services)
   - DNS Issues (Zone transfer, Cache poisoning)
   - Network Segmentation Bypass
   - Firewall Evasion
   - Load Balancer Misconfigurations
   - CDN Bypass Techniques
   - VPN Vulnerabilities

5. **ثغرات العميل - فحص متقدم:**
   - CSRF (Cross-Site Request Forgery)
   - Clickjacking (UI redressing, Frame busting bypass)
   - DOM XSS (PostMessage, Hash manipulation)
   - WebSocket Vulnerabilities
   - Client-side Template Injection
   - Browser Cache Poisoning
   - Local Storage Vulnerabilities
   - Session Storage Issues
   - IndexedDB Vulnerabilities
   - Service Worker Attacks

6. **ثغرات الملفات والبيانات:**
   - File Upload (RCE, Path traversal, Content-type bypass)
   - XXE (XML External Entity)
   - Deserialization (Java, .NET, Python, PHP)
   - Path Traversal (Directory traversal, LFI, RFI)
   - Template Injection (Server-side, Client-side)
   - File Inclusion Vulnerabilities
   - Archive Extraction Vulnerabilities
   - Image Processing Vulnerabilities
   - Document Parser Vulnerabilities
   - Backup File Exposure

7. **ثغرات التشفير والأمان:**
   - Weak Cryptography (MD5, SHA1, Weak keys)
   - Insecure Random Number Generation
   - Certificate Issues (Self-signed, Expired, Weak algorithms)
   - Information Disclosure (Error messages, Debug info)
   - Security Headers (CSP, HSTS, X-Frame-Options)
   - TLS/SSL Vulnerabilities
   - Key Management Issues
   - Cryptographic Oracle Attacks
   - Side-channel Attacks
   - Timing Attacks

8. **ثغرات API والخدمات:**
   - REST API Security (Authentication, Authorization)
   - GraphQL Vulnerabilities (Introspection, DoS)
   - SOAP Injection
   - API Rate Limiting
   - Microservices Security
   - gRPC Vulnerabilities
   - WebSocket Security
   - Message Queue Vulnerabilities
   - Event-driven Architecture Issues
   - Service Mesh Security

9. **ثغرات متقدمة وغير تقليدية:**
   - Business Logic Flaws
   - Zero-day Potential
   - Advanced Persistent Threats
   - Social Engineering Vectors
   - Supply Chain Attacks
   - Container Security Issues
   - Kubernetes Vulnerabilities
   - Cloud Misconfigurations
   - DevOps Pipeline Vulnerabilities
   - AI/ML Security Issues
`;

    try {
        console.log('🚀 بدء اختبار استخراج الثغرات من البرومبت...');
        
        // اختبار الدالة الجديدة
        const vulnerabilities = await core.extractAllVulnerabilitiesFromExpandedPrompt(testPrompt, 'https://example.com');
        
        console.log(`\n📊 نتائج الاستخراج:`);
        console.log(`🎯 إجمالي الثغرات المستخرجة: ${vulnerabilities.length}`);
        
        if (vulnerabilities.length > 0) {
            console.log(`\n📋 أول 20 ثغرة مستخرجة:`);
            vulnerabilities.slice(0, 20).forEach((vuln, index) => {
                console.log(`   ${index + 1}. ${vuln.name} (${vuln.severity || 'Unknown'})`);
            });
            
            if (vulnerabilities.length > 20) {
                console.log(`   ... و ${vulnerabilities.length - 20} ثغرة إضافية`);
            }
        }
        
        // مقارنة مع النظام القديم
        console.log(`\n📈 مقارنة:`);
        console.log(`   النظام القديم: ~40 ثغرة`);
        console.log(`   النظام الجديد: ${vulnerabilities.length} ثغرة`);
        
        if (vulnerabilities.length > 100) {
            console.log(`✅ نجح الإصلاح! النظام يستخرج المئات من الثغرات`);
        } else if (vulnerabilities.length > 40) {
            console.log(`🔄 تحسن جزئي - يحتاج مزيد من التحسين`);
        } else {
            console.log(`❌ الإصلاح لم ينجح - لا يزال محدود`);
        }
        
    } catch (error) {
        console.error(`❌ خطأ في الاختبار: ${error.message}`);
    }
}

// تشغيل الاختبار
testNewSystem().catch(console.error);
