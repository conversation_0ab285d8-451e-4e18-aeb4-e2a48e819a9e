# مراقبة الفحص الحقيقي الرئيسي
Write-Host "🔍 بدء مراقبة الفحص الحقيقي الرئيسي..." -ForegroundColor Green
Write-Host "📊 مراقبة سيرفر Python للثغرات المتعددة..." -ForegroundColor Yellow
Write-Host "⏰ الوقت: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan

# متغيرات المراقبة
$vulnerabilityCount = 0
$lastLogSize = 0
$detectedVulnerabilities = @()

Write-Host "`n🚀 جاهز للمراقبة! ابدأ الفحص الحقيقي الآن..." -ForegroundColor Green
Write-Host "📋 سأراقب:" -ForegroundColor Yellow
Write-Host "   - الثغرات المكتشفة" -ForegroundColor White
Write-Host "   - البيانات المرسلة والمستقبلة" -ForegroundColor White
Write-Host "   - التقاط الصور" -ForegroundColor White
Write-Host "   - معالجة البيانات الحقيقية" -ForegroundColor White

# حلقة المراقبة
$monitoringStartTime = Get-Date
$maxMonitoringTime = 600 # 10 دقائق

while ((Get-Date) -lt $monitoringStartTime.AddSeconds($maxMonitoringTime)) {
    try {
        # فحص حالة السيرفر
        try {
            $serverCheck = Invoke-RestMethod -Uri "http://localhost:8000/" -Method GET -TimeoutSec 2 -ErrorAction SilentlyContinue
            $serverStatus = "✅ يعمل"
        } catch {
            $serverStatus = "❌ متوقف"
        }
        
        # فحص الصور الجديدة
        $screenshotsPath = "assets\modules\bugbounty\screenshots"
        if (Test-Path $screenshotsPath) {
            $recentImages = Get-ChildItem -Path $screenshotsPath -Recurse -Filter "*.png" -ErrorAction SilentlyContinue | 
                           Where-Object { $_.LastWriteTime -gt $monitoringStartTime } | 
                           Sort-Object LastWriteTime -Descending
            
            if ($recentImages.Count -gt 0) {
                Write-Host "`n📸 صور جديدة مكتشفة:" -ForegroundColor Green
                foreach ($img in $recentImages | Select-Object -First 5) {
                    $size = [math]::Round($img.Length / 1KB, 2)
                    $timeDiff = [math]::Round(((Get-Date) - $img.LastWriteTime).TotalSeconds, 1)
                    Write-Host "   🖼️ $($img.Name) - ${size}KB - منذ ${timeDiff}s" -ForegroundColor Cyan
                }
            }
        }
        
        # عرض حالة المراقبة
        $elapsed = [math]::Round(((Get-Date) - $monitoringStartTime).TotalSeconds, 1)
        Write-Host "`r⏱️ مراقبة: ${elapsed}s | سيرفر: $serverStatus | ثغرات: $vulnerabilityCount" -NoNewline -ForegroundColor Yellow
        
        # انتظار قصير
        Start-Sleep -Seconds 3
        
    } catch {
        Write-Host "`n❌ خطأ في المراقبة: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep -Seconds 5
    }
}

Write-Host "`n`n🎯 انتهت المراقبة!" -ForegroundColor Green
Write-Host "📊 إجمالي الثغرات المراقبة: $vulnerabilityCount" -ForegroundColor Yellow
Write-Host "⏰ مدة المراقبة: $([math]::Round(((Get-Date) - $monitoringStartTime).TotalMinutes, 1)) دقيقة" -ForegroundColor Cyan
