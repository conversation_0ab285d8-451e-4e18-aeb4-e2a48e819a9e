#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر للسيرفر python_web_service.py
"""

import requests
import json
import time

def test_direct_server():
    """اختبار مباشر للسيرفر لرؤية الاستجابة الحقيقية"""
    
    print("🔥 اختبار مباشر للسيرفر python_web_service.py...")
    print("🌐 السيرفر يجب أن يعمل على: http://localhost:8000")
    
    # التحقق من تشغيل السيرفر
    try:
        response = requests.get("http://localhost:8000", timeout=5)
        print("✅ السيرفر يعمل ويستجيب")
    except requests.exceptions.RequestException as e:
        print(f"❌ السيرفر لا يعمل: {e}")
        return
    
    # اختبار endpoint /capture
    print("\n📸 اختبار endpoint /capture...")
    
    test_data = {
        "url": "https://httpbin.org/json",
        "vulnerability_name": "Information Disclosure Test",
        "vulnerability_type": "Information Disclosure",
        "payload": "debug=true",
        "report_id": "direct_server_test",
        "stage": "after"
    }
    
    try:
        print("📤 إرسال طلب إلى السيرفر...")
        response = requests.post(
            "http://localhost:8000/capture",
            json=test_data,
            timeout=60
        )
        
        print(f"📥 استجابة السيرفر: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ نجح الطلب!")
            print(f"📊 النتيجة: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                image_path = result.get('image_path')
                if image_path:
                    print(f"📁 مسار الصورة: {image_path}")
                    
                    # فتح الصورة للفحص
                    import webbrowser
                    import os
                    if os.path.exists(image_path):
                        webbrowser.open(f"file:///{os.path.abspath(image_path)}")
                        print("🌐 تم فتح الصورة للفحص")
                        print("🔍 تحقق من قسم REAL SERVER RESPONSE!")
                    else:
                        print("❌ ملف الصورة غير موجود")
            else:
                print(f"❌ فشل الطلب: {result.get('error', 'خطأ غير محدد')}")
        else:
            print(f"❌ خطأ في السيرفر: {response.status_code}")
            print(f"📝 الرسالة: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ خطأ في الاتصال: {e}")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

if __name__ == "__main__":
    test_direct_server()
