/**
 * API Configuration Interface
 * User interface for configuring external AI APIs
 */

class APIConfigInterface {
    constructor() {
        this.isVisible = false;
        this.currentStep = 'providers';
        this.selectedProvider = null;
        
        this.init();
    }

    // Initialize the interface
    init() {
        console.log('🔧 تهيئة واجهة تكوين API...');
        this.createInterface();
        console.log('✅ تم تهيئة واجهة تكوين API');
    }

    // Show the interface
    show() {
        if (!this.modal) {
            this.createInterface();
        }

        // إعادة تحميل النماذج المدعومة لضمان الحصول على أحدث البيانات
        if (window.apiManager) {
            console.log('🔄 إعادة تحميل النماذج المدعومة...');
            console.log('📋 النماذج الحالية:', Object.keys(window.apiManager.supportedProviders));

            // التحقق من وجود DeepSeek
            if (window.apiManager.supportedProviders.deepseek) {
                console.log('✅ تم العثور على DeepSeek:', window.apiManager.supportedProviders.deepseek);
            } else {
                console.error('❌ لم يتم العثور على DeepSeek في النماذج المدعومة');
            }
        }

        this.modal.style.display = 'flex';
        this.isVisible = true;
        this.updateContent();

        console.log('📱 عرض واجهة تكوين API');
    }

    // Hide the interface
    hide() {
        if (this.modal) {
            this.modal.style.display = 'none';
        }
        this.isVisible = false;
        console.log('📱 إخفاء واجهة تكوين API');
    }

    // Create the main interface
    createInterface() {
        // Remove existing modal if any
        const existingModal = document.getElementById('apiConfigModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal
        this.modal = document.createElement('div');
        this.modal.id = 'apiConfigModal';
        this.modal.className = 'api-config-modal';
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;

        // Create content container
        this.contentContainer = document.createElement('div');
        this.contentContainer.className = 'api-config-content';
        this.contentContainer.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            color: white;
            position: relative;
        `;

        // Create header
        this.createHeader();
        
        // Create content area
        this.contentArea = document.createElement('div');
        this.contentArea.className = 'api-config-body';
        this.contentContainer.appendChild(this.contentArea);

        // Create footer
        this.createFooter();

        this.modal.appendChild(this.contentContainer);
        document.body.appendChild(this.modal);

        // Add event listeners
        this.addEventListeners();
    }

    // Create header
    createHeader() {
        const header = document.createElement('div');
        header.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        `;

        const title = document.createElement('h2');
        title.innerHTML = '<i class="fas fa-cog"></i> تكوين API للنماذج الخارجية';
        title.style.cssText = `
            margin: 0;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        `;

        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.style.cssText = `
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        `;

        closeBtn.addEventListener('click', () => this.hide());
        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.background = 'rgba(255, 255, 255, 0.3)';
        });
        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.background = 'rgba(255, 255, 255, 0.2)';
        });

        header.appendChild(title);
        header.appendChild(closeBtn);
        this.contentContainer.appendChild(header);
    }

    // Create footer
    createFooter() {
        this.footer = document.createElement('div');
        this.footer.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 25px;
            padding-top: 15px;
            border-top: 2px solid rgba(255, 255, 255, 0.2);
        `;

        this.contentContainer.appendChild(this.footer);
    }

    // Update content based on current step
    updateContent() {
        this.contentArea.innerHTML = '';
        this.footer.innerHTML = '';

        switch (this.currentStep) {
            case 'providers':
                this.showProvidersStep();
                break;
            case 'configure':
                this.showConfigureStep();
                break;
            case 'test':
                this.showTestStep();
                break;
            case 'complete':
                this.showCompleteStep();
                break;
        }
    }

    // Show providers selection step
    showProvidersStep() {
        const title = document.createElement('h3');
        title.innerHTML = '<i class="fas fa-list"></i> اختر مزود API';
        title.style.marginBottom = '20px';

        const description = document.createElement('p');
        description.textContent = 'اختر مزود الذكاء الاصطناعي الذي تريد الاتصال به:';
        description.style.cssText = `
            margin-bottom: 25px;
            opacity: 0.9;
            line-height: 1.6;
        `;

        const providersGrid = document.createElement('div');
        providersGrid.style.cssText = `
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        `;

        // Add provider cards with debugging
        console.log('🔍 النماذج المدعومة:', Object.keys(window.apiManager.supportedProviders));

        Object.entries(window.apiManager.supportedProviders).forEach(([key, provider]) => {
            console.log(`📋 إضافة مزود: ${key} - ${provider.name}`);
            const card = this.createProviderCard(key, provider);
            providersGrid.appendChild(card);
        });

        this.contentArea.appendChild(title);
        this.contentArea.appendChild(description);
        this.contentArea.appendChild(providersGrid);

        // API Status Toggle
        const statusContainer = document.createElement('div');
        statusContainer.style.cssText = `
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        `;

        const statusTitle = document.createElement('h4');
        statusTitle.innerHTML = '<i class="fas fa-power-off"></i> حالة تكامل API';
        statusTitle.style.marginBottom = '15px';

        const statusIndicator = document.createElement('div');
        statusIndicator.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        `;

        const statusIcon = document.createElement('i');
        statusIcon.className = `fas fa-circle`;
        statusIcon.style.cssText = `
            font-size: 1.5rem;
            color: ${window.apiManager.isEnabled ? '#2ecc71' : '#e74c3c'};
        `;

        const statusText = document.createElement('span');
        statusText.textContent = window.apiManager.isEnabled ? 'مفعل' : 'غير مفعل';
        statusText.style.cssText = `
            font-size: 1.2rem;
            font-weight: 600;
        `;

        statusIndicator.appendChild(statusIcon);
        statusIndicator.appendChild(statusText);

        const toggleBtn = this.createButton(
            window.apiManager.isEnabled ? 'إيقاف تكامل API' : 'تفعيل تكامل API',
            window.apiManager.isEnabled ? 'fas fa-toggle-off' : 'fas fa-toggle-on',
            () => {
                const newStatus = window.apiManager.toggle();

                // تحديث الواجهة
                statusIcon.style.color = newStatus ? '#2ecc71' : '#e74c3c';
                statusText.textContent = newStatus ? 'مفعل' : 'غير مفعل';
                toggleBtn.innerHTML = `<i class="fas fa-${newStatus ? 'toggle-off' : 'toggle-on'}"></i> ${newStatus ? 'إيقاف تكامل API' : 'تفعيل تكامل API'}`;
                toggleBtn.className = `api-btn ${newStatus ? 'api-btn-danger' : 'api-btn-success'}`;

                // إشعار صوتي
                if (typeof window.speakText === 'function') {
                    window.speakText(newStatus ? 'تم تفعيل تكامل API' : 'تم إيقاف تكامل API');
                }

                console.log(`🔌 تم ${newStatus ? 'تفعيل' : 'إيقاف'} تكامل API`);
            },
            window.apiManager.isEnabled ? 'danger' : 'success'
        );

        statusContainer.appendChild(statusTitle);
        statusContainer.appendChild(statusIndicator);
        statusContainer.appendChild(toggleBtn);

        this.contentArea.appendChild(statusContainer);

        // Footer buttons
        const configBtn = this.createButton('إدارة المزودين', 'fas fa-cogs', () => {
            // يمكن إضافة واجهة إدارة متقدمة هنا
        });

        this.footer.appendChild(configBtn);
    }

    // Create provider card
    createProviderCard(key, provider) {
        const isConfigured = window.apiManager.apiConfigs[key] && window.apiManager.apiConfigs[key].apiKey;
        const isCurrent = window.apiManager.currentProvider === key;

        const card = document.createElement('div');
        card.style.cssText = `
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid ${isCurrent ? '#2ecc71' : 'rgba(255, 255, 255, 0.3)'};
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
        `;

        const icon = document.createElement('div');
        icon.innerHTML = this.getProviderIcon(key);
        icon.style.cssText = `
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: ${isConfigured ? '#2ecc71' : '#f39c12'};
        `;

        const name = document.createElement('h4');
        name.textContent = provider.name;
        name.style.cssText = `
            margin: 0 0 10px 0;
            font-size: 1.1rem;
        `;

        const status = document.createElement('div');
        status.innerHTML = `
            <i class="fas fa-${isConfigured ? 'check-circle' : 'exclamation-circle'}"></i>
            ${isConfigured ? 'مكون' : 'غير مكون'}
        `;
        status.style.cssText = `
            font-size: 0.9rem;
            color: ${isConfigured ? '#2ecc71' : '#f39c12'};
            margin-bottom: 15px;
        `;

        const configBtn = document.createElement('button');
        configBtn.textContent = isConfigured ? 'إعادة تكوين' : 'تكوين';
        configBtn.style.cssText = `
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        `;

        configBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectedProvider = key;
            this.currentStep = 'configure';
            this.updateContent();
        });

        configBtn.addEventListener('mouseenter', () => {
            configBtn.style.background = 'rgba(255, 255, 255, 0.3)';
        });

        configBtn.addEventListener('mouseleave', () => {
            configBtn.style.background = 'rgba(255, 255, 255, 0.2)';
        });

        // Current provider indicator
        if (isCurrent) {
            const currentBadge = document.createElement('div');
            currentBadge.innerHTML = '<i class="fas fa-star"></i> حالي';
            currentBadge.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: #2ecc71;
                color: white;
                padding: 4px 8px;
                border-radius: 10px;
                font-size: 0.8rem;
            `;
            card.appendChild(currentBadge);
        }

        card.appendChild(icon);
        card.appendChild(name);
        card.appendChild(status);
        card.appendChild(configBtn);

        // Set as current provider on click
        if (isConfigured) {
            card.addEventListener('click', () => {
                try {
                    window.apiManager.setCurrentProvider(key);
                    this.updateContent();
                } catch (error) {
                    console.error('خطأ في تعيين المزود:', error);
                }
            });

            card.addEventListener('mouseenter', () => {
                card.style.background = 'rgba(255, 255, 255, 0.15)';
                card.style.transform = 'translateY(-2px)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.background = 'rgba(255, 255, 255, 0.1)';
                card.style.transform = 'translateY(0)';
            });
        }

        return card;
    }

    // Get provider icon
    getProviderIcon(provider) {
        const icons = {
            openai: '<i class="fas fa-brain"></i>',
            gemini: '<i class="fas fa-gem"></i>',
            'gemini-studio': '<i class="fas fa-star"></i>',
            claude: '<i class="fas fa-robot"></i>',
            cohere: '<i class="fas fa-comments"></i>',
            huggingface: '<i class="fas fa-face-smile"></i>',
            perplexity: '<i class="fas fa-search"></i>',
            groq: '<i class="fas fa-bolt"></i>',
            mistral: '<i class="fas fa-wind"></i>',
            deepseek: '<i class="fas fa-eye"></i>',
            custom: '<i class="fas fa-cog"></i>'
        };
        return icons[provider] || '<i class="fas fa-question"></i>';
    }

    // Show configure step
    showConfigureStep() {
        const provider = window.apiManager.supportedProviders[this.selectedProvider];
        const existingConfig = window.apiManager.apiConfigs[this.selectedProvider] || {};

        const title = document.createElement('h3');
        title.innerHTML = `<i class="fas fa-cog"></i> تكوين ${provider.name}`;
        title.style.marginBottom = '20px';

        const form = document.createElement('form');
        form.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 20px;
        `;

        // API Key field
        const apiKeyGroup = this.createFormGroup(
            'مفتاح API',
            'password',
            'أدخل مفتاح API الخاص بك',
            existingConfig.apiKey || ''
        );
        apiKeyGroup.input.id = 'apiKey';

        // Model selection
        const modelGroup = this.createFormGroup(
            'النموذج',
            'select',
            'اختر النموذج',
            existingConfig.model || provider.models[0],
            provider.models
        );
        modelGroup.input.id = 'model';

        // Voice selection (if provider supports voice)
        let voiceGroup = null;
        if (provider.hasVoice && provider.voiceModels) {
            voiceGroup = this.createFormGroup(
                'الصوت',
                'select',
                'اختر الصوت',
                existingConfig.voice || provider.voiceModels[0],
                provider.voiceModels
            );
            voiceGroup.input.id = 'voice';
        }

        // Custom endpoint for custom provider
        if (this.selectedProvider === 'custom') {
            const endpointGroup = this.createFormGroup(
                'نقطة النهاية',
                'url',
                'https://api.example.com/v1/chat',
                existingConfig.endpoint || ''
            );
            endpointGroup.input.id = 'endpoint';
            form.appendChild(endpointGroup.container);
        }

        form.appendChild(apiKeyGroup.container);
        form.appendChild(modelGroup.container);

        if (voiceGroup) {
            form.appendChild(voiceGroup.container);
        }

        this.contentArea.appendChild(title);
        this.contentArea.appendChild(form);

        // Footer buttons
        const backBtn = this.createButton('رجوع', 'fas fa-arrow-left', () => {
            this.currentStep = 'providers';
            this.updateContent();
        });

        const saveBtn = this.createButton('حفظ واختبار', 'fas fa-save', () => {
            this.saveConfiguration();
        }, 'success');

        this.footer.appendChild(backBtn);
        this.footer.appendChild(saveBtn);
    }

    // Create form group
    createFormGroup(label, type, placeholder, value = '', options = []) {
        const container = document.createElement('div');
        container.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 8px;
        `;

        const labelEl = document.createElement('label');
        labelEl.textContent = label;
        labelEl.style.cssText = `
            font-weight: 600;
            font-size: 1rem;
        `;

        let input;
        if (type === 'select') {
            input = document.createElement('select');
            options.forEach(option => {
                const optionEl = document.createElement('option');
                optionEl.value = option;
                optionEl.textContent = option;
                if (option === value) {
                    optionEl.selected = true;
                }
                input.appendChild(optionEl);
            });
        } else {
            input = document.createElement('input');
            input.type = type;
            input.value = value;
        }

        input.placeholder = placeholder;
        input.style.cssText = `
            padding: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        `;

        input.addEventListener('focus', () => {
            input.style.borderColor = 'rgba(255, 255, 255, 0.6)';
            input.style.background = 'rgba(255, 255, 255, 0.15)';
        });

        input.addEventListener('blur', () => {
            input.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            input.style.background = 'rgba(255, 255, 255, 0.1)';
        });

        container.appendChild(labelEl);
        container.appendChild(input);

        return { container, input };
    }

    // Create button
    createButton(text, icon, onClick, type = 'default') {
        const colors = {
            default: 'rgba(255, 255, 255, 0.2)',
            success: 'rgba(46, 204, 113, 0.8)',
            danger: 'rgba(231, 76, 60, 0.8)',
            warning: 'rgba(243, 156, 18, 0.8)'
        };

        const button = document.createElement('button');
        button.innerHTML = `<i class="${icon}"></i> ${text}`;
        button.style.cssText = `
            background: ${colors[type]};
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        `;

        button.addEventListener('click', onClick);
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
            button.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.3)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0)';
            button.style.boxShadow = 'none';
        });

        return button;
    }

    // Save configuration
    async saveConfiguration() {
        const apiKey = document.getElementById('apiKey').value.trim();
        const model = document.getElementById('model').value;
        const voice = document.getElementById('voice')?.value;
        const endpoint = document.getElementById('endpoint')?.value.trim();

        if (!apiKey && this.selectedProvider !== 'custom') {
            alert('يرجى إدخال مفتاح API');
            return;
        }

        if (this.selectedProvider === 'custom' && !endpoint) {
            alert('يرجى إدخال نقطة النهاية');
            return;
        }

        const config = {
            apiKey: apiKey,
            model: model
        };

        if (voice) {
            config.voice = voice;
        }

        if (endpoint) {
            config.endpoint = endpoint;
        }

        try {
            window.apiManager.configureProvider(this.selectedProvider, config);
            this.currentStep = 'test';
            this.updateContent();
        } catch (error) {
            alert(`خطأ في الحفظ: ${error.message}`);
        }
    }

    // Show test step
    showTestStep() {
        const provider = window.apiManager.supportedProviders[this.selectedProvider];

        const title = document.createElement('h3');
        title.innerHTML = `<i class="fas fa-flask"></i> اختبار ${provider.name}`;
        title.style.marginBottom = '20px';

        const description = document.createElement('p');
        description.textContent = 'جاري اختبار الاتصال مع المزود...';
        description.style.cssText = `
            margin-bottom: 25px;
            opacity: 0.9;
            line-height: 1.6;
            text-align: center;
        `;

        const loadingSpinner = document.createElement('div');
        loadingSpinner.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        loadingSpinner.style.cssText = `
            text-align: center;
            font-size: 3rem;
            margin: 30px 0;
            color: #f39c12;
        `;

        this.contentArea.appendChild(title);
        this.contentArea.appendChild(description);
        this.contentArea.appendChild(loadingSpinner);

        // Test the connection
        this.testConnection();
    }

    // Test connection
    async testConnection() {
        try {
            console.log(`🧪 بدء اختبار الاتصال مع ${this.selectedProvider}...`);

            const isWorking = await window.apiManager.testProvider(this.selectedProvider);

            if (isWorking) {
                console.log('✅ نجح اختبار الاتصال');
                this.showTestSuccess();
            } else {
                console.log('❌ فشل اختبار الاتصال');
                this.showTestFailure('فشل في الاتصال - تحقق من الإعدادات');
            }
        } catch (error) {
            console.error('❌ خطأ في اختبار الاتصال:', error);

            // تحسين رسائل الخطأ
            let errorMessage = error.message;

            if (errorMessage.includes('401')) {
                errorMessage = '🔑 مفتاح API غير صحيح أو منتهي الصلاحية';
            } else if (errorMessage.includes('403')) {
                errorMessage = '🚫 ليس لديك صلاحية للوصول لهذا النموذج';
            } else if (errorMessage.includes('429')) {
                errorMessage = '⏰ تم تجاوز حد الطلبات - حاول لاحقاً';
            } else if (errorMessage.includes('404')) {
                errorMessage = '❓ النموذج أو نقطة النهاية غير موجودة';
            } else if (errorMessage.includes('network')) {
                errorMessage = '🌐 مشكلة في الاتصال بالإنترنت';
            } else if (!errorMessage || errorMessage === 'Failed to fetch') {
                errorMessage = '🔌 فشل في الاتصال - تحقق من الإنترنت والإعدادات';
            }

            this.showTestFailure(errorMessage);
        }
    }

    // Show test success
    showTestSuccess() {
        this.contentArea.innerHTML = '';

        const title = document.createElement('h3');
        title.innerHTML = `<i class="fas fa-check-circle" style="color: #2ecc71;"></i> نجح الاختبار!`;
        title.style.cssText = `
            margin-bottom: 20px;
            text-align: center;
            color: #2ecc71;
        `;

        const description = document.createElement('p');
        description.textContent = 'تم الاتصال بنجاح! يمكنك الآن استخدام هذا المزود.';
        description.style.cssText = `
            margin-bottom: 25px;
            opacity: 0.9;
            line-height: 1.6;
            text-align: center;
        `;

        this.contentArea.appendChild(title);
        this.contentArea.appendChild(description);

        // Footer buttons
        this.footer.innerHTML = '';
        
        const setCurrentBtn = this.createButton('تعيين كمزود حالي', 'fas fa-star', () => {
            window.apiManager.setCurrentProvider(this.selectedProvider);
            this.currentStep = 'complete';
            this.updateContent();
        }, 'success');

        const backBtn = this.createButton('رجوع للقائمة', 'fas fa-list', () => {
            this.currentStep = 'providers';
            this.updateContent();
        });

        this.footer.appendChild(backBtn);
        this.footer.appendChild(setCurrentBtn);
    }

    // Show test failure
    showTestFailure(error) {
        this.contentArea.innerHTML = '';

        const title = document.createElement('h3');
        title.innerHTML = `<i class="fas fa-exclamation-triangle" style="color: #e74c3c;"></i> فشل الاختبار`;
        title.style.cssText = `
            margin-bottom: 20px;
            text-align: center;
            color: #e74c3c;
        `;

        const description = document.createElement('p');
        description.textContent = `خطأ: ${error}`;
        description.style.cssText = `
            margin-bottom: 25px;
            opacity: 0.9;
            line-height: 1.6;
            text-align: center;
            color: #e74c3c;
        `;

        this.contentArea.appendChild(title);
        this.contentArea.appendChild(description);

        // Footer buttons
        this.footer.innerHTML = '';
        
        const retryBtn = this.createButton('إعادة المحاولة', 'fas fa-redo', () => {
            this.currentStep = 'configure';
            this.updateContent();
        }, 'warning');

        const backBtn = this.createButton('رجوع للقائمة', 'fas fa-list', () => {
            this.currentStep = 'providers';
            this.updateContent();
        });

        this.footer.appendChild(backBtn);
        this.footer.appendChild(retryBtn);
    }

    // Show complete step
    showCompleteStep() {
        this.contentArea.innerHTML = '';

        const title = document.createElement('h3');
        title.innerHTML = `<i class="fas fa-check-circle" style="color: #2ecc71;"></i> تم التكوين بنجاح!`;
        title.style.cssText = `
            margin-bottom: 20px;
            text-align: center;
            color: #2ecc71;
        `;

        const description = document.createElement('p');
        description.innerHTML = `
            تم تكوين ${window.apiManager.supportedProviders[this.selectedProvider].name} بنجاح وتعيينه كمزود حالي.<br>
            يمكنك الآن استخدام المساعد للتواصل مع النماذج الخارجية!
        `;
        description.style.cssText = `
            margin-bottom: 25px;
            opacity: 0.9;
            line-height: 1.6;
            text-align: center;
        `;

        this.contentArea.appendChild(title);
        this.contentArea.appendChild(description);

        // Footer buttons
        this.footer.innerHTML = '';
        
        const doneBtn = this.createButton('تم', 'fas fa-check', () => {
            this.hide();
        }, 'success');

        const configMoreBtn = this.createButton('تكوين مزود آخر', 'fas fa-plus', () => {
            this.currentStep = 'providers';
            this.updateContent();
        });

        this.footer.appendChild(configMoreBtn);
        this.footer.appendChild(doneBtn);
    }

    // Add event listeners
    addEventListeners() {
        // Close on outside click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });

        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });
    }
}

// Create global instance
const apiConfigInterface = new APIConfigInterface();

// Export for global use
if (typeof window !== 'undefined') {
    window.apiConfigInterface = apiConfigInterface;
    window.APIConfigInterface = APIConfigInterface;
}
