// اختبار سيرفر Python
async function testPythonServer() {
    console.log('🔍 اختبار سيرفر Python...');
    
    try {
        // اختبار 1: Health Check
        console.log('📡 اختبار 1: Health Check...');
        const healthResponse = await fetch('http://localhost:8000/health');
        const healthData = await healthResponse.json();
        console.log('✅ Health Check:', healthData.status);
        
        // اختبار 2: Screenshot Capture
        console.log('📡 اختبار 2: Screenshot Capture...');
        const captureData = {
            url: 'https://httpbin.org/get',
            vulnerability_name: 'Test_Vulnerability',
            stage: 'before',
            report_id: 'test_' + Date.now()
        };
        
        const captureResponse = await fetch('http://localhost:8000/capture', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(captureData)
        });
        
        if (captureResponse.ok) {
            const captureResult = await captureResponse.json();
            console.log('✅ Screenshot Capture نجح:', captureResult.success);
            console.log('📄 الرسالة:', captureResult.message);
            
            if (captureResult.image_path) {
                console.log('🖼️ مسار الصورة:', captureResult.image_path);
            }
        } else {
            console.log('❌ Screenshot Capture فشل:', captureResponse.status);
            const errorText = await captureResponse.text();
            console.log('📄 خطأ:', errorText);
        }
        
        // اختبار 3: Vulnerability Sequence
        console.log('📡 اختبار 3: Vulnerability Sequence...');
        const sequenceData = {
            url: 'https://httpbin.org/get',
            vulnerability_name: 'SQL_Injection_Test',
            report_id: 'test_sequence_' + Date.now()
        };
        
        const sequenceResponse = await fetch('http://localhost:8000/vulnerability_sequence', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(sequenceData)
        });
        
        if (sequenceResponse.ok) {
            const sequenceResult = await sequenceResponse.json();
            console.log('✅ Vulnerability Sequence نجح:', sequenceResult.success);
            console.log('📄 الرسالة:', sequenceResult.message);
            console.log('📊 المراحل الناجحة:', sequenceResult.successful_stages);
        } else {
            console.log('❌ Vulnerability Sequence فشل:', sequenceResponse.status);
        }
        
        console.log('\n🎯 ملخص الاختبار:');
        console.log('✅ السيرفر يعمل بشكل صحيح');
        console.log('✅ جميع endpoints تستجيب');
        console.log('✅ لا توجد مشاكل في الاستجابة');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار السيرفر:', error.message);
    }
}

// تشغيل الاختبار
testPythonServer();
