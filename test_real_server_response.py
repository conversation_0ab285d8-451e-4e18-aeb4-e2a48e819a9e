#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قسم REAL SERVER RESPONSE في الصور
"""

import sys
import asyncio
import os
from pathlib import Path

# إضافة المسار للوحدات
sys.path.append('.')

from assets.modules.bugbounty.screenshot_service import ScreenshotService

async def test_real_server_response():
    """اختبار قسم REAL SERVER RESPONSE في صور بعد الاستغلال"""
    
    print("🔥 بدء اختبار قسم REAL SERVER RESPONSE...")
    
    service = ScreenshotService()
    
    try:
        # اختبار مع ثغرة SQL Injection
        print("📸 التقاط صور مع ثغرة SQL Injection...")
        
        result = await service.capture_vulnerability_sequence(
            url='https://httpbin.org/get?id=1',
            vulnerability_name='SQL Injection Test',
            report_id='real_server_test',
            payload_data="' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --",
            vulnerability_type='SQL Injection',
            v4_data={
                'response': 'MySQL Error: You have an error in your SQL syntax',
                'server_response': 'Database connection failed: Access denied for user'
            },
            v4_real_data={
                'actual_response': 'MySQL Error: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 at line 1'
            }
        )
        
        if result and result.get('success'):
            print("✅ تم التقاط الصور بنجاح")
            print(f"📊 عدد الصور: {result.get('total_screenshots', 0)}")
            
            # فحص صورة after
            after_result = result.get('after')
            if after_result:
                image_path = after_result.get('path')
                print(f"📁 مسار صورة after: {image_path}")
                print(f"📊 حجم الملف: {after_result.get('file_size', 0)} bytes")
                
                # التحقق من وجود الملف
                if os.path.exists(image_path):
                    print("✅ ملف الصورة موجود")
                    
                    # فحص محتوى الصورة باستخدام OCR بسيط
                    try:
                        from PIL import Image
                        import pytesseract
                        
                        # قراءة الصورة
                        img = Image.open(image_path)
                        print(f"📐 أبعاد الصورة: {img.size}")
                        
                        # استخراج النص من الصورة
                        extracted_text = pytesseract.image_to_string(img)
                        
                        # البحث عن قسم REAL SERVER RESPONSE
                        if "REAL SERVER RESPONSE" in extracted_text:
                            print("✅ تم العثور على قسم REAL SERVER RESPONSE في الصورة!")
                            print("📝 النص المستخرج:")
                            print(extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text)
                        else:
                            print("❌ لم يتم العثور على قسم REAL SERVER RESPONSE في الصورة")
                            print("📝 النص المستخرج (أول 500 حرف):")
                            print(extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text)
                            
                    except ImportError:
                        print("⚠️ pytesseract غير متوفر - لا يمكن فحص محتوى الصورة")
                        print("💡 تثبيت: pip install pytesseract pillow")
                        
                        # فحص بديل - حفظ نسخة مصغرة للفحص اليدوي
                        try:
                            from PIL import Image
                            img = Image.open(image_path)
                            # حفظ نسخة مصغرة للفحص
                            thumbnail_path = image_path.replace('.png', '_thumbnail.png')
                            img.thumbnail((800, 600))
                            img.save(thumbnail_path)
                            print(f"💾 تم حفظ نسخة مصغرة: {thumbnail_path}")
                        except:
                            pass
                            
                else:
                    print("❌ ملف الصورة غير موجود")
            else:
                print("❌ لم يتم التقاط صورة after")
                
        else:
            print("❌ فشل في التقاط الصور")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await service.cleanup()
        print("🔒 تم تنظيف الموارد")

if __name__ == "__main__":
    asyncio.run(test_real_server_response())
