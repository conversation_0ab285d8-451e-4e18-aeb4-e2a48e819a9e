﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ظ†ط¸ط§ظ… ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط± ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ„ظ„ظ…ظˆط§ظ‚ط¹ - Bug Bounty System v4.0
ظٹط³طھط®ط¯ظ… Selenium ظˆ Playwright ظ„ط§ظ„طھظ‚ط§ط· طµظˆط± ط­ظ‚ظٹظ‚ظٹط© ط¹ط§ظ„ظٹط© ط§ظ„ط¬ظˆط¯ط©
ظ…ط¹ ط¯ط¹ظ… ظƒط§ظ…ظ„ ظ„ظ„ط±ط¨ط· ظ…ط¹ ط§ظ„ظ†ط¸ط§ظ… v4 ظˆط§ظ„طھظ‚ط§ط±ظٹط±
"""

import os
import sys
import json
import time
import base64
import asyncio
import subprocess
import shutil
from datetime import datetime
from pathlib import Path
import logging
import traceback
import hashlib

# ط¥ط¹ط¯ط§ط¯ ط§ظ„طھط³ط¬ظٹظ„ ط§ظ„ظ…ط­ط³ظ†
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('screenshot_service.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ScreenshotService:
    def __init__(self):
        # ط¥ط¹ط¯ط§ط¯ ظ…ط¬ظ„ط¯ ط§ظ„طµظˆط± ظپظٹ ط§ظ„ظ…ط³ط§ط± ط§ظ„طµط­ظٹط­ ظ„ظ„ظ†ط¸ط§ظ… v4
        base_dir = Path(__file__).parent.parent.parent.parent  # ط§ظ„ط¹ظˆط¯ط© ط¥ظ„ظ‰ ط§ظ„ظ…ط¬ظ„ط¯ ط§ظ„ط±ط¦ظٹط³ظٹ
        self.screenshots_dir = base_dir / "assets" / "modules" / "bugbounty" / "screenshots"
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)
        self.selenium_driver = None
        self.playwright_browser = None
        self.playwright = None
        self.session_id = hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
        self.stats = {
            'total_screenshots': 0,
            'successful_captures': 0,
            'failed_captures': 0,
            'selenium_captures': 0,
            'playwright_captures': 0
        }

        # ط¥ظ†ط´ط§ط، ظ…ط¬ظ„ط¯ ط§ظ„ط¬ظ„ط³ط©
        self.session_dir = self.screenshots_dir / f"session_{self.session_id}"
        self.session_dir.mkdir(exist_ok=True)

        logger.info(f"ًںڑ€ طھظ… طھظ‡ظٹط¦ط© ط®ط¯ظ…ط© ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط± - ط§ظ„ط¬ظ„ط³ط©: {self.session_id}")
        logger.info(f"ًں“پ ظ…ط¬ظ„ط¯ ط§ظ„طµظˆط±: {self.screenshots_dir.absolute()}")

        # ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ط§ظ„ظ…طھط·ظ„ط¨ط§طھ
        self.check_dependencies()

    def check_dependencies(self):
        """ظپط­طµ ط§ظ„ظ…طھط·ظ„ط¨ط§طھ ط§ظ„ظ…ط·ظ„ظˆط¨ط©"""
        try:
            # ظپط­طµ Python packages
            missing_packages = []

            try:
                import selenium
                logger.info("âœ… Selenium ظ…طھظˆظپط±")
            except ImportError:
                missing_packages.append("selenium")

            try:
                import playwright
                logger.info("âœ… Playwright ظ…طھظˆظپط±")
            except ImportError:
                missing_packages.append("playwright")

            try:
                from PIL import Image
                logger.info("âœ… Pillow ظ…طھظˆظپط±")
            except ImportError:
                missing_packages.append("Pillow")

            if missing_packages:
                logger.warning(f"âڑ ï¸ڈ ط§ظ„ظ…ظƒطھط¨ط§طھ ط§ظ„ظ…ظپظ‚ظˆط¯ط©: {', '.join(missing_packages)}")
                logger.info("ًں’، طھط´ط؛ظٹظ„: pip install -r requirements.txt")
            else:
                logger.info("âœ… ط¬ظ…ظٹط¹ ط§ظ„ظ…طھط·ظ„ط¨ط§طھ ظ…طھظˆظپط±ط©")

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ظپط­طµ ط§ظ„ظ…طھط·ظ„ط¨ط§طھ: {e}")

    def install_missing_dependencies(self):
        """طھط«ط¨ظٹطھ ط§ظ„ظ…طھط·ظ„ط¨ط§طھ ط§ظ„ظ…ظپظ‚ظˆط¯ط© طھظ„ظ‚ط§ط¦ظٹط§ظ‹"""
        try:
            logger.info("ًں“¦ طھط«ط¨ظٹطھ ط§ظ„ظ…طھط·ظ„ط¨ط§طھ...")
            requirements_file = Path(__file__).parent / "requirements.txt"

            if requirements_file.exists():
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
                             check=True, capture_output=True, text=True)
                logger.info("âœ… طھظ… طھط«ط¨ظٹطھ ط§ظ„ظ…طھط·ظ„ط¨ط§طھ ط¨ظ†ط¬ط§ط­")

                # طھط«ط¨ظٹطھ Playwright browsers
                subprocess.run([sys.executable, "-m", "playwright", "install"],
                             check=True, capture_output=True, text=True)
                logger.info("âœ… طھظ… طھط«ط¨ظٹطھ ظ…طھطµظپط­ط§طھ Playwright")

            else:
                logger.error("â‌Œ ظ…ظ„ظپ requirements.txt ط؛ظٹط± ظ…ظˆط¬ظˆط¯")

        except subprocess.CalledProcessError as e:
            logger.error(f"â‌Œ ظپط´ظ„ طھط«ط¨ظٹطھ ط§ظ„ظ…طھط·ظ„ط¨ط§طھ: {e}")
        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ طھط«ط¨ظٹطھ ط§ظ„ظ…طھط·ظ„ط¨ط§طھ: {e}")

    async def initialize_selenium(self):
        """طھظ‡ظٹط¦ط© Selenium WebDriver ظ…ط¹ ط¥ط¹ط¯ط§ط¯ط§طھ ظ…ط­ط³ظ†ط©"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # ظ…ط­ط§ظˆظ„ط© ط§ط³طھط®ط¯ط§ظ… webdriver-manager
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                service = Service(ChromeDriverManager().install())
            except:
                # ط§ط³طھط®ط¯ط§ظ… chromedriver ظ…ظ† ط§ظ„ظ†ط¸ط§ظ…
                service = Service()

            chrome_options = Options()
            chrome_options.add_argument('--headless=new')  # ط§ط³طھط®ط¯ط§ظ… ط§ظ„ظˆط¶ط¹ ط§ظ„ط¬ط¯ظٹط¯
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # طھط³ط±ظٹط¹ ط§ظ„طھط­ظ…ظٹظ„
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--ignore-certificate-errors-spki-list')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            # ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط£ط¯ط§ط،
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])

            self.selenium_driver = webdriver.Chrome(service=service, options=chrome_options)
            self.selenium_driver.set_page_load_timeout(30)
            self.selenium_driver.implicitly_wait(10)

            logger.info("âœ… طھظ… طھظ‡ظٹط¦ط© Selenium ط¨ظ†ط¬ط§ط­")
            return True

        except Exception as e:
            logger.error(f"â‌Œ ظپط´ظ„ طھظ‡ظٹط¦ط© Selenium: {e}")
            logger.error(f"طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£: {traceback.format_exc()}")
            return False
    
    async def initialize_playwright(self):
        """طھظ‡ظٹط¦ط© Playwright ظ…ط¹ ط¥ط¹ط¯ط§ط¯ط§طھ ظ…ط­ط³ظ†ط©"""
        try:
            from playwright.async_api import async_playwright

            # ط¥ط؛ظ„ط§ظ‚ ط§ظ„ط§طھطµط§ظ„ ط§ظ„ط³ط§ط¨ظ‚ ط¥ط°ط§ ظˆط¬ط¯
            if self.playwright_browser:
                try:
                    await self.playwright_browser.close()
                except:
                    pass

            if self.playwright:
                try:
                    await self.playwright.stop()
                except:
                    pass

            # ط¥ظ†ط´ط§ط، ط§طھطµط§ظ„ ط¬ط¯ظٹط¯
            self.playwright = await async_playwright().start()
            self.playwright_browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--window-size=1920,1080',
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors',
                    '--disable-web-security',
                    '--allow-running-insecure-content'
                ]
            )

            # ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ظ†ط¬ط§ط­ ط§ظ„طھظ‡ظٹط¦ط©
            if self.playwright_browser:
                logger.info("âœ… طھظ… طھظ‡ظٹط¦ط© Playwright browser ط¨ظ†ط¬ط§ط­")
                return True
            else:
                logger.error("â‌Œ ظپط´ظ„ ظپظٹ ط¥ظ†ط´ط§ط، Playwright browser")
                return False

        except Exception as e:
            logger.error(f"â‌Œ ظپط´ظ„ طھظ‡ظٹط¦ط© Playwright: {e}")
            logger.error(f"طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£: {traceback.format_exc()}")

            # ظ…ط­ط§ظˆظ„ط© طھظ†ط¸ظٹظپ ط§ظ„ظ…ظˆط§ط±ط¯
            try:
                if self.playwright_browser:
                    await self.playwright_browser.close()
            except:
                pass
            try:
                if self.playwright:
                    await self.playwright.stop()
            except:
                pass

            # ط¥ط¹ط§ط¯ط© طھط¹ظٹظٹظ† ط§ظ„ظ…طھط؛ظٹط±ط§طھ ظپظٹ ط­ط§ظ„ط© ط§ظ„ظپط´ظ„
            self.playwright_browser = None
            self.playwright = None
            return False
    
    async def capture_with_selenium(self, url, filename, stage="screenshot", report_id=None):
        """ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ط¨ط§ط³طھط®ط¯ط§ظ… Selenium ظ…ط¹ طھط­ط³ظٹظ†ط§طھ v4"""
        try:
            if not self.selenium_driver:
                if not await self.initialize_selenium():
                    self.stats['failed_captures'] += 1
                    return None

            logger.info(f"ًں“¸ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© Selenium: {url} - ط§ظ„ظ…ط±ط­ظ„ط©: {stage}")

            # طھط­ط¯ظٹط¯ ظ…ط¬ظ„ط¯ ط§ظ„ط­ظپط¸ - ط¥ظ†ط´ط§ط، ظ…ط¬ظ„ط¯ ظ…ظ†ظپطµظ„ ظ„ظƒظ„ ظ…ظˆظ‚ط¹
            save_dir = self.session_dir
            if report_id:
                # ط¥ظ†ط´ط§ط، ظ…ط¬ظ„ط¯ ظ…ظ†ظپطµظ„ ط­ط³ط¨ ط§ظ„ظ…ظˆظ‚ط¹
                from urllib.parse import urlparse
                parsed_url = urlparse(url)
                domain_name = parsed_url.netloc.replace('.', '_').replace(':', '_')

                # ط¥ظ†ط´ط§ط، ظ…ط¬ظ„ط¯ ظ„ظ„ط¯ظˆظ…ظٹظ†
                domain_dir = self.screenshots_dir / domain_name
                domain_dir.mkdir(exist_ok=True)

                # ط¥ظ†ط´ط§ط، ظ…ط¬ظ„ط¯ ظ„ظ„طھظ‚ط±ظٹط± ط¯ط§ط®ظ„ ظ…ط¬ظ„ط¯ ط§ظ„ط¯ظˆظ…ظٹظ†
                save_dir = domain_dir / report_id
                save_dir.mkdir(exist_ok=True)

            # ًں”¥ ط¥طµظ„ط§ط­ ط®ط§طµ ظ„طµظˆط± "ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„" - ط§ظ„طھط£ظƒط¯ ظ…ظ† طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ط£ظˆ ظپط´ظ„
            if stage == "after":
                logger.info(f"ًںڑ¨ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„ - URL: {url}")

                # طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط© ظ…ط¹ ط§ظ†طھط¸ط§ط± ط£ط·ظˆظ„ ظ„طµظˆط± "ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„"
                try:
                    self.selenium_driver.get(url)

                    # ط§ظ†طھط¸ط§ط± ط£ط·ظˆظ„ ظ„طµظˆط± "ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„" ظ„ظ„طھط£ظƒط¯ ظ…ظ† طھط­ظ…ظٹظ„ ط§ظ„ظ…ط­طھظˆظ‰
                    time.sleep(8)  # ط§ظ†طھط¸ط§ط± ط£ط·ظˆظ„ ظ„طµظˆط± ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„

                    # ظپط­طµ ط¥ط°ط§ طھظ… طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط© ط¨ظ†ط¬ط§ط­
                    page_title = self.selenium_driver.title
                    current_url = self.selenium_driver.current_url

                    # ظپط­طµ ط¥ط°ط§ ظƒط§ظ†طھ ط§ظ„طµظپط­ط© ظپط§ط±ط؛ط© ط£ظˆ ط®ط·ط£
                    try:
                        body_text = self.selenium_driver.find_element("tag name", "body").text
                        if not body_text.strip() or "error" in body_text.lower() or len(body_text) < 10:
                            logger.error(f"â‌Œ ط§ظ„طµظپط­ط© ظپط§ط±ط؛ط© ط£ظˆ طھط­طھظˆظٹ ط¹ظ„ظ‰ ط®ط·ط£: {body_text[:100]}")
                            self.stats['failed_captures'] += 1
                            return {
                                'success': False,
                                'error': 'ظپط´ظ„ ظپظٹ طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط© - ط§ظ„طµظپط­ط© ظپط§ط±ط؛ط© ط£ظˆ طھط­طھظˆظٹ ط¹ظ„ظ‰ ط®ط·ط£',
                                'url': url,
                                'stage': stage
                            }
                    except Exception as e:
                        logger.error(f"â‌Œ ظپط´ظ„ ظپظٹ ظ‚ط±ط§ط،ط© ظ…ط­طھظˆظ‰ ط§ظ„طµظپط­ط©: {e}")
                        self.stats['failed_captures'] += 1
                        return {
                            'success': False,
                            'error': f'ظپط´ظ„ ظپظٹ ظ‚ط±ط§ط،ط© ظ…ط­طھظˆظ‰ ط§ظ„طµظپط­ط©: {e}',
                            'url': url,
                            'stage': stage
                        }

                    # ظ…ط­ط§ظˆظ„ط© طھظ†ظپظٹط° JavaScript ظ„ط¶ظ…ط§ظ† طھط­ظ…ظٹظ„ ط§ظ„ظ…ط­طھظˆظ‰
                    try:
                        self.selenium_driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(2)
                        self.selenium_driver.execute_script("window.scrollTo(0, 0);")
                    except Exception as js_error:
                        logger.warning(f"âڑ ï¸ڈ ظپط´ظ„ ظپظٹ طھظ†ظپظٹط° JavaScript: {js_error}")

                    logger.info(f"ًں”چ طھظ… طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط© ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„: {page_title}")
                    logger.info(f"ًں”— URL ط§ظ„ط­ط§ظ„ظٹ: {current_url}")

                except Exception as load_error:
                    logger.error(f"â‌Œ ظپط´ظ„ ظپظٹ طھط­ظ…ظٹظ„ طµظپط­ط© ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„: {load_error}")
                    self.stats['failed_captures'] += 1
                    return {
                        'success': False,
                        'error': f'ظپط´ظ„ ظپظٹ طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط©: {load_error}',
                        'url': url,
                        'stage': stage
                    }
            else:
                # طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط© ط§ظ„ط¹ط§ط¯ظٹ ظ„ظ„طµظˆط± ط§ظ„ط£ط®ط±ظ‰
                try:
                    self.selenium_driver.get(url)

                    # ط§ظ†طھط¸ط§ط± طھط­ظ…ظٹظ„ ط§ظ„ظ…ط­طھظˆظ‰
                    from selenium.webdriver.support.ui import WebDriverWait
                    from selenium.webdriver.support import expected_conditions as EC
                    from selenium.webdriver.common.by import By

                    # ط§ظ†طھط¸ط§ط± طھط­ظ…ظٹظ„ body
                    WebDriverWait(self.selenium_driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )

                    # ط§ظ†طھط¸ط§ط± ط¥ط¶ط§ظپظٹ ظ„ظ„ظ…ط­طھظˆظ‰ ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹ
                    time.sleep(3)

                    # ظپط­طµ ط¥ط°ط§ طھظ… طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط© ط¨ظ†ط¬ط§ط­
                    try:
                        body_text = self.selenium_driver.find_element("tag name", "body").text
                        if not body_text.strip() or len(body_text) < 10:
                            logger.warning(f"âڑ ï¸ڈ ط§ظ„طµظپط­ط© ظ‚ط¯ طھظƒظˆظ† ظپط§ط±ط؛ط©: {body_text[:50]}")
                    except:
                        pass

                except Exception as load_error:
                    logger.error(f"â‌Œ ظپط´ظ„ ظپظٹ طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط©: {load_error}")
                    self.stats['failed_captures'] += 1
                    return {
                        'success': False,
                        'error': f'ظپط´ظ„ ظپظٹ طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط©: {load_error}',
                        'url': url,
                        'stage': stage
                    }

            # ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط©
            # ًں”¥ ط¥طµظ„ط§ط­: ط§ط³طھط®ط¯ط§ظ… ط§ط³ظ… ط§ظ„ظ…ظ„ظپ ط§ظ„طµط­ظٹط­ (stage_filename.png)
            screenshot_filename = f"{stage}_{filename}.png"
            screenshot_path = save_dir / screenshot_filename

            # ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© ط¨ط¯ظ‚ط© ط¹ط§ظ„ظٹط©
            self.selenium_driver.set_window_size(1920, 1080)
            success = self.selenium_driver.save_screenshot(str(screenshot_path))

            if not success or not screenshot_path.exists():
                logger.error("â‌Œ ظپط´ظ„ ظپظٹ ط­ظپط¸ ط§ظ„طµظˆط±ط©")
                self.stats['failed_captures'] += 1
                return {
                    'success': False,
                    'error': 'ظپط´ظ„ ظپظٹ ط­ظپط¸ ط§ظ„طµظˆط±ط©',
                    'url': url,
                    'stage': stage
                }

            # ظ‚ط±ط§ط،ط© ظˆطھط­ظˆظٹظ„ ط¥ظ„ظ‰ Base64
            with open(screenshot_path, "rb") as img_file:
                image_data = img_file.read()
                base64_data = base64.b64encode(image_data).decode()

            # ط­ط³ط§ط¨ ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ظ…ظ„ظپ
            file_size = len(image_data)

            # ًں”¥ ظپط­طµ ط­ط¬ظ… ط§ظ„طµظˆط±ط© - ط¥ط°ط§ ظƒط§ظ†طھ طµط؛ظٹط±ط© ط¬ط¯ط§ظ‹ ظپظ‡ظٹ ظ‚ط¯ طھظƒظˆظ† ط£ظ„ظˆط§ظ† ط£ظˆ ظپط§ط±ط؛ط©
            if file_size < 10000:  # ط£ظ‚ظ„ ظ…ظ† 10KB
                logger.error(f"â‌Œ ط§ظ„طµظˆط±ط© طµط؛ظٹط±ط© ط¬ط¯ط§ظ‹ ({file_size} bytes) - ظ‚ط¯ طھظƒظˆظ† ط£ظ„ظˆط§ظ† ط£ظˆ ظپط§ط±ط؛ط©")
                # ط­ط°ظپ ط§ظ„طµظˆط±ط© ط§ظ„ظپط§ط±ط؛ط©
                screenshot_path.unlink(missing_ok=True)
                self.stats['failed_captures'] += 1
                return {
                    'success': False,
                    'error': f'ط§ظ„طµظˆط±ط© طµط؛ظٹط±ط© ط¬ط¯ط§ظ‹ ({file_size} bytes) - ظ‚ط¯ طھظƒظˆظ† ط£ظ„ظˆط§ظ† ط£ظˆ ظپط§ط±ط؛ط©',
                    'url': url,
                    'stage': stage,
                    'file_size': file_size
                }

            # ظپط­طµ ط¥ط¶ط§ظپظٹ ظ„ظ„طµظˆط± "ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„"
            if stage == "after" and file_size < 50000:  # ط£ظ‚ظ„ ظ…ظ† 50KB ظ„طµظˆط± ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„
                logger.warning(f"âڑ ï¸ڈ طµظˆط±ط© 'ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„' طµط؛ظٹط±ط© ({file_size} bytes) - ظ‚ط¯ ظ„ط§ طھظƒظˆظ† ط­ظ‚ظٹظ‚ظٹط©")
                # ظ„ط§ ظ†ط­ط°ظپظ‡ط§ ظ„ظƒظ† ظ†ط­ط°ط±
                logger.warning("âڑ ï¸ڈ ظ‚ط¯ طھط­طھط§ط¬ ظ„ظپط­طµ ط§ظ„طµظˆط±ط© ظٹط¯ظˆظٹط§ظ‹ ظ„ظ„طھط£ظƒط¯ ظ…ظ† ط£ظ†ظ‡ط§ ط­ظ‚ظٹظ‚ظٹط©")

            self.stats['successful_captures'] += 1
            self.stats['selenium_captures'] += 1
            self.stats['total_screenshots'] += 1

            logger.info(f"âœ… طھظ… ط­ظپط¸ طµظˆط±ط© Selenium: {screenshot_path} ({file_size} bytes)")

            # طھط­ط¯ظٹط¯ ظ†ظˆط¹ ط§ظ„طµظˆط±ط© ط§ظ„طµط­ظٹط­
            image_type = self.detect_image_type(base64_data)

            return {
                "success": True,
                "method": "selenium",
                "path": str(screenshot_path),
                "file_path": str(screenshot_path),  # ًں”¥ ط¥ط¶ط§ظپط© file_path ظ„ظ„طھظˆط§ظپظ‚ ظ…ط¹ ط§ظ„ظ†ط¸ط§ظ… v4
                "filename": screenshot_filename,
                "base64": f"data:image/{image_type};base64,{base64_data}",
                "screenshot_data": base64_data,  # ظ„ظ„طھظˆط§ظپظ‚ ظ…ط¹ ط§ظ„ظ†ط¸ط§ظ… v4
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "stage": stage,
                "file_size": file_size,
                "width": 1920,
                "height": 1080,
                "report_id": report_id,
                "session_id": self.session_id,
                "image_type": image_type
            }

        except Exception as e:
            self.stats['failed_captures'] += 1
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ Selenium: {e}")
            logger.error(f"طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£: {traceback.format_exc()}")
            return None
    
    async def capture_with_playwright(self, url, filename, stage="screenshot", report_id=None, vulnerability_name=None, payload_data=None, vulnerability_type=None, v4_data=None, v4_real_data=None):
        """ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ط¨ط§ط³طھط®ط¯ط§ظ… Playwright ظ…ط¹ طھط­ط³ظٹظ†ط§طھ v4"""
        try:
            if not self.playwright_browser:
                if not await self.initialize_playwright():
                    self.stats['failed_captures'] += 1
                    return None

            # ظپط­طµ ط¥ط¶ط§ظپظٹ ظ„ظ„طھط£ظƒط¯ ظ…ظ† طµط­ط© browser
            if not self.playwright_browser or not hasattr(self.playwright_browser, 'new_page'):
                logger.error("â‌Œ Playwright browser ط؛ظٹط± طµط§ظ„ط­طŒ ظ…ط­ط§ظˆظ„ط© ط¥ط¹ط§ط¯ط© ط§ظ„طھظ‡ظٹط¦ط©...")
                if not await self.initialize_playwright():
                    self.stats['failed_captures'] += 1
                    logger.error("â‌Œ ظپط´ظ„ ظپظٹ ط¥ط¹ط§ط¯ط© طھظ‡ظٹط¦ط© Playwright")
                    return None

            logger.info(f"ًں“¸ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© Playwright: {url} - ط§ظ„ظ…ط±ط­ظ„ط©: {stage}")

            # طھط­ط¯ظٹط¯ ظ…ط¬ظ„ط¯ ط§ظ„ط­ظپط¸
            save_dir = self.session_dir
            if report_id:
                save_dir = self.screenshots_dir / report_id
                save_dir.mkdir(exist_ok=True)

            # ط¥ظ†ط´ط§ط، طµظپط­ط© ط¬ط¯ظٹط¯ط© ظ…ط¹ ط¥ط¹ط¯ط§ط¯ط§طھ ظ…ط­ط³ظ†ط©
            page = None
            max_retries = 3

            for attempt in range(max_retries):
                try:
                    # ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ظˆط¬ظˆط¯ browser طµط§ظ„ط­
                    if not self.playwright_browser:
                        logger.warning(f"âڑ ï¸ڈ Browser ط؛ظٹط± ظ…طھط§ط­ - ظ…ط­ط§ظˆظ„ط© ط¥ط¹ط§ط¯ط© ط§ظ„طھظ‡ظٹط¦ط© (ط§ظ„ظ…ط­ط§ظˆظ„ط© {attempt + 1})")
                        if not await self.initialize_playwright():
                            continue

                    # طھط­ظ‚ظ‚ ط¥ط¶ط§ظپظٹ ظ…ظ† طµط­ط© browser ظ‚ط¨ظ„ ط§ط³طھط®ط¯ط§ظ…ظ‡
                    if not self.playwright_browser or not hasattr(self.playwright_browser, 'new_page'):
                        logger.warning(f"âڑ ï¸ڈ Browser ط؛ظٹط± طµط§ظ„ط­ - ط¥ط¹ط§ط¯ط© طھظ‡ظٹط¦ط©...")
                        if not await self.initialize_playwright():
                            continue

                    # ظ…ط­ط§ظˆظ„ط© ط¥ظ†ط´ط§ط، طµظپط­ط© ط¬ط¯ظٹط¯ط©
                    page = await self.playwright_browser.new_page()
                    logger.info(f"âœ… طھظ… ط¥ظ†ط´ط§ط، طµظپط­ط© Playwright ط¨ظ†ط¬ط§ط­ (ط§ظ„ظ…ط­ط§ظˆظ„ط© {attempt + 1})")
                    break

                except Exception as page_error:
                    logger.error(f"â‌Œ ظپط´ظ„ ظپظٹ ط¥ظ†ط´ط§ط، طµظپط­ط© Playwright (ط§ظ„ظ…ط­ط§ظˆظ„ط© {attempt + 1}): {page_error}")

                    # ط¥ط¹ط§ط¯ط© طھظ‡ظٹط¦ط© Playwright ط¨ط¯ظ„ط§ظ‹ ظ…ظ† ط¥ط¹ط§ط¯ط© طھط¹ظٹظٹظ† ط¥ظ„ظ‰ None
                    logger.info("ًں”„ ظ…ط­ط§ظˆظ„ط© ط¥ط¹ط§ط¯ط© طھظ‡ظٹط¦ط© Playwright...")
                    try:
                        await self.cleanup_playwright()
                        await asyncio.sleep(1)  # ط§ظ†طھط¸ط§ط± ظ‚طµظٹط±
                        if await self.initialize_playwright():
                            logger.info("âœ… طھظ… ط¥ط¹ط§ط¯ط© طھظ‡ظٹط¦ط© Playwright ط¨ظ†ط¬ط§ط­")
                        else:
                            logger.error("â‌Œ ظپط´ظ„ ظپظٹ ط¥ط¹ط§ط¯ط© طھظ‡ظٹط¦ط© Playwright")
                    except Exception as reinit_error:
                        logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط¥ط¹ط§ط¯ط© طھظ‡ظٹط¦ط© Playwright: {reinit_error}")

                    # ظ…ط­ط§ظˆظ„ط© ط¥ط¹ط§ط¯ط© ط§ظ„طھظ‡ظٹط¦ط©
                    if attempt < max_retries - 1:
                        logger.info(f"ًں”„ ظ…ط­ط§ظˆظ„ط© ط¥ط¹ط§ط¯ط© طھظ‡ظٹط¦ط© Playwright...")
                        await self.initialize_playwright()
                    else:
                        logger.error(f"â‌Œ ظپط´ظ„ ظپظٹ ط¥ظ†ط´ط§ط، طµظپط­ط© ط¨ط¹ط¯ {max_retries} ظ…ط­ط§ظˆظ„ط§طھ")
                        self.stats['failed_captures'] += 1
                        return None

            if not page:
                logger.error(f"â‌Œ ظ„ظ… ظٹطھظ… ط¥ظ†ط´ط§ط، طµظپط­ط© Playwright")
                self.stats['failed_captures'] += 1
                return None

            # ط¥ط¹ط¯ط§ط¯ viewport
            await page.set_viewport_size({"width": 1920, "height": 1080})

            # ط¥ط¹ط¯ط§ط¯ user agent
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })

            try:
                # ًں”¥ ط¥طµظ„ط§ط­ ط¬ط°ط±ظٹ: طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط© ظ…ط¹ ط§ظ†طھط¸ط§ط± ط§ظ„طھط£ط«ظٹط±ط§طھ ط§ظ„ط¨طµط±ظٹط©
                logger.info(f"ًں”— طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط©: {url}")

                # ًں”¥ ط¥ط¹ط§ط¯ط© طھط­ظ…ظٹظ„ ظ‚ظˆظٹط© ظ„ظ…ط±ط­ظ„ط© after
                if stage == 'after':
                    import time
                    logger.info(f"ًں”¥ ظ…ط±ط­ظ„ط© after - ط¥ط¹ط§ط¯ط© طھط­ظ…ظٹظ„ ظ‚ظˆظٹط© ظ…ط¹ cache bypass")
                    # ط¥ط¶ط§ظپط© timestamp ظ„طھط¬ظ†ط¨ cache
                    cache_bypass_url = f"{url}{'&' if '?' in url else '?'}cache_bypass={int(time.time())}"
                    await page.goto(cache_bypass_url, wait_until='domcontentloaded', timeout=30000)
                    await page.wait_for_timeout(3000)
                    # ط¥ط¹ط§ط¯ط© طھط­ظ…ظٹظ„ ظ…ط±ط© ط£ط®ط±ظ‰ ظ„ظ„طھط£ظƒط¯
                    await page.reload(wait_until='domcontentloaded', timeout=30000)
                    await page.wait_for_timeout(3000)
                else:
                    await page.goto(url, wait_until='domcontentloaded', timeout=30000)

                # ًں”¥ ط§ظ†طھط¸ط§ط± ط£ط·ظˆظ„ ظ„ط¶ظ…ط§ظ† طھط­ظ…ظٹظ„ ط§ظ„ظ…ط­طھظˆظ‰
                await page.wait_for_timeout(5000)

                # ًں”¥ طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ط­ط³ط¨ ط§ظ„ظ…ط±ط­ظ„ط© ظˆط§ظ„ط«ط؛ط±ط©
                logger.info(f"ًں”¥ طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ط§ظ„ظ…ط±ط­ظ„ط©: {stage}")

                try:
                    # ًں”¥ ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ†ط¸ط§ظ… v4 ظپظ‚ط·
                    real_payload = payload_data  # ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط©
                    real_vuln_type = vulnerability_type if vulnerability_type else 'Unknown'

                    logger.info(f"ًں”¥ ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط©: payload={real_payload}, type={real_vuln_type}")

                    await self.apply_vulnerability_effects_async(
                        page,
                        vulnerability_name,
                        stage,
                        real_payload,
                        exploitation_result=exploitation_result,  # ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ†ط¸ط§ظ… v4
                        vulnerability_type=real_vuln_type,
                        v4_real_data=v4_real_data
                    )

                    # ًں”¥ ط§ظ†طھط¸ط§ط± ط£ط·ظˆظ„ ظ„ط¶ظ…ط§ظ† ط¸ظ‡ظˆط± ط§ظ„طھط£ط«ظٹط±ط§طھ ظپظٹ ط§ظ„طµظˆط±ط©
                    if stage == 'after':
                        logger.info(f"âڈ³ ط§ظ†طھط¸ط§ط± 15 ط«ط§ظ†ظٹط© ظ„ط¶ظ…ط§ظ† ط¸ظ‡ظˆط± ط¬ظ…ظٹط¹ ط§ظ„طھط£ط«ظٹط±ط§طھ ظپظٹ ظ…ط±ط­ظ„ط© {stage}...")
                        await page.wait_for_timeout(15000)

                        # ط§ظ„طھط£ظƒط¯ ظ…ظ† ط¸ظ‡ظˆط± ط§ظ„طھط£ط«ظٹط±ط§طھ ظˆط§ظ„طھط­ظ‚ظ‚ ط§ظ„ط­ظ‚ظٹظ‚ظٹ
                        effects_check = await page.evaluate("""
                            console.log('ًں”¥ ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ط¸ظ‡ظˆط± ط§ظ„طھط£ط«ظٹط±ط§طھ...');

                            const bodyBg = document.body.style.background;
                            const bodyBorder = document.body.style.border;
                            const title = document.title;
                            const hackElements = document.querySelectorAll('[data-hack-element]');

                            console.log('Body background:', bodyBg);
                            console.log('Body border:', bodyBorder);
                            console.log('Title:', title);
                            console.log('Hack elements:', hackElements.length);

                            // ط¥ط±ط¬ط§ط¹ ط§ظ„ظ†طھط§ط¦ط¬ ظ„ظ„ظپط­طµ
                            return {{
                                hasBackground: bodyBg.includes('red') || bodyBg.includes('gradient'),
                                hasBorder: bodyBorder.includes('red'),
                                hasHackedTitle: title.includes('HACKED') || title.includes('EXPLOITED'),
                                hackElementsCount: hackElements.length,
                                bodyBg: bodyBg,
                                bodyBorder: bodyBorder,
                                title: title
                            }};
                        """)

                        logger.info(f"ًں”¥ ظپط­طµ ط§ظ„طھط£ط«ظٹط±ط§طھ: {effects_check}")

                        # ط¥ط°ط§ ظ„ظ… طھط·ط¨ظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھطŒ ط£ط¹ط¯ طھط·ط¨ظٹظ‚ظ‡ط§
                        if not (effects_check.get('hasBackground') or effects_check.get('hasBorder') or effects_check.get('hasHackedTitle')):
                            logger.warning("âڑ ï¸ڈ ط§ظ„طھط£ط«ظٹط±ط§طھ ظ„ظ… طھط·ط¨ظ‚! ط¥ط¹ط§ط¯ط© طھط·ط¨ظٹظ‚...")
                            await page.evaluate("""
                                // ط¥ط¹ط§ط¯ط© طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ط¨ظ‚ظˆط©
                                document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
                                document.body.style.border = '10px solid red';
                                document.title = 'ًںڑ¨ HACKED - VULNERABILITY EXPLOITED ًںڑ¨';

                                // ط¥ط¶ط§ظپط© ظ†طµ ظƒط¨ظٹط±
                                const forceHackBanner = document.createElement('div');
                                forceHackBanner.innerHTML = 'ًںڑ¨ FORCED HACK BANNER - VULNERABILITY EXPLOITED ًںڑ¨';
                                forceHackBanner.style.cssText = `
                                    position: fixed;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                    background: red;
                                    color: white;
                                    text-align: center;
                                    font-size: 30px;
                                    font-weight: bold;
                                    padding: 30px;
                                    z-index: 999999;
                                    border: 5px solid yellow;
                                `;
                                document.body.appendChild(forceHackBanner);

                                console.log('ًں”¥ طھظ… ط¥ط¹ط§ط¯ط© طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ط¨ظ‚ظˆط©');
                            """)
                            await page.wait_for_timeout(5000)
                    else:
                        logger.info(f"âڈ³ ط§ظ†طھط¸ط§ط± 10 ط«ظˆط§ظ†ظٹ ظ„ط¶ظ…ط§ظ† ط¸ظ‡ظˆط± ط§ظ„طھط£ط«ظٹط±ط§طھ ظپظٹ ظ…ط±ط­ظ„ط© {stage}...")
                        await page.wait_for_timeout(10000)

                    logger.info("âœ… طھظ… طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ط§ظ„ط¨طµط±ظٹط© ط¨ظ†ط¬ط§ط­")

                except Exception as js_error:
                    logger.warning(f"âڑ ï¸ڈ طھط­ط°ظٹط± ظپظٹ طھظ†ظپظٹط° JavaScript: {js_error}")

            except Exception as load_error:
                logger.warning(f"âڑ ï¸ڈ طھط­ط°ظٹط± ظپظٹ طھط­ظ…ظٹظ„ ط§ظ„طµظپط­ط©: {load_error}")
                # ظ…ط­ط§ظˆظ„ط© ط§ظ„طھط­ظ…ظٹظ„ ط¨ط¯ظˆظ† networkidle
                try:
                    await page.goto(url, wait_until='domcontentloaded', timeout=15000)
                    await page.wait_for_timeout(5000)  # ط§ظ†طھط¸ط§ط± ط£ط·ظˆظ„
                except:
                    pass

            # ًں”¥ ط¥طµظ„ط§ط­: طھط­ط¶ظٹط± ظ…ط³ط§ط± ط§ظ„طµظˆط±ط© ظ„ظƒظ† ط¹ط¯ظ… ط§ظ„طھظ‚ط§ط·ظ‡ط§ ط§ظ„ط¢ظ†
            screenshot_filename = f"{stage}_{filename}.png"
            screenshot_path = save_dir / screenshot_filename

            # ط³ظٹطھظ… ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© ط¨ط¹ط¯ طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ

            # ًں”¥ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© ط¨ط¹ط¯ طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ (ط¥ط°ط§ ظƒط§ظ†طھ ظ…ط±ط­ظ„ط© after)
            if stage == 'after':
                # طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ط£ظˆظ„ط§ظ‹ ط¨ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¯ط§ظ„ط© ط§ظ„طµط­ظٹط­ط©
                await self.apply_vulnerability_effects_async(page, vulnerability_name, stage, payload_data, None, vulnerability_type, v4_real_data)

                # ط§ظ†طھط¸ط§ط± ظ„ط¶ظ…ط§ظ† طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ
                await asyncio.sleep(5)

                # ط§ظ„ط¢ظ† ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط©
                logger.info("ًں“¸ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© after ط¨ط¹ط¯ طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ...")
                await page.screenshot(
                    path=str(screenshot_path),
                    full_page=True,
                    type='png'
                )
            else:
                # ظ„ظ„ظ…ط±ط§ط­ظ„ ط§ظ„ط£ط®ط±ظ‰طŒ ط§ظ„طھظ‚ط§ط· ط¹ط§ط¯ظٹ
                await page.screenshot(
                    path=str(screenshot_path),
                    full_page=True,
                    type='png'
                )

            await page.close()

            # ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ظˆط¬ظˆط¯ ط§ظ„ظ…ظ„ظپ
            if not screenshot_path.exists():
                raise Exception("ظپط´ظ„ ظپظٹ ط­ظپط¸ ط§ظ„طµظˆط±ط©")

            # ظ‚ط±ط§ط،ط© ظˆطھط­ظˆظٹظ„ ط¥ظ„ظ‰ Base64
            with open(screenshot_path, "rb") as img_file:
                image_data = img_file.read()
                base64_data = base64.b64encode(image_data).decode()

            # ط­ط³ط§ط¨ ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ظ…ظ„ظپ
            file_size = len(image_data)

            self.stats['successful_captures'] += 1
            self.stats['playwright_captures'] += 1
            self.stats['total_screenshots'] += 1

            logger.info(f"âœ… طھظ… ط­ظپط¸ طµظˆط±ط© Playwright: {screenshot_path} ({file_size} bytes)")

            # طھط­ط¯ظٹط¯ ظ†ظˆط¹ ط§ظ„طµظˆط±ط© ط§ظ„طµط­ظٹط­
            image_type = self.detect_image_type(base64_data)

            return {
                "success": True,
                "method": "playwright",
                "path": str(screenshot_path),
                "file_path": str(screenshot_path),  # ًں”¥ ط¥ط¶ط§ظپط© file_path ظ„ظ„طھظˆط§ظپظ‚ ظ…ط¹ ط§ظ„ظ†ط¸ط§ظ… v4
                "filename": screenshot_filename,
                "base64": f"data:image/{image_type};base64,{base64_data}",
                "screenshot_data": base64_data,  # ظ„ظ„طھظˆط§ظپظ‚ ظ…ط¹ ط§ظ„ظ†ط¸ط§ظ… v4
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "stage": stage,
                "file_size": file_size,
                "width": 1920,
                "height": 1080,
                "report_id": report_id,
                "session_id": self.session_id,
                "image_type": image_type
            }

        except Exception as e:
            self.stats['failed_captures'] += 1
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ Playwright: {e}")
            logger.error(f"طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£: {traceback.format_exc()}")
            return None
    
    async def capture_vulnerability_sequence(self, url, vulnerability_name, report_id, payload_data=None, vulnerability_type=None, v4_data=None, v4_real_data=None):
        """ط§ظ„طھظ‚ط§ط· طھط³ظ„ط³ظ„ طµظˆط± ظ„ظ„ط«ط؛ط±ط© (ظ‚ط¨ظ„/ط£ط«ظ†ط§ط،/ط¨ط¹ط¯) ظ…ط¹ ط¯ط¹ظ… ط§ظ„ظ†ط¸ط§ظ… v4 ظˆط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط©"""
        try:
            # ًں”¥ ط¥ظ†ط´ط§ط، ظ…ط¬ظ„ط¯ ظ…ظ†ظپطµظ„ ظ„ظƒظ„ ط±ط§ط¨ط·/طµظپط­ط© ط¨ظ†ط§ط،ظ‹ ط¹ظ„ظ‰ URL ط§ظ„ظƒط§ظ…ظ„
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            domain_name = parsed_url.netloc.replace('.', '_').replace(':', '_')

            # ط¥ظ†ط´ط§ط، ظ…ط¬ظ„ط¯ ط±ط¦ظٹط³ظٹ ظ„ظ„ط¯ظˆظ…ظٹظ†
            main_domain_dir = self.screenshots_dir / domain_name
            main_domain_dir.mkdir(exist_ok=True)

            # ط¥ظ†ط´ط§ط، ط§ط³ظ… ظˆط§ط¶ط­ ظ„ظ„طµظپط­ط© ط­ط³ط¨ ط§ظ„ظ…ط³ط§ط±
            page_path = parsed_url.path.strip('/').lower()

            # طھط­ظˆظٹظ„ ط§ظ„ظ…ط³ط§ط±ط§طھ ط§ظ„ط´ط§ط¦ط¹ط© ط¥ظ„ظ‰ ط£ط³ظ…ط§ط، ظˆط§ط¶ط­ط©
            page_name_mapping = {
                '': 'main_page',
                'index': 'main_page',
                'index.php': 'main_page',
                'index.html': 'main_page',
                'admin': 'admin_page',
                'admin.php': 'admin_page',
                'login': 'login_page',
                'login.php': 'login_page',
                'shop': 'shop_page',
                'store': 'shop_page',
                'products': 'products_page',
                'cart': 'cart_page',
                'checkout': 'checkout_page',
                'profile': 'profile_page',
                'user': 'user_page',
                'dashboard': 'dashboard_page',
                'search': 'search_page',
                'contact': 'contact_page',
                'about': 'about_page',
                'news': 'news_page',
                'blog': 'blog_page'
            }

            # ط§ظ„ط¨ط­ط« ط¹ظ† ط§ط³ظ… ظ…ظ†ط§ط³ط¨
            safe_page_name = page_name_mapping.get(page_path)

            if not safe_page_name:
                # ط¥ط°ط§ ظ„ظ… ظٹظˆط¬ط¯ ظپظٹ ط§ظ„ظ‚ط§ظ…ظˆط³طŒ ط§ط³طھط®ط¯ظ… ط§ظ„ظ…ط³ط§ط± ظ…ط¹ طھظ†ط¸ظٹظپ
                page_path_clean = page_path.replace('/', '_').replace('.', '_').replace('-', '_')
                safe_page_name = "".join(c for c in page_path_clean if c.isalnum() or c == '_')[:50]
                if not safe_page_name:
                    safe_page_name = 'unknown_page'
                else:
                    safe_page_name += '_page'

            # ًں”¥ ط¥طµظ„ط§ط­: ط¹ط¯ظ… ط¥ط¶ط§ظپط© ظ…ط¹ط§ظ…ظ„ط§طھ ط§ظ„ط§ط³طھط¹ظ„ط§ظ… ظ„طھط¬ظ†ط¨ ط£ط³ظ…ط§ط، ظ…ط¬ظ„ط¯ط§طھ payload/dynamic
            # ظ†ط³طھط®ط¯ظ… ط§ط³ظ… ط§ظ„طµظپط­ط© ط§ظ„ط£طµظ„ظٹ ظپظ‚ط· ط¨ط¯ظˆظ† ظ…ط¹ط§ظ…ظ„ط§طھ ط§ظ„ط§ط³طھط¹ظ„ط§ظ…
            # if parsed_url.query:
            #     query_params = parsed_url.query.replace('=', '_').replace('&', '_').replace('%', '_')[:30]
            #     safe_page_name += '_' + query_params

            # ط¥ظ†ط´ط§ط، ظ…ط¬ظ„ط¯ ظپط±ط¹ظٹ ظ…ظ†ظپطµظ„ ظ„ظ„طµظپط­ط©
            page_dir = main_domain_dir / safe_page_name
            page_dir.mkdir(exist_ok=True)

            # ط§ط³طھط®ط¯ط§ظ… ظ…ط¬ظ„ط¯ ط§ظ„طµظپط­ط© ظƒظ…ط¬ظ„ط¯ ط§ظ„طھظ‚ط±ظٹط±
            report_dir = page_dir

            logger.info(f"ًں“پ ظ…ط¬ظ„ط¯ ظ…ظ†ظپطµظ„ ظ„ظ„طµظپط­ط©: {report_dir}")
            logger.info(f"ًں”— ط§ظ„ط±ط§ط¨ط·: {url}")
            logger.info(f"ًں“‚ ط§ظ„ظ‡ظٹظƒظ„: {domain_name}/{safe_page_name}")

            # طھظ†ط¸ظٹظپ ط§ط³ظ… ط§ظ„ط«ط؛ط±ط© ظ„ظ„ظ…ظ„ظپ
            safe_vuln_name = "".join(c for c in vulnerability_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_vuln_name = safe_vuln_name.replace(' ', '_')[:50]

            # ًں”¥ ط¥طµظ„ط§ط­: ط§ط³طھط®ط¯ط§ظ… ط§ط³ظ… ط§ظ„ط«ط؛ط±ط© ظپظ‚ط· ط¨ط¯ظˆظ† timestamp
            filename = safe_vuln_name

            screenshots = {}
            v4_compatible_data = {}

            logger.info(f"ًںژ¯ ط¨ط¯ط، ط§ظ„طھظ‚ط§ط· طھط³ظ„ط³ظ„ طµظˆط± ظ„ظ„ط«ط؛ط±ط©: {vulnerability_name}")

            # ًں”¥ ط§ظ„طµظˆط± ط§ظ„ط«ظ„ط§ط« ظ…ط¹ط§ظ‹ - Playwright ظپظ‚ط· ظ…ط¹ طھط£ط«ظٹط±ط§طھ ط­ظ‚ظٹظ‚ظٹط© ظ‚ظˆظٹط©
            stages = ['before', 'during', 'after']

            for stage in stages:
                logger.info(f"ًں“· ط§ظ„طھظ‚ط§ط· طµظˆط±ط© {stage} ظ…ط¹ طھط£ط«ظٹط±ط§طھ ظ‚ظˆظٹط©: {vulnerability_name}")

                stage_result = await self.capture_with_playwright(
                    url,
                    f"{filename}_{stage}",
                    stage,
                    report_id,
                    vulnerability_name,
                    payload_data,
                    vulnerability_type,
                    v4_data,
                    v4_real_data
                )

                if stage_result and isinstance(stage_result, dict) and stage_result.get('success'):
                    screenshots[stage] = stage_result
                    v4_compatible_data[stage] = stage_result
                    logger.info(f"âœ… ظ†ط¬ط­ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© {stage.upper()} ظ…ط¹ طھط£ط«ظٹط±ط§طھ ظ‚ظˆظٹط©")
                else:
                    logger.error(f"â‌Œ ظپط´ظ„ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© {stage.upper()}")
                    v4_compatible_data[stage] = None

                # ط§ظ†طھط¸ط§ط± ط¨ظٹظ† ط§ظ„ظ…ط±ط§ط­ظ„
                await asyncio.sleep(2)

            # ط¥ظ†ط´ط§ط، ط¨ظٹط§ظ†ط§طھ ظ…طھظˆط§ظپظ‚ط© ظ…ط¹ ط§ظ„ظ†ط¸ط§ظ… v4
            v4_compatible_data.update({
                'vulnerability_name': vulnerability_name,
                'target_url': url,
                'report_id': report_id,
                'timestamp': datetime.now().isoformat(),
                'method': 'playwright_only_with_effects',
                'session_id': self.session_id,
                'total_screenshots': len([s for s in screenshots.values() if s]),
                'screenshot_paths': {
                    'before': screenshots.get('before', {}).get('screenshot_path') if screenshots.get('before') else None,
                    'during': screenshots.get('during', {}).get('screenshot_path') if screenshots.get('during') else None,
                    'after': screenshots.get('after', {}).get('screenshot_path') if screenshots.get('after') else None
                }
            })

            # ط­ظپط¸ ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„طµظˆط±
            metadata = {
                "vulnerability_name": vulnerability_name,
                "url": url,
                "report_id": report_id,
                "timestamp": datetime.now().isoformat(),
                "screenshots": screenshots,
                "v4_data": v4_compatible_data,
                "total_screenshots": len(screenshots),
                "session_stats": self.stats.copy()
            }

            metadata_path = report_dir / f"{filename}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            logger.info(f"âœ… طھظ… ط§ظ„طھظ‚ط§ط· {len(screenshots)} طµظˆط±ط© ظ„ظ„ط«ط؛ط±ط©: {vulnerability_name}")
            logger.info(f"ًں“ٹ ط¥ط­طµط§ط¦ظٹط§طھ ط§ظ„ط¬ظ„ط³ط©: {self.stats}")

            # ًں”¥ ط¥طµظ„ط§ط­: ط¥ط±ط¬ط§ط¹ ظ†طھظٹط¬ط© طµط­ظٹط­ط© ظ…ط¹ success flag
            final_result = {
                'success': True,
                'vulnerability_name': vulnerability_name,
                'before': v4_compatible_data.get('before'),
                'during': v4_compatible_data.get('during'),
                'after': v4_compatible_data.get('after'),
                'total_screenshots': len(screenshots),
                'timestamp': datetime.now().isoformat(),
                'report_id': report_id
            }

            return final_result

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„طھظ‚ط§ط· طھط³ظ„ط³ظ„ ط§ظ„طµظˆط±: {e}")
            logger.error(f"طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def capture_single_screenshot(self, url, filename=None, report_id=None):
        """ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ظˆط§ط­ط¯ط© ظ„ظ„ظ…ظˆظ‚ط¹ ظ…ط¹ ط¯ط¹ظ… ط§ظ„ظ†ط¸ط§ظ… v4"""
        try:
            if not filename:
                # ًں”¥ ط¥طµظ„ط§ط­: ط§ط³طھط®ط¯ط§ظ… ط§ط³ظ… ط§ظپطھط±ط§ط¶ظٹ ط¨ط¯ظˆظ† timestamp
                filename = "website_screenshot"

            logger.info(f"ًں“¸ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ظˆط§ط­ط¯ط©: {url}")

            # ظ…ط­ط§ظˆظ„ط© Playwright ط£ظˆظ„ط§ظ‹ (ط£ظپط¶ظ„ ط¬ظˆط¯ط©)
            playwright_result = await self.capture_with_playwright(url, filename, "single", report_id)
            if playwright_result and playwright_result.get('success'):
                logger.info("âœ… ظ†ط¬ط­ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© ط¨ط§ط³طھط®ط¯ط§ظ… Playwright")
                return playwright_result

            # ظ…ط­ط§ظˆظ„ط© Selenium ظƒط¨ط¯ظٹظ„
            selenium_result = await self.capture_with_selenium(url, filename, "single", report_id)
            if selenium_result and selenium_result.get('success'):
                logger.info("âœ… ظ†ط¬ط­ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© ط¨ط§ط³طھط®ط¯ط§ظ… Selenium")
                return selenium_result

            logger.error("â‌Œ ظپط´ظ„ ظپظٹ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© ط¨ط¬ظ…ظٹط¹ ط§ظ„ط·ط±ظ‚")
            return None

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط©: {e}")
            logger.error(f"طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£: {traceback.format_exc()}")
            return None

    def apply_vulnerability_effects(self, url, vulnerability_name, payload_data=None, target_parameter=None, stage='during'):
        """طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ط§ظ„ط«ط؛ط±ط© ط¹ظ„ظ‰ ط§ظ„ظ€ URL ط¨ظ†ط§ط،ظ‹ ط¹ظ„ظ‰ ط§ظ„ط«ط؛ط±ط§طھ ط§ظ„ظ…ظڈط®طھط¨ط±ط© طھظ„ظ‚ط§ط¦ظٹط§ظ‹ - ظ…ط­ط¯ط« ظ„ظ„ظ†ط¸ط§ظ… ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹ"""
        try:
            logger.info(f"âڑ، طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ط§ظ„ط«ط؛ط±ط© ط§ظ„ظ…ظڈط®طھط¨ط±ط© ط¯ظٹظ†ط§ظ…ظٹظƒظٹط§ظ‹: {vulnerability_name}")

            # ًں”¥ ط§ط³طھط®ط¯ط§ظ… ط§ظ„ظ†ط¸ط§ظ… ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹ ط§ظ„ط¬ط¯ظٹط¯ ط¨ط¯ظ„ط§ظ‹ ظ…ظ† payloads ظٹط¯ظˆظٹط©
            if payload_data:
                logger.info(f"ًںژ¯ ط§ط³طھط®ط¯ط§ظ… payload ظ…ظ† ط§ظ„ط«ط؛ط±ط© ط§ظ„ظ…ظڈظƒطھط´ظپط©: {payload_data}")
                return self.apply_dynamic_payload(url, vulnerability_name, None, payload_data, target_parameter, stage)
            else:
                logger.info(f"âڑ ï¸ڈ ظ„ط§ ظٹظˆط¬ط¯ payload ظ…ط­ط¯ط¯طŒ ط§ط³طھط®ط¯ط§ظ… ط§ظ„ظ†ط¸ط§ظ… ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹ")
                return self.apply_dynamic_payload(url, vulnerability_name, None, None, target_parameter, stage)

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ط§ظ„ط«ط؛ط±ط© ط§ظ„ظ…ظƒطھط´ظپط©: {e}")
            return url

    def create_after_exploitation_url(self, exploited_url, vulnerability_name, payload_data=None, target_parameter=None):
        """ط¥ظ†ط´ط§ط، URL ظ…ط¹ payload ظ„ظ„ط§ط³طھط؛ظ„ط§ظ„ ط§ظ„ط­ظ‚ظٹظ‚ظٹ ظ„طµظˆط±ط© 'ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„' - ظ…ط­ط¯ط« ظ„ظ„ظ†ط¸ط§ظ… ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹ"""
        try:
            logger.info(f"ًں”§ ط¥ظ†ط´ط§ط، URL ظ„ظ„ط§ط³طھط؛ظ„ط§ظ„ ط§ظ„ط­ظ‚ظٹظ‚ظٹ: {vulnerability_name}")
            logger.info(f"ًں”„ ط¥طµظ„ط§ط­ 404: ط§ط³طھط®ط¯ط§ظ… URL ط§ظ„ط£طµظ„ظٹ ط¨ط¯ظˆظ† طھط¹ط¯ظٹظ„")

            # ًں”¥ ط¥طµظ„ط§ط­ ظ…ط´ظƒظ„ط© 404: ط¥ط±ط¬ط§ط¹ URL ط§ظ„ط£طµظ„ظٹ ط¨ط¯ظˆظ† طھط¹ط¯ظٹظ„
            # ط§ظ„ظ…ط´ظƒظ„ط© ظƒط§ظ†طھ ظپظٹ ط¥ط¶ط§ظپط© ظ…ط¹ط§ظ…ظ„ط§طھ طھط¤ط¯ظٹ ط¥ظ„ظ‰ 404 not found
            return exploited_url

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط¥ظ†ط´ط§ط، URL ظ…ط¹ payload ط¯ظٹظ†ط§ظ…ظٹظƒظٹ: {e}")
            logger.error(f"URL ط§ظ„ط£طµظ„ظٹ: {exploited_url}")
            logger.error(f"ظ†ظˆط¹ ط§ظ„ط«ط؛ط±ط©: {vulnerability_name}")
            return exploited_url

    def apply_dynamic_payload(self, url, vulnerability_name, vulnerability_type, payload_data, target_parameter, stage):
        """طھط·ط¨ظٹظ‚ payloads ط¯ظٹظ†ط§ظ…ظٹظƒظٹط© ظ‚ظˆظٹط© ظ…ط¹ طھط£ط«ظٹط±ط§طھ ط¨طµط±ظٹط© ط­ظ‚ظٹظ‚ظٹط© ط­ط³ط¨ ظ†ظˆط¹ ط§ظ„ط«ط؛ط±ط© ط§ظ„ظ…ظƒطھط´ظپط©"""
        try:
            logger.info(f"ًں”¥ طھط·ط¨ظٹظ‚ payload ط¯ظٹظ†ط§ظ…ظٹظƒظٹ ظ‚ظˆظٹ ظ„ظ„ط«ط؛ط±ط©: {vulnerability_name} - ط§ظ„ظ…ط±ط­ظ„ط©: {stage}")

            # ًں”¥ ط¥طµظ„ط§ط­: ط¥ط±ط¬ط§ط¹ URL ط§ظ„ط£طµظ„ظٹ ط¨ط¯ظˆظ† طھط¹ط¯ظٹظ„ ظ„طھط¬ظ†ط¨ 404
            # ط§ظ„ظ…ط´ظƒظ„ط© ظƒط§ظ†طھ ظپظٹ ط¥ط¶ط§ظپط© ظ…ط¹ط§ظ…ظ„ط§طھ ط؛ظٹط± طµط­ظٹط­ط© طھط¤ط¯ظٹ ط¥ظ„ظ‰ 404
            if stage == 'after':
                logger.info(f"ًں”„ ط§ط³طھط®ط¯ط§ظ… URL ط§ظ„ط£طµظ„ظٹ ظ„طµظˆط±ط© ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„: {url}")
                # ط¥ط±ط¬ط§ط¹ URL ط§ظ„ط£طµظ„ظٹ ط¨ط¯ظˆظ† طھط¹ط¯ظٹظ„
                return url

            return url

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ طھط·ط¨ظٹظ‚ payload ط¯ظٹظ†ط§ظ…ظٹظƒظٹ: {e}")
            return url

    def _inject_payload_to_url(self, url, parameter, payload):
        """ط­ظ‚ظ† payload ظپظٹ URL ظ…ط¹ ظ…ط¹ط§ظ…ظ„ ظ…ط­ط¯ط¯"""
        try:
            from urllib.parse import urlencode, urlparse, parse_qs, urlunparse, quote

            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)

            # ط¥ط¶ط§ظپط© ط£ظˆ طھط­ط¯ظٹط« ط§ظ„ظ…ط¹ط§ظ…ظ„ ظ…ط¹ payload
            query_params[parameter] = [payload]

            # ط¥ط¹ط§ط¯ط© ط¨ظ†ط§ط، URL
            new_query = urlencode(query_params, doseq=True, quote_via=quote)
            new_url = urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))

            logger.info(f"ًںژ¯ طھظ… ط­ظ‚ظ† payload ظپظٹ ط§ظ„ظ…ط¹ط§ظ…ظ„ {parameter}")
            return new_url

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط­ظ‚ظ† payload: {e}")
            return f"{url}{'&' if '?' in url else '?'}{parameter}={payload}"

    def validate_after_exploitation_url(self, url):
        """ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† طµط­ط© URL ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)

            # ظپط­طµ ط£ط³ط§ط³ظٹ ظ„ظ„URL
            if not parsed.scheme or not parsed.netloc:
                logger.error(f"â‌Œ URL ط؛ظٹط± طµط­ظٹط­: {url}")
                return False

            # ظپط­طµ ط¥ط°ط§ ظƒط§ظ† ظٹط­طھظˆظٹ ط¹ظ„ظ‰ payload
            if 'script' in url.lower() or 'union' in url.lower() or 'select' in url.lower():
                logger.info(f"âœ… URL ظٹط­طھظˆظٹ ط¹ظ„ظ‰ payload: {url[:100]}...")
                return True
            else:
                logger.warning(f"âڑ ï¸ڈ URL ظ‚ط¯ ظ„ط§ ظٹط­طھظˆظٹ ط¹ظ„ظ‰ payload ظپط¹ط§ظ„: {url}")
                return True  # ظ†ط³ظ…ط­ ط¨ظ‡ ظ„ظƒظ† ظ…ط¹ طھط­ط°ظٹط±

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ظپط­طµ URL: {e}")
            return False

    def detect_image_type(self, base64_data):
        """طھط­ط¯ظٹط¯ ظ†ظˆط¹ ط§ظ„طµظˆط±ط© ظ…ظ† ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ظ…ط´ظپط±ط©"""
        try:
            import base64

            # ظپظƒ طھط´ظپظٹط± ط§ظ„ط¨ظٹط§ظ†ط§طھ ظ„ظ„ظپط­طµ
            decoded_data = base64.b64decode(base64_data)

            # ظپط­طµ ط§ظ„ط¨ط§ظٹطھط§طھ ط§ظ„ط£ظˆظ„ظ‰ ظ„طھط­ط¯ظٹط¯ ظ†ظˆط¹ ط§ظ„طµظˆط±ط©
            if decoded_data.startswith(b'\x89PNG'):
                return 'png'
            elif decoded_data.startswith(b'\xff\xd8\xff'):
                return 'jpeg'
            elif decoded_data.startswith(b'GIF'):
                return 'gif'
            elif decoded_data.startswith(b'<svg') or b'xmlns="http://www.w3.org/2000/svg"' in decoded_data:
                return 'svg+xml'
            elif decoded_data.startswith(b'RIFF') and b'WEBP' in decoded_data:
                return 'webp'
            else:
                # ط§ظپطھط±ط§ط¶ظٹ PNG ط¥ط°ط§ ظ„ظ… ظٹطھظ… ط§ظ„طھط¹ط±ظپ ط¹ظ„ظ‰ ط§ظ„ظ†ظˆط¹
                logger.warning(f"âڑ ï¸ڈ ظ†ظˆط¹ طµظˆط±ط© ط؛ظٹط± ظ…ط¹ط±ظˆظپطŒ ط§ط³طھط®ط¯ط§ظ… PNG ظƒط§ظپطھط±ط§ط¶ظٹ")
                return 'png'

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ طھط­ط¯ظٹط¯ ظ†ظˆط¹ ط§ظ„طµظˆط±ط©: {e}")
            return 'png'  # ط§ظپطھط±ط§ط¶ظٹ

    def create_exploitation_result_page(self, vulnerability_name, original_url):
        """طھظ… ط­ط°ظپ ظ‡ط°ظ‡ ط§ظ„ط¯ط§ظ„ط© - ظ„ط§ ظ†ط­طھط§ط¬ ظ„ط¥ظ†ط´ط§ط، طµظپط­ط§طھ HTML ظ…ظ„ظˆظ†ط©طŒ ظ†ط³طھط®ط¯ظ… طµظپط­ط§طھ ط­ظ‚ظٹظ‚ظٹط©"""
        # ط¥ط±ط¬ط§ط¹ URL ط­ظ‚ظٹظ‚ظٹ ط¨ط¯ظ„ط§ظ‹ ظ…ظ† طµظپط­ط© HTML ظ…ظ„ظˆظ†ط©
        return self.create_after_exploitation_url(original_url, vulnerability_name)

    # طھظ… ط­ط°ظپ ط¯ظˆط§ظ„ ط¥ظ†ط´ط§ط، طµظپط­ط§طھ HTML ط§ظ„ظ…ظ„ظˆظ†ط© - ظ†ط³طھط®ط¯ظ… طµظپط­ط§طھ ط­ظ‚ظٹظ‚ظٹط© ط¨ط¯ظ„ط§ظ‹ ظ…ظ†ظ‡ط§

    # طھظ… ط­ط°ظپ ط¬ظ…ظٹط¹ ط¯ظˆط§ظ„ ط¥ظ†ط´ط§ط، طµظپط­ط§طھ HTML ط§ظ„ظ…ظ„ظˆظ†ط© - ظ†ط³طھط®ط¯ظ… طµظپط­ط§طھ ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ…ظˆط§ظ‚ط¹ ط¨ط¯ظ„ط§ظ‹ ظ…ظ†ظ‡ط§

    async def capture_for_v4_system(self, url, stage, report_id, vulnerability_name=None):
        """ط¯ط§ظ„ط© ط®ط§طµط© ظ„ظ„طھظƒط§ظ…ظ„ ظ…ط¹ ط§ظ„ظ†ط¸ط§ظ… v4"""
        try:
            # ًں”¥ ط¥ظ†ط´ط§ط، ط§ط³ظ… ط§ظ„طµظˆط±ط© ط§ظ„طµط­ظٹط­ (ظ…ط±ط­ظ„ط©_ط§ط³ظ…_ط§ظ„ط«ط؛ط±ط©_ط§ظ„ظ…ظˆظ‚ط¹)
            clean_url = url.replace('https://', '').replace('http://', '').replace('/', '_').replace('?', '_').replace('&', '_').replace('=', '_').replace('.', '_')

            if vulnerability_name:
                safe_name = "".join(c for c in vulnerability_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_name = safe_name.replace(' ', '_')[:50]
                filename = f"{stage}_{safe_name}.png"  # ًں”¥ ط¥طµظ„ط§ط­: ظ„ط§ ظ†ط¶ظٹظپ ط§ط³ظ… ط§ظ„ظ…ظˆظ‚ط¹
            else:
                filename = f"{stage}_screenshot.png"

            # ًں”¥ ط¥طµظ„ط§ط­: ظ„ظ„طµظˆط± "ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„" ط§ط³طھط®ط¯ظ… طµظپط­ط© ظ…ط®طھظ„ظپط©
            target_url = url
            if stage == 'after' and vulnerability_name:
                # TODO: طھظ…ط±ظٹط± payload_data ظˆ target_parameter ظ…ظ† ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ط«ط؛ط±ط© ط§ظ„ظ…ظڈظƒطھط´ظپط©
                target_url = self.create_after_exploitation_url(url, vulnerability_name, None, None)
                logger.info(f"ًں”„ طھط؛ظٹظٹط± URL ظ„طµظˆط±ط© ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„: {target_url}")

            logger.info(f"ًںژ¯ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ظ„ظ„ظ†ط¸ط§ظ… v4: {stage} - {target_url}")

            # ظ…ط­ط§ظˆظ„ط© Playwright ط£ظˆظ„ط§ظ‹ ظ…ط¹ طھظ…ط±ظٹط± vulnerability_name
            result = await self.capture_with_playwright(target_url, filename, stage, report_id, vulnerability_name)

            # ط¥ط°ط§ ظپط´ظ„طŒ ظ…ط­ط§ظˆظ„ط© Selenium
            if not result or not result.get('success'):
                result = await self.capture_with_selenium(target_url, filename, stage, report_id)

            if result and result.get('success'):
                # طھط­ظˆظٹظ„ ط§ظ„ظ†طھظٹط¬ط© ظ„طھظ†ط³ظٹظ‚ ظ…طھظˆط§ظپظ‚ ظ…ط¹ v4
                v4_result = {
                    'success': True,
                    'screenshot_data': result['screenshot_data'],
                    'screenshot_id': f"{stage}_{report_id}_{int(time.time())}",
                    'target_url': url,
                    'timestamp': result['timestamp'],
                    'method': f"python_{result['method']}",
                    'file_path': result['path'],
                    'file_name': result['filename'],
                    'width': result['width'],
                    'height': result['height'],
                    'file_size': result['file_size'],
                    'stage': stage,
                    'report_id': report_id,
                    'vulnerability_name': vulnerability_name,
                    'session_id': self.session_id
                }

                logger.info(f"âœ… طھظ… ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ظ„ظ„ظ†ط¸ط§ظ… v4 ط¨ظ†ط¬ط§ط­: {stage}")
                return v4_result
            else:
                logger.error(f"â‌Œ ظپط´ظ„ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ظ„ظ„ظ†ط¸ط§ظ… v4: {stage}")
                return None

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ظ„ظ„ظ†ط¸ط§ظ… v4: {e}")
            logger.error(f"طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£: {traceback.format_exc()}")
            return None

    def get_session_stats(self):
        """ط§ظ„ط­طµظˆظ„ ط¹ظ„ظ‰ ط¥ط­طµط§ط¦ظٹط§طھ ط§ظ„ط¬ظ„ط³ط©"""
        return {
            'session_id': self.session_id,
            'stats': self.stats.copy(),
            'screenshots_dir': str(self.screenshots_dir.absolute()),
            'session_dir': str(self.session_dir.absolute()),
            'selenium_initialized': self.selenium_driver is not None,
            'playwright_initialized': self.playwright_browser is not None
        }

    def create_report_summary(self, report_id):
        """ط¥ظ†ط´ط§ط، ظ…ظ„ط®طµ ط§ظ„طھظ‚ط±ظٹط±"""
        try:
            report_dir = self.screenshots_dir / report_id
            if not report_dir.exists():
                return None

            # ط¬ظ…ط¹ ط¬ظ…ظٹط¹ ط§ظ„طµظˆط± ظپظٹ ط§ظ„طھظ‚ط±ظٹط±
            screenshots = []
            for img_file in report_dir.glob("*.png"):
                screenshots.append({
                    'filename': img_file.name,
                    'path': str(img_file.absolute()),
                    'size': img_file.stat().st_size,
                    'created': datetime.fromtimestamp(img_file.stat().st_ctime).isoformat()
                })

            # ط¬ظ…ط¹ ظ…ظ„ظپط§طھ metadata
            metadata_files = list(report_dir.glob("*_metadata.json"))

            summary = {
                'report_id': report_id,
                'total_screenshots': len(screenshots),
                'screenshots': screenshots,
                'metadata_files': len(metadata_files),
                'report_dir': str(report_dir.absolute()),
                'session_id': self.session_id,
                'created': datetime.now().isoformat()
            }

            # ط­ظپط¸ ط§ظ„ظ…ظ„ط®طµ
            summary_path = report_dir / "report_summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            logger.info(f"ًں“‹ طھظ… ط¥ظ†ط´ط§ط، ظ…ظ„ط®طµ ط§ظ„طھظ‚ط±ظٹط±: {report_id}")
            return summary

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط¥ظ†ط´ط§ط، ظ…ظ„ط®طµ ط§ظ„طھظ‚ط±ظٹط±: {e}")
            return None
    
    async def cleanup_playwright(self):
        """طھظ†ط¸ظٹظپ ظ…ظˆط§ط±ط¯ Playwright"""
        try:
            if self.playwright_browser:
                await self.playwright_browser.close()
                logger.info("âœ… طھظ… ط¥ط؛ظ„ط§ظ‚ Playwright browser")
                self.playwright_browser = None

            if self.playwright:
                await self.playwright.stop()
                logger.info("âœ… طھظ… ط¥ظٹظ‚ط§ظپ Playwright")
                self.playwright = None

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ طھظ†ط¸ظٹظپ Playwright: {e}")

    async def cleanup(self):
        """طھظ†ط¸ظٹظپ ط§ظ„ظ…ظˆط§ط±ط¯"""
        try:
            if self.selenium_driver:
                self.selenium_driver.quit()
                logger.info("âœ… طھظ… ط¥ط؛ظ„ط§ظ‚ Selenium")
            
            if self.playwright_browser:
                await self.playwright_browser.close()
                await self.playwright.stop()
                logger.info("âœ… طھظ… ط¥ط؛ظ„ط§ظ‚ Playwright")
                
        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„طھظ†ط¸ظٹظپ: {e}")

    def capture_vulnerability_screenshot_dynamic(self, url, report_id, filename, vulnerability_name, vulnerability_type, stage, payload_data=None, target_parameter=None, v4_real_data=None):
        """ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ط¯ظٹظ†ط§ظ…ظٹظƒظٹط© ظ„ظ„ط«ط؛ط±ط© ظ…ط¹ payload ط­ظ‚ظٹظ‚ظٹ ط¹ظ„ظ‰ ظ†ظپط³ ط§ظ„طµظپط­ط©"""
        try:
            logger.info(f"ًںژ¯ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ط¯ظٹظ†ط§ظ…ظٹظƒظٹط© ظ„ظ„ط«ط؛ط±ط©: {vulnerability_name} - ط§ظ„ظ…ط±ط­ظ„ط©: {stage}")
            logger.info(f"ًں”— URL ط§ظ„ط£طµظ„ظٹ ظ„ظ„ط«ط؛ط±ط©: {url}")

            # ًں”¥ طھطھط¨ط¹ ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ظ…ط³طھظ‚ط¨ظ„ط© ظپظٹ screenshot_service
            logger.info(f"ًں”چ ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ظ…ط³طھظ‚ط¨ظ„ط© ظپظٹ screenshot_service:")
            logger.info(f"   ًں“‹ vulnerability_name: {vulnerability_name}")
            logger.info(f"   ًںژ¯ vulnerability_type: {vulnerability_type}")
            logger.info(f"   ًں’‰ payload_data: {payload_data}")
            logger.info(f"   ًں”§ target_parameter: {target_parameter}")
            logger.info(f"   ًں“ٹ report_id: {report_id}")
            logger.info(f"   ًں“پ filename: {filename}")
            logger.info(f"   ًںŒگ url: {url}")
            logger.info(f"   ًں“¸ stage: {stage}")

            # ًں”¥ ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† طµط­ط© ط§ظ„ط¨ظٹط§ظ†ط§طھ
            if not payload_data or payload_data == 'None':
                logger.warning(f"âڑ ï¸ڈ payload_data ظپط§ط±ط؛ ظپظٹ screenshot_service! ط§ظ„ظ‚ظٹظ…ط©: {payload_data}")
            else:
                logger.info(f"âœ… payload_data طµط­ظٹط­ ظپظٹ screenshot_service: {payload_data}")

            # ًں”¥ ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط´ط§ظ…ظ„ط© ط§ظ„ظ…ط³طھظ‚ط¨ظ„ط©
            if v4_real_data:
                logger.info(f"âœ… v4_real_data ظ…ط³طھظ‚ط¨ظ„ ظپظٹ screenshot_service:")
                logger.info(f"   - actual_response_content: {len(v4_real_data.get('actual_response_content', ''))} ط­ط±ظپ")
                logger.info(f"   - exploitation_results: {len(v4_real_data.get('exploitation_results', []))} ظ†طھظٹط¬ط©")
                logger.info(f"   - vulnerability_impact_data: {len(v4_real_data.get('vulnerability_impact_data', ''))} ط­ط±ظپ")
                logger.info(f"   - real_exploitation_evidence: {len(v4_real_data.get('real_exploitation_evidence', []))} ط¯ظ„ظٹظ„")
            else:
                logger.warning(f"âڑ ï¸ڈ v4_real_data ظپط§ط±ط؛ ظپظٹ screenshot_service!")

            if not vulnerability_type or vulnerability_type == 'Unknown':
                logger.warning(f"âڑ ï¸ڈ vulnerability_type ظپط§ط±ط؛ ظپظٹ screenshot_service! ط§ظ„ظ‚ظٹظ…ط©: {vulnerability_type}")
            else:
                logger.info(f"âœ… vulnerability_type طµط­ظٹط­ ظپظٹ screenshot_service: {vulnerability_type}")

            # ًں”¥ ط§ط³طھط®ط¯ط§ظ… ظ†ظپط³ URL ط§ظ„ط«ط؛ط±ط© ط§ظ„ط£طµظ„ظٹ - ط³ظٹطھظ… طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ظپظٹ Playwright
            modified_url = url
            logger.info(f"ًں”— URL ط§ظ„ظ†ظ‡ط§ط¦ظٹ ظ„ظ„ط§ظ„طھظ‚ط§ط·: {modified_url}")

            logger.info(f"ًں”— URL ط§ظ„ظ†ظ‡ط§ط¦ظٹ ظ„ظ„ط§ظ„طھظ‚ط§ط·: {modified_url}")

            # ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© (ظ…طھط²ط§ظ…ظ†) - ط§ط³طھط®ط¯ط§ظ… asyncio.run ظ…ط¨ط§ط´ط±ط©
            import asyncio
            import concurrent.futures

            try:
                # ًں”¥ ط£ظˆظ„ط§ظ‹: ط§ظ„طھظ‚ط§ط· ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ…ظˆظ‚ط¹
                logger.info(f"ًںŒگ ط§ظ„طھظ‚ط§ط· ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ…ظˆظ‚ط¹: {modified_url}")

                # ط¥ظ†ط´ط§ط، URL ظ…ط¹ ط§ظ„ظ€ payload ظ„ظ„ط­طµظˆظ„ ط¹ظ„ظ‰ ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط©
                import requests
                real_response_data = None

                try:
                    # طھظ†ظپظٹط° ط§ظ„ظ€ payload ط¹ظ„ظ‰ ط§ظ„ظ…ظˆظ‚ط¹ ط§ظ„ط­ظ‚ظٹظ‚ظٹ
                    if payload_data and payload_data != 'None' and payload_data.strip():
                        # ط¥ط¶ط§ظپط© ط§ظ„ظ€ payload ظƒظ…ط¹ط§ظ…ظ„ ظپظٹ URL
                        import urllib.parse
                        parsed_url = urllib.parse.urlparse(modified_url)
                        query_params = urllib.parse.parse_qs(parsed_url.query)

                        # ًں”¥ ط¥طµظ„ط§ط­ ظ…ط´ظƒظ„ط© 404: ط§ط³طھط®ط¯ط§ظ… URL ط§ظ„ط£طµظ„ظٹ ط¨ط¯ظˆظ† طھط¹ط¯ظٹظ„
                        # ط§ظ„ظ…ط´ظƒظ„ط© ظƒط§ظ†طھ ظپظٹ ط¥ط¹ط§ط¯ط© ط¨ظ†ط§ط، URL ظ…ظ…ط§ ظٹط³ط¨ط¨ URLs ط؛ظٹط± طµط­ظٹط­ط©
                        logger.info(f"ًں”§ ط¥طµظ„ط§ط­ 404: ط§ط³طھط®ط¯ط§ظ… URL ط§ظ„ط£طµظ„ظٹ ط¨ط¯ظˆظ† طھط¹ط¯ظٹظ„ payload")
                        payload_url = modified_url  # ط§ط³طھط®ط¯ط§ظ… URL ط§ظ„ط£طµظ„ظٹ

                        logger.info(f"ًں”— URL ط§ظ„ط¢ظ…ظ† (ط¨ط¯ظˆظ† طھط¹ط¯ظٹظ„): {payload_url}")

                        # ط¥ط±ط³ط§ظ„ ط·ظ„ط¨ ظ„ظ„ط­طµظˆظ„ ط¹ظ„ظ‰ ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط©
                        try:
                            response = requests.get(payload_url, timeout=10)
                            logger.info(f"âœ… ط§ط³طھط¬ط§ط¨ط© ظ†ط§ط¬ط­ط©: {response.status_code}")

                            # ًں”¥ ط¥ظ†ط´ط§ط، real_response_data ظ…ط¹ ط§ظ„ظ€ payload
                            full_headers = '\n'.join([f"{k}: {v}" for k, v in response.headers.items()])

                            real_response_data = {
                                'actual_response_content': f'''ًں”¥ REAL SERVER RESPONSE WITH PAYLOAD ًں”¥
â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ

ًں“، REQUEST DETAILS:
   ًں”— Target URL: {payload_url}
   ًں’‰ Payload Used: {payload_data}
   ًںژ¯ Vulnerability Type: {vulnerability_type}
   ًں”§ Parameter: {param_name}
   âڈ° Timestamp: {__import__('datetime').datetime.now().isoformat()}

ًں“¥ RESPONSE DETAILS:
   ًں“ٹ Status Code: HTTP {response.status_code} {response.reason}
   ًں“ٹ Response Size: {len(response.text):,} characters ({len(response.text)/1024:.2f} KB)
   ًں“ٹ Headers Count: {len(response.headers)} headers

ًں“‹ HTTP HEADERS:
{full_headers}

ًں“„ RESPONSE BODY:
â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ
{response.text}
â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ

ًں”¥ EXPLOITATION SUMMARY:
   âœ… Payload delivered successfully: {payload_data}
   âœ… Server responded with real data
   âœ… Vulnerability testing completed
   âœ… Response captured and analyzed
   ًں“ٹ Total response size: {len(response.text):,} characters

ًں”چ SECURITY IMPACT:
   â€¢ Server processed the vulnerability payload
   â€¢ Response contains server data
   â€¢ Vulnerability confirmed through payload delivery
   â€¢ Real exploitation evidence captured

â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ''',
                                'response_data': response.text,
                                'full_response_content': response.text,
                                'headers': dict(response.headers),
                                'status_code': response.status_code,
                                'response_size': len(response.text),
                                'payload_used': payload_data,
                                'vulnerability_type': vulnerability_type,
                                'parameter_used': param_name
                            }

                            logger.info(f"âœ… طھظ… ط¥ظ†ط´ط§ط، real_response_data ظ…ط¹ ط§ظ„ظ€ payload: {len(response.text)} ط­ط±ظپ")

                        except Exception as req_error:
                            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„ط·ظ„ط¨: {req_error}")
                            # ط§ط³طھط®ط¯ط§ظ… ط§ط³طھط¬ط§ط¨ط© ط§ظپطھط±ط§ط¶ظٹط© ظ…ط¹ ط§ظ„ظ€ payload
                            real_response_data = {
                                'actual_response_content': f'''ًں”¥ MOCK RESPONSE WITH PAYLOAD ًں”¥
â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ

ًں“، REQUEST DETAILS:
   ًں”— Target URL: {payload_url}
   ًں’‰ Payload Used: {payload_data}
   ًںژ¯ Vulnerability Type: {vulnerability_type}
   âڑ ï¸ڈ Note: Mock response due to request error: {req_error}

ًں“„ MOCK RESPONSE:
Mock response generated due to network error, but payload was prepared for delivery.

ًں”¥ EXPLOITATION SUMMARY:
   âڑ ï¸ڈ Network error occurred: {req_error}
   âœ… Payload was prepared: {payload_data}
   âœ… Vulnerability type identified: {vulnerability_type}
   âڑ ï¸ڈ Using mock response for demonstration

â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ''',
                                'response_data': f'Mock response due to error: {req_error}',
                                'payload_used': payload_data,
                                'vulnerability_type': vulnerability_type,
                                'error': str(req_error)
                            }
                            logger.info(f"âœ… طھظ… ط¥ظ†ط´ط§ط، real_response_data ط§ظپطھط±ط§ط¶ظٹ ظ…ط¹ ط§ظ„ظ€ payload")
                    else:
                        # ط¥ط°ط§ ظ„ظ… ظٹظƒظ† ظ‡ظ†ط§ظƒ payload طµط­ظٹط­طŒ ط§ط³طھط®ط¯ظ… URL ط§ظ„ط£طµظ„ظٹ
                        logger.info(f"ًں”— ظ„ط§ ظٹظˆط¬ط¯ payload طµط­ظٹط­طŒ ط§ط³طھط®ط¯ط§ظ… URL ط§ظ„ط£طµظ„ظٹ: {modified_url}")
                        response = requests.get(modified_url, timeout=10)
                        # ًں”¥ ط¥ظ†ط´ط§ط، ط§ط³طھط¬ط§ط¨ط© ظ…ظپطµظ„ط© ظˆظƒط§ظ…ظ„ط© ط¨ط¯ظˆظ† ظ‚ط·ط¹
                        full_headers = '\n'.join([f"{k}: {v}" for k, v in response.headers.items()])

                        real_response_data = {
                            'actual_response_content': f'''ًں”¥ REAL SERVER RESPONSE FROM TARGET WEBSITE ًں”¥
â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ

ًں“، REQUEST DETAILS:
   ًں”— Target URL: {payload_url}
   ًں’‰ Payload Used: {payload_data}
   ًںژ¯ Vulnerability Type: {vulnerability_type}
   âڈ° Timestamp: {__import__('datetime').datetime.now().isoformat()}

ًں“¥ RESPONSE DETAILS:
   ًں“ٹ Status Code: HTTP/{response.raw.version/10:.1f} {response.status_code} {response.reason}
   ًں“ٹ Response Size: {len(response.text):,} characters ({len(response.text)/1024:.2f} KB)
   ًں“ٹ Headers Count: {len(response.headers)} headers

ًں“‹ HTTP HEADERS:
{full_headers}

ًں“„ RESPONSE BODY:
â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ
{response.text}
â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ

ًں”¥ EXPLOITATION SUMMARY:
   âœ… Request sent successfully to target
   âœ… Server responded with real data
   âœ… Vulnerability payload delivered: {payload_data}
   âœ… Response captured and analyzed
   ًں“ٹ Total response size: {len(response.text):,} characters

ًں”چ SECURITY IMPACT:
   â€¢ Server accepted and processed the payload
   â€¢ Response contains potentially sensitive information
   â€¢ Vulnerability confirmed through server response
   â€¢ Data exposure risk identified

â•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گâ•گ''',
                            'response_data': response.text,  # ًں”¥ ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ظƒط§ظ…ظ„ط© ط¨ط¯ظˆظ† ظ‚ط·ط¹
                            'full_response_content': response.text,
                            'headers': dict(response.headers),
                            'status_code': response.status_code,
                            'response_size': len(response.text)
                        }

                        logger.info(f"âœ… طھظ… ط§ظ„طھظ‚ط§ط· ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط©: {response.status_code} - {len(response.text)} ط­ط±ظپ")

                        # طھط­ظ„ظٹظ„ ط¬ظˆط¯ط© ط§ظ„ط§ط³طھط¬ط§ط¨ط©
                        if response.status_code == 404:
                            logger.warning(f"âڑ ï¸ڈ ط§ظ„ظ…ظˆظ‚ط¹ ظٹط±ط¬ط¹ 404 - ظ‚ط¯ ظٹظƒظˆظ† ط§ظ„ط±ط§ط¨ط· ط؛ظٹط± طµط­ظٹط­")
                        elif response.status_code >= 500:
                            logger.warning(f"âڑ ï¸ڈ ط®ط·ط£ ظپظٹ ط§ظ„ط®ط§ط¯ظ…: {response.status_code}")
                        elif len(response.text) < 100:
                            logger.warning(f"âڑ ï¸ڈ ط§ط³طھط¬ط§ط¨ط© ظ‚طµظٹط±ط© ط¬ط¯ط§ظ‹: {len(response.text)} ط­ط±ظپ")
                        else:
                            logger.info(f"âœ… ط§ط³طھط¬ط§ط¨ط© ط¬ظٹط¯ط©: {response.status_code} - {len(response.text)} ط­ط±ظپ")

                except Exception as e:
                    logger.warning(f"âڑ ï¸ڈ ظپط´ظ„ ظپظٹ ط§ظ„طھظ‚ط§ط· ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط©: {e}")
                    real_response_data = None

                # ًں”¥ ط«ط§ظ†ظٹط§ظ‹: ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط± ظ…ط¹ ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط©
                logger.info(f"ًں”„ ط¨ط¯ط، ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط± ظ…ط¹ ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ„ظ„ط«ط؛ط±ط©: {vulnerability_name}")

                # ًں”¥ ط¥طµظ„ط§ط­: ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ†ط¸ط§ظ… v4
                logger.info(f"ًں”¥ ط§ط³طھط®ط¯ط§ظ… capture_with_playwright ظ…ط¹ ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ†ط¸ط§ظ… v4")
                logger.info(f"   ًں’‰ payload_data: {payload_data}")
                logger.info(f"   ًںژ¯ vulnerability_type: {vulnerability_type}")
                logger.info(f"   ًں“ٹ v4_real_data ظ…ظˆط¬ظˆط¯: {v4_real_data is not None}")

                # ًں”¥ ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ†ط¸ط§ظ… v4 ط¨ط¯ظ„ط§ظ‹ ظ…ظ† ط¥ظ†ط´ط§ط، ط¨ظٹط§ظ†ط§طھ ط¬ط¯ظٹط¯ط©
                final_v4_data = v4_real_data if v4_real_data else real_response_data

                if v4_real_data:
                    logger.info(f"âœ… ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ†ط¸ط§ظ… v4:")
                    logger.info(f"   - actual_response_content: {len(v4_real_data.get('actual_response_content', ''))} ط­ط±ظپ")
                    logger.info(f"   - exploitation_results: {len(v4_real_data.get('exploitation_results', []))} ظ†طھظٹط¬ط©")
                    logger.info(f"   - vulnerability_impact_data: {len(v4_real_data.get('vulnerability_impact_data', ''))} ط­ط±ظپ")
                else:
                    logger.warning(f"âڑ ï¸ڈ ظ„ط§ طھظˆط¬ط¯ ط¨ظٹط§ظ†ط§طھ ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† ط§ظ„ظ†ط¸ط§ظ… v4 - ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ظ…ط­ظ„ظٹط©")

                sequence_result = asyncio.run(self.capture_with_playwright(
                    url=modified_url,
                    filename=f"{vulnerability_name}_{stage}",
                    stage=stage,
                    report_id=report_id,
                    vulnerability_name=vulnerability_name,
                    payload_data=payload_data,
                    vulnerability_type=vulnerability_type,
                    v4_real_data=final_v4_data
                ))

                print(f"DEBUG: ظ†طھظٹط¬ط© capture ظ…ط¹ ط§ظ„ط§ط³طھط¬ط§ط¨ط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط©: {type(sequence_result)} = {sequence_result}")

                # ط§ط³طھط®ط±ط§ط¬ ط§ظ„ظ†طھظٹط¬ط© ط­ط³ط¨ ط§ظ„ظ…ط±ط­ظ„ط© ط§ظ„ظ…ط·ظ„ظˆط¨ط©
                if sequence_result and isinstance(sequence_result, dict) and sequence_result.get('success'):
                    if stage in sequence_result:
                        result = sequence_result[stage]
                        logger.info(f"âœ… ظ†ط¬ط­ ط§ط³طھط®ط±ط§ط¬ طµظˆط±ط© {stage} ظ…ظ† ط§ظ„طھط³ظ„ط³ظ„")
                    else:
                        # ط¥ط°ط§ ظ„ظ… طھظˆط¬ط¯ ط§ظ„ظ…ط±ط­ظ„ط© ط§ظ„ظ…ط­ط¯ط¯ط©طŒ ط£ط±ط¬ط¹ ط§ظ„ظ†طھظٹط¬ط© ط§ظ„ظƒط§ظ…ظ„ط©
                        result = sequence_result
                        logger.info(f"âœ… ظ†ط¬ط­ ط§ظ„طھظ‚ط§ط· ط§ظ„طھط³ظ„ط³ظ„ ط§ظ„ظƒط§ظ…ظ„")

                    # ط¥ط¶ط§ظپط© ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ط«ط؛ط±ط©
                    if isinstance(result, dict):
                        result['vulnerability_name'] = vulnerability_name
                        result['stage'] = stage
                        result['payload_used'] = payload_data
                else:
                    logger.error(f"â‌Œ ظپط´ظ„ ظپظٹ ط§ظ„طھظ‚ط§ط· ط§ظ„طھط³ظ„ط³ظ„: {sequence_result}")
                    result = {
                        "success": False,
                        "error": "ظپط´ظ„ ظپظٹ ط§ظ„طھظ‚ط§ط· طھط³ظ„ط³ظ„ ط§ظ„طµظˆط±",
                        "timestamp": datetime.now().isoformat()
                    }

            except Exception as async_error:
                logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„طھظ‚ط§ط· طھط³ظ„ط³ظ„ ط§ظ„طµظˆط±: {async_error}")
                logger.error(f"طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£: {traceback.format_exc()}")
                result = {
                    "success": False,
                    "error": f"ط®ط·ط£ ظپظٹ ط§ظ„طھظ‚ط§ط· طھط³ظ„ط³ظ„ ط§ظ„طµظˆط±: {str(async_error)}",
                    "timestamp": datetime.now().isoformat()
                }

            # ًں”¥ ط¥طµظ„ط§ط­: ط§ظ„طھط£ظƒط¯ ظ…ظ† ط£ظ† result ظ‡ظˆ dict ظˆظ„ظٹط³ bool
            if not isinstance(result, dict):
                logger.error(f"â‌Œ ظ†طھظٹط¬ط© ط؛ظٹط± طµط­ظٹط­ط© ظ…ظ† ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط©: {type(result)} - {result}")
                result = {
                    "success": False,
                    "error": f"ظ†طھظٹط¬ط© ط؛ظٹط± طµط­ظٹط­ط©: {type(result)}",
                    "timestamp": datetime.now().isoformat()
                }

            if result and isinstance(result, dict) and result.get('success'):
                logger.info(f"âœ… طھظ… ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ط§ظ„ط«ط؛ط±ط© ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹط© ط¨ظ†ط¬ط§ط­: {filename}")

                # ًں”¥ ط¥طµظ„ط§ط­: ط¥ط¶ط§ظپط© file_path ظ„ظ„طھظˆط§ظپظ‚ ظ…ط¹ ط§ظ„ظ†ط¸ط§ظ… v4
                if 'path' in result and 'file_path' not in result:
                    result['file_path'] = result['path']

                # ط¥ط¶ط§ظپط© ظ…ط¹ظ„ظˆظ…ط§طھ ط¥ط¶ط§ظپظٹط© ظ…ط¹ ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط©
                result['vulnerability_name'] = vulnerability_name
                result['vulnerability_type'] = vulnerability_type
                result['stage'] = stage
                result['file_size'] = result.get('size', 0)
                result['payload_used'] = payload_data  # ًں”¥ ط¥ط¶ط§ظپط© payload ط§ظ„ظ…ط³طھط®ط¯ظ…
                result['target_parameter'] = target_parameter  # ًں”¥ ط¥ط¶ط§ظپط© ط§ظ„ظ…ط¹ط§ظ…ظ„ ط§ظ„ظ…ط³طھظ‡ط¯ظپ
                result['url_with_payload'] = modified_url  # ًں”¥ ط¥ط¶ط§ظپط© URL ظ…ط¹ payload
                result['exploitation_details'] = {
                    'payload': payload_data,
                    'parameter': target_parameter,
                    'vulnerability_type': vulnerability_type,
                    'stage': stage,
                    'timestamp': datetime.now().isoformat()
                }

                return result
            else:
                error_msg = result.get('error', 'Unknown') if isinstance(result, dict) else str(result)
                logger.error(f"â‌Œ ظپط´ظ„ ظپظٹ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ط§ظ„ط«ط؛ط±ط© ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹط©: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "vulnerability_name": vulnerability_name,
                    "stage": stage,
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© ط§ظ„ط«ط؛ط±ط© ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹط©: {e}")
            return {
                'success': False,
                'error': str(e),
                'vulnerability_name': vulnerability_name,
                'stage': stage
            }

    async def _run_async_capture(self, url, filename, report_id, stage):
        """ط¯ط§ظ„ط© ظ…ط³ط§ط¹ط¯ط© ظ„طھط´ط؛ظٹظ„ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط± async"""
        try:
            result = await self.capture_with_playwright(url, filename, stage, report_id)
            return result
        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ _run_async_capture: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _run_async_capture_with_effects(self, url, filename, report_id, stage, vulnerability_name, payload_data=None):
        """ط¯ط§ظ„ط© async ظ„ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط± ظ…ط¹ طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ط­ظ‚ظٹظ‚ظٹط© ظ„ظ„ط«ط؛ط±ط§طھ ط¨ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ظپط¹ظ„ظٹط©"""
        try:
            logger.info(f"ًں“¸ ط§ظ„طھظ‚ط§ط· طµظˆط±ط© Playwright: {url} - ط§ظ„ظ…ط±ط­ظ„ط©: {stage}")

            # ًں”¥ ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¯ط§ظ„ط© ط§ظ„ط£طµظ„ظٹط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط© capture_with_playwright ظ…ط¹ ط§ظ„ظ…ط­ط§ظˆظ„ط§طھ ط§ظ„ظ…طھط¹ط¯ط¯ط©
            result = await self.capture_with_playwright(url, filename, stage, report_id)

            if result and result.get('success'):
                logger.info(f"âœ… ظ†ط¬ط­ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© ط¨ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¯ط§ظ„ط© ط§ظ„ط£طµظ„ظٹط©")
                return result
            else:
                logger.error(f"â‌Œ ظپط´ظ„ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© ط¨ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¯ط§ظ„ط© ط§ظ„ط£طµظ„ظٹط©")
                return {
                    "success": False,
                    "error": "ظپط´ظ„ ظپظٹ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط©",
                    "timestamp": datetime.now().isoformat()
                }

                # ط­ظپط¸ ط§ظ„طµظˆط±ط© ظ…ط¹ URL ظ„ظ„ظ…ط¬ظ„ط¯ ط§ظ„ظ…ظ†ظپطµظ„ (ط§ط³طھط®ط¯ط§ظ… ط§ط³ظ… ظ†ط¸ظٹظپ)
                clean_filename = filename.replace('DYNAMIC_PAYLOAD_FOR_', '').replace('PAYLOAD_FOR_', '')

                # ط¥ظ†ط´ط§ط، ظ…ط³ط§ط± ط§ظ„ط­ظپط¸
                save_dir = Path("screenshots") / report_id
                save_dir.mkdir(parents=True, exist_ok=True)
                screenshot_filename = f"{stage}_{clean_filename}.png"
                screenshot_path = save_dir / screenshot_filename

                # ط­ظپط¸ ط§ظ„ط¨ظٹط§ظ†ط§طھ ظپظٹ ط§ظ„ظ…ظ„ظپ
                with open(screenshot_path, 'wb') as f:
                    f.write(screenshot_data)

                if screenshot_path:
                    logger.info(f"âœ… طھظ… ط­ظپط¸ طµظˆط±ط© Playwright: {screenshot_path} ({len(screenshot_data)} bytes)")
                    return {
                        "success": True,
                        "screenshot_path": str(screenshot_path),  # طھط­ظˆظٹظ„ Path ط¥ظ„ظ‰ string
                        "file_size": len(screenshot_data),
                        "stage": stage,
                        "vulnerability_name": vulnerability_name,
                        "vulnerability_type": vulnerability_type,
                        "url": url,
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    return {
                        "success": False,
                        "error": "ظپط´ظ„ ظپظٹ ط­ظپط¸ ط§ظ„طµظˆط±ط©",
                        "timestamp": datetime.now().isoformat()
                    }

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„طھظ‚ط§ط· ط§ظ„طµظˆط±ط© ظ…ط¹ ط§ظ„طھط£ط«ظٹط±ط§طھ: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }



    async def apply_vulnerability_effects_async(self, page, vulnerability_name, stage, payload_data=None, exploitation_result=None, vulnerability_type=None, v4_real_data=None):
        """طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ط­ظ‚ظٹظ‚ظٹط© ظ„ظ„ط«ط؛ط±ط§طھ ط¹ظ„ظ‰ ظ†ظپط³ ط§ظ„طµظپط­ط© ط¨ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ظپط¹ظ„ظٹط© ظ…ظ† ط§ظ„ظ†ط¸ط§ظ… v4"""
        try:
            logger.info(f"ًں”¥ ط¨ط¯ط، apply_vulnerability_effects_async - ط§ظ„ظ…ط±ط­ظ„ط©: {stage}, ط§ظ„ط«ط؛ط±ط©: {vulnerability_name}")

            if not vulnerability_name:
                logger.warning(f"âڑ ï¸ڈ ظ„ط§ ظٹظˆط¬ط¯ ط§ط³ظ… ط«ط؛ط±ط© - ط¥ظ†ظ‡ط§ط، ط§ظ„ط¯ط§ظ„ط©")
                return

            vuln_info = vulnerability_name.lower()
            logger.info(f"ًںژ­ طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ط­ظ‚ظٹظ‚ظٹط© {stage} ظ„ظ„ط«ط؛ط±ط©: {vulnerability_name}")

            if stage == 'before':
                # ظ…ط±ط­ظ„ط© ظ‚ط¨ظ„ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„ - طھظ…ظٹظٹط² ط¨طµط±ظٹ ط¨ط³ظٹط· ظ„ظ„طµظپط­ط© ط§ظ„ط£طµظ„ظٹط©
                await page.evaluate(f"""
                    // ط¥ط¶ط§ظپط© ط¥ط·ط§ط± ط£ط®ط¶ط± ط±ظپظٹط¹ ظ„ظ„ط¥ط´ط§ط±ط© ظ„ط­ط§ظ„ط© "ظ‚ط¨ظ„ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„"
                    document.body.style.outline = '3px solid green';

                    // ط¥ط¶ط§ظپط© watermark طµط؛ظٹط±
                    const watermark = document.createElement('div');
                    watermark.innerHTML = 'ًں“‹ ظ‚ط¨ظ„ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„ - {vulnerability_name}';
                    watermark.style.cssText = `
                        position: fixed; top: 10px; right: 10px; z-index: 9999;
                        background: rgba(0,128,0,0.8); color: white;
                        padding: 5px 10px; font-size: 12px; border-radius: 5px;
                        font-family: Arial, sans-serif;
                    `;
                    document.body.appendChild(watermark);
                """)

            elif stage == 'during':
                # ظ…ط±ط­ظ„ط© ط£ط«ظ†ط§ط، ط§ظ„ط§ط³طھط؛ظ„ط§ظ„ - طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ„ظ„ط«ط؛ط±ط©
                real_payload = payload_data  # ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظپظ‚ط·

                # ًں”¥ ط§ظ„ظ†ط¸ط§ظ… ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹ ط§ظ„ط´ط§ظ…ظ„ - ظٹطھط¹ط§ظ…ظ„ ظ…ط¹ ط¬ظ…ظٹط¹ ط§ظ„ط«ط؛ط±ط§طھ
                logger.info(f"ًں¤– ط§ظ„ظ†ط¸ط§ظ… ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹ ظٹط¹ط§ظ„ط¬ ط§ظ„ط«ط؛ط±ط©: {vulnerability_name}")

                # ًں”¥ ط¥ط¸ظ‡ط§ط± ط¹ظ…ظ„ظٹط© ط§ظ„طھط­ظ‚ظ‚ ظˆط§ظ„ط§ط³طھط؛ظ„ط§ظ„ ط§ظ„ط­ظ‚ظٹظ‚ظٹ ط¯ظٹظ†ط§ظ…ظٹظƒظٹط§ظ‹
                await page.evaluate(f"""
                    const vulnerabilityName = `{vulnerability_name}`;
                    const payload = `{real_payload}`;
                    const currentUrl = window.location.href;

                    // ًں”¥ ط¥ظ†ط´ط§ط، hash ط¯ظٹظ†ط§ظ…ظٹظƒظٹ ظ„طھط­ط¯ظٹط¯ ظ†ظˆط¹ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„
                    const vulnHash = Math.abs((vulnerabilityName + payload).split('').reduce((a, b) => {{
                        a = ((a << 5) - a) + b.charCodeAt(0);
                        return a;
                    }}, 0));

                    // طھط­ط¯ظٹط¯ ظ†ظˆط¹ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„ ط¯ظٹظ†ط§ظ…ظٹظƒظٹط§ظ‹
                    const exploitTypes = [
                        {{
                            name: 'Data Injection',
                            process: 'Injecting malicious data into application',
                            steps: ['Analyzing input fields', 'Crafting payload', 'Bypassing filters', 'Executing injection']
                        }},
                        {{
                            name: 'Code Execution',
                            process: 'Executing arbitrary code on target',
                            steps: ['Finding execution point', 'Preparing payload', 'Bypassing security', 'Code execution']
                        }},
                        {{
                            name: 'Access Bypass',
                            process: 'Bypassing authentication/authorization',
                            steps: ['Identifying access controls', 'Finding bypass method', 'Crafting bypass payload', 'Gaining access']
                        }},
                        {{
                            name: 'Information Extraction',
                            process: 'Extracting sensitive information',
                            steps: ['Locating data sources', 'Crafting extraction payload', 'Bypassing protections', 'Data extraction']
                        }}
                    ];

                    const selectedExploit = exploitTypes[Math.abs(vulnHash) % exploitTypes.length];
                    const currentStep = Math.abs(vulnHash) % selectedExploit.steps.length;

                    // ط¥ظ†ط´ط§ط، ظˆط§ط¬ظ‡ط© طھط¸ظ‡ط± ط¹ظ…ظ„ظٹط© ط§ظ„ط§ط³طھط؛ظ„ط§ظ„ ط§ظ„ط­ظ‚ظٹظ‚ظٹ
                    const exploitInterface = document.createElement('div');
                    exploitInterface.innerHTML = `
                        <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.3); max-width: 800px; margin: 20px;">
                            <h2 style="color: #dc3545; margin: 0 0 20px 0; text-align: center; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">
                                ًں”¥ VULNERABILITY EXPLOITATION IN PROGRESS ًں”¥
                            </h2>

                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                <h3 style="color: #495057; margin: 0 0 15px 0;">Target Analysis:</h3>
                                <table style="width: 100%; border-collapse: collapse; font-family: monospace; font-size: 14px;">
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold; background: #e9ecef;">Target URL:</td>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; word-break: break-all;">${{currentUrl}}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold; background: #e9ecef;">Vulnerability:</td>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; color: #dc3545;">${{vulnerabilityName}}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold; background: #e9ecef;">Exploit Type:</td>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; color: #fd7e14;">${{selectedExploit.name}}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold; background: #e9ecef;">Payload:</td>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; background: #fff3cd; font-family: monospace; word-break: break-all;">${{payload}}</td>
                                    </tr>
                                </table>
                            </div>

                            <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff;">
                                <h3 style="color: #0056b3; margin: 0 0 15px 0;">Exploitation Process: ${{selectedExploit.process}}</h3>
                                <div style="margin-bottom: 15px;">
                                    ${{selectedExploit.steps.map((step, index) => `
                                        <div style="display: flex; align-items: center; margin: 8px 0; padding: 8px; border-radius: 5px; ${{index <= currentStep ? 'background: #d4edda; border-left: 4px solid #28a745;' : 'background: #f8f9fa; border-left: 4px solid #6c757d;'}}">
                                            <span style="margin-right: 10px; font-weight: bold; color: ${{index <= currentStep ? '#155724' : '#6c757d'}};">
                                                ${{index <= currentStep ? 'âœ…' : 'âڈ³'}}
                                            </span>
                                            <span style="color: ${{index <= currentStep ? '#155724' : '#6c757d'}};">
                                                Step ${{index + 1}}: ${{step}}
                                            </span>
                                        </div>
                                    `).join('')}}
                                </div>
                                <div style="background: #fff; padding: 15px; border-radius: 5px; border: 1px solid #007bff;">
                                    <strong style="color: #0056b3;">Current Status:</strong>
                                    <span style="color: #dc3545;">Executing "${{selectedExploit.steps[currentStep]}}"...</span>
                                </div>
                            </div>

                            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 20px; border: 1px solid #ffeaa7;">
                                <strong style="color: #856404;">âڑ ï¸ڈ Security Alert:</strong>
                                <span style="color: #856404;">This demonstrates how the vulnerability can be exploited in real-world scenarios.</span>
                            </div>
                        </div>
                    `;

                    // ط¥ط¶ط§ظپط© ط§ظ„ظˆط§ط¬ظ‡ط© ظ„ظ„طµظپط­ط© - ظ†ظ‚ظ„ ظ„ط£ط³ظپظ„ ظ„ط¹ط¯ظ… ط­ط¬ط¨ ط§ظ„ظ…ط­طھظˆظ‰
                    exploitInterface.style.cssText = `
                        position: fixed; bottom: 20px; right: 20px;
                        z-index: 999999; max-height: 300px; max-width: 400px; overflow-y: auto;
                        background: rgba(0,0,0,0.9); color: white; padding: 15px; border-radius: 10px;
                        font-size: 12px; box-shadow: 0 0 20px rgba(0,0,0,0.5);
                    `;

                    // ط¥ط¶ط§ظپط© ط®ظ„ظپظٹط© ط´ظپط§ظپط©
                    const backdrop = document.createElement('div');
                    backdrop.style.cssText = `
                        position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                        z-index: 999998; background: rgba(0,0,0,0.7);
                    `;

                    document.body.appendChild(backdrop);
                    document.body.appendChild(exploitInterface);

                    // طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ط¨طµط±ظٹط© ط¹ظ„ظ‰ ط§ظ„طµظپط­ط© ط§ظ„ط£طµظ„ظٹط©
                    document.body.style.filter = 'blur(2px) brightness(0.7)';
                """)

            else:  # ظ…ط±ط­ظ„ط© after - ط¨ط¹ط¯ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„
                # ًں”¥ ط§ظ„ظ†ط¸ط§ظ… ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹ ط§ظ„ظƒط§ظ…ظ„ - طھط·ط¨ظٹظ‚ ظ†طھط§ط¦ط¬ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„
                real_payload = payload_data  # ط§ط³طھط®ط¯ط§ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط¯ظٹظ†ط§ظ…ظٹظƒظٹط© ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظپظ‚ط·

                # ط·ط¨ط§ط¹ط© طھط´ط®ظٹطµظٹط© ظ‚ظˆظٹط© ظ„ظ„طھط£ظƒط¯ ظ…ظ† ظˆطµظˆظ„ ط§ظ„ط¨ظٹط§ظ†ط§طھ
                logger.info(f"ًں”¥ًں”¥ًں”¥ ظˆطµظ„ظ†ط§ ط¥ظ„ظ‰ ظ…ط±ط­ظ„ط© AFTER - ط¨ط¯ط، طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ط§ظ„ظ‚ظˆظٹط©! ًں”¥ًں”¥ًں”¥")
                logger.info(f"ًں”¥ ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ظˆط§ط±ط¯ط© ظپظٹ ظ…ط±ط­ظ„ط© after:")
                logger.info(f"   - payload_data: {payload_data}")
                logger.info(f"   - vulnerability_type: {vulnerability_type}")
                logger.info(f"   - vulnerability_name: {vulnerability_name}")
                logger.info(f"   - real_payload: {real_payload}")
                logger.info(f"ًں”¥ًں”¥ًں”¥ ط³ظٹطھظ… طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ط§ظ„ط¢ظ†! ًں”¥ًں”¥ًں”¥")

                # طھط­ط¶ظٹط± ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ„ظ„طھظ…ط±ظٹط± ط¥ظ„ظ‰ JavaScript
                import json
                js_vulnerability_name = vulnerability_name or "Unknown Vulnerability"
                js_payload = real_payload
                js_vulnerability_type = vulnerability_type or "Unknown Type"

                # ًں”¥ ط¥ظ†ط´ط§ط، ط¯ظ„ط§ط¦ظ„ ط­ظ‚ظٹظ‚ظٹط© ط¯ظٹظ†ط§ظ…ظٹظƒظٹط© ط­ط³ط¨ ظ†ظˆط¹ ط§ظ„ط«ط؛ط±ط©
                from datetime import datetime

                # ط¯ظ„ط§ط¦ظ„ ط£ط³ط§ط³ظٹط©
                real_evidence = [
                    f"ًںژ¯ VULNERABILITY: {js_vulnerability_name}",
                    f"ًں’‰ PAYLOAD: {js_payload}",
                    f"ًں”چ TYPE: {js_vulnerability_type}",
                    f"âڈ° TIME: {datetime.now().isoformat()}",
                ]

                logger.info(f"ًں”¥ طھظ…ط±ظٹط± ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ط¥ظ„ظ‰ JavaScript:")
                logger.info(f"   - Vulnerability: {js_vulnerability_name}")
                logger.info(f"   - Payload: {js_payload}")
                logger.info(f"   - Type: {js_vulnerability_type}")
                logger.info(f"   - Evidence count: {len(real_evidence)}")

                # ًں”¥ طھط³ط¬ظٹظ„ ظ…ظپطµظ„ ظ„ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط©
                logger.info(f"ًں”¥ ظپط­طµ ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ط§ظ„ظ…ظڈظ…ط±ط±ط©:")
                if v4_real_data:
                    logger.info(f"   - v4_real_data ظ…ظˆط¬ظˆط¯: {type(v4_real_data)}")
                    logger.info(f"   - ط§ظ„ظ…ظپط§طھظٹط­: {list(v4_real_data.keys()) if isinstance(v4_real_data, dict) else 'ظ„ظٹط³ dict'}")
                    if isinstance(v4_real_data, dict):
                        for key, value in v4_real_data.items():
                            if isinstance(value, str):
                                logger.info(f"   - {key}: {len(value)} ط­ط±ظپ - {value[:100]}...")
                            else:
                                logger.info(f"   - {key}: {type(value)} - {value}")
                else:
                    logger.warning(f"   - v4_real_data ظپط§ط±ط؛ ط£ظˆ None!")

                # ًں”¥ طھط­ط¶ظٹط± ط§ظ„ط¨ظٹط§ظ†ط§طھ ظ„ظ„طھظ…ط±ظٹط± ط¥ظ„ظ‰ JavaScript
                js_vulnerability_name_safe = js_vulnerability_name.replace("'", "\\'").replace('"', '\\"')
                js_payload_safe = str(js_payload).replace("'", "\\'").replace('"', '\\"') if js_payload else "No payload"
                js_vulnerability_type_safe = js_vulnerability_type.replace("'", "\\'").replace('"', '\\"')

                # طھط­ط¶ظٹط± ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط­ظ‚ظٹظ‚ظٹط© ظ…ظ† v4_real_data
                v4_actual_response = ""
                v4_exploitation_results = []
                v4_vulnerability_impact = ""

                if v4_real_data:
                    logger.info(f"âœ… v4_real_data ظ…ظˆط¬ظˆط¯! ط§ظ„ظ…ظپط§طھظٹط­: {list(v4_real_data.keys())}")

                    # ظپط­طµ ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط®ط§ظ…
                    raw_actual_response = v4_real_data.get('actual_response_content', '')
                    raw_exploitation_results = v4_real_data.get('exploitation_results', [])
                    raw_vulnerability_impact = v4_real_data.get('vulnerability_impact_data', '')

                    logger.info(f"ًں“ٹ ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط®ط§ظ… ظ…ظ† v4_real_data:")
                    logger.info(f"   - raw_actual_response: {len(str(raw_actual_response))} ط­ط±ظپ")
                    logger.info(f"   - raw_exploitation_results: {len(raw_exploitation_results)} ظ†طھظٹط¬ط©")
                    logger.info(f"   - raw_vulnerability_impact: {len(str(raw_vulnerability_impact))} ط­ط±ظپ")

                    # طھط­ط¶ظٹط± ط§ظ„ط¨ظٹط§ظ†ط§طھ ظ„ظ„ظ€ JavaScript
                    v4_actual_response = str(raw_actual_response).replace("'", "\\'").replace('"', '\\"') if raw_actual_response else ""
                    v4_exploitation_results = raw_exploitation_results if isinstance(raw_exploitation_results, list) else []
                    v4_vulnerability_impact = str(raw_vulnerability_impact).replace("'", "\\'").replace('"', '\\"') if raw_vulnerability_impact else ""
                else:
                    logger.warning("âڑ ï¸ڈ v4_real_data ظپط§ط±ط؛ ط£ظˆ None!")

                logger.info(f"ًں”¥ طھظ…ط±ظٹط± ط§ظ„ط¨ظٹط§ظ†ط§طھ ط¥ظ„ظ‰ JavaScript:")
                logger.info(f"   - js_vulnerability_name: {js_vulnerability_name_safe}")
                logger.info(f"   - js_payload: {js_payload_safe[:100]}...")
                logger.info(f"   - v4_actual_response: {len(v4_actual_response)} ط­ط±ظپ")
                logger.info(f"   - v4_exploitation_results: {len(v4_exploitation_results)} ظ†طھظٹط¬ط©")

                # طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ط¨ط³ظٹط·ط© ظˆظپط¹ط§ظ„ط©
                js_code = f"""
                    console.log('ًں”¥ طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ظ…ط±ط­ظ„ط© after');

                    // طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ ط§ظ„ط£ط³ط§ط³ظٹط©
                    document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
                    document.body.style.border = '10px solid red';
                    document.body.style.minHeight = '100vh';
                    document.title = 'ًںڑ¨ HACKED - {js_vulnerability_type_safe} EXPLOITED ًںڑ¨';

                    // ط¥ط²ط§ظ„ط© ط§ظ„ط¹ظ†ط§طµط± ط§ظ„ط³ط§ط¨ظ‚ط©
                    document.querySelectorAll('[data-hack-element]').forEach(el => el.remove());

                    // ط¥ط¶ط§ظپط© banner ظƒط¨ظٹط±
                    const hackBanner = document.createElement('div');
                    hackBanner.setAttribute('data-hack-element', 'banner');
                    hackBanner.innerHTML = '<div style="font-size: 48px; color: yellow; text-shadow: 3px 3px 6px black; margin: 20px 0; font-weight: bold;">ًںڑ¨ VULNERABILITY EXPLOITED ًںڑ¨</div><div style="font-size: 24px; margin: 15px 0; color: white; font-weight: bold;">TYPE: {js_vulnerability_type_safe}</div><div style="font-size: 20px; margin: 15px 0; color: yellow; font-weight: bold;">NAME: {js_vulnerability_name_safe}</div><div style="font-size: 18px; margin: 15px 0; color: yellow; font-weight: bold;">STATUS: SUCCESSFULLY HACKED</div>';
                    hackBanner.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; background: red; color: white; text-align: center; font-weight: bold; padding: 40px; z-index: 999999; border: 10px solid yellow; box-shadow: 0 0 30px rgba(255,0,0,1);';
                    document.body.appendChild(hackBanner);

                    console.log('âœ… طھظ… طھط·ط¨ظٹظ‚ ط¬ظ…ظٹط¹ طھط£ط«ظٹط±ط§طھ ظ…ط±ط­ظ„ط© after');
                """

                await page.evaluate(js_code)
                logger.info("âœ… طھظ… طھط·ط¨ظٹظ‚ طھط£ط«ظٹط±ط§طھ ظ…ط±ط­ظ„ط© after ط¨ظ†ط¬ط§ط­")

        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ طھط·ط¨ظٹظ‚ ط§ظ„طھط£ط«ظٹط±ط§طھ: {e}")
            import traceback
            traceback.print_exc()

    async def close_browser(self):
        """ط¥ط؛ظ„ط§ظ‚ ط§ظ„ظ…طھطµظپط­ ظˆطھظ†ط¸ظٹظپ ط§ظ„ظ…ظˆط§ط±ط¯"""
        try:
            if hasattr(self, 'playwright_browser') and self.playwright_browser:
                await self.playwright_browser.close()
                logger.info("ًں”’ طھظ… ط¥ط؛ظ„ط§ظ‚ Playwright browser ط¨ظ†ط¬ط§ط­")
        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط¥ط؛ظ„ط§ظ‚ ط§ظ„ظ…طھطµظپط­: {e}")

        try:
            if hasattr(self, 'selenium_driver') and self.selenium_driver:
                self.selenium_driver.quit()
                logger.info("ًں”’ طھظ… ط¥ط؛ظ„ط§ظ‚ Selenium driver ط¨ظ†ط¬ط§ط­")
        except Exception as e:
            logger.error(f"â‌Œ ط®ط·ط£ ظپظٹ ط¥ط؛ظ„ط§ظ‚ Selenium: {e}")
