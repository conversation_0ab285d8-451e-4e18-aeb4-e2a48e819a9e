/**
 * اختبار شامل للنظام v4 - فحص المشاكل الحقيقية
 * التحقق من السيرفر Python والتقاط الصور
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🧪 بدء الاختبار الشامل للنظام v4...');

class ComprehensiveSystemTester {
    constructor() {
        this.pythonServerUrl = 'http://localhost:8000';
        this.testResults = {
            serverAvailable: false,
            endpointsWorking: {},
            screenshotCapture: false,
            vulnerabilityTesting: false,
            imageGeneration: false
        };
        this.testId = `test_${Date.now()}`;
    }

    // طلب HTTP مع تفاصيل كاملة
    async makeDetailedRequest(endpoint, method = 'GET', data = null) {
        return new Promise((resolve, reject) => {
            const url = `${this.pythonServerUrl}${endpoint}`;
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'ComprehensiveSystemTester/1.0'
                },
                timeout: 30000 // 30 ثانية timeout
            };

            console.log(`📡 إرسال طلب: ${method} ${url}`);
            if (data) {
                console.log(`📄 البيانات: ${JSON.stringify(data, null, 2)}`);
            }

            const req = http.request(options, (res) => {
                let responseData = '';
                
                console.log(`📥 استجابة: ${res.statusCode} ${res.statusMessage}`);
                console.log(`📋 Headers: ${JSON.stringify(res.headers, null, 2)}`);
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    console.log(`📦 حجم الاستجابة: ${responseData.length} bytes`);
                    
                    try {
                        const jsonData = responseData ? JSON.parse(responseData) : {};
                        resolve({
                            status: res.statusCode,
                            headers: res.headers,
                            data: jsonData,
                            raw: responseData,
                            success: res.statusCode >= 200 && res.statusCode < 300
                        });
                    } catch (error) {
                        console.log(`📄 استجابة نصية: ${responseData.substring(0, 200)}...`);
                        resolve({
                            status: res.statusCode,
                            headers: res.headers,
                            data: null,
                            raw: responseData,
                            success: res.statusCode >= 200 && res.statusCode < 300
                        });
                    }
                });
            });

            req.on('error', (error) => {
                console.error(`❌ خطأ في الطلب: ${error.message}`);
                reject(error);
            });

            req.on('timeout', () => {
                console.error(`⏰ انتهت مهلة الطلب (30 ثانية)`);
                req.destroy();
                reject(new Error('Request timeout'));
            });

            if (data) {
                req.write(JSON.stringify(data));
            }
            
            req.end();
        });
    }

    // اختبار 1: توفر السيرفر Python
    async testServerAvailability() {
        console.log('\n🔍 اختبار 1: توفر السيرفر Python...');
        
        try {
            const response = await this.makeDetailedRequest('/health');
            
            if (response.success) {
                console.log('✅ السيرفر Python متاح ويعمل');
                console.log(`📊 الاستجابة: ${response.raw}`);
                this.testResults.serverAvailable = true;
                return true;
            } else {
                console.log(`❌ السيرفر Python لا يستجيب - الكود: ${response.status}`);
                return false;
            }
            
        } catch (error) {
            console.log(`❌ خطأ في الاتصال بالسيرفر: ${error.message}`);
            return false;
        }
    }

    // اختبار 2: فحص جميع endpoints المتاحة
    async testAllEndpoints() {
        console.log('\n🔍 اختبار 2: فحص جميع endpoints المتاحة...');
        
        const endpoints = [
            { path: '/health', method: 'GET', description: 'فحص حالة السيرفر' },
            { path: '/capture', method: 'POST', description: 'التقاط صورة عامة' },
            { path: '/v4_website', method: 'POST', description: 'فحص موقع v4' },
            { path: '/vulnerability_sequence', method: 'POST', description: 'تسلسل الثغرات' }
        ];
        
        for (const endpoint of endpoints) {
            console.log(`\n📡 اختبار endpoint: ${endpoint.method} ${endpoint.path}`);
            console.log(`📝 الوصف: ${endpoint.description}`);
            
            try {
                let response;
                
                if (endpoint.method === 'GET') {
                    response = await this.makeDetailedRequest(endpoint.path);
                } else {
                    // إرسال بيانات اختبار للـ POST endpoints
                    const testData = this.generateTestDataForEndpoint(endpoint.path);
                    response = await this.makeDetailedRequest(endpoint.path, 'POST', testData);
                }
                
                if (response.success) {
                    console.log(`✅ ${endpoint.path} يعمل بنجاح`);
                    this.testResults.endpointsWorking[endpoint.path] = true;
                } else {
                    console.log(`❌ ${endpoint.path} فشل - الكود: ${response.status}`);
                    console.log(`📄 الخطأ: ${response.raw}`);
                    this.testResults.endpointsWorking[endpoint.path] = false;
                }
                
            } catch (error) {
                console.log(`❌ خطأ في ${endpoint.path}: ${error.message}`);
                this.testResults.endpointsWorking[endpoint.path] = false;
            }
            
            // تأخير بين الطلبات
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    // إنشاء بيانات اختبار لكل endpoint
    generateTestDataForEndpoint(endpoint) {
        switch (endpoint) {
            case '/capture':
                return {
                    url: 'http://testphp.vulnweb.com/',
                    screenshot_id: `test_capture_${this.testId}`,
                    report_id: this.testId
                };
                
            case '/v4_website':
                return {
                    url: 'http://testphp.vulnweb.com/',
                    scan_type: 'comprehensive',
                    enable_screenshots: true,
                    report_id: this.testId
                };
                
            case '/vulnerability_sequence':
                return {
                    url: 'http://testphp.vulnweb.com/',
                    vulnerability_name: 'Test_SQL_Injection',
                    stage: 'before',
                    page_identifier: 'test_page',
                    report_id: this.testId
                };
                
            default:
                return {};
        }
    }

    // اختبار 3: التقاط صور حقيقي
    async testScreenshotCapture() {
        console.log('\n🔍 اختبار 3: التقاط صور حقيقي...');
        
        const testVulnerabilities = [
            'Test_SQL_Injection',
            'Test_XSS_Attack',
            'Test_CSRF_Vulnerability'
        ];
        
        const stages = ['before', 'during', 'after'];
        let successfulCaptures = 0;
        let totalAttempts = 0;
        
        for (const vuln of testVulnerabilities) {
            console.log(`\n🎯 اختبار التقاط صور للثغرة: ${vuln}`);
            
            for (const stage of stages) {
                totalAttempts++;
                console.log(`  📸 [${stage}] محاولة التقاط صورة...`);
                
                try {
                    const response = await this.makeDetailedRequest('/vulnerability_sequence', 'POST', {
                        url: 'http://testphp.vulnweb.com/',
                        vulnerability_name: vuln,
                        stage: stage,
                        page_identifier: 'test_page',
                        report_id: this.testId
                    });
                    
                    if (response.success) {
                        console.log(`  ✅ [${stage}] تم التقاط الصورة بنجاح`);
                        console.log(`  📊 البيانات: ${JSON.stringify(response.data, null, 2)}`);
                        successfulCaptures++;
                    } else {
                        console.log(`  ❌ [${stage}] فشل التقاط الصورة - الكود: ${response.status}`);
                        console.log(`  📄 الخطأ: ${response.raw}`);
                    }
                    
                } catch (error) {
                    console.log(`  ❌ [${stage}] خطأ في التقاط الصورة: ${error.message}`);
                }
                
                // تأخير بين الصور
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        const successRate = (successfulCaptures / totalAttempts) * 100;
        console.log(`\n📊 نتائج التقاط الصور:`);
        console.log(`   ✅ نجح: ${successfulCaptures}/${totalAttempts} (${successRate.toFixed(1)}%)`);
        
        if (successRate > 50) {
            console.log('✅ التقاط الصور يعمل بشكل جيد');
            this.testResults.screenshotCapture = true;
        } else {
            console.log('❌ مشكلة في التقاط الصور');
            this.testResults.screenshotCapture = false;
        }
        
        return successRate > 50;
    }

    // اختبار 4: فحص الملفات المُنشأة
    async testGeneratedFiles() {
        console.log('\n🔍 اختبار 4: فحص الملفات المُنشأة...');
        
        const screenshotsDir = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots');
        
        try {
            if (fs.existsSync(screenshotsDir)) {
                console.log(`✅ مجلد الصور موجود: ${screenshotsDir}`);
                
                // البحث عن ملفات الاختبار
                const files = fs.readdirSync(screenshotsDir, { recursive: true });
                const testFiles = files.filter(file => 
                    file.includes(this.testId) || 
                    file.includes('Test_') ||
                    file.includes('test_')
                );
                
                console.log(`📁 إجمالي الملفات: ${files.length}`);
                console.log(`🧪 ملفات الاختبار: ${testFiles.length}`);
                
                if (testFiles.length > 0) {
                    console.log('📋 ملفات الاختبار المُنشأة:');
                    testFiles.forEach(file => {
                        const filePath = path.join(screenshotsDir, file);
                        const stats = fs.statSync(filePath);
                        console.log(`   📄 ${file} (${stats.size} bytes)`);
                    });
                    
                    this.testResults.imageGeneration = true;
                    return true;
                } else {
                    console.log('⚠️ لم يتم إنشاء ملفات اختبار');
                    this.testResults.imageGeneration = false;
                    return false;
                }
                
            } else {
                console.log(`❌ مجلد الصور غير موجود: ${screenshotsDir}`);
                return false;
            }
            
        } catch (error) {
            console.log(`❌ خطأ في فحص الملفات: ${error.message}`);
            return false;
        }
    }

    // اختبار 5: فحص سجلات السيرفر Python
    async testServerLogs() {
        console.log('\n🔍 اختبار 5: فحص سجلات السيرفر Python...');
        
        // إرسال طلب اختبار ومراقبة الاستجابة
        try {
            console.log('📡 إرسال طلب اختبار للسيرفر...');
            
            const response = await this.makeDetailedRequest('/capture', 'POST', {
                url: 'http://testphp.vulnweb.com/',
                screenshot_id: `log_test_${this.testId}`,
                report_id: this.testId,
                debug: true
            });
            
            console.log('📋 تحليل استجابة السيرفر:');
            console.log(`   📊 الحالة: ${response.status}`);
            console.log(`   📦 حجم البيانات: ${response.raw.length} bytes`);
            console.log(`   ✅ نجح: ${response.success}`);
            
            if (response.data) {
                console.log('📄 محتوى الاستجابة:');
                console.log(JSON.stringify(response.data, null, 2));
            }
            
            return response.success;
            
        } catch (error) {
            console.log(`❌ خطأ في اختبار السجلات: ${error.message}`);
            return false;
        }
    }

    // تشغيل جميع الاختبارات
    async runAllTests() {
        console.log('🚀 بدء تشغيل جميع الاختبارات الشاملة...');
        console.log('=' .repeat(60));
        
        const tests = [
            { name: 'توفر السيرفر', func: () => this.testServerAvailability() },
            { name: 'فحص Endpoints', func: () => this.testAllEndpoints() },
            { name: 'التقاط الصور', func: () => this.testScreenshotCapture() },
            { name: 'الملفات المُنشأة', func: () => this.testGeneratedFiles() },
            { name: 'سجلات السيرفر', func: () => this.testServerLogs() }
        ];
        
        let passedTests = 0;
        
        for (const test of tests) {
            try {
                console.log(`\n${'='.repeat(50)}`);
                console.log(`🧪 تشغيل اختبار: ${test.name}`);
                console.log(`${'='.repeat(50)}`);
                
                const result = await test.func();
                if (result) {
                    passedTests++;
                    console.log(`\n✅ اختبار ${test.name}: نجح`);
                } else {
                    console.log(`\n❌ اختبار ${test.name}: فشل`);
                }
            } catch (error) {
                console.log(`\n❌ اختبار ${test.name}: خطأ - ${error.message}`);
            }
        }
        
        // النتيجة النهائية
        this.generateFinalReport(passedTests, tests.length);
        
        return passedTests === tests.length;
    }

    // إنشاء تقرير نهائي
    generateFinalReport(passed, total) {
        console.log('\n' + '='.repeat(60));
        console.log('📊 تقرير الاختبار الشامل النهائي');
        console.log('='.repeat(60));
        
        console.log(`🎯 النتيجة العامة: ${passed}/${total} اختبارات نجحت`);
        console.log(`📈 معدل النجاح: ${((passed / total) * 100).toFixed(1)}%`);
        
        console.log('\n📋 تفاصيل النتائج:');
        Object.entries(this.testResults).forEach(([test, result]) => {
            console.log(`${result ? '✅' : '❌'} ${test}`);
        });
        
        console.log('\n🔍 تحليل المشاكل:');
        if (!this.testResults.serverAvailable) {
            console.log('❌ السيرفر Python غير متاح - تحقق من تشغيله');
        }
        
        if (!this.testResults.screenshotCapture) {
            console.log('❌ مشكلة في التقاط الصور - تحقق من إعدادات السيرفر');
        }
        
        if (!this.testResults.imageGeneration) {
            console.log('❌ لا يتم إنشاء ملفات الصور - تحقق من مسارات الحفظ');
        }
        
        console.log('\n' + '='.repeat(60));
        
        if (passed === total) {
            console.log('🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي');
        } else if (passed >= total * 0.8) {
            console.log('⚠️ معظم الاختبارات نجحت - بعض التحسينات مطلوبة');
        } else {
            console.log('❌ عدة اختبارات فشلت - يحتاج مراجعة شاملة');
        }
    }
}

// تشغيل الاختبار
const tester = new ComprehensiveSystemTester();
tester.runAllTests().then(success => {
    console.log(`\n🏁 انتهاء الاختبار الشامل: ${success ? 'نجح' : 'فشل'}`);
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ خطأ في تشغيل الاختبارات:', error);
    process.exit(1);
});
