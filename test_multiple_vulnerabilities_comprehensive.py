#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لثغرات متنوعة مع السيرفر الحقيقي
"""

import requests
import json
import time
import os

def test_comprehensive_vulnerabilities():
    """اختبار شامل لثغرات متنوعة مع التحقق من قسم REAL SERVER RESPONSE"""
    
    print("🔥 اختبار شامل لثغرات متنوعة مع السيرفر الحقيقي...")
    print("🌐 السيرفر يجب أن يعمل على: http://localhost:8000")
    
    # التحقق من تشغيل السيرفر
    try:
        response = requests.get("http://localhost:8000", timeout=5)
        print("✅ السيرفر يعمل ويستجيب")
    except requests.exceptions.RequestException as e:
        print(f"❌ السيرفر لا يعمل: {e}")
        return
    
    # تعريف ثغرات متنوعة للاختبار
    vulnerabilities = [
        {
            "name": "SQL Injection - Error Based",
            "type": "SQL Injection",
            "url": "https://httpbin.org/get?id=1",
            "payload": "' OR 1=1 --",
            "description": "اختبار SQL Injection مع استجابة حقيقية"
        },
        {
            "name": "XSS - Reflected",
            "type": "Cross-Site Scripting",
            "url": "https://httpbin.org/get?search=test",
            "payload": "<script>alert('XSS')</script>",
            "description": "اختبار XSS مع استجابة حقيقية"
        },
        {
            "name": "Command Injection",
            "type": "Command Injection",
            "url": "https://httpbin.org/get?cmd=whoami",
            "payload": "; cat /etc/passwd",
            "description": "اختبار Command Injection مع استجابة حقيقية"
        },
        {
            "name": "Directory Traversal",
            "type": "Directory Traversal",
            "url": "https://httpbin.org/get?file=config.txt",
            "payload": "../../../etc/passwd",
            "description": "اختبار Directory Traversal مع استجابة حقيقية"
        },
        {
            "name": "LDAP Injection",
            "type": "LDAP Injection",
            "url": "https://httpbin.org/get?username=admin",
            "payload": "*)(uid=*))(|(uid=*",
            "description": "اختبار LDAP Injection مع استجابة حقيقية"
        },
        {
            "name": "XXE Injection",
            "type": "XXE Injection",
            "url": "https://httpbin.org/post",
            "payload": "<!DOCTYPE foo [<!ENTITY xxe SYSTEM 'file:///etc/passwd'>]>",
            "description": "اختبار XXE مع استجابة حقيقية"
        },
        {
            "name": "SSTI - Server Side Template Injection",
            "type": "SSTI",
            "url": "https://httpbin.org/get?template=hello",
            "payload": "{{7*7}}",
            "description": "اختبار SSTI مع استجابة حقيقية"
        },
        {
            "name": "NoSQL Injection",
            "type": "NoSQL Injection",
            "url": "https://httpbin.org/get?user=admin",
            "payload": "admin'||'1'=='1",
            "description": "اختبار NoSQL Injection مع استجابة حقيقية"
        }
    ]
    
    print(f"\n🎯 سيتم اختبار {len(vulnerabilities)} ثغرات متنوعة...")
    
    results = []
    
    for i, vuln in enumerate(vulnerabilities):
        print(f"\n📸 اختبار {i+1}/{len(vulnerabilities)}: {vuln['name']}")
        print(f"   🎯 النوع: {vuln['type']}")
        print(f"   💉 Payload: {vuln['payload']}")
        print(f"   🔗 URL: {vuln['url']}")
        print(f"   📝 الوصف: {vuln['description']}")
        
        test_data = {
            "url": vuln['url'],
            "vulnerability_name": vuln['name'],
            "vulnerability_type": vuln['type'],
            "payload": vuln['payload'],
            "report_id": f"comprehensive_test_{i+1}",
            "stage": "after"
        }
        
        try:
            print("   📤 إرسال طلب إلى السيرفر...")
            response = requests.post(
                "http://localhost:8000/capture",
                json=test_data,
                timeout=120  # مهلة أطول للثغرات المعقدة
            )
            
            print(f"   📥 استجابة السيرفر: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("   ✅ نجح الطلب!")
                
                if result.get('success'):
                    image_path = result.get('screenshot_path')
                    if image_path and os.path.exists(image_path):
                        file_size = os.path.getsize(image_path)
                        print(f"   📁 مسار الصورة: {os.path.basename(image_path)}")
                        print(f"   📊 حجم الملف: {file_size:,} bytes")
                        
                        results.append({
                            'vulnerability': vuln['name'],
                            'type': vuln['type'],
                            'success': True,
                            'image_path': image_path,
                            'file_size': file_size,
                            'payload': vuln['payload'],
                            'url': vuln['url']
                        })
                        
                        # حفظ نسخة مصغرة للفحص
                        try:
                            from PIL import Image
                            img = Image.open(image_path)
                            thumbnail_path = image_path.replace('.png', '_comprehensive_thumbnail.png')
                            img.thumbnail((1000, 800))
                            img.save(thumbnail_path)
                            print(f"   💾 نسخة مصغرة: {os.path.basename(thumbnail_path)}")
                        except Exception as e:
                            print(f"   ⚠️ خطأ في النسخة المصغرة: {e}")
                            
                    else:
                        print("   ❌ ملف الصورة غير موجود")
                        results.append({
                            'vulnerability': vuln['name'],
                            'type': vuln['type'],
                            'success': False,
                            'error': 'ملف الصورة غير موجود'
                        })
                else:
                    error_msg = result.get('error', 'خطأ غير محدد')
                    print(f"   ❌ فشل الطلب: {error_msg}")
                    results.append({
                        'vulnerability': vuln['name'],
                        'type': vuln['type'],
                        'success': False,
                        'error': error_msg
                    })
            else:
                print(f"   ❌ خطأ في السيرفر: {response.status_code}")
                print(f"   📝 الرسالة: {response.text[:200]}...")
                results.append({
                    'vulnerability': vuln['name'],
                    'type': vuln['type'],
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                })
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ خطأ في الاتصال: {e}")
            results.append({
                'vulnerability': vuln['name'],
                'type': vuln['type'],
                'success': False,
                'error': f'خطأ اتصال: {e}'
            })
        except Exception as e:
            print(f"   ❌ خطأ عام: {e}")
            results.append({
                'vulnerability': vuln['name'],
                'type': vuln['type'],
                'success': False,
                'error': f'خطأ عام: {e}'
            })
        
        # انتظار بين الاختبارات
        print("   ⏳ انتظار 5 ثواني...")
        time.sleep(5)
    
    # عرض النتائج النهائية
    print("\n" + "="*100)
    print("🎯 ملخص نتائج اختبار الثغرات المتنوعة:")
    print("="*100)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ اختبارات ناجحة: {len(successful_tests)}/{len(results)}")
    print(f"❌ اختبارات فاشلة: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        print("\n📸 الصور المُنشأة بنجاح:")
        total_size = 0
        
        for result in successful_tests:
            total_size += result['file_size']
            print(f"\n🔥 {result['vulnerability']}")
            print(f"   📁 الملف: {os.path.basename(result['image_path'])}")
            print(f"   📊 الحجم: {result['file_size']:,} bytes ({result['file_size']/1024:.1f} KB)")
            print(f"   🎯 النوع: {result['type']}")
            print(f"   💉 Payload: {result['payload'][:50]}...")
            print(f"   🔗 URL: {result['url']}")
        
        print(f"\n📊 إحصائيات إجمالية:")
        print(f"   📊 إجمالي حجم الصور: {total_size:,} bytes ({total_size/1024:.1f} KB)")
        print(f"   📊 متوسط حجم الصورة: {total_size//len(successful_tests):,} bytes")
    
    if failed_tests:
        print("\n❌ الاختبارات الفاشلة:")
        for result in failed_tests:
            print(f"   ❌ {result['vulnerability']}: {result.get('error', 'خطأ غير محدد')}")
    
    print("\n📂 مجلد الصور: assets/modules/bugbounty/screenshots/")
    print("🔍 افتح الصور للتحقق من:")
    print("   ✅ قسم REAL SERVER RESPONSE مع الاستجابة الحقيقية")
    print("   ✅ التأثيرات المختلفة حسب نوع الثغرة")
    print("   ✅ محتوى JSON/HTML الحقيقي من المواقع")
    print("   ✅ HTTP headers والاستجابات الكاملة")
    
    # فتح أول صورة ناجحة للفحص
    if successful_tests:
        first_image = successful_tests[0]['image_path']
        import webbrowser
        webbrowser.open(f"file:///{os.path.abspath(first_image)}")
        print(f"\n🌐 تم فتح أول صورة للفحص: {os.path.basename(first_image)}")
        print("🔍 تحقق من قسم REAL SERVER RESPONSE - هل يعرض الاستجابة الحقيقية؟")

if __name__ == "__main__":
    test_comprehensive_vulnerabilities()
