import psutil
import os

# الأقراص المستهدفة
target_drives = ["D:\\", "C:\\"]

# استثناء برنامج MiniTool من الإغلاق
EXCLUDED_PROCESSES = ["partitionwizard.exe", "explorer.exe", "python.exe"]

def uses_target_drive(proc):
    try:
        # فحص الملفات المفتوحة
        for f in proc.open_files():
            if any(f.path.startswith(drive) for drive in target_drives):
                return True
        # فحص الـ cwd
        if proc.cwd() and any(proc.cwd().startswith(drive) for drive in target_drives):
            return True
    except (psutil.AccessDenied, psutil.NoSuchProcess):
        pass
    return False

def main():
    print("🔍 جاري فحص العمليات التي تستخدم D أو C ...")
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            name = proc.info['name']
            pid = proc.info['pid']

            if name.lower() in EXCLUDED_PROCESSES:
                continue

            if uses_target_drive(proc):
                print(f"🛑 قتل العملية: {name} (PID: {pid})")
                proc.terminate()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

    print("\n✅ تم إنهاء العمليات التي تعيق القرص. حاول الآن تنفيذ عملية MiniTool دون إعادة تشغيل.")

if __name__ == "__main__":
    main()
