#!/usr/bin/env python3
"""
اختبار تشخيصي مفصل للتأثيرات البصرية
يحلل بدقة لماذا لا تظهر التأثيرات في الصور
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from screenshot_service import ScreenshotService

class VisualEffectsDiagnostic:
    def __init__(self):
        self.service = ScreenshotService()
        self.test_results = []
        
    async def run_visual_effects_diagnostic(self):
        """تشغيل اختبار تشخيصي مفصل للتأثيرات البصرية"""
        print("🔍 بدء الاختبار التشخيصي المفصل للتأثيرات البصرية")
        print("=" * 80)
        
        # تهيئة الخدمة
        await self.service.initialize_playwright()
        
        # اختبار 1: فحص التأثيرات قبل التقاط الصورة
        await self.test_effects_before_screenshot()
        
        # اختبار 2: فحص التأثيرات أثناء التقاط الصورة
        await self.test_effects_during_screenshot()
        
        # اختبار 3: فحص التوقيت والانتظار
        await self.test_timing_and_waiting()
        
        # اختبار 4: فحص JavaScript execution
        await self.test_javascript_execution()
        
        # اختبار 5: مقارنة الصور قبل وبعد
        await self.test_before_after_comparison()
        
        # تقرير النتائج
        await self.generate_visual_effects_report()
        
    async def test_effects_before_screenshot(self):
        """اختبار التأثيرات قبل التقاط الصورة"""
        print("\n🎭 اختبار 1: فحص التأثيرات قبل التقاط الصورة")
        
        try:
            page = await self.service.playwright_browser.new_page()
            await page.goto("https://httpbin.org/html")
            
            print("📍 تطبيق التأثيرات...")
            # تطبيق التأثيرات
            await self.service.apply_vulnerability_effects_async(
                page=page,
                vulnerability_name="Visual Test",
                stage="after",
                payload_data="<script>alert('test')</script>",
                vulnerability_type="XSS",
                v4_real_data={
                    'actual_response_content': 'Test response data',
                    'vulnerability_impact_data': 'High impact test',
                    'exploitation_results': ['Test result 1', 'Test result 2']
                }
            )
            
            print("⏳ انتظار 5 ثوانٍ...")
            await asyncio.sleep(5)
            
            print("🔍 فحص التأثيرات المطبقة...")
            # فحص التأثيرات
            effects_status = await page.evaluate("""
                () => {
                    const body = document.body;
                    const computedStyle = window.getComputedStyle(body);
                    
                    // فحص العناصر المضافة
                    const hackElements = document.querySelectorAll('[data-hack-element]');
                    const exploitContainers = document.querySelectorAll('[data-exploit-container]');
                    
                    // فحص الخصائص المطبقة
                    return {
                        // خصائص CSS
                        backgroundColor: computedStyle.backgroundColor,
                        background: computedStyle.background,
                        border: computedStyle.border,
                        borderColor: computedStyle.borderColor,
                        borderWidth: computedStyle.borderWidth,
                        
                        // العناصر المضافة
                        hackElementsCount: hackElements.length,
                        exploitContainersCount: exploitContainers.length,
                        
                        // خصائص الصفحة
                        documentTitle: document.title,
                        bodyInnerHTML: document.body.innerHTML.length,
                        
                        // فحص العناصر المحددة
                        hackElementsDetails: Array.from(hackElements).map(el => ({
                            tagName: el.tagName,
                            id: el.id,
                            className: el.className,
                            innerHTML: el.innerHTML.substring(0, 100),
                            style: el.style.cssText
                        })),
                        
                        // فحص الـ inline styles
                        bodyStyleAttribute: body.getAttribute('style'),
                        bodyStyleCssText: body.style.cssText,
                        
                        // معلومات إضافية
                        viewport: {
                            width: window.innerWidth,
                            height: window.innerHeight
                        }
                    };
                }
            """)
            
            print(f"📊 نتائج فحص التأثيرات:")
            print(f"   🎨 Background: {effects_status['background'][:100]}...")
            print(f"   🔲 Border: {effects_status['border']}")
            print(f"   📝 Title: {effects_status['documentTitle']}")
            print(f"   🏷️ Hack Elements: {effects_status['hackElementsCount']}")
            print(f"   📦 Exploit Containers: {effects_status['exploitContainersCount']}")
            print(f"   💾 Body Style: {effects_status['bodyStyleCssText']}")
            
            # حفظ النتائج
            self.test_results.append({
                "test": "effects_before_screenshot",
                "status": "success",
                "effects_status": effects_status,
                "has_visual_changes": (
                    effects_status['hackElementsCount'] > 0 or
                    'red' in effects_status['background'].lower() or
                    'red' in effects_status['border'].lower() or
                    'hacked' in effects_status['documentTitle'].lower()
                )
            })
            
            await page.close()
            
        except Exception as e:
            print(f"❌ خطأ في اختبار التأثيرات: {e}")
            self.test_results.append({
                "test": "effects_before_screenshot",
                "status": "failed",
                "error": str(e)
            })
            
    async def test_effects_during_screenshot(self):
        """اختبار التأثيرات أثناء التقاط الصورة"""
        print("\n📸 اختبار 2: فحص التأثيرات أثناء التقاط الصورة")
        
        try:
            # التقاط صورة مع التأثيرات
            result = await self.service.capture_with_playwright(
                url="https://httpbin.org/html",
                filename="visual_diagnostic_test",
                stage="after",
                report_id="visual_diagnostic",
                vulnerability_name="Visual Diagnostic Test",
                payload_data="<script>alert('visual test')</script>",
                vulnerability_type="XSS",
                v4_real_data={
                    'actual_response_content': 'Visual diagnostic test response',
                    'vulnerability_impact_data': 'Critical visual impact',
                    'exploitation_results': ['Visual test 1', 'Visual test 2']
                }
            )
            
            if result and result.get('success'):
                print(f"✅ تم التقاط الصورة: {result['path']}")
                print(f"📊 حجم الملف: {result['file_size']:,} بايت")
                
                # فحص حجم الملف (الصور مع التأثيرات عادة أكبر)
                file_size_analysis = "normal"
                if result['file_size'] > 50000:
                    file_size_analysis = "large (likely has effects)"
                elif result['file_size'] < 20000:
                    file_size_analysis = "small (likely no effects)"
                
                print(f"📈 تحليل حجم الملف: {file_size_analysis}")
                
                self.test_results.append({
                    "test": "effects_during_screenshot",
                    "status": "success",
                    "file_size": result['file_size'],
                    "file_size_analysis": file_size_analysis,
                    "screenshot_path": result['path']
                })
            else:
                print("❌ فشل في التقاط الصورة")
                self.test_results.append({
                    "test": "effects_during_screenshot",
                    "status": "failed",
                    "error": "Screenshot capture failed"
                })
                
        except Exception as e:
            print(f"❌ خطأ في التقاط الصورة: {e}")
            self.test_results.append({
                "test": "effects_during_screenshot",
                "status": "failed",
                "error": str(e)
            })
            
    async def test_timing_and_waiting(self):
        """اختبار التوقيت والانتظار"""
        print("\n⏰ اختبار 3: فحص التوقيت والانتظار")
        
        try:
            page = await self.service.playwright_browser.new_page()
            await page.goto("https://httpbin.org/html")
            
            # اختبار أوقات انتظار مختلفة
            wait_times = [1, 3, 5, 10]
            timing_results = []
            
            for wait_time in wait_times:
                print(f"🕐 اختبار انتظار {wait_time} ثانية...")
                
                # تطبيق التأثيرات
                await page.evaluate("""
                    // تطبيق تأثيرات بسيطة
                    document.body.style.background = 'linear-gradient(45deg, red, darkred)';
                    document.body.style.border = '10px solid yellow';
                    document.title = 'HACKED - TIMING TEST';
                    
                    // إضافة عنصر مرئي
                    const banner = document.createElement('div');
                    banner.id = 'timing-test-banner';
                    banner.innerHTML = 'TIMING TEST BANNER';
                    banner.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; background: red; color: white; text-align: center; font-size: 24px; padding: 20px; z-index: 999999;';
                    document.body.appendChild(banner);
                """)
                
                # انتظار
                await asyncio.sleep(wait_time)
                
                # فحص التأثيرات
                effects_check = await page.evaluate("""
                    () => {
                        const banner = document.getElementById('timing-test-banner');
                        const bodyStyle = window.getComputedStyle(document.body);
                        return {
                            bannerExists: banner ? true : false,
                            bannerVisible: banner ? banner.offsetHeight > 0 : false,
                            bodyBackground: bodyStyle.background,
                            bodyBorder: bodyStyle.border,
                            documentTitle: document.title
                        };
                    }
                """)
                
                timing_results.append({
                    "wait_time": wait_time,
                    "effects_applied": effects_check['bannerExists'],
                    "effects_visible": effects_check['bannerVisible'],
                    "background_changed": 'red' in effects_check['bodyBackground'].lower(),
                    "border_changed": 'yellow' in effects_check['bodyBorder'].lower(),
                    "title_changed": 'hacked' in effects_check['documentTitle'].lower()
                })
                
                print(f"   📊 النتائج: Banner={effects_check['bannerExists']}, Visible={effects_check['bannerVisible']}")
                
            print(f"📈 تحليل التوقيت:")
            for result in timing_results:
                status = "✅" if result['effects_applied'] and result['effects_visible'] else "❌"
                print(f"   {status} {result['wait_time']}s: Applied={result['effects_applied']}, Visible={result['effects_visible']}")
                
            self.test_results.append({
                "test": "timing_and_waiting",
                "status": "success",
                "timing_results": timing_results
            })
            
            await page.close()
            
        except Exception as e:
            print(f"❌ خطأ في اختبار التوقيت: {e}")
            self.test_results.append({
                "test": "timing_and_waiting",
                "status": "failed",
                "error": str(e)
            })
            
    async def test_javascript_execution(self):
        """اختبار تنفيذ JavaScript"""
        print("\n🔧 اختبار 4: فحص تنفيذ JavaScript")
        
        try:
            page = await self.service.playwright_browser.new_page()
            await page.goto("https://httpbin.org/html")
            
            # اختبار تنفيذ JavaScript بسيط
            print("🔍 اختبار JavaScript بسيط...")
            simple_test = await page.evaluate("""
                () => {
                    try {
                        document.body.style.backgroundColor = 'red';
                        return { success: true, error: null };
                    } catch (e) {
                        return { success: false, error: e.message };
                    }
                }
            """)
            
            print(f"   📊 JavaScript بسيط: {simple_test}")
            
            # اختبار تنفيذ JavaScript معقد
            print("🔍 اختبار JavaScript معقد...")
            complex_test = await page.evaluate("""
                () => {
                    try {
                        // تطبيق تأثيرات معقدة
                        document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
                        document.body.style.border = '10px solid red';
                        document.title = 'HACKED - COMPLEX TEST';
                        
                        // إضافة عناصر
                        const banner = document.createElement('div');
                        banner.innerHTML = 'COMPLEX TEST BANNER';
                        banner.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; background: red; color: white; text-align: center; font-size: 24px; padding: 20px; z-index: 999999;';
                        document.body.appendChild(banner);
                        
                        return { 
                            success: true, 
                            error: null,
                            elementsAdded: document.querySelectorAll('div').length,
                            bodyStyle: document.body.style.cssText
                        };
                    } catch (e) {
                        return { success: false, error: e.message };
                    }
                }
            """)
            
            print(f"   📊 JavaScript معقد: {complex_test}")
            
            self.test_results.append({
                "test": "javascript_execution",
                "status": "success",
                "simple_test": simple_test,
                "complex_test": complex_test
            })
            
            await page.close()
            
        except Exception as e:
            print(f"❌ خطأ في اختبار JavaScript: {e}")
            self.test_results.append({
                "test": "javascript_execution",
                "status": "failed",
                "error": str(e)
            })
            
    async def test_before_after_comparison(self):
        """اختبار مقارنة الصور قبل وبعد"""
        print("\n🔄 اختبار 5: مقارنة الصور قبل وبعد")
        
        try:
            # التقاط صورة قبل
            print("📸 التقاط صورة قبل التأثيرات...")
            before_result = await self.service.capture_with_playwright(
                url="https://httpbin.org/html",
                filename="comparison_before",
                stage="before",
                report_id="comparison_test"
            )
            
            # التقاط صورة بعد
            print("📸 التقاط صورة بعد التأثيرات...")
            after_result = await self.service.capture_with_playwright(
                url="https://httpbin.org/html",
                filename="comparison_after",
                stage="after",
                report_id="comparison_test",
                vulnerability_name="Comparison Test",
                payload_data="<script>alert('comparison')</script>",
                vulnerability_type="XSS",
                v4_real_data={
                    'actual_response_content': 'Comparison test data',
                    'vulnerability_impact_data': 'High impact comparison',
                    'exploitation_results': ['Comparison result 1']
                }
            )
            
            if before_result and after_result:
                size_difference = after_result['file_size'] - before_result['file_size']
                size_change_percent = (size_difference / before_result['file_size']) * 100
                
                print(f"📊 مقارنة الأحجام:")
                print(f"   📸 قبل: {before_result['file_size']:,} بايت")
                print(f"   📸 بعد: {after_result['file_size']:,} بايت")
                print(f"   📈 الفرق: {size_difference:,} بايت ({size_change_percent:.1f}%)")
                
                # تحليل النتائج
                has_significant_change = abs(size_change_percent) > 5  # تغيير أكثر من 5%
                
                self.test_results.append({
                    "test": "before_after_comparison",
                    "status": "success",
                    "before_size": before_result['file_size'],
                    "after_size": after_result['file_size'],
                    "size_difference": size_difference,
                    "size_change_percent": size_change_percent,
                    "has_significant_change": has_significant_change,
                    "before_path": before_result['path'],
                    "after_path": after_result['path']
                })
                
                if has_significant_change:
                    print("✅ تم اكتشاف تغيير كبير في حجم الصورة")
                else:
                    print("⚠️ لم يتم اكتشاف تغيير كبير في حجم الصورة")
            else:
                print("❌ فشل في التقاط إحدى الصور")
                self.test_results.append({
                    "test": "before_after_comparison",
                    "status": "failed",
                    "error": "Failed to capture one or both images"
                })
                
        except Exception as e:
            print(f"❌ خطأ في مقارنة الصور: {e}")
            self.test_results.append({
                "test": "before_after_comparison",
                "status": "failed",
                "error": str(e)
            })
            
    async def generate_visual_effects_report(self):
        """إنشاء تقرير مفصل للتأثيرات البصرية"""
        print("\n" + "=" * 80)
        print("📋 تقرير التشخيص المفصل للتأثيرات البصرية")
        print("=" * 80)
        
        # تحليل النتائج
        total_tests = len(self.test_results)
        successful_tests = len([t for t in self.test_results if t['status'] == 'success'])
        
        print(f"📊 إجمالي الاختبارات: {total_tests}")
        print(f"✅ نجحت: {successful_tests}")
        print(f"❌ فشلت: {total_tests - successful_tests}")
        
        # تحليل مفصل لكل اختبار
        print(f"\n🔍 تحليل مفصل:")
        
        for result in self.test_results:
            test_name = result['test']
            status = result['status']
            
            print(f"\n📋 {test_name}:")
            print(f"   حالة: {'✅ نجح' if status == 'success' else '❌ فشل'}")
            
            if status == 'success':
                if test_name == 'effects_before_screenshot':
                    effects = result.get('effects_status', {})
                    has_changes = result.get('has_visual_changes', False)
                    print(f"   تأثيرات مرئية: {'✅ موجودة' if has_changes else '❌ غير موجودة'}")
                    print(f"   عناصر hack: {effects.get('hackElementsCount', 0)}")
                    print(f"   حاويات exploit: {effects.get('exploitContainersCount', 0)}")
                    
                elif test_name == 'before_after_comparison':
                    has_change = result.get('has_significant_change', False)
                    size_change = result.get('size_change_percent', 0)
                    print(f"   تغيير كبير: {'✅ نعم' if has_change else '❌ لا'}")
                    print(f"   نسبة التغيير: {size_change:.1f}%")
                    
            else:
                print(f"   خطأ: {result.get('error', 'Unknown error')}")
        
        # توصيات
        print(f"\n💡 التوصيات:")
        
        # فحص إذا كانت التأثيرات تطبق ولكن لا تظهر في الصور
        effects_test = next((t for t in self.test_results if t['test'] == 'effects_before_screenshot'), None)
        comparison_test = next((t for t in self.test_results if t['test'] == 'before_after_comparison'), None)
        
        if effects_test and effects_test.get('has_visual_changes'):
            print("✅ التأثيرات تطبق بنجاح في المتصفح")
            
            if comparison_test and not comparison_test.get('has_significant_change'):
                print("⚠️ المشكلة: التأثيرات لا تظهر في الصور المحفوظة")
                print("🔧 الحلول المقترحة:")
                print("   1. زيادة وقت الانتظار قبل التقاط الصورة")
                print("   2. التأكد من تطبيق التأثيرات قبل التقاط الصورة مباشرة")
                print("   3. فحص إعدادات Playwright للتقاط الصور")
                print("   4. استخدام full_page=True في screenshot")
            else:
                print("✅ التأثيرات تظهر في الصور بنجاح")
        else:
            print("❌ المشكلة: التأثيرات لا تطبق في المتصفح")
            print("🔧 الحلول المقترحة:")
            print("   1. فحص أخطاء JavaScript")
            print("   2. التأكد من تنفيذ الكود في الوقت المناسب")
            print("   3. فحص selectors المستخدمة")
        
        # حفظ التقرير
        report_data = {
            'timestamp': time.time(),
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'test_results': self.test_results
        }
        
        report_path = Path("visual_effects_diagnostic_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
            
        print(f"\n💾 تم حفظ التقرير المفصل في: {report_path}")
        
        # إغلاق الخدمة
        try:
            await self.service.cleanup()
            print("🔒 تم إغلاق الخدمة بنجاح")
        except:
            pass

async def main():
    """تشغيل الاختبار التشخيصي للتأثيرات البصرية"""
    diagnostic = VisualEffectsDiagnostic()
    await diagnostic.run_visual_effects_diagnostic()

if __name__ == "__main__":
    print("🚀 بدء الاختبار التشخيصي المفصل للتأثيرات البصرية")
    asyncio.run(main())
    print("✅ انتهى الاختبار التشخيصي")
