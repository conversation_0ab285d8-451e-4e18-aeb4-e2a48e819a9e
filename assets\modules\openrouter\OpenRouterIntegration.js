/**
 * OpenRouter Smart Integration
 * نظام التكامل الذكي مع المساعد التقني
 */

class OpenRouterIntegration {
    constructor() {
        this.isEnabled = false;
        this.fallbackToLocal = true;
        this.integrationMode = 'smart'; // smart, force, disabled
        
        this.init();
        console.log('🔗 تم تهيئة نظام التكامل الذكي مع OpenRouter');
    }

    // تهيئة النظام
    init() {
        // التحقق من حالة الاتصال عند بدء التشغيل
        this.checkConnectionStatus();
        
        // مراقبة تغييرات الاتصال
        this.setupConnectionMonitoring();
        
        // إضافة زر الإعدادات للواجهة
        this.addSettingsButton();
    }

    // التحقق من حالة الاتصال
    checkConnectionStatus() {
        const status = window.openRouterManager.getConnectionStatus();

        if (status.isConnected && status.selectedModel) {
            this.isEnabled = true;
            console.log('✅ OpenRouter متاح ومُفعل - النموذج:', status.selectedModel);
            console.log('🔗 سيتم استخدام OpenRouter في جميع الميزات');
            this.showConnectionIndicator(true);

            // إشعار في الواجهة
            this.showNotification('🔗 OpenRouter متصل ومُفعل!', 'success');
        } else {
            this.isEnabled = false;
            console.log('⚪ OpenRouter غير متاح، استخدام النظام المحلي');
            this.showConnectionIndicator(false);
        }
    }

    // مراقبة تغييرات الاتصال
    setupConnectionMonitoring() {
        // مراقبة تغييرات التخزين المحلي
        window.addEventListener('storage', (e) => {
            if (e.key === 'openrouter_config') {
                this.checkConnectionStatus();
            }
        });

        // فحص دوري كل 30 ثانية
        setInterval(() => {
            this.checkConnectionStatus();
        }, 30000);
    }

    // إضافة زر الإعدادات
    addSettingsButton() {
        // البحث عن منطقة الإعدادات في الواجهة
        const settingsArea = document.querySelector('.settings-panel') || 
                            document.querySelector('.control-panel') ||
                            document.querySelector('#settings-container');

        if (settingsArea) {
            this.insertSettingsButton(settingsArea);
        } else {
            // إنشاء زر عائم إذا لم توجد منطقة إعدادات
            this.createFloatingButton();
        }
    }

    // إدراج زر الإعدادات في المنطقة المحددة
    insertSettingsButton(container) {
        const button = document.createElement('button');
        button.id = 'openrouter-settings-btn';
        button.innerHTML = `
            <i class="fas fa-plug"></i>
            <span>OpenRouter.ai</span>
            <span id="openrouter-status-indicator" class="status-indicator"></span>
        `;
        button.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; border: none; padding: 12px 20px; border-radius: 25px;
            cursor: pointer; font-size: 14px; margin: 10px 5px;
            transition: all 0.3s ease; position: relative;
            display: flex; align-items: center; gap: 8px;
        `;

        // إضافة مؤشر الحالة
        const indicator = button.querySelector('#openrouter-status-indicator');
        indicator.style.cssText = `
            width: 8px; height: 8px; border-radius: 50%;
            background: #95a5a6; position: absolute; top: 5px; right: 5px;
        `;

        button.onmouseover = () => {
            button.style.transform = 'translateY(-2px)';
            button.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';
        };
        button.onmouseout = () => {
            button.style.transform = 'translateY(0)';
            button.style.boxShadow = 'none';
        };

        button.onclick = () => {
            window.openRouterInterface.show();
        };

        container.appendChild(button);
        console.log('🔘 تم إضافة زر إعدادات OpenRouter');
    }

    // إنشاء زر عائم
    createFloatingButton() {
        const button = document.createElement('button');
        button.id = 'openrouter-floating-btn';
        button.innerHTML = `
            <i class="fas fa-plug"></i>
            <span id="openrouter-floating-status" class="floating-status"></span>
        `;
        button.style.cssText = `
            position: fixed; bottom: 20px; left: 20px; z-index: 9999;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; border: none; width: 60px; height: 60px;
            border-radius: 50%; cursor: pointer; font-size: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease; display: flex;
            align-items: center; justify-content: center;
        `;

        // مؤشر الحالة للزر العائم
        const indicator = button.querySelector('#openrouter-floating-status');
        indicator.style.cssText = `
            position: absolute; top: 5px; right: 5px;
            width: 12px; height: 12px; border-radius: 50%;
            background: #95a5a6; border: 2px solid white;
        `;

        button.onmouseover = () => {
            button.style.transform = 'scale(1.1)';
        };
        button.onmouseout = () => {
            button.style.transform = 'scale(1)';
        };

        button.onclick = () => {
            window.openRouterInterface.show();
        };

        document.body.appendChild(button);
        console.log('🎈 تم إنشاء زر OpenRouter العائم');
    }

    // عرض مؤشر حالة الاتصال
    showConnectionIndicator(isConnected) {
        const indicators = document.querySelectorAll('#openrouter-status-indicator, #openrouter-floating-status');

        indicators.forEach(indicator => {
            if (isConnected) {
                indicator.style.background = '#2ecc71';
                indicator.title = 'متصل بـ OpenRouter - يتم استخدامه في جميع الميزات';
            } else {
                indicator.style.background = '#95a5a6';
                indicator.title = 'غير متصل - يتم استخدام النظام المحلي';
            }
        });

        // إضافة إشعار في وحدة التحكم
        if (isConnected) {
            console.log('🔗 OpenRouter متكامل مع جميع ميزات المساعد:');
            console.log('  ✅ المحادثة النصية');
            console.log('  ✅ المحادثة الصوتية');
            console.log('  ✅ تحليل الفيديو');
            console.log('  ✅ الترجمة');
            console.log('  ✅ إنشاء الملفات');
            console.log('  ✅ Bug Bounty Mode');
            console.log('  ✅ جميع الأوامر الصوتية');
        }
    }

    // التكامل الذكي مع دالة الإرسال
    async smartSendMessage(message, options = {}) {
        console.log('🔗 smartSendMessage تم استدعاؤها:', {
            message: message.substring(0, 50),
            isEnabled: this.isEnabled,
            integrationMode: this.integrationMode,
            hasOpenRouterManager: !!window.openRouterManager,
            selectedModel: window.openRouterManager?.selectedModel
        });

        // التحقق من حالة OpenRouter
        if (this.isEnabled && this.integrationMode !== 'disabled') {
            try {
                console.log('🔗 استخدام OpenRouter للرد');
                console.log('📤 إرسال رسالة إلى OpenRouter:', message.substring(0, 100));

                // إرسال الرسالة عبر OpenRouter مع تمرير الوضع
                const response = await window.openRouterManager.sendMessage(message, {
                    temperature: options.temperature || 0.7,
                    maxTokens: options.maxTokens || 1000,
                    topP: options.topP || 0.9,
                    mode: options.mode || 'general' // تمرير الوضع للنموذج
                });

                console.log('✅ تم استلام رد من OpenRouter:', response.text?.substring(0, 100));

                // إضافة معلومات إضافية للرد
                response.source = 'openrouter';
                response.model = window.openRouterManager.selectedModel;

                return response;

            } catch (error) {
                console.error('❌ خطأ في OpenRouter:', error);
                console.warn('⚠️ التبديل للنظام المحلي');

                // إذا كان الوضع "force"، إرجاع الخطأ
                if (this.integrationMode === 'force') {
                    throw error;
                }

                // وإلا، استخدام النظام المحلي كبديل
                return this.fallbackToLocalSystem(message, options);
            }
        } else {
            console.log('🏠 استخدام النظام المحلي (OpenRouter معطل أو غير متصل)');
            // استخدام النظام المحلي
            return this.fallbackToLocalSystem(message, options);
        }
    }

    // العودة للنظام المحلي
    async fallbackToLocalSystem(message, options) {
        console.log('🏠 استخدام النظام المحلي');
        
        // استدعاء النظام المحلي الأصلي
        if (window.localAIManager && typeof window.localAIManager.sendMessage === 'function') {
            const response = await window.localAIManager.sendMessage(message, options);
            response.source = 'local';
            return response;
        } else {
            // استخدام النموذج المتكامل الأساسي كبديل
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام technicalAssistant كبديل...');
                try {
                    const response = await technicalAssistant.getResponse(message);
                    return {
                        text: response || 'أهلين! شلون أقدر أساعدك؟',
                        source: 'technicalAssistant',
                        timestamp: Date.now()
                    };
                } catch (error) {
                    console.error('❌ خطأ في technicalAssistant:', error);
                }
            }

            // رد ذكي افتراضي بدلاً من رسالة الخطأ
            return {
                text: `أهلين حبيبي! فهمت كلامك "${message}". أنا هنا لمساعدتك بأي شيء تحتاجه. شنو تريد مني أسوي لك؟`,
                source: 'smart_fallback',
                timestamp: Date.now()
            };
        }
    }

    // تغيير وضع التكامل
    setIntegrationMode(mode) {
        const validModes = ['smart', 'force', 'disabled'];
        
        if (!validModes.includes(mode)) {
            throw new Error('وضع تكامل غير صالح');
        }

        this.integrationMode = mode;
        console.log(`🔧 تم تغيير وضع التكامل إلى: ${mode}`);

        // حفظ الإعداد
        localStorage.setItem('openrouter_integration_mode', mode);
    }

    // الحصول على معلومات التكامل
    getIntegrationInfo() {
        const status = window.openRouterManager.getConnectionStatus();
        
        return {
            isEnabled: this.isEnabled,
            integrationMode: this.integrationMode,
            fallbackToLocal: this.fallbackToLocal,
            openRouterStatus: status,
            currentModel: status.selectedModel,
            modelsCount: status.modelsCount
        };
    }

    // تفعيل/إلغاء تفعيل التكامل
    toggleIntegration() {
        if (this.integrationMode === 'disabled') {
            this.setIntegrationMode('smart');
        } else {
            this.setIntegrationMode('disabled');
        }
        
        this.checkConnectionStatus();
        return this.integrationMode;
    }

    // اختبار التكامل
    async testIntegration() {
        try {
            const testMessage = 'مرحبا، هذا اختبار للتكامل الذكي';
            const response = await this.smartSendMessage(testMessage);

            return {
                success: true,
                source: response.source,
                model: response.model,
                responseLength: response.text.length,
                message: 'تم اختبار التكامل بنجاح'
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'فشل اختبار التكامل'
            };
        }
    }

    // عرض إشعار
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 10001;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            animation: slideIn 0.5s ease;
        `;

        const colors = {
            success: '#2ecc71',
            error: '#e74c3c',
            warning: '#f39c12',
            info: '#3498db'
        };

        notification.style.background = colors[type] || colors.info;
        notification.textContent = message;

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }
}

// إنشاء مثيل عام
window.openRouterIntegration = new OpenRouterIntegration();

// ربط التكامل مع النظام الحالي
if (window.localAIManager) {
    // حفظ الدالة الأصلية
    window.localAIManager._originalSendMessage = window.localAIManager.sendMessage;
    
    // استبدال الدالة بالنسخة الذكية
    window.localAIManager.sendMessage = async function(message, options) {
        return await window.openRouterIntegration.smartSendMessage(message, options);
    };
    
    console.log('🔗 تم ربط OpenRouter مع النظام المحلي بنجاح');
}

console.log('🚀 تم تحميل نظام التكامل الذكي مع OpenRouter بنجاح');
