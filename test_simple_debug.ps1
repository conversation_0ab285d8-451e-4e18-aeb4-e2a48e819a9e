# Simple test for data flow from v4 system to Python server
Write-Host "Testing data flow..." -ForegroundColor Green

# Test 1: Basic data reception
Write-Host "`nTest 1: Basic data reception" -ForegroundColor Yellow

$testData = @{
    url = "http://testphp.vulnweb.com"
    vulnerability_name = "Test_SQL_Injection"
    vulnerability_type = "SQL_Injection"
    stage = "after"
    payload_data = "' OR '1'='1' -- test123"
    target_parameter = "id"
    report_id = "debug_test_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
} | ConvertTo-Json

Write-Host "Sending test data:" -ForegroundColor Cyan
Write-Host $testData -ForegroundColor White

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/vulnerability_sequence" -Method POST -Body $testData -ContentType "application/json" -TimeoutSec 60
    Write-Host "Python server response:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor White
} catch {
    Write-Host "Error connecting to Python server: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Multiple vulnerability types
Write-Host "`nTest 2: Multiple vulnerability types" -ForegroundColor Yellow

$vulnerabilities = @(
    @{
        name = "SQL_Injection_Login"
        type = "SQL_Injection"
        payload = "admin' OR '1'='1' -- hack123"
        param = "username"
    },
    @{
        name = "XSS_Search_Field"
        type = "Cross_Site_Scripting"
        payload = "<script>alert('XSS_test456')</script>"
        param = "search"
    },
    @{
        name = "LFI_File_Include"
        type = "Local_File_Inclusion"
        payload = "../../../etc/passwd?test789"
        param = "file"
    }
)

foreach ($vuln in $vulnerabilities) {
    Write-Host "`nTesting vulnerability: $($vuln.name)" -ForegroundColor Cyan
    
    $vulnData = @{
        url = "http://testphp.vulnweb.com"
        vulnerability_name = $vuln.name
        vulnerability_type = $vuln.type
        stage = "after"
        payload_data = $vuln.payload
        target_parameter = $vuln.param
        report_id = "debug_$($vuln.name)_$(Get-Date -Format 'HHmmss')"
    } | ConvertTo-Json
    
    Write-Host "Payload: $($vuln.payload)" -ForegroundColor White
    Write-Host "Parameter: $($vuln.param)" -ForegroundColor White
    
    try {
        $vulnResponse = Invoke-RestMethod -Uri "http://localhost:8000/vulnerability_sequence" -Method POST -Body $vulnData -ContentType "application/json" -TimeoutSec 30
        
        if ($vulnResponse.success) {
            Write-Host "SUCCESS: $($vulnResponse.message)" -ForegroundColor Green
            if ($vulnResponse.payload_used) {
                Write-Host "Payload used: $($vulnResponse.payload_used)" -ForegroundColor Yellow
                
                # Check if payload is unique
                if ($vulnResponse.payload_used -eq $vuln.payload) {
                    Write-Host "Payload matches sent data" -ForegroundColor Green
                } else {
                    Write-Host "Payload differs from sent data!" -ForegroundColor Red
                    Write-Host "Sent: $($vuln.payload)" -ForegroundColor White
                    Write-Host "Received: $($vulnResponse.payload_used)" -ForegroundColor White
                }
            } else {
                Write-Host "No payload in response" -ForegroundColor Red
            }
        } else {
            Write-Host "FAILED: $($vulnResponse.error)" -ForegroundColor Red
        }
    } catch {
        Write-Host "Request error: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 2
}

# Test 3: Check generated images
Write-Host "`nTest 3: Check generated images" -ForegroundColor Yellow

$screenshotsPath = "assets\modules\bugbounty\screenshots"
if (Test-Path $screenshotsPath) {
    $recentImages = Get-ChildItem -Path $screenshotsPath -Recurse -Filter "*.png" | Sort-Object LastWriteTime -Descending | Select-Object -First 5
    
    Write-Host "Recent images:" -ForegroundColor Cyan
    foreach ($img in $recentImages) {
        $size = [math]::Round($img.Length / 1KB, 2)
        Write-Host "  $($img.Name) - ${size}KB - $($img.LastWriteTime)" -ForegroundColor White
    }
} else {
    Write-Host "Screenshots folder not found: $screenshotsPath" -ForegroundColor Red
}

# Summary
Write-Host "`nTest Summary:" -ForegroundColor Green
Write-Host "- Tested data flow from PowerShell to Python server" -ForegroundColor White
Write-Host "- Tested multiple vulnerability types" -ForegroundColor White
Write-Host "- Checked generated images" -ForegroundColor White
Write-Host "`nCheck Python server terminal for detailed logs" -ForegroundColor Yellow

Write-Host "`nData flow test completed!" -ForegroundColor Green
