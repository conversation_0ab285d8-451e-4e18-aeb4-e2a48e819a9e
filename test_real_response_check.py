#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار للتحقق من عرض الاستجابة الحقيقية في قسم REAL SERVER RESPONSE
"""

import sys
import asyncio
import os
import webbrowser

# إضافة المسار للوحدات
sys.path.append('.')

from assets.modules.bugbounty.screenshot_service import ScreenshotService

async def test_real_response_display():
    """اختبار عرض الاستجابة الحقيقية في قسم REAL SERVER RESPONSE"""
    
    print("🔥 اختبار عرض الاستجابة الحقيقية...")
    print("🔍 سنتحقق من ظهور البيانات الحقيقية في قسم REAL SERVER RESPONSE")
    
    service = ScreenshotService()
    
    try:
        # اختبار مع بيانات حقيقية واضحة
        print("\n📸 التقاط صورة مع بيانات حقيقية من النظام v4...")
        
        # بيانات حقيقية واضحة للاختبار
        real_server_data = {
            'actual_response_content': '''🔥 هذا اختبار للاستجابة الحقيقية من النظام v4 🔥

HTTP/1.1 200 OK
Content-Type: application/json
Server: nginx/1.18.0
Content-Length: 256

{
  "message": "تم العثور على ثغرة أمنية",
  "vulnerability_type": "Information Disclosure",
  "severity": "High",
  "payload_executed": "debug=true",
  "server_response": "تم كشف معلومات حساسة",
  "exploitation_status": "successful",
  "data_exposed": {
    "database_version": "MySQL 8.0.28",
    "server_info": "Ubuntu 20.04.3 LTS",
    "application_path": "/var/www/html",
    "debug_mode": "enabled"
  }
}

🔥 تأكيد الثغرة: تم كشف معلومات النظام بنجاح
🔥 مستوى الخطر: عالي
🔥 حالة الاستغلال: ناجح''',
            'response_data': 'بيانات الاستجابة الحقيقية من النظام v4',
            'full_response_content': 'محتوى الاستجابة الكامل مع تفاصيل الثغرة'
        }
        
        result = await service.capture_with_playwright(
            url='https://httpbin.org/json',
            filename='real_response_display_test',
            stage='after',
            report_id='real_response_check',
            vulnerability_name='Information Disclosure Test',
            payload_data='debug=true',
            vulnerability_type='Information Disclosure',
            v4_data={
                'response': 'Information disclosure detected',
                'server_response': 'Debug information exposed'
            },
            v4_real_data=real_server_data
        )
        
        if result and result.get('success'):
            image_path = result.get('path')
            file_size = result.get('file_size', 0)
            
            print(f"✅ تم التقاط الصورة بنجاح: {os.path.basename(image_path)}")
            print(f"📊 حجم الملف: {file_size:,} bytes")
            
            # التحقق من وجود الملف
            if os.path.exists(image_path):
                print("✅ ملف الصورة موجود")
                
                # فتح الصورة للفحص البصري
                webbrowser.open(f"file:///{os.path.abspath(image_path)}")
                print("🌐 تم فتح الصورة في المتصفح للفحص البصري")
                
                print("\n🔍 تحقق بصرياً من الصورة المفتوحة:")
                print("   ✅ هل ترى قسم '🔥🔥🔥 REAL SERVER RESPONSE 🔥🔥🔥'؟")
                print("   ✅ هل يعرض النص: 'هذا اختبار للاستجابة الحقيقية من النظام v4'؟")
                print("   ✅ هل ترى JSON response مع معلومات الثغرة؟")
                print("   ✅ هل ترى 'تأكيد الثغرة: تم كشف معلومات النظام بنجاح'؟")
                print("   ❌ أم ترى رسالة 'No server response data available from v4 system'؟")
                
                # حفظ نسخة مصغرة للفحص
                try:
                    from PIL import Image
                    img = Image.open(image_path)
                    thumbnail_path = image_path.replace('.png', '_response_check_thumbnail.png')
                    img.thumbnail((1000, 800))
                    img.save(thumbnail_path)
                    print(f"💾 تم حفظ نسخة مصغرة: {os.path.basename(thumbnail_path)}")
                    
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة الصورة: {e}")
                    
            else:
                print("❌ ملف الصورة غير موجود")
        else:
            print("❌ فشل في التقاط الصورة")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await service.cleanup()
        print("🔒 تم تنظيف الموارد")

if __name__ == "__main__":
    asyncio.run(test_real_response_display())
