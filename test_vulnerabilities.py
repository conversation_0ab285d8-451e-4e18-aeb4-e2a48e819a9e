#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للثغرات المتعددة - النسخة المحدثة
"""

import requests
import time
import json
from datetime import datetime

class VulnerabilityTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.results = {}

    def test_vulnerability_sequence(self, vuln_name, vuln_type):
        """اختبار تسلسل الثغرة باستخدام endpoint الصحيح"""
        print(f"\n{'='*50}")
        print(f"🔍 اختبار ثغرة: {vuln_name} ({vuln_type})")
        print(f"{'='*50}")

        result = {
            'vulnerability': vuln_name,
            'type': vuln_type,
            'sequence_test': {'status': None, 'error': None, 'time': None, 'response': None}
        }

        # إعداد بيانات الاختبار
        test_data = {
            "url": "https://httpbin.org/get",
            "vulnerability_name": vuln_name,
            "vulnerability_type": vuln_type,
            "report_id": f"test_{vuln_name}_{int(time.time())}",
            "payload_data": self.get_test_payload(vuln_type),
            "target_parameter": "test_param"
        }

        print(f"📋 اختبار تسلسل الثغرة...")
        print(f"🎯 URL: {test_data['url']}")
        print(f"🔥 Payload: {test_data['payload_data']}")

        start_time = time.time()
        try:
            response = requests.post(
                f"{self.base_url}/vulnerability_sequence",
                json=test_data,
                timeout=30
            )
            result['sequence_test']['status'] = response.status_code
            result['sequence_test']['time'] = round(time.time() - start_time, 2)

            if response.status_code == 200:
                response_data = response.json()
                result['sequence_test']['response'] = response_data
                print(f"  ✅ تسلسل الثغرة: {response.status_code} - {result['sequence_test']['time']}s")
                print(f"  📊 النتيجة: {response_data.get('message', 'لا توجد رسالة')}")

                # عرض تفاصيل المراحل
                if 'stages_results' in response_data:
                    stages = response_data['stages_results']
                    for stage, stage_result in stages.items():
                        if stage_result and stage_result.get('success'):
                            print(f"    ✅ {stage}: نجح")
                        else:
                            print(f"    ❌ {stage}: فشل")
            else:
                print(f"  ⚠️  تسلسل الثغرة: {response.status_code} - {result['sequence_test']['time']}s")
                print(f"     المحتوى: {response.text[:200]}...")

        except Exception as e:
            result['sequence_test']['error'] = str(e)
            result['sequence_test']['time'] = round(time.time() - start_time, 2)
            print(f"  ❌ تسلسل الثغرة: خطأ - {str(e)}")

        self.results[vuln_name] = result
        return result

    def get_test_payload(self, vuln_type):
        """الحصول على payload اختبار حسب نوع الثغرة"""
        payloads = {
            'sql_injection': "' OR '1'='1' --",
            'xss_reflected': "<script>alert('XSS')</script>",
            'xss_stored': "<img src=x onerror=alert('XSS')>",
            'command_injection': "; whoami",
            'file_inclusion': "../../../etc/passwd",
            'xxe': "<?xml version='1.0'?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>",
            'ssrf': "http://localhost:22",
            'path_traversal': "../../../windows/system32/drivers/etc/hosts",
            'csrf': "csrf_token=fake_token",
            'lfi': "../../../../etc/passwd"
        }
        return payloads.get(vuln_type, "test_payload")
    
    def test_endpoints(self):
        """اختبار endpoints المتاحة"""
        print(f"\n🔍 اختبار endpoints المتاحة...")

        endpoints = [
            {'path': '/health', 'method': 'GET', 'description': 'فحص صحة السيرفر'},
            {'path': '/stats', 'method': 'GET', 'description': 'إحصائيات السيرفر'},
            {'path': '/', 'method': 'GET', 'description': 'الصفحة الرئيسية'}
        ]

        for endpoint in endpoints:
            print(f"\n📡 اختبار: {endpoint['method']} {endpoint['path']}")
            try:
                if endpoint['method'] == 'GET':
                    response = requests.get(f"{self.base_url}{endpoint['path']}", timeout=10)
                    print(f"  ✅ {endpoint['path']}: {response.status_code}")
                    if response.status_code == 200:
                        data = response.json()
                        print(f"     📊 البيانات: {str(data)[:100]}...")
                else:
                    print(f"  ⏭️  تخطي {endpoint['method']} endpoint")
            except Exception as e:
                print(f"  ❌ {endpoint['path']}: خطأ - {str(e)}")

    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        # قائمة الثغرات مع أنواعها
        vulnerabilities = [
            ('SQL Injection', 'sql_injection'),
            ('XSS Reflected', 'xss_reflected'),
            ('XSS Stored', 'xss_stored'),
            ('Command Injection', 'command_injection'),
            ('File Inclusion', 'file_inclusion'),
            ('XXE Injection', 'xxe'),
            ('SSRF', 'ssrf'),
            ('Path Traversal', 'path_traversal'),
            ('CSRF', 'csrf'),
            ('LFI', 'lfi')
        ]

        print(f"\n🚀 بدء اختبار {len(vulnerabilities)} ثغرة...")
        print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # اختبار endpoints أولاً
        self.test_endpoints()

        # اختبار الثغرات
        for vuln_name, vuln_type in vulnerabilities:
            self.test_vulnerability_sequence(vuln_name, vuln_type)
            time.sleep(2)  # انتظار بين الاختبارات

        self.print_summary()
        self.save_results()
    
    def print_summary(self):
        """طباعة ملخص النتائج"""
        print(f"\n{'='*60}")
        print(f"📊 ملخص النتائج")
        print(f"{'='*60}")

        success_count = 0
        total_tests = len(self.results)

        for vuln, result in self.results.items():
            sequence_ok = result['sequence_test']['status'] == 200

            if sequence_ok:
                success_count += 1

            status_icon = "✅" if sequence_ok else "❌"
            time_taken = result['sequence_test']['time'] or 0

            print(f"{vuln:25} | Status: {status_icon} | Time: {time_taken:.2f}s")

            # عرض تفاصيل إضافية للاختبارات الناجحة
            if sequence_ok and result['sequence_test']['response']:
                response_data = result['sequence_test']['response']
                successful_stages = response_data.get('successful_stages', 0)
                total_stages = response_data.get('total_stages', 3)
                print(f"{'':25}   📊 المراحل: {successful_stages}/{total_stages}")

        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        print(f"\n📈 معدل النجاح: {success_count}/{total_tests} ({success_rate:.1f}%)")

        # عرض المشاكل
        problems = []
        for vuln, result in self.results.items():
            if result['sequence_test']['status'] != 200:
                status = result['sequence_test']['status'] or 'خطأ'
                error = result['sequence_test']['error']
                if error:
                    problems.append(f"{vuln} - Status: {status}, Error: {error}")
                else:
                    problems.append(f"{vuln} - Status: {status}")

        if problems:
            print(f"\n⚠️  المشاكل المكتشفة:")
            for problem in problems:
                print(f"  - {problem}")
        else:
            print(f"\n🎉 جميع الاختبارات نجحت!")
    
    def save_results(self):
        """حفظ النتائج في ملف"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"vulnerability_test_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ النتائج في: {filename}")

if __name__ == "__main__":
    tester = VulnerabilityTester()
    tester.run_all_tests()
