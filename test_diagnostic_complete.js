// اختبار تشخيصي شامل للمشاكل
console.log('🔍 اختبار تشخيصي شامل للمشاكل...');

async function diagnosticTest() {
    console.log('🚀 بدء الاختبار التشخيصي الشامل...');
    
    const testData = {
        url: 'http://testphp.vulnweb.com/login.php',
        filename: 'diagnostic_test',
        report_id: 'diagnostic_test_id',
        vulnerability_name: 'Diagnostic SQL Injection Test',
        vulnerability_type: 'SQL_Injection',
        stage: 'after',
        payload_data: "admin' UNION SELECT 1,2,3,database(),version() -- diagnostic",
        target_parameter: 'username',
        v4_data: {
            test_results: [
                'DIAGNOSTIC: HTTP Response Code: 200',
                'DIAGNOSTIC: Response Size: 5000 characters',
                'DIAGNOSTIC: Database Version: MySQL 5.7.33',
                'DIAGNOSTIC: Response Time: 300ms',
                'DIAGNOSTIC: Payload Successfully Injected'
            ],
            exploitation_data: {
                status: 'diagnostic_successful',
                method: 'UNION_based_injection_diagnostic',
                payload_used: "admin' UNION SELECT 1,2,3,database(),version() -- diagnostic",
                target_url: 'http://testphp.vulnweb.com/login.php',
                evidence_count: 6
            },
            verification_data: {
                proof: [
                    'DIAGNOSTIC: SQL injection URL created',
                    'DIAGNOSTIC: Database version extracted: MySQL 5.7.33',
                    'DIAGNOSTIC: Database name revealed: testphp_db',
                    'DIAGNOSTIC: SQL error messages confirmed vulnerability',
                    'DIAGNOSTIC: Authentication bypass achieved',
                    'DIAGNOSTIC: Full database access confirmed'
                ]
            },
            response_data: `<!DOCTYPE html>
<html>
<head><title>DIAGNOSTIC Login Response - SQL Injection Detected</title></head>
<body>
<h1>DIAGNOSTIC Database Query Results</h1>
<div class="sql-results">
    <p><strong>DIAGNOSTIC Database:</strong> testphp_db</p>
    <p><strong>DIAGNOSTIC Version:</strong> MySQL 5.7.33</p>
    <p><strong>DIAGNOSTIC User:</strong> root@localhost</p>
    <p><strong>DIAGNOSTIC Tables:</strong> users, products, orders</p>
</div>
<div class="error-log">
    <p>DIAGNOSTIC SQL Error: You have an error in your SQL syntax near 'UNION SELECT 1,2,3,database(),version()' at line 1</p>
    <p>DIAGNOSTIC Query: SELECT * FROM users WHERE username='admin' UNION SELECT 1,2,3,database(),version() -- diagnostic' AND password='...'</p>
</div>
<script>
    console.log('DIAGNOSTIC SQL Injection payload executed successfully');
    document.body.style.backgroundColor = '#ffcccc';
</script>
</body>
</html>`,
            actual_response_content: `🔥 DIAGNOSTIC SQL INJECTION EXPLOITATION RESULTS 🔥

Target: http://testphp.vulnweb.com/login.php
Payload: admin' UNION SELECT 1,2,3,database(),version() -- diagnostic
Method: UNION-based SQL Injection DIAGNOSTIC
Status: DIAGNOSTIC SUCCESSFUL EXPLOITATION

DIAGNOSTIC Database Information Extracted:
- Database Name: testphp_db
- MySQL Version: 5.7.33
- Current User: root@localhost
- Available Tables: users, products, orders
- Server Info: MySQL Community Server

DIAGNOSTIC SQL Injection Evidence:
1. DIAGNOSTIC Payload successfully injected into username parameter
2. DIAGNOSTIC UNION SELECT statement executed without errors
3. DIAGNOSTIC Database metadata extracted successfully
4. DIAGNOSTIC SQL error messages revealed database structure
5. DIAGNOSTIC Response contains injected database information
6. DIAGNOSTIC Authentication bypass confirmed

DIAGNOSTIC Security Impact: CRITICAL
- DIAGNOSTIC Full database access achieved
- DIAGNOSTIC Sensitive data extraction possible
- DIAGNOSTIC Authentication mechanisms bypassed
- DIAGNOSTIC Server configuration disclosed

DIAGNOSTIC Technical Details:
- Injection Point: username parameter
- Attack Vector: UNION-based injection
- Database Type: MySQL 5.7.33
- Privilege Level: root access
- Data Extraction: Confirmed

DIAGNOSTIC Recommendation: Immediate patching required for SQL injection vulnerability`,
            success_indicators: [
                'DIAGNOSTIC SQL Injection vulnerability confirmed',
                'DIAGNOSTIC Database information successfully extracted',
                'DIAGNOSTIC UNION attack executed successfully',
                'DIAGNOSTIC Authentication bypass achieved',
                'DIAGNOSTIC Critical security impact identified',
                'DIAGNOSTIC Full database access confirmed'
            ],
            error_messages: [],
            real_exploitation_evidence: [
                'DIAGNOSTIC Database version disclosure: MySQL 5.7.33',
                'DIAGNOSTIC Database name extraction: testphp_db',
                'DIAGNOSTIC User privilege escalation: root@localhost',
                'DIAGNOSTIC Table enumeration: users, products, orders'
            ],
            exploitation_results: [
                'DIAGNOSTIC Successful UNION-based SQL injection',
                'DIAGNOSTIC Database metadata extraction completed',
                'DIAGNOSTIC Authentication bypass confirmed',
                'DIAGNOSTIC Critical vulnerability impact verified'
            ],
            vulnerability_impact_data: 'DIAGNOSTIC Critical SQL injection vulnerability allows full database access, authentication bypass, and sensitive data extraction. Immediate remediation required.'
        }
    };
    
    console.log('📊 بيانات الاختبار التشخيصي:');
    console.log(`   🎯 اسم الثغرة: ${testData.vulnerability_name}`);
    console.log(`   💉 Payload: ${testData.payload_data}`);
    console.log(`   📊 actual_response_content: ${testData.v4_data.actual_response_content.length} characters`);
    console.log(`   📊 exploitation_results: ${testData.v4_data.exploitation_results.length} results`);
    console.log(`   📊 vulnerability_impact_data: ${testData.v4_data.vulnerability_impact_data.length} characters`);
    console.log(`   📊 response_data: ${testData.v4_data.response_data.length} characters`);
    
    try {
        console.log('\n📤 إرسال طلب تشخيصي...');
        const startTime = Date.now();
        
        const response = await fetch('http://localhost:8000/v4_website', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        });
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`📡 حالة الاستجابة: ${response.status}`);
        console.log(`⏱️ مدة الطلب: ${duration}ms`);
        
        if (response.ok) {
            const result = await response.json();
            console.log('\n✅ نجح الطلب!');
            console.log(`📝 الرسالة: ${result.message || 'تم بنجاح'}`);
            
            if (result.success) {
                console.log(`📸 حجم الصورة: ${result.file_size || 0} bytes`);
                console.log(`📁 مسار الصورة: ${result.file_path || 'غير محدد'}`);
                
                console.log('\n🔍 تحليل النتائج:');
                if (result.vulnerability_name_used) {
                    console.log(`   🎯 اسم الثغرة المستخدم: ${result.vulnerability_name_used}`);
                    if (result.vulnerability_name_used.includes('DIAGNOSTIC')) {
                        console.log(`   ✅ اسم الثغرة يحتوي على DIAGNOSTIC - البيانات تُمرر!`);
                    } else {
                        console.log(`   ❌ اسم الثغرة لا يحتوي على DIAGNOSTIC - البيانات لا تُمرر!`);
                    }
                }
                
                if (result.payload_used) {
                    console.log(`   💉 Payload المستخدم: ${result.payload_used}`);
                    if (result.payload_used.includes('diagnostic')) {
                        console.log(`   ✅ Payload يحتوي على diagnostic - البيانات تُمرر!`);
                    } else {
                        console.log(`   ❌ Payload لا يحتوي على diagnostic - البيانات لا تُمرر!`);
                    }
                }
                
            } else {
                console.log(`❌ فشل: ${result.error || 'خطأ غير محدد'}`);
            }
            
        } else {
            const errorText = await response.text();
            console.log(`❌ فشل الطلب: ${response.status}`);
            console.log(`📄 تفاصيل الخطأ: ${errorText}`);
        }
        
    } catch (error) {
        console.error(`❌ خطأ في الطلب: ${error.message}`);
    }
    
    console.log('\n📋 تعليمات التحقق:');
    console.log('1. تحقق من سجلات سيرفر Python للبحث عن "DIAGNOSTIC"');
    console.log('2. تحقق من الصور المولدة في مجلد screenshots');
    console.log('3. ابحث عن التأثيرات البصرية في الصور');
    console.log('4. تحقق من محتوى الاستجابة في قسم SERVER RESPONSE');
}

// تشغيل الاختبار
diagnosticTest()
    .then(() => {
        console.log('\n🎉 انتهى الاختبار التشخيصي!');
        console.log('📋 راجع السجلات والصور للتحقق من المشاكل');
    })
    .catch(error => {
        console.error('❌ خطأ في الاختبار التشخيصي:', error);
    });
