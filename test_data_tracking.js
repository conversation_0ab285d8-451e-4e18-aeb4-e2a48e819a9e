// اختبار تتبع البيانات في سيرفر Python
console.log('🔍 اختبار تتبع البيانات في سيرفر Python...');

async function testDataTracking() {
    console.log('🚀 بدء اختبار تتبع البيانات...');
    
    // اختبار مع بيانات كاملة
    const testData = {
        url: 'http://testphp.vulnweb.com',
        vulnerability_name: 'SQL_Injection_Test_Tracking',
        vulnerability_type: 'SQL_Injection',
        payload_data: "admin' OR '1'='1' -- tracking_test",
        target_parameter: 'username',
        report_id: 'tracking_test_' + Date.now()
    };
    
    console.log('📤 إرسال بيانات للتتبع:', testData);
    
    try {
        const response = await fetch('http://localhost:8000/vulnerability_sequence', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        });
        
        console.log('📡 حالة الاستجابة:', response.status);
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ استجابة ناجحة:', result);
            
            // التحقق من البيانات المرجعة
            if (result.payload_used) {
                console.log('💉 Payload مستخدم:', result.payload_used);
                if (result.payload_used === testData.payload_data) {
                    console.log('🎯 Payload صحيح ومطابق!');
                } else {
                    console.log('⚠️ Payload مختلف!');
                    console.log('   المرسل:', testData.payload_data);
                    console.log('   المستلم:', result.payload_used);
                }
            } else {
                console.log('❌ لا يوجد payload في الاستجابة');
            }
            
            if (result.vulnerability_type_used) {
                console.log('🔍 Type مستخدم:', result.vulnerability_type_used);
            }
            
        } else {
            const errorText = await response.text();
            console.log('❌ خطأ في الاستجابة:', errorText);
        }
        
    } catch (error) {
        console.error('❌ خطأ في الطلب:', error.message);
    }
    
    console.log('\n🎯 تحقق من سجلات سيرفر Python لرؤية البيانات المستقبلة');
}

// تشغيل الاختبار
testDataTracking().catch(console.error);
