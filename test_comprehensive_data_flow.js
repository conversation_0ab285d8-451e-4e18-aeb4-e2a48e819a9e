// اختبار شامل لتدفق البيانات من النظام الرئيسي إلى سيرفر Python
console.log('🔍 اختبار شامل لتدفق البيانات والإصلاحات...');

// محاكاة ثغرات مختلفة لاختبار payloads فريدة
const testVulnerabilities = [
    {
        name: 'SQL Injection in Login Form',
        type: 'SQL Injection',
        url: 'https://httpbin.org/get',
        description: 'SQL injection vulnerability found in login form'
    },
    {
        name: 'XSS in Search Parameter',
        type: 'Cross-Site Scripting',
        url: 'https://httpbin.org/get',
        description: 'Reflected XSS in search functionality'
    },
    {
        name: 'Command Injection in File Upload',
        type: 'Command Injection',
        url: 'https://httpbin.org/get',
        description: 'Command injection in file processing'
    },
    {
        name: 'LFI in File Parameter',
        type: 'Local File Inclusion',
        url: 'https://httpbin.org/get',
        description: 'Local file inclusion vulnerability'
    },
    {
        name: 'IDOR in User Profile',
        type: 'Insecure Direct Object Reference',
        url: 'https://httpbin.org/get',
        description: 'IDOR vulnerability in user profiles'
    }
];

// محاكاة النظام الرئيسي
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testDataFlow() {
    console.log('🚀 بدء اختبار تدفق البيانات...');
    
    const core = new BugBountyCore();
    
    for (let i = 0; i < testVulnerabilities.length; i++) {
        const vuln = testVulnerabilities[i];
        console.log(`\n📋 اختبار ${i + 1}: ${vuln.name}`);
        console.log(`   🎯 النوع: ${vuln.type}`);
        
        try {
            // 1. اختبار إنشاء payload فريد
            console.log('   🔍 اختبار إنشاء payload فريد...');
            const uniquePayload = core.generateUniquePayload(vuln);
            console.log(`   💉 Payload فريد: ${uniquePayload}`);
            
            // 2. اختبار استخراج معلومات الثغرة
            console.log('   🔍 اختبار استخراج معلومات الثغرة...');
            const extractedData = { url: vuln.url, payload: uniquePayload };
            const vulnInfo = core.extractVulnerabilityTestingInfo(vuln, extractedData);
            console.log(`   📊 معلومات الثغرة:`, {
                name: vulnInfo.name,
                type: vulnInfo.type,
                tested_payload: vulnInfo.tested_payload,
                target_parameter: vulnInfo.target_parameter
            });
            
            // 3. اختبار إرسال البيانات إلى سيرفر Python
            console.log('   📡 اختبار إرسال البيانات إلى سيرفر Python...');
            
            // محاكاة البيانات المرسلة
            const requestData = {
                url: vuln.url,
                vulnerability_name: vulnInfo.name,
                vulnerability_type: vulnInfo.type,
                stage: 'after',
                payload_data: vulnInfo.tested_payload,
                target_parameter: vulnInfo.target_parameter,
                report_id: `test_${Date.now()}_${i}`
            };
            
            console.log(`   📤 البيانات المرسلة:`, requestData);
            
            // إرسال طلب إلى سيرفر Python
            const response = await fetch('http://localhost:8000/vulnerability_sequence', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log(`   ✅ استجابة سيرفر Python:`, {
                    success: result.success,
                    message: result.message,
                    successful_stages: result.successful_stages,
                    payload_used: result.payload_used || 'غير محدد'
                });
                
                // فحص إذا كان payload مختلف
                if (result.payload_used && result.payload_used !== 'غير محدد') {
                    console.log(`   🎯 Payload مستخدم في الصورة: ${result.payload_used}`);
                    
                    // التحقق من أن payload فريد
                    if (result.payload_used.includes('test_payload_') || 
                        result.payload_used.includes('XSS_') ||
                        result.payload_used.includes('-- ')) {
                        console.log(`   ✅ Payload فريد تم استخدامه بنجاح!`);
                    } else {
                        console.log(`   ⚠️ Payload قد يكون افتراضي`);
                    }
                }
                
            } else {
                console.log(`   ❌ فشل في الاتصال بسيرفر Python: ${response.status}`);
            }
            
            // انتظار قصير بين الاختبارات
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error(`   ❌ خطأ في اختبار ${vuln.name}: ${error.message}`);
        }
    }
    
    console.log('\n🎯 ملخص الاختبار الشامل:');
    console.log('✅ تم اختبار إنشاء payloads فريدة');
    console.log('✅ تم اختبار استخراج معلومات الثغرات');
    console.log('✅ تم اختبار إرسال البيانات إلى سيرفر Python');
    console.log('✅ تم اختبار استقبال الاستجابات');
    
    console.log('\n📊 النتائج المتوقعة:');
    console.log('🔥 كل ثغرة يجب أن تحصل على payload فريد');
    console.log('🔥 سيرفر Python يجب أن يستقبل البيانات المختلفة');
    console.log('🔥 الصور يجب أن تظهر تأثيرات مختلفة لكل ثغرة');
}

// تشغيل الاختبار
testDataFlow().catch(console.error);
