#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص محتوى قسم REAL SERVER RESPONSE في الصورة
"""

import os
import glob
from PIL import Image

def verify_real_server_response():
    """فحص محتوى قسم REAL SERVER RESPONSE في أحدث صورة"""
    
    print("🔍 فحص محتوى قسم REAL SERVER RESPONSE في الصورة...")
    
    # البحث عن أحدث صورة after
    pattern = 'assets/modules/bugbounty/screenshots/**/after_*.png'
    images = glob.glob(pattern, recursive=True)
    
    if not images:
        print("❌ لم يتم العثور على أي صور")
        return
    
    # أخذ أحدث صورة
    latest_image = max(images, key=os.path.getmtime)
    print(f"🔍 فحص الصورة: {latest_image}")
    
    if not os.path.exists(latest_image):
        print("❌ ملف الصورة غير موجود")
        return
    
    img = Image.open(latest_image)
    print(f"📐 أبعاد الصورة: {img.size}")
    print(f"📊 حجم الملف: {os.path.getsize(latest_image):,} bytes")
    
    # محاولة استخراج النص باستخدام OCR
    try:
        import pytesseract
        
        print("🔍 استخراج النص من الصورة باستخدام OCR...")
        
        # استخراج النص من الصورة
        extracted_text = pytesseract.image_to_string(img, lang='eng')
        
        print("\n📝 النص المستخرج من الصورة:")
        print("="*80)
        print(extracted_text[:1000] + "..." if len(extracted_text) > 1000 else extracted_text)
        print("="*80)
        
        # البحث عن قسم REAL SERVER RESPONSE
        if 'REAL SERVER RESPONSE' in extracted_text:
            print("\n✅ تم العثور على قسم REAL SERVER RESPONSE!")
            
            # استخراج محتوى القسم
            lines = extracted_text.split('\n')
            in_real_response = False
            real_response_content = []
            
            for line in lines:
                if 'REAL SERVER RESPONSE' in line:
                    in_real_response = True
                    real_response_content.append(line)
                    continue
                
                if in_real_response:
                    if line.strip():
                        real_response_content.append(line)
                        if len(real_response_content) > 30:  # توقف بعد محتوى كافي
                            break
            
            print("\n📋 محتوى قسم REAL SERVER RESPONSE:")
            print("-"*60)
            for i, line in enumerate(real_response_content[:25]):  # أول 25 سطر
                print(f"{i+1:2d}: {line}")
            print("-"*60)
            
            # فحص نوع المحتوى
            content_str = '\n'.join(real_response_content).lower()
            
            print("\n🔍 تحليل المحتوى:")
            
            if 'httpbin.org' in content_str:
                print("✅ يحتوي على استجابة من httpbin.org")
            
            if 'json' in content_str:
                print("✅ يحتوي على بيانات JSON")
            
            if 'http/' in content_str or 'content-type' in content_str:
                print("✅ يحتوي على HTTP headers حقيقية")
            
            if 'origin' in content_str and 'url' in content_str:
                print("✅ يحتوي على بيانات httpbin حقيقية")
            
            if 'no server response' in content_str or 'لا توجد بيانات' in content_str:
                print("❌ لا يزال يعرض رسالة 'لا توجد بيانات'")
            
            if 'vulnerability confirmed' in content_str or 'python web service' in content_str:
                print("⚠️ يحتوي على بيانات مزيفة من السيرفر")
            
            if 'loading' in content_str:
                print("⚠️ يعرض رسالة 'Loading...'")
                
            # فحص خاص للمحتوى المتوقع من httpbin.org/json
            if '"origin"' in content_str and '"url"' in content_str:
                print("🎯 يحتوي على الاستجابة الحقيقية المتوقعة من httpbin.org/json")
            
        else:
            print("❌ لم يتم العثور على قسم REAL SERVER RESPONSE في الصورة")
            print("🔍 البحث عن أقسام أخرى...")
            
            if 'hacked' in extracted_text.lower():
                print("✅ وجد تأثيرات الاستغلال")
            
            if 'vulnerability' in extracted_text.lower():
                print("✅ وجد معلومات الثغرة")
                
    except ImportError:
        print("⚠️ pytesseract غير متوفر - لا يمكن فحص محتوى الصورة")
        print("💡 تثبيت: pip install pytesseract")
        
        # فحص بديل - حفظ نسخة مصغرة للفحص اليدوي
        try:
            thumbnail_path = latest_image.replace('.png', '_content_check.png')
            img.thumbnail((1200, 900))
            img.save(thumbnail_path)
            print(f"💾 تم حفظ نسخة مصغرة للفحص اليدوي: {thumbnail_path}")
        except Exception as e:
            print(f"❌ خطأ في حفظ النسخة المصغرة: {e}")
            
    except Exception as e:
        print(f"❌ خطأ في فحص الصورة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_real_server_response()
