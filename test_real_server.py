#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار السيرفر الحقيقي v4
"""

import requests
import json
import time
from datetime import datetime

def test_real_server():
    """اختبار السيرفر الحقيقي"""
    server_url = 'http://localhost:8000'
    
    print('🔍 اختبار السيرفر الحقيقي v4')
    print('=' * 50)
    
    # التحقق من صحة السيرفر
    try:
        response = requests.get(f'{server_url}/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f'✅ السيرفر يعمل: {data.get("service", "Unknown")}')
        else:
            print(f'❌ السيرفر لا يعمل: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ خطأ في الاتصال: {e}')
        return False
    
    # اختبار ثغرة XSS مع بيانات حقيقية
    print('\n🔥 اختبار XSS مع بيانات حقيقية من v4')
    
    v4_real_data = {
        'test_results': 'Real XSS vulnerability test results from v4 system',
        'exploitation_status': 'XSS exploitation successful',
        'verification_proof': 'Verified XSS vulnerability with payload: <script>alert("XSS")</script>',
        'response_data': 'Complete HTTP response data for XSS vulnerability',
        'error_messages': [],
        'success_indicators': [
            'XSS payload executed successfully',
            'Server responded with vulnerable behavior',
            f'Exploitation confirmed at {datetime.now()}'
        ],
        'vulnerability_meta': {
            'type': 'XSS',
            'severity': 'Critical',
            'confidence': 'High',
            'impact': 'Critical XSS vulnerability detected',
            'remediation': 'Fix XSS vulnerability by proper input validation'
        },
        'original_v4_data': {
            'scan_id': f'v4_scan_{int(time.time())}',
            'target_url': 'https://httpbin.org/html',
            'payload_used': '<script>alert("XSS")</script>',
            'vulnerability_type': 'XSS',
            'timestamp': datetime.now().isoformat(),
            'scanner_version': 'v4.0.0',
            'detection_method': 'XSS_detection_engine'
        }
    }
    
    request_data = {
        'url': 'https://httpbin.org/html',
        'filename': 'xss_real_test',
        'report_id': f'diagnostic_xss_{int(time.time())}',
        'vulnerability_name': 'XSS Real Test',
        'vulnerability_type': 'XSS',
        'stage': 'after',
        'payload_data': '<script>alert("XSS")</script>',
        'target_parameter': 'test_param',
        'response_callback': None,
        'real_test_results': v4_real_data['test_results'],
        'real_exploitation_status': v4_real_data['exploitation_status'],
        'real_verification_proof': v4_real_data['verification_proof'],
        'real_response_data': v4_real_data['response_data'],
        'real_error_messages': v4_real_data['error_messages'],
        'real_success_indicators': v4_real_data['success_indicators'],
        'vulnerability_meta': v4_real_data['vulnerability_meta'],
        'v4_data_main': v4_real_data['original_v4_data']
    }
    
    print(f'📤 إرسال البيانات للسيرفر الحقيقي...')
    print(f'📊 حجم البيانات: {len(str(request_data))} حرف')
    
    try:
        response = requests.post(
            f'{server_url}/v4_website',
            json=request_data,
            timeout=60,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f'📥 استجابة السيرفر: {response.status_code}')
        
        if response.status_code == 200:
            result_data = response.json()
            
            success = result_data.get('success', False)
            screenshot_data = result_data.get('screenshot_data')
            v4_real_data_received = result_data.get('v4_real_data', {})
            
            print(f'✅ نجح الاختبار: {success}')
            print(f'📸 بيانات الصورة: {"موجودة" if screenshot_data else "غير موجودة"}')
            print(f'📊 بيانات v4 المُستلمة: {len(str(v4_real_data_received))} حرف')
            
            if v4_real_data_received:
                print(f'🔍 فحص بيانات v4 المُستلمة:')
                for key, value in v4_real_data_received.items():
                    if isinstance(value, str):
                        print(f'   - {key}: {len(value)} حرف - {value[:50]}...')
                    elif isinstance(value, list):
                        print(f'   - {key}: {len(value)} عنصر')
                    elif isinstance(value, dict):
                        print(f'   - {key}: {len(value)} مفتاح')
                    else:
                        print(f'   - {key}: {type(value)} - {value}')
            
            print('✅ اختبار السيرفر الحقيقي نجح!')
            return True
            
        else:
            print(f'❌ فشل الطلب: {response.status_code}')
            print(f'📄 رسالة الخطأ: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ خطأ في الاختبار: {e}')
        return False

if __name__ == "__main__":
    success = test_real_server()
    print('\n🎉 انتهى اختبار السيرفر الحقيقي')
    if success:
        print('✅ جميع الاختبارات نجحت!')
    else:
        print('❌ بعض الاختبارات فشلت!')
