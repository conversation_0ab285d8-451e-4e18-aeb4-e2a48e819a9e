{"version": "1.33.0", "domains": ["llm", "embedding"], "engine": "llama.cpp", "target_libraries": [{"name": "llm_engine_cuda.node", "type": "llm_engine", "version": "0.1.2"}, {"name": "liblmstudio_bindings_cuda.node", "type": "liblmstudio", "version": "0.2.26"}], "platform": "win", "cpu": {"architecture": "x86_64", "instruction_set_extensions": ["AVX2"]}, "gpu": {"make": "Nvidia", "framework": "CUDA"}, "supported_model_formats": ["gguf"], "manifest_version": "3", "vendor_lib_package_names": ["win-llama-cuda-vendor-v1"], "name": "llama.cpp-win-x86_64-nvidia-cuda-avx2"}