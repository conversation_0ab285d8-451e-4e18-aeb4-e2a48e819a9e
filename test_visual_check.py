#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بصري مباشر للصورة
"""

import sys
import asyncio
import os
from pathlib import Path

# إضافة المسار للوحدات
sys.path.append('.')

from assets.modules.bugbounty.screenshot_service import ScreenshotService

async def test_visual_check():
    """فحص بصري مباشر للتأكد من ظهور قسم REAL SERVER RESPONSE"""
    
    print("🔥 فحص بصري مباشر لقسم REAL SERVER RESPONSE...")
    
    service = ScreenshotService()
    
    try:
        # اختبار مع بيانات حقيقية واضحة
        print("📸 التقاط صورة مع بيانات حقيقية واضحة...")
        
        # بيانات حقيقية واضحة للاختبار
        real_server_data = {
            'actual_response_content': '''HTTP/1.1 500 Internal Server Error
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubuntu)

MySQL Error: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'UNION SELECT 1,2,3,4,5' at line 1

Query: SELECT * FROM users WHERE id = '' UNION SELECT 1,2,3,4,5 --'
Error Code: 1064
Affected Rows: 0

Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)

Warning: mysql_fetch_array() expects parameter 1 to be resource, boolean given in /var/www/html/vulnerable.php on line 42

🔥 VULNERABILITY CONFIRMED: SQL Injection detected
🔥 PAYLOAD EXECUTED: ' UNION SELECT 1,2,3,4,5 --
🔥 EXPLOITATION STATUS: SUCCESSFUL
🔥 DATABASE ERRORS EXPOSED: YES''',
            'response_data': 'MySQL Error: SQL syntax error detected',
            'full_response_content': 'Complete database error response with vulnerability confirmation'
        }
        
        result = await service.capture_with_playwright(
            url='https://httpbin.org/get?test=visual_check',
            filename='visual_check_test',
            stage='after',
            report_id='visual_check',
            vulnerability_name='SQL Injection Visual Test',
            payload_data="' UNION SELECT 1,2,3,4,5 --",
            vulnerability_type='SQL Injection',
            v4_data={
                'response': 'MySQL Error detected in visual test',
                'server_response': 'Database error occurred during visual test'
            },
            v4_real_data=real_server_data
        )
        
        if result and result.get('success'):
            image_path = result.get('path')
            print(f"✅ تم التقاط الصورة بنجاح: {image_path}")
            print(f"📊 حجم الملف: {result.get('file_size', 0)} bytes")
            
            # التحقق من وجود الملف
            if os.path.exists(image_path):
                print("✅ ملف الصورة موجود")
                
                # فتح الصورة في المتصفح للفحص البصري المباشر
                import webbrowser
                webbrowser.open(f"file:///{os.path.abspath(image_path)}")
                print("🌐 تم فتح الصورة في المتصفح للفحص البصري")
                
                # حفظ نسخة مصغرة للفحص
                try:
                    from PIL import Image
                    img = Image.open(image_path)
                    thumbnail_path = image_path.replace('.png', '_visual_check_thumbnail.png')
                    img.thumbnail((800, 600))
                    img.save(thumbnail_path)
                    print(f"💾 تم حفظ نسخة مصغرة: {thumbnail_path}")
                    
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة الصورة: {e}")
                    
                print("\n🔍 تحقق بصرياً من الصورة المفتوحة:")
                print("   ✅ هل ترى قسم '🔥🔥🔥 REAL SERVER RESPONSE 🔥🔥🔥'؟")
                print("   ✅ هل ترى الصندوق الأسود مع النص الأخضر؟")
                print("   ✅ هل ترى محتوى استجابة الخادم الحقيقية؟")
                print("   ✅ هل ترى رسائل خطأ MySQL؟")
                    
            else:
                print("❌ ملف الصورة غير موجود")
        else:
            print("❌ فشل في التقاط الصورة")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await service.cleanup()
        print("🔒 تم تنظيف الموارد")

if __name__ == "__main__":
    asyncio.run(test_visual_check())
