# Network Scanner Script using Scapy
# Coded by <PERSON><PERSON><PERSON> - All rights reserved

from scapy.all import ARP, <PERSON>ther, srp

def scan_network(target_ip):
    print(f"[+] Scanning network: {target_ip}")

    # Create ARP request packet
    arp_request = ARP(pdst=target_ip)
    broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
    arp_request_broadcast = broadcast / arp_request

    # Send the request and receive responses
    answered_list = srp(arp_request_broadcast, timeout=2, verbose=False)[0]

    # Display the results
    print("\nConnected devices on the network:\n")
    print("IP Address\t\tMAC Address")
    print("-" * 40)
    for sent, received in answered_list:
        print(f"{received.psrc}\t\t{received.hwsrc}")

if __name__ == "__main__":
    target = input("Enter target IP range (e.g. ***********/24): ")
    scan_network(target)
