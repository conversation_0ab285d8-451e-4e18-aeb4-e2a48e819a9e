#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لقسم REAL SERVER RESPONSE
"""

import sys
import asyncio
import os
from pathlib import Path

# إضافة المسار للوحدات
sys.path.append('.')

from assets.modules.bugbounty.screenshot_service import ScreenshotService

async def test_final_real_response():
    """اختبار نهائي للتأكد من ظهور قسم REAL SERVER RESPONSE في الصورة"""
    
    print("🔥 اختبار نهائي لقسم REAL SERVER RESPONSE...")
    
    service = ScreenshotService()
    
    try:
        # اختبار مع ثغرات مختلفة
        vulnerabilities = [
            {
                'name': 'SQL Injection',
                'type': 'SQL Injection',
                'payload': "' UNION SELECT 1,2,3,4,5 --",
                'url': 'https://httpbin.org/get?id=1'
            },
            {
                'name': 'XSS Attack',
                'type': 'Cross-Site Scripting',
                'payload': '<script>alert("XSS")</script>',
                'url': 'https://httpbin.org/get?search=test'
            },
            {
                'name': 'Command Injection',
                'type': 'Command Injection',
                'payload': '; ls -la',
                'url': 'https://httpbin.org/get?cmd=whoami'
            }
        ]
        
        for i, vuln in enumerate(vulnerabilities):
            print(f"\n📸 اختبار {i+1}: {vuln['name']}")
            
            result = await service.capture_with_playwright(
                url=vuln['url'],
                filename=f'final_test_{vuln["name"].replace(" ", "_").lower()}',
                stage='after',
                report_id=f'final_test_{i+1}',
                vulnerability_name=vuln['name'],
                payload_data=vuln['payload'],
                vulnerability_type=vuln['type'],
                v4_data={
                    'response': f'{vuln["type"]} detected',
                    'server_response': f'Error in {vuln["name"]}'
                },
                v4_real_data={
                    'actual_response_content': f'Server error for {vuln["name"]}',
                    'response_data': f'Error response for {vuln["type"]}',
                    'full_response_content': f'Complete error response for {vuln["name"]}'
                }
            )
            
            if result and result.get('success'):
                image_path = result.get('path')
                print(f"✅ تم التقاط صورة {vuln['name']}: {image_path}")
                print(f"📊 حجم الملف: {result.get('file_size', 0)} bytes")
                
                # التحقق من وجود الملف
                if os.path.exists(image_path):
                    print(f"✅ ملف الصورة موجود: {vuln['name']}")
                    
                    # حفظ نسخة مصغرة للفحص
                    try:
                        from PIL import Image
                        img = Image.open(image_path)
                        thumbnail_path = image_path.replace('.png', '_final_thumbnail.png')
                        img.thumbnail((800, 600))
                        img.save(thumbnail_path)
                        print(f"💾 نسخة مصغرة: {thumbnail_path}")
                        
                    except Exception as e:
                        print(f"⚠️ خطأ في معالجة الصورة: {e}")
                        
                else:
                    print(f"❌ ملف الصورة غير موجود: {vuln['name']}")
            else:
                print(f"❌ فشل في التقاط صورة {vuln['name']}")
            
            # انتظار بين الاختبارات
            await asyncio.sleep(2)
        
        print("\n🎯 تم الانتهاء من جميع الاختبارات!")
        print("📁 تحقق من مجلد الصور للتأكد من ظهور قسم 'REAL SERVER RESPONSE'")
        print("📂 المسار: assets/modules/bugbounty/screenshots/")
        
        # عرض قائمة بجميع الصور المُنشأة
        screenshots_dir = Path("assets/modules/bugbounty/screenshots")
        if screenshots_dir.exists():
            print("\n📋 الصور المُنشأة:")
            for png_file in screenshots_dir.rglob("*.png"):
                if "final_test" in png_file.name:
                    print(f"   📸 {png_file}")
                    
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await service.cleanup()
        print("🔒 تم تنظيف الموارد")

if __name__ == "__main__":
    asyncio.run(test_final_real_response())
