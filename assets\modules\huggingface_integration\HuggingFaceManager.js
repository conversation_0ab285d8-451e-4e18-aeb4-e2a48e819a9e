// مدير التكامل مع Hugging Face
class HuggingFaceManager {
    constructor() {
        this.isEnabled = false;
        this.apiToken = '';
        this.currentModel = '';
        this.baseURL = 'https://api-inference.huggingface.co/models/';
        this.customAPIURL = ''; // URL مخصص للنماذج
        this.useCustomURL = false; // استخدام URL مخصص
        this.supportedModels = {
            // نماذج مؤكدة التوفر - تم اختبارها
            'gpt2': {
                name: 'GPT-2',
                description: 'نموذج توليد نصوص كلاسيكي وموثوق',
                type: 'text-generation',
                hasVoice: false,
                category: 'basic',
                verified: true
            },
            'distilgpt2': {
                name: 'DistilGPT-2',
                description: 'نسخة مضغوطة وسريعة من GPT-2',
                type: 'text-generation',
                hasVoice: false,
                category: 'basic',
                verified: true
            },
            // نماذج المحادثة المؤكدة
            'microsoft/DialoGPT-medium': {
                name: 'DialoGPT Medium',
                description: 'نموذج محادثة من Microsoft - مؤكد التوفر',
                type: 'conversational',
                hasVoice: false,
                category: 'conversation',
                verified: true
            },
            'microsoft/DialoGPT-small': {
                name: 'DialoGPT Small',
                description: 'نموذج محادثة صغير وسريع',
                type: 'conversational',
                hasVoice: false,
                category: 'conversation',
                verified: true
            },

            // نماذج Google المؤكدة
            'google/flan-t5-small': {
                name: 'FLAN-T5 Small',
                description: 'نموذج Google صغير ومؤكد التوفر',
                type: 'text-generation',
                hasVoice: false,
                category: 'advanced',
                verified: true
            },
            'google/flan-t5-base': {
                name: 'FLAN-T5 Base',
                description: 'نموذج Google متوسط الحجم',
                type: 'text-generation',
                hasVoice: false,
                category: 'advanced',
                verified: true
            },

            // نماذج متعددة اللغات مؤكدة
            'bigscience/bloom-560m': {
                name: 'BLOOM 560M',
                description: 'نموذج متعدد اللغات مؤكد التوفر',
                type: 'text-generation',
                hasVoice: false,
                category: 'multilingual',
                verified: true
            },

            // نماذج EleutherAI مؤكدة
            'EleutherAI/gpt-neo-125M': {
                name: 'GPT-Neo 125M',
                description: 'نموذج صغير وسريع مؤكد التوفر',
                type: 'text-generation',
                hasVoice: false,
                category: 'basic',
                verified: true
            },

            // نماذج البرمجة مؤكدة التوفر
            'Salesforce/codegen-350M-mono': {
                name: 'CodeGen 350M',
                description: 'نموذج توليد كود Python مؤكد التوفر',
                type: 'text-generation',
                hasVoice: false,
                category: 'coding',
                verified: true
            },

            // نماذج Anthropic وOpenAI البديلة
            'huggingface/CodeBERTa-small-v1': {
                name: 'CodeBERTa Small',
                description: 'نموذج كود مدرب على GitHub',
                type: 'code-generation',
                hasVoice: false,
                category: 'coding'
            },

            // نماذج متخصصة أخرى
            'sentence-transformers/all-MiniLM-L6-v2': {
                name: 'MiniLM Embeddings',
                description: 'نموذج تحويل النصوص لمتجهات',
                type: 'embeddings',
                hasVoice: false,
                category: 'specialized'
            },
            'facebook/bart-large-cnn': {
                name: 'BART CNN',
                description: 'نموذج تلخيص النصوص مؤكد التوفر',
                type: 'summarization',
                hasVoice: false,
                category: 'specialized',
                verified: true
            },
            'sshleifer/distilbart-cnn-12-6': {
                name: 'DistilBART CNN',
                description: 'نموذج تلخيص مضغوط وسريع',
                type: 'summarization',
                hasVoice: false,
                category: 'specialized',
                verified: true
            },

            // نماذج الترجمة مؤكدة التوفر
            'Helsinki-NLP/opus-mt-en-ar': {
                name: 'OPUS MT EN-AR',
                description: 'ترجمة من الإنجليزية للعربية مؤكدة',
                type: 'translation',
                hasVoice: false,
                category: 'translation',
                verified: true
            },
            'Helsinki-NLP/opus-mt-ar-en': {
                name: 'OPUS MT AR-EN',
                description: 'ترجمة من العربية للإنجليزية مؤكدة',
                type: 'translation',
                hasVoice: false,
                category: 'translation',
                verified: true
            },

            // نماذج إضافية للاختبار
            'DeepMount00/GPT-4o-ITA-INSTRUCT': {
                name: 'GPT-4o ITA INSTRUCT',
                description: 'نموذج GPT-4 محسن (للاختبار)',
                type: 'text-generation',
                hasVoice: false,
                category: 'experimental',
                verified: false
            }
        };
        
        this.settings = {
            maxTokens: 500,
            temperature: 0.7,
            topP: 0.9,
            repetitionPenalty: 1.1,
            waitForModel: true,
            useCache: true
        };

        // النماذج المخصصة المضافة من المستخدم
        this.customModels = {};

        // النماذج المجلبة من API
        this.fetchedModels = {};
        this.lastFetchTime = null;
        this.fetchCacheTime = 5 * 60 * 1000; // 5 دقائق

        // إعدادات طرق HTTP
        this.httpMethod = 'POST'; // POST أو GET
        this.autoDetectMethod = true; // كشف تلقائي لطريقة HTTP

        this.loadSettings();
        console.log('🤗 تم تهيئة مدير Hugging Face');
    }

    // تحميل الإعدادات المحفوظة
    loadSettings() {
        try {
            const savedSettings = localStorage.getItem('huggingface_settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                this.isEnabled = settings.isEnabled || false;
                this.apiToken = settings.apiToken || '';
                this.currentModel = settings.currentModel || '';
                this.customAPIURL = settings.customAPIURL || '';
                this.useCustomURL = settings.useCustomURL || false;
                this.settings = { ...this.settings, ...settings.modelSettings };
                this.customModels = settings.customModels || {};
                console.log('✅ تم تحميل إعدادات Hugging Face');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل إعدادات Hugging Face:', error);
        }
    }

    // حفظ الإعدادات
    saveSettings() {
        try {
            const settings = {
                isEnabled: this.isEnabled,
                apiToken: this.apiToken,
                currentModel: this.currentModel,
                customAPIURL: this.customAPIURL,
                useCustomURL: this.useCustomURL,
                modelSettings: this.settings,
                customModels: this.customModels
            };
            localStorage.setItem('huggingface_settings', JSON.stringify(settings));
            console.log('💾 تم حفظ إعدادات Hugging Face');
        } catch (error) {
            console.error('❌ خطأ في حفظ إعدادات Hugging Face:', error);
        }
    }

    // تفعيل/إلغاء تفعيل الخدمة
    toggle() {
        this.isEnabled = !this.isEnabled;
        this.saveSettings();
        this.updateUI();
        
        const status = this.isEnabled ? 'مفعل' : 'معطل';
        console.log(`🤗 Hugging Face ${status}`);
        
        return this.isEnabled;
    }

    // تحديث الواجهة
    updateUI() {
        const statusElement = document.getElementById('hf-status');
        const toggleButton = document.getElementById('hf-toggle-btn');
        
        if (statusElement) {
            statusElement.textContent = this.isEnabled ? 'مفعل' : 'معطل';
            statusElement.className = `status ${this.isEnabled ? 'enabled' : 'disabled'}`;
        }
        
        if (toggleButton) {
            toggleButton.textContent = this.isEnabled ? 'إلغاء التفعيل' : 'تفعيل';
            toggleButton.className = `btn ${this.isEnabled ? 'btn-danger' : 'btn-success'}`;
        }

        // تحديث معلومات النموذج الحالي
        this.updateCurrentModelInfo();
    }

    // تحديث معلومات النموذج الحالي
    updateCurrentModelInfo() {
        const modelInfoElement = document.getElementById('hf-current-model-info');
        if (modelInfoElement && this.currentModel) {
            const modelInfo = this.supportedModels[this.currentModel];
            if (modelInfo) {
                modelInfoElement.innerHTML = `
                    <div class="current-model-info">
                        <strong>${modelInfo.name}</strong>
                        <p>${modelInfo.description}</p>
                        <span class="model-type">${modelInfo.type}</span>
                    </div>
                `;
            }
        }
    }

    // تعيين التوكن
    setToken(token) {
        this.apiToken = token.trim();
        this.saveSettings();
        console.log('🔑 تم تحديث توكن Hugging Face');
    }

    // تعيين API URL مخصص
    setCustomAPIURL(url) {
        this.customAPIURL = url.trim();
        this.saveSettings();
        console.log('🔗 تم تحديث API URL المخصص');
    }

    // تفعيل/إلغاء تفعيل استخدام URL مخصص
    toggleCustomURL(enabled) {
        this.useCustomURL = enabled;
        this.saveSettings();
        console.log(`🔄 استخدام URL مخصص: ${enabled ? 'مفعل' : 'معطل'}`);
    }

    // الحصول على URL الفعلي للاستخدام
    getEffectiveURL() {
        if (this.useCustomURL && this.customAPIURL) {
            // إذا كان URL مخصص ولا ينتهي بـ /، أضف /
            const url = this.customAPIURL.endsWith('/') ? this.customAPIURL : this.customAPIURL + '/';
            return url;
        }
        return this.baseURL;
    }

    // تعيين النموذج
    setModel(modelId) {
        const allModels = { ...this.supportedModels, ...this.customModels };
        if (allModels[modelId]) {
            this.currentModel = modelId;
            this.saveSettings();
            this.updateCurrentModelInfo();
            console.log(`🤖 تم تعيين النموذج: ${allModels[modelId].name}`);
            return true;
        }
        return false;
    }

    // إضافة نموذج مخصص
    addCustomModel(modelId, modelInfo) {
        if (!modelId || !modelInfo.name) {
            throw new Error('معرف النموذج والاسم مطلوبان');
        }

        this.customModels[modelId] = {
            name: modelInfo.name,
            description: modelInfo.description || 'نموذج مخصص',
            type: modelInfo.type || 'text-generation',
            hasVoice: false,
            category: 'custom',
            addedDate: new Date().toISOString()
        };

        this.saveSettings();
        console.log(`➕ تم إضافة النموذج المخصص: ${modelInfo.name}`);
        return true;
    }

    // حذف نموذج مخصص
    removeCustomModel(modelId) {
        if (this.customModels[modelId]) {
            const modelName = this.customModels[modelId].name;
            delete this.customModels[modelId];

            // إذا كان النموذج المحذوف هو النموذج الحالي، قم بإلغاء تحديده
            if (this.currentModel === modelId) {
                this.currentModel = '';
            }

            this.saveSettings();
            console.log(`🗑️ تم حذف النموذج المخصص: ${modelName}`);
            return true;
        }
        return false;
    }

    // التحقق من صحة معرف النموذج
    validateModelId(modelId) {
        // التحقق من تنسيق معرف النموذج (مثل: username/model-name)
        const modelIdPattern = /^[a-zA-Z0-9_-]+\/[a-zA-Z0-9_.-]+$/;
        return modelIdPattern.test(modelId);
    }

    // بناء payload متوافق مع النموذج
    buildPayloadForModel(message, modelType) {
        console.log(`🔧 بناء payload لنوع النموذج: ${modelType}`);

        // معاملات أساسية آمنة
        const baseParameters = {
            max_length: this.settings.maxTokens,
            temperature: this.settings.temperature,
            top_p: this.settings.topP
        };

        // معاملات اختيارية حسب النموذج
        const optionalParameters = {};

        // إضافة معاملات حسب النوع
        if (modelType === 'conversational') {
            return {
                inputs: {
                    past_user_inputs: [],
                    generated_responses: [],
                    text: message
                },
                parameters: baseParameters,
                options: {
                    wait_for_model: this.settings.waitForModel,
                    use_cache: this.settings.useCache
                }
            };
        }

        if (modelType === 'text-generation') {
            // معاملات آمنة لتوليد النص
            const textGenParams = {
                ...baseParameters,
                max_new_tokens: this.settings.maxTokens,
                do_sample: true
            };

            // إضافة معاملات اختيارية فقط إذا كانت مدعومة
            if (this.supportsParameter('repetition_penalty')) {
                textGenParams.repetition_penalty = this.settings.repetitionPenalty;
            }

            return {
                inputs: message,
                parameters: textGenParams,
                options: {
                    wait_for_model: this.settings.waitForModel,
                    use_cache: this.settings.useCache
                }
            };
        }

        if (modelType === 'summarization') {
            return {
                inputs: message,
                parameters: {
                    max_length: this.settings.maxTokens,
                    min_length: Math.min(30, Math.floor(this.settings.maxTokens * 0.1))
                },
                options: {
                    wait_for_model: this.settings.waitForModel,
                    use_cache: this.settings.useCache
                }
            };
        }

        if (modelType === 'translation') {
            return {
                inputs: message,
                options: {
                    wait_for_model: this.settings.waitForModel,
                    use_cache: this.settings.useCache
                }
            };
        }

        // للأنواع الأخرى - payload بسيط
        return {
            inputs: message,
            parameters: {
                max_length: this.settings.maxTokens
            },
            options: {
                wait_for_model: this.settings.waitForModel,
                use_cache: this.settings.useCache
            }
        };
    }

    // فحص دعم معامل معين
    supportsParameter(paramName) {
        // قائمة النماذج التي تدعم معاملات معينة
        const parameterSupport = {
            'repetition_penalty': [
                'gpt2', 'distilgpt2', 'EleutherAI/gpt-neo-125M',
                'microsoft/DialoGPT-medium', 'microsoft/DialoGPT-small'
            ],
            'return_full_text': [
                'gpt2', 'distilgpt2'
            ]
        };

        const supportedModels = parameterSupport[paramName] || [];
        return supportedModels.some(model => this.currentModel.includes(model));
    }

    // كشف طريقة HTTP المناسبة للنموذج
    async detectHTTPMethod(url) {
        console.log(`🔍 كشف طريقة HTTP المناسبة لـ: ${url}`);

        try {
            // جرب طلب بسيط أولاً
            const testPayload = {
                inputs: "test",
                options: {
                    wait_for_model: false,
                    use_cache: false
                }
            };

            // جرب POST أولاً (الأكثر شيوع<|im_start|>)
            const postResponse = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testPayload)
            });

            console.log(`📡 رد POST: ${postResponse.status}`);

            // إذا كان POST يعمل أو يعطي خطأ غير 405
            if (postResponse.ok || postResponse.status !== 405) {
                console.log(`✅ POST يعمل`);
                return 'POST';
            }

            // إذا كان 405، جرب GET
            if (postResponse.status === 405) {
                console.log(`⚠️ POST غير مدعوم، جرب GET`);

                const getResponse = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${this.apiToken}`
                    }
                });

                console.log(`📡 رد GET: ${getResponse.status}`);

                if (getResponse.ok) {
                    console.log(`✅ GET يعمل`);
                    return 'GET';
                }
            }

            console.log(`❌ لا توجد طريقة مدعومة`);
            return 'POST'; // افتراضي

        } catch (error) {
            console.error(`❌ خطأ في كشف طريقة HTTP:`, error);
            return 'POST'; // افتراضي
        }
    }

    // تحديث إعدادات النموذج
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
        console.log('⚙️ تم تحديث إعدادات النموذج');
    }

    // التحقق من صحة الإعدادات
    validateSettings() {
        const errors = [];
        
        if (!this.apiToken) {
            errors.push('توكن API مطلوب');
        }
        
        if (!this.currentModel) {
            errors.push('يجب اختيار نموذج');
        }
        
        return errors;
    }

    // إرسال رسالة للنموذج
    async sendMessage(message, options = {}) {
        if (!this.isEnabled) {
            throw new Error('Hugging Face غير مفعل');
        }

        const errors = this.validateSettings();
        if (errors.length > 0) {
            throw new Error(`إعدادات غير صحيحة: ${errors.join(', ')}`);
        }

        try {
            console.log('🤗 إرسال رسالة لـ Hugging Face:', message);
            
            const modelInfo = this.supportedModels[this.currentModel];
            const response = await this.callHuggingFaceAPI(message, modelInfo.type);
            
            console.log('✅ رد من Hugging Face:', response);
            return {
                text: response,
                provider: 'Hugging Face',
                model: modelInfo.name,
                hasVoice: modelInfo.hasVoice
            };
            
        } catch (error) {
            console.error('❌ خطأ في Hugging Face:', error);
            throw error;
        }
    }

    // استدعاء API الخاص بـ Hugging Face مع معالجة أفضل للأخطاء
    async callHuggingFaceAPI(message, modelType) {
        // استخدام URL المخصص إذا كان مفعل، وإلا استخدم الافتراضي
        const baseUrl = this.getEffectiveURL();
        const url = this.useCustomURL && this.customAPIURL ?
            this.customAPIURL : // استخدم URL مخصص كامل
            `${baseUrl}${this.currentModel}`; // أو أضف اسم النموذج للـ URL الافتراضي

        console.log(`🤗 استدعاء API: ${url}`);
        console.log(`📝 نوع النموذج: ${modelType}`);
        console.log(`🔗 استخدام URL مخصص: ${this.useCustomURL}`);
        console.log(`🌐 Base URL: ${baseUrl}`);
        console.log(`🎯 النموذج الحالي: ${this.currentModel}`);

        let payload;

        // تحضير البيانات حسب نوع النموذج مع معاملات متوافقة
        payload = this.buildPayloadForModel(message, modelType);

        console.log('📤 البيانات المرسلة:', JSON.stringify(payload, null, 2));

        try {
            // كشف طريقة HTTP المناسبة إذا كان مفعل
            let method = 'POST';
            if (this.autoDetectMethod) {
                const detectedMethod = await this.detectHTTPMethod(url);
                if (detectedMethod) {
                    method = detectedMethod;
                    console.log(`🎯 استخدام طريقة: ${method}`);
                }
            }

            let response;

            if (method === 'GET') {
                // للـ GET، أرسل البيانات كـ query parameters
                const params = new URLSearchParams();
                params.append('inputs', typeof payload.inputs === 'string' ? payload.inputs : JSON.stringify(payload.inputs));

                if (payload.parameters) {
                    params.append('parameters', JSON.stringify(payload.parameters));
                }

                const getUrl = `${url}?${params.toString()}`;
                console.log(`📡 GET URL: ${getUrl}`);

                response = await fetch(getUrl, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${this.apiToken}`,
                        'User-Agent': 'TechnicalAssistant/1.0'
                    }
                });
            } else {
                // للـ POST، أرسل البيانات في الـ body
                response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiToken}`,
                        'Content-Type': 'application/json',
                        'User-Agent': 'TechnicalAssistant/1.0'
                    },
                    body: JSON.stringify(payload)
                });
            }

            console.log(`📡 رد الخادم: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

                try {
                    const errorData = await response.json();
                    console.error('❌ تفاصيل الخطأ:', errorData);

                    if (errorData.error) {
                        errorMessage = errorData.error;

                        // معالجة خاصة لأخطاء model_kwargs
                        if (errorMessage.includes('model_kwargs') && errorMessage.includes('not used')) {
                            const unusedParams = this.extractUnusedParams(errorMessage);
                            errorMessage = `معاملات غير مدعومة من النموذج: ${unusedParams.join(', ')}

هذا يحدث لأن النماذج المختلفة تدعم معاملات مختلفة.
الحل: النظام سيعيد المحاولة بمعاملات مبسطة.`;

                            // إعادة المحاولة بمعاملات مبسطة
                            return await this.retryWithSimpleParams(message, modelType, url);
                        }
                    }

                    // رسائل خطأ مخصصة ومفصلة
                    if (response.status === 404) {
                        errorMessage = `النموذج "${this.currentModel}" غير موجود. تأكد من:
- صحة اسم النموذج
- أن النموذج يدعم Inference API
- أن النموذج متاح للاستخدام العام
- صحة URL إذا كنت تستخدم URL مخصص`;
                    } else if (response.status === 401) {
                        errorMessage = 'توكن API غير صحيح أو منتهي الصلاحية. تحقق من التوكن في إعدادات Hugging Face';
                    } else if (response.status === 403) {
                        errorMessage = 'ليس لديك صلاحية للوصول لهذا النموذج. قد يكون النموذج خاص أو يحتاج اشتراك مدفوع';
                    } else if (response.status === 405) {
                        errorMessage = `طريقة POST غير مدعومة لهذا URL. هذا يعني:
- URL غير صحيح (تحقق من URL المخصص)
- النموذج لا يدعم Inference API
- جرب نموذج آخر أو URL مختلف`;
                    } else if (response.status === 429) {
                        errorMessage = 'تم تجاوز حد الطلبات. انتظر قليلاً وحاول مرة أخرى';
                    } else if (response.status === 503) {
                        errorMessage = 'النموذج محمل حالياً. انتظر قليلاً وحاول مرة أخرى';
                    }
                } catch (parseError) {
                    console.error('❌ خطأ في تحليل رد الخطأ:', parseError);
                }

                throw new Error(errorMessage);
            }

            const data = await response.json();
            console.log('✅ البيانات المستلمة:', data);

            return this.extractResponseText(data, modelType);

        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('خطأ في الاتصال بالشبكة. تحقق من اتصال الإنترنت');
            }
            throw error;
        }
    }

    // استخراج المعاملات غير المدعومة من رسالة الخطأ
    extractUnusedParams(errorMessage) {
        const match = errorMessage.match(/\[(.*?)\]/);
        if (match) {
            return match[1].split(',').map(param => param.trim().replace(/'/g, ''));
        }
        return [];
    }

    // إعادة المحاولة بمعاملات مبسطة
    async retryWithSimpleParams(message, modelType, url) {
        console.log('🔄 إعادة المحاولة بمعاملات مبسطة...');

        // payload مبسط جداً
        const simplePayload = {
            inputs: message,
            options: {
                wait_for_model: this.settings.waitForModel,
                use_cache: this.settings.useCache
            }
        };

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'TechnicalAssistant/1.0'
                },
                body: JSON.stringify(simplePayload)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: فشل حتى مع المعاملات المبسطة`);
            }

            const data = await response.json();
            console.log('✅ نجح مع المعاملات المبسطة:', data);

            return this.extractResponseText(data, modelType);

        } catch (error) {
            console.error('❌ فشل حتى مع المعاملات المبسطة:', error);
            throw new Error(`فشل النموذج حتى مع المعاملات المبسطة: ${error.message}`);
        }
    }

    // استخراج النص من الرد حسب نوع النموذج
    extractResponseText(data, modelType) {
        try {
            console.log('📄 استخراج النص من:', data);

            if (modelType === 'conversational') {
                if (data.generated_text) {
                    return data.generated_text;
                }
                if (data.conversation && data.conversation.generated_responses) {
                    return data.conversation.generated_responses[data.conversation.generated_responses.length - 1];
                }
                return data.response || 'لا يوجد رد';
            }

            if (modelType === 'text-generation') {
                if (Array.isArray(data) && data.length > 0) {
                    return data[0].generated_text || 'لا يوجد رد';
                }
                return data.generated_text || 'لا يوجد رد';
            }

            if (modelType === 'translation') {
                if (Array.isArray(data) && data.length > 0) {
                    return data[0].translation_text || 'لا يوجد ترجمة';
                }
                return data.translation_text || 'لا يوجد ترجمة';
            }

            if (modelType === 'summarization') {
                if (Array.isArray(data) && data.length > 0) {
                    return data[0].summary_text || 'لا يوجد تلخيص';
                }
                return data.summary_text || 'لا يوجد تلخيص';
            }

            // للأنواع الأخرى - جرب جميع الحقول الممكنة
            if (Array.isArray(data) && data.length > 0) {
                const firstItem = data[0];
                return firstItem.generated_text ||
                       firstItem.text ||
                       firstItem.summary_text ||
                       firstItem.translation_text ||
                       JSON.stringify(firstItem);
            }

            return data.generated_text ||
                   data.text ||
                   data.summary_text ||
                   data.translation_text ||
                   JSON.stringify(data);

        } catch (error) {
            console.error('❌ خطأ في استخراج النص:', error);
            return 'خطأ في معالجة الرد من النموذج';
        }
    }

    // فحص توفر النموذج مع معالجة أفضل للأخطاء
    async checkModelAvailability(modelId) {
        // استخدام نفس منطق URL المستخدم في callHuggingFaceAPI
        const baseUrl = this.getEffectiveURL();
        const url = this.useCustomURL && this.customAPIURL ?
            this.customAPIURL :
            `${baseUrl}${modelId}`;

        console.log(`🔍 فحص توفر النموذج: ${url}`);

        try {
            // أولاً: جرب طلب GET للتحقق من وجود النموذج
            const getResponse = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`
                }
            });

            console.log(`📡 رد GET: ${getResponse.status}`);

            // إذا كان GET يعمل، جرب POST
            if (getResponse.ok) {
                const postResponse = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        inputs: "test",
                        options: {
                            wait_for_model: false,
                            use_cache: false
                        }
                    })
                });

                console.log(`📡 رد POST: ${postResponse.status}`);

                if (postResponse.status === 405) {
                    return {
                        available: false,
                        reason: 'النموذج لا يدعم Inference API أو URL غير صحيح. جرب URL مختلف أو نموذج آخر.'
                    };
                }

                return { available: true, reason: 'النموذج متاح ويدعم POST' };
            }

            // معالجة أخطاء GET
            if (getResponse.status === 404) {
                return {
                    available: false,
                    reason: 'النموذج غير موجود. تحقق من اسم النموذج أو URL'
                };
            }

            if (getResponse.status === 401) {
                return {
                    available: false,
                    reason: 'توكن API غير صحيح أو منتهي الصلاحية'
                };
            }

            if (getResponse.status === 403) {
                return {
                    available: false,
                    reason: 'ليس لديك صلاحية للوصول لهذا النموذج'
                };
            }

            if (getResponse.status === 405) {
                return {
                    available: false,
                    reason: 'URL غير صحيح أو النموذج لا يدعم هذا النوع من الطلبات'
                };
            }

            return {
                available: false,
                reason: `خطأ غير متوقع: HTTP ${getResponse.status}`
            };

        } catch (error) {
            console.error('❌ خطأ في فحص النموذج:', error);
            return {
                available: false,
                reason: `خطأ في الاتصال: ${error.message}`
            };
        }
    }

    // اختبار الاتصال المحسن - يستخدم نفس منطق الإرسال
    async testConnection() {
        try {
            console.log('🧪 بدء اختبار الاتصال...');
            console.log(`📋 النموذج الحالي: ${this.currentModel}`);
            console.log(`🔗 استخدام URL مخصص: ${this.useCustomURL}`);
            console.log(`🌐 URL المخصص: ${this.customAPIURL}`);

            // التحقق من الإعدادات الأساسية
            const errors = this.validateSettings();
            if (errors.length > 0) {
                return {
                    success: false,
                    message: `إعدادات غير صحيحة: ${errors.join(', ')}`
                };
            }

            // اختبار إرسال رسالة فعلية مباشرة (نفس منطق sendMessage)
            const testMessage = "Hello";
            console.log('📤 إرسال رسالة اختبار...');

            const response = await this.sendMessage(testMessage);

            if (response && response.text && response.text.trim().length > 0) {
                const previewText = response.text.substring(0, 100);
                return {
                    success: true,
                    message: `✅ تم الاتصال بنجاح!\n\nرد النموذج: "${previewText}${response.text.length > 100 ? '...' : ''}"\n\nالنموذج يعمل بشكل صحيح!`
                };
            } else {
                return {
                    success: false,
                    message: 'النموذج اتصل لكن لم يرد بنص صحيح. قد يحتاج إعدادات مختلفة.'
                };
            }

        } catch (error) {
            console.error('❌ خطأ في اختبار الاتصال:', error);

            // تحليل نوع الخطأ لإعطاء رسالة مفيدة
            let errorMessage = error.message;

            if (errorMessage.includes('404')) {
                errorMessage = `النموذج غير موجود أو URL خاطئ.

تأكد من:
- صحة اسم النموذج: ${this.currentModel}
- إذا كنت تستخدم URL مخصص، تأكد من صحته
- أن النموذج يدعم Inference API`;
            } else if (errorMessage.includes('401')) {
                errorMessage = `توكن API غير صحيح.

تأكد من:
- صحة التوكن
- أن التوكن لم ينته
- أن التوكن له صلاحيات Inference API`;
            } else if (errorMessage.includes('403')) {
                errorMessage = `ليس لديك صلاحية للوصول لهذا النموذج.

قد يكون:
- النموذج خاص
- يحتاج اشتراك مدفوع
- التوكن ليس له صلاحيات كافية`;
            }

            return {
                success: false,
                message: `فشل الاختبار: ${errorMessage}`
            };
        }
    }

    // الحصول على معلومات النموذج
    getModelInfo(modelId) {
        return this.supportedModels[modelId] || this.customModels[modelId] || null;
    }

    // الحصول على قائمة النماذج المدعومة
    getSupportedModels() {
        return this.supportedModels;
    }

    // الحصول على قائمة النماذج المخصصة
    getCustomModels() {
        return this.customModels;
    }

    // الحصول على جميع النماذج (مدعومة + مخصصة)
    getAllModels() {
        return { ...this.supportedModels, ...this.customModels };
    }

    // الحصول على النماذج حسب الفئة
    getModelsByCategory(category) {
        const allModels = this.getAllModels();
        const filteredModels = {};

        for (const [id, model] of Object.entries(allModels)) {
            if (model.category === category) {
                filteredModels[id] = model;
            }
        }

        return filteredModels;
    }

    // الحصول على الفئات المتاحة
    getAvailableCategories() {
        const allModels = this.getAllModels();
        const categories = new Set();

        for (const model of Object.values(allModels)) {
            categories.add(model.category);
        }

        return Array.from(categories);
    }

    // جلب النماذج المتاحة من Hugging Face API
    async fetchAvailableModels(forceRefresh = false) {
        // التحقق من وجود التوكن
        if (!this.apiToken) {
            throw new Error('يجب إدخال توكن API أولاً');
        }

        // التحقق من الكاش
        const now = Date.now();
        if (!forceRefresh && this.lastFetchTime && (now - this.lastFetchTime) < this.fetchCacheTime) {
            console.log('📋 استخدام النماذج المحفوظة من الكاش');
            return this.fetchedModels;
        }

        console.log('🌐 جلب النماذج المتاحة من Hugging Face...');

        try {
            // جلب النماذج الشائعة والمتاحة للـ Inference API
            const modelsToCheck = [
                // نماذج أساسية مؤكدة
                'gpt2',
                'distilgpt2',
                'microsoft/DialoGPT-medium',
                'microsoft/DialoGPT-small',
                'google/flan-t5-small',
                'google/flan-t5-base',
                'facebook/bart-large-cnn',
                'sshleifer/distilbart-cnn-12-6',
                'bigscience/bloom-560m',
                'EleutherAI/gpt-neo-125M',
                'Salesforce/codegen-350M-mono',
                'Helsinki-NLP/opus-mt-en-ar',
                'Helsinki-NLP/opus-mt-ar-en',

                // نماذج متقدمة للاختبار
                'microsoft/DialoGPT-large',
                'google/flan-t5-large',
                'EleutherAI/gpt-neo-2.7B',
                'facebook/blenderbot-400M-distill',
                'DeepMount00/GPT-4o-ITA-INSTRUCT',
                'bigscience/bloom-1b1',
                'Salesforce/codegen-2B-mono'
            ];

            const availableModels = {};
            const checkPromises = modelsToCheck.map(async (modelId) => {
                try {
                    // اختبار بسيط: جرب إرسال طلب للنموذج
                    const isAvailable = await this.quickModelCheck(modelId);
                    if (isAvailable) {
                        const modelInfo = this.getModelInfoFromId(modelId);
                        availableModels[modelId] = {
                            ...modelInfo,
                            verified: true,
                            lastChecked: new Date().toISOString()
                        };
                        console.log(`✅ ${modelId} متاح`);
                    } else {
                        console.log(`❌ ${modelId} غير متاح`);
                    }
                } catch (error) {
                    console.log(`⚠️ خطأ في فحص ${modelId}:`, error.message);
                }
            });

            // انتظار فحص جميع النماذج (مع timeout)
            await Promise.allSettled(checkPromises);

            this.fetchedModels = availableModels;
            this.lastFetchTime = now;

            console.log(`🎉 تم جلب ${Object.keys(availableModels).length} نموذج متاح`);
            return availableModels;

        } catch (error) {
            console.error('❌ خطأ في جلب النماذج:', error);
            throw new Error(`فشل في جلب النماذج: ${error.message}`);
        }
    }

    // فحص سريع للنموذج (بدون تعقيدات)
    async quickModelCheck(modelId) {
        try {
            const url = `${this.baseURL}${modelId}`;

            // طلب بسيط جداً
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inputs: "test",
                    options: {
                        wait_for_model: false,
                        use_cache: false
                    }
                })
            });

            // إذا لم يكن 404 أو 401 أو 403، فالنموذج متاح
            return response.status !== 404 && response.status !== 401 && response.status !== 403;

        } catch (error) {
            return false;
        }
    }

    // الحصول على معلومات النموذج من معرفه
    getModelInfoFromId(modelId) {
        // البحث في النماذج المدعومة أولاً
        if (this.supportedModels[modelId]) {
            return this.supportedModels[modelId];
        }

        // إنشاء معلومات تلقائية للنماذج غير المعروفة
        const parts = modelId.split('/');
        const modelName = parts.length > 1 ? parts[1] : modelId;
        const organization = parts.length > 1 ? parts[0] : 'Unknown';

        // تخمين نوع النموذج من الاسم
        let type = 'text-generation';
        let category = 'general';

        if (modelId.includes('DialoGPT') || modelId.includes('blenderbot')) {
            type = 'conversational';
            category = 'conversation';
        } else if (modelId.includes('bart') && modelId.includes('cnn')) {
            type = 'summarization';
            category = 'specialized';
        } else if (modelId.includes('opus-mt')) {
            type = 'translation';
            category = 'translation';
        } else if (modelId.includes('codegen') || modelId.includes('code')) {
            type = 'text-generation';
            category = 'coding';
        } else if (modelId.includes('flan-t5')) {
            type = 'text-generation';
            category = 'advanced';
        } else if (modelId.includes('bloom')) {
            type = 'text-generation';
            category = 'multilingual';
        }

        return {
            name: modelName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            description: `نموذج ${type} من ${organization}`,
            type: type,
            hasVoice: false,
            category: category,
            organization: organization
        };
    }

    // جلب النماذج مع مؤشر التقدم
    async fetchModelsWithProgress(progressCallback) {
        if (!this.apiToken) {
            throw new Error('يجب إدخال توكن API أولاً');
        }

        const modelsToCheck = [
            'gpt2', 'distilgpt2', 'microsoft/DialoGPT-medium', 'microsoft/DialoGPT-small',
            'google/flan-t5-small', 'google/flan-t5-base', 'facebook/bart-large-cnn',
            'sshleifer/distilbart-cnn-12-6', 'bigscience/bloom-560m', 'EleutherAI/gpt-neo-125M',
            'Salesforce/codegen-350M-mono', 'Helsinki-NLP/opus-mt-en-ar', 'Helsinki-NLP/opus-mt-ar-en',
            'DeepMount00/GPT-4o-ITA-INSTRUCT'
        ];

        const availableModels = {};
        let completed = 0;

        for (const modelId of modelsToCheck) {
            try {
                if (progressCallback) {
                    progressCallback({
                        current: completed + 1,
                        total: modelsToCheck.length,
                        modelId: modelId,
                        status: 'checking'
                    });
                }

                const isAvailable = await this.quickModelCheck(modelId);

                if (isAvailable) {
                    const modelInfo = this.getModelInfoFromId(modelId);
                    availableModels[modelId] = {
                        ...modelInfo,
                        verified: true,
                        lastChecked: new Date().toISOString()
                    };
                }

                completed++;

                if (progressCallback) {
                    progressCallback({
                        current: completed,
                        total: modelsToCheck.length,
                        modelId: modelId,
                        status: isAvailable ? 'available' : 'unavailable',
                        available: isAvailable
                    });
                }

            } catch (error) {
                completed++;
                if (progressCallback) {
                    progressCallback({
                        current: completed,
                        total: modelsToCheck.length,
                        modelId: modelId,
                        status: 'error',
                        error: error.message
                    });
                }
            }
        }

        this.fetchedModels = availableModels;
        this.lastFetchTime = Date.now();

        return availableModels;
    }

    // الحصول على حالة الخدمة
    getStatus() {
        return {
            isEnabled: this.isEnabled,
            hasToken: !!this.apiToken,
            hasModel: !!this.currentModel,
            currentModel: this.currentModel,
            modelInfo: this.currentModel ? this.getModelInfo(this.currentModel) : null,
            useCustomURL: this.useCustomURL,
            customAPIURL: this.customAPIURL,
            effectiveURL: this.getEffectiveURL()
        };
    }
}

// إنشاء مثيل عام
window.huggingFaceManager = new HuggingFaceManager();
