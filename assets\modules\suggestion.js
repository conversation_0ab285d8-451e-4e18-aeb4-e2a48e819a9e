// وحدة الاقتراحات الذكية والتحسين الذاتي
// Smart Suggestions and Self-Improvement Module

class SmartSuggestionEngine {
    constructor() {
        this.userBehavior = {};
        this.conversationPatterns = [];
        this.improvementSuggestions = [];
        this.codeAnalysis = {};
        this.learningHistory = [];
        this.isMonitoring = false;
    }

    // بدء مراقبة سلوك المستخدم
    startMonitoring() {
        this.isMonitoring = true;
        console.log('🔍 بدء مراقبة سلوك المستخدم للاقتراحات الذكية');
        
        // مراقبة التفاعلات
        this.monitorUserInteractions();
        
        // تحليل دوري للمحادثة
        setInterval(() => {
            this.analyzeConversationPatterns();
        }, 30000); // كل 30 ثانية

        // توليد اقتراحات دورية
        setInterval(() => {
            this.generatePeriodicSuggestions();
        }, 120000); // كل دقيقتين
    }

    // مراقبة تفاعلات المستخدم
    monitorUserInteractions() {
        // مراقبة النقرات على الأزرار
        document.addEventListener('click', (event) => {
            if (event.target.classList.contains('tool-btn')) {
                this.recordToolUsage(event.target.textContent);
            }
        });

        // مراقبة الرسائل المرسلة
        const originalSendMessage = window.sendMessage;
        window.sendMessage = () => {
            this.recordMessageSent();
            originalSendMessage();
        };

        // مراقبة استخدام الصوت
        const originalToggleVoice = window.toggleVoiceRecording;
        window.toggleVoiceRecording = () => {
            this.recordVoiceUsage();
            originalToggleVoice();
        };
    }

    // تسجيل استخدام الأدوات
    recordToolUsage(toolName) {
        const timestamp = new Date().toISOString();
        
        if (!this.userBehavior.toolUsage) {
            this.userBehavior.toolUsage = {};
        }
        
        if (!this.userBehavior.toolUsage[toolName]) {
            this.userBehavior.toolUsage[toolName] = [];
        }
        
        this.userBehavior.toolUsage[toolName].push(timestamp);
        
        console.log(`📊 تم تسجيل استخدام الأداة: ${toolName}`);
        this.analyzeToolUsagePattern(toolName);
    }

    // تسجيل إرسال الرسائل
    recordMessageSent() {
        const timestamp = new Date().toISOString();
        
        if (!this.userBehavior.messageActivity) {
            this.userBehavior.messageActivity = [];
        }
        
        this.userBehavior.messageActivity.push(timestamp);
        this.analyzeMessagePatterns();
    }

    // تسجيل استخدام الصوت
    recordVoiceUsage() {
        const timestamp = new Date().toISOString();
        
        if (!this.userBehavior.voiceUsage) {
            this.userBehavior.voiceUsage = [];
        }
        
        this.userBehavior.voiceUsage.push(timestamp);
        console.log('🎤 تم تسجيل استخدام الميزة الصوتية');
    }

    // تحليل أنماط استخدام الأدوات
    analyzeToolUsagePattern(toolName) {
        const usage = this.userBehavior.toolUsage[toolName];
        
        if (usage.length >= 3) {
            // اقتراح تحسينات بناءً على الاستخدام المتكرر
            this.suggestToolOptimization(toolName, usage.length);
        }
    }

    // تحليل أنماط المحادثة
    analyzeConversationPatterns() {
        if (!currentConversation || currentConversation.length < 5) return;

        const recentMessages = currentConversation.slice(-10);
        const patterns = this.extractConversationPatterns(recentMessages);
        
        this.conversationPatterns.push({
            timestamp: new Date().toISOString(),
            patterns: patterns,
            messageCount: recentMessages.length
        });

        // توليد اقتراحات بناءً على الأنماط
        this.generatePatternBasedSuggestions(patterns);
    }

    // استخراج أنماط المحادثة
    extractConversationPatterns(messages) {
        const patterns = {
            questionTypes: [],
            topicChanges: 0,
            codeRequests: 0,
            explanationRequests: 0,
            averageMessageLength: 0
        };

        let totalLength = 0;
        let lastTopic = null;

        messages.forEach(msg => {
            if (msg.sender === 'user') {
                totalLength += msg.content.length;
                
                // تحليل نوع السؤال
                if (msg.content.includes('كيف') || msg.content.includes('شلون')) {
                    patterns.questionTypes.push('how-to');
                } else if (msg.content.includes('ما هو') || msg.content.includes('شنو')) {
                    patterns.questionTypes.push('definition');
                } else if (msg.content.includes('كود') || msg.content.includes('برمجة')) {
                    patterns.codeRequests++;
                } else if (msg.content.includes('اشرح') || msg.content.includes('وضح')) {
                    patterns.explanationRequests++;
                }

                // تحليل تغيير المواضيع
                const currentTopic = this.identifyTopic(msg.content);
                if (lastTopic && currentTopic !== lastTopic) {
                    patterns.topicChanges++;
                }
                lastTopic = currentTopic;
            }
        });

        const userMessages = messages.filter(msg => msg.sender === 'user');
        patterns.averageMessageLength = userMessages.length > 0 ? totalLength / userMessages.length : 0;

        return patterns;
    }

    // تحديد موضوع الرسالة
    identifyTopic(content) {
        const topics = {
            'programming': ['كود', 'برمجة', 'javascript', 'python', 'html'],
            'networking': ['شبكة', 'إنترنت', 'network'],
            'database': ['قاعدة بيانات', 'database', 'sql'],
            'design': ['تصميم', 'واجهة', 'ui', 'ux'],
            'general': ['مساعدة', 'شرح', 'تعلم']
        };

        const lowerContent = (content && typeof content === 'string') ? content.toLowerCase() : '';
        
        for (const [topic, keywords] of Object.entries(topics)) {
            if (keywords.some(keyword => lowerContent.includes(keyword))) {
                return topic;
            }
        }
        
        return 'general';
    }

    // توليد اقتراحات بناءً على الأنماط
    generatePatternBasedSuggestions(patterns) {
        const suggestions = [];

        // اقتراحات بناءً على نوع الأسئلة
        if (patterns.codeRequests >= 2) {
            suggestions.push({
                type: 'workflow',
                title: 'تحسين سير العمل البرمجي',
                description: 'لاحظت أنك تطلب أمثلة كود كثيراً. هل تريد إعداد بيئة تطوير محسنة؟',
                action: () => this.suggestDevelopmentEnvironment()
            });
        }

        if (patterns.explanationRequests >= 3) {
            suggestions.push({
                type: 'learning',
                title: 'مسار تعلم مخصص',
                description: 'يبدو أنك تحب التعلم! هل تريد إنشاء مسار تعلم مخصص لك؟',
                action: () => this.suggestLearningPath()
            });
        }

        if (patterns.topicChanges >= 3) {
            suggestions.push({
                type: 'organization',
                title: 'تنظيم المحادثة',
                description: 'تنتقل بين مواضيع متعددة. هل تريد تنظيم المحادثة في أقسام؟',
                action: () => this.suggestConversationOrganization()
            });
        }

        // عرض الاقتراحات
        if (suggestions.length > 0) {
            this.displaySuggestions(suggestions);
        }
    }

    // توليد اقتراحات دورية
    generatePeriodicSuggestions() {
        if (!this.isMonitoring) return;

        const suggestions = [];

        // فحص الأدوات غير المستخدمة
        const unusedTools = this.findUnusedTools();
        if (unusedTools.length > 0) {
            suggestions.push({
                type: 'feature',
                title: 'ميزات لم تجربها بعد',
                description: `هناك ${unusedTools.length} ميزة لم تجربها: ${unusedTools.join(', ')}`,
                action: () => this.demonstrateUnusedTools(unusedTools)
            });
        }

        // اقتراح تحسينات الأداء
        if (this.shouldSuggestPerformanceImprovement()) {
            suggestions.push({
                type: 'performance',
                title: 'تحسين الأداء',
                description: 'يمكنني تحسين أدائي بناءً على استخدامك. هل تريد تطبيق التحسينات؟',
                action: () => this.applyPerformanceImprovements()
            });
        }

        // عرض الاقتراحات إذا وجدت
        if (suggestions.length > 0 && Math.random() < 0.3) { // 30% احتمال العرض
            this.displaySuggestions(suggestions.slice(0, 2)); // أقصى اقتراحين
        }
    }

    // البحث عن الأدوات غير المستخدمة
    findUnusedTools() {
        const allTools = ['مشاركة الشاشة', 'تحليل فيديو', 'عرض ثلاثي الأبعاد', 'توليد ملخص', 'التفاعل الصوتي'];
        const usedTools = Object.keys(this.userBehavior.toolUsage || {});
        
        return allTools.filter(tool => !usedTools.some(used => used.includes(tool.split(' ')[0])));
    }

    // فحص ما إذا كان يجب اقتراح تحسين الأداء
    shouldSuggestPerformanceImprovement() {
        const messageCount = this.userBehavior.messageActivity?.length || 0;
        const toolUsageCount = Object.values(this.userBehavior.toolUsage || {}).flat().length;
        
        return messageCount > 20 || toolUsageCount > 10;
    }

    // عرض الاقتراحات
    displaySuggestions(suggestions) {
        const suggestionHtml = this.createSuggestionHtml(suggestions);
        addMessageToChat('assistant', suggestionHtml, 'html');
        
        console.log('💡 تم عرض اقتراحات ذكية:', suggestions.map(s => s.title));
    }

    // إنشاء HTML للاقتراحات
    createSuggestionHtml(suggestions) {
        let html = '<div class="smart-suggestions">';
        html += '<h4>💡 اقتراحات ذكية</h4>';
        
        suggestions.forEach((suggestion, index) => {
            html += `
                <div class="suggestion-item" style="margin: 10px 0; padding: 10px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #667eea;">
                    <h5 style="margin: 0 0 5px 0; color: #333;">${suggestion.title}</h5>
                    <p style="margin: 0 0 10px 0; color: #666; font-size: 0.9rem;">${suggestion.description}</p>
                    <button onclick="smartSuggestionEngine.executeSuggestion(${index})" 
                            style="background: #667eea; color: white; border: none; padding: 5px 15px; border-radius: 5px; cursor: pointer;">
                        تطبيق الاقتراح
                    </button>
                </div>
            `;
        });
        
        html += '</div>';
        
        // حفظ الاقتراحات للتنفيذ
        this.currentSuggestions = suggestions;
        
        return html;
    }

    // تنفيذ اقتراح محدد
    executeSuggestion(index) {
        if (this.currentSuggestions && this.currentSuggestions[index]) {
            const suggestion = this.currentSuggestions[index];
            console.log('⚡ تنفيذ الاقتراح:', suggestion.title);
            
            if (suggestion.action) {
                suggestion.action();
            }
            
            addMessageToChat('assistant', `تم تطبيق الاقتراح: ${suggestion.title}`);
        }
    }

    // اقتراح بيئة تطوير محسنة
    suggestDevelopmentEnvironment() {
        const envSuggestion = `
🛠️ **بيئة التطوير المقترحة:**

**المحرر المقترح:**
• Visual Studio Code مع الإضافات التالية:
  - Live Server للمعاينة المباشرة
  - Prettier لتنسيق الكود
  - Auto Rename Tag لـ HTML

**أدوات مفيدة:**
• Chrome DevTools للتطوير
• Git للتحكم في الإصدارات
• Node.js لتشغيل JavaScript

**نصائح للإنتاجية:**
• استخدم اختصارات لوحة المفاتيح
• نظم ملفاتك في مجلدات واضحة
• اكتب تعليقات توضيحية في الكود
        `;
        
        addMessageToChat('assistant', envSuggestion);
    }

    // اقتراح مسار تعلم
    suggestLearningPath() {
        const learningPath = `
📚 **مسار التعلم المخصص لك:**

**المرحلة الأولى - الأساسيات:**
1. HTML و CSS الأساسي
2. JavaScript الأساسي
3. مفاهيم البرمجة العامة

**المرحلة الثانية - التطبيق:**
1. بناء مشاريع صغيرة
2. التعامل مع APIs
3. إدارة البيانات

**المرحلة الثالثة - التقدم:**
1. إطارات العمل (React/Vue)
2. قواعد البيانات
3. أمان التطبيقات

**موارد مقترحة:**
• MDN Web Docs للمراجع
• freeCodeCamp للتمارين
• GitHub للمشاريع العملية
        `;
        
        addMessageToChat('assistant', learningPath);
    }

    // اقتراح تنظيم المحادثة
    suggestConversationOrganization() {
        addMessageToChat('assistant', 'سأقوم بتنظيم محادثتنا في أقسام لسهولة المتابعة...');
        
        // تحليل المحادثة وتقسيمها
        setTimeout(() => {
            const organizedSummary = smartSummarizer.generateConversationSummary('detailed');
            addMessageToChat('assistant', 'تم تنظيم المحادثة وإنشاء ملخص مفصل في منطقة العرض.');
        }, 1000);
    }

    // عرض الأدوات غير المستخدمة
    demonstrateUnusedTools(tools) {
        let demo = '🎯 **ميزات رائعة لم تجربها:**\n\n';
        
        tools.forEach(tool => {
            switch (tool) {
                case 'مشاركة الشاشة':
                    demo += '🖥️ **مشاركة الشاشة:** شارك شاشتك وسجل فيديوهات تعليمية\n';
                    break;
                case 'تحليل فيديو':
                    demo += '📹 **تحليل الفيديو:** حلل محتوى الفيديوهات واستخرج النقاط المهمة\n';
                    break;
                case 'عرض ثلاثي الأبعاد':
                    demo += '🎮 **العرض ثلاثي الأبعاد:** تفاعل مع نماذج ثلاثية الأبعاد\n';
                    break;
                case 'توليد ملخص':
                    demo += '📝 **توليد الملخصات:** احصل على ملخصات ذكية للمحادثات والمستندات\n';
                    break;
                case 'التفاعل الصوتي':
                    demo += '🎤 **التفاعل الصوتي:** تحدث معي بصوتك واستمع للردود\n';
                    break;
            }
        });
        
        demo += '\nجرب هذه الميزات لتحصل على تجربة أفضل!';
        addMessageToChat('assistant', demo);
    }

    // تطبيق تحسينات الأداء
    applyPerformanceImprovements() {
        addMessageToChat('assistant', 'جاري تطبيق تحسينات الأداء...');
        
        // محاكاة تحسينات
        setTimeout(() => {
            const improvements = [
                '⚡ تم تحسين سرعة الاستجابة',
                '🧠 تم تحديث نماذج الذكاء الاصطناعي',
                '💾 تم تحسين إدارة الذاكرة',
                '🔧 تم ضبط الإعدادات حسب استخدامك'
            ];
            
            let message = '✅ **تم تطبيق التحسينات:**\n\n';
            improvements.forEach(improvement => {
                message += improvement + '\n';
            });
            
            addMessageToChat('assistant', message);
        }, 2000);
    }

    // حفظ بيانات التعلم
    saveUserLearningData() {
        const learningData = {
            userBehavior: this.userBehavior,
            conversationPatterns: this.conversationPatterns,
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('assistantLearningData', JSON.stringify(learningData));
        console.log('💾 تم حفظ بيانات التعلم');
    }

    // تحميل بيانات التعلم
    loadUserLearningData() {
        const saved = localStorage.getItem('assistantLearningData');
        if (saved) {
            const data = JSON.parse(saved);
            this.userBehavior = data.userBehavior || {};
            this.conversationPatterns = data.conversationPatterns || [];
            console.log('📂 تم تحميل بيانات التعلم المحفوظة');
        }
    }
}

// إنشاء مثيل محرك الاقتراحات الذكية
const smartSuggestionEngine = new SmartSuggestionEngine();

// بدء المراقبة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    smartSuggestionEngine.loadUserLearningData();
    smartSuggestionEngine.startMonitoring();
    
    // حفظ البيانات عند إغلاق الصفحة
    window.addEventListener('beforeunload', function() {
        smartSuggestionEngine.saveUserLearningData();
    });
});

// تصدير المحرك للاستخدام العام
window.smartSuggestionEngine = smartSuggestionEngine;
