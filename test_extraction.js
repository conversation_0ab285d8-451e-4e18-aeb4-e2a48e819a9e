// اختبار استخراج الثغرات من البرومبت
console.log('🔍 اختبار استخراج الثغرات الجديد...');

// محاكاة البرومبت
const testPrompt = `
🧠 المهمة: أنت خبير Bug Bounty محترف متخصص في اكتشاف الثغرات الأمنية الحقيقية.

1. **ثغرات الحقن (Injection Vulnerabilities) - التحليل المتقدم:**
   - SQL Injection (Union-based, Boolean-based, Time-based, Error-based)
   - XSS (Reflected, Stored, DOM-based, Mutation XSS)
   - Command Injection (OS, Code, LDAP, XPath, Template)
   - NoSQL Injection (MongoDB, CouchDB, Redis)

2. **ثغرات المصادقة والتخويل - التحليل الشامل:**
   - Authentication Bypass (Weak passwords, Default credentials)
   - Session Management (Session fixation, hijacking, weak tokens)
   - Privilege Escalation (Horizontal, Vertical)
   - JWT Vulnerabilities (Weak secrets, algorithm confusion)
   - OAuth/SAML Misconfigurations

3. **ثغرات منطق الأعمال - فحص متعمق:**
   - IDOR (Insecure Direct Object References)
   - Race Conditions (Payment, Registration, File upload)
   - Business Logic Bypass (Workflow manipulation)
   - Price Manipulation (Negative values, Currency bypass)
   - Rate Limiting Issues

4. **ثغرات الشبكة والبنية - تحليل شامل:**
   - SSRF (Server-Side Request Forgery)
   - Open Redirects (Header injection, Parameter manipulation)
   - CORS Misconfigurations (Wildcard origins, Credential exposure)
   - Subdomain Takeover (DNS, CDN, Cloud services)
   - DNS Issues (Zone transfer, Cache poisoning)

5. **ثغرات العميل - فحص متقدم:**
   - CSRF (Cross-Site Request Forgery)
   - Clickjacking (UI redressing, Frame busting bypass)
   - DOM XSS (PostMessage, Hash manipulation)
   - WebSocket Vulnerabilities
   - Client-side Template Injection

6. **ثغرات الملفات والبيانات:**
   - File Upload (RCE, Path traversal, Content-type bypass)
   - XXE (XML External Entity)
   - Deserialization (Java, .NET, Python, PHP)
   - Path Traversal (Directory traversal, LFI, RFI)
   - Template Injection (Server-side, Client-side)

7. **ثغرات التشفير والأمان:**
   - Weak Cryptography (MD5, SHA1, Weak keys)
   - Insecure Random Number Generation
   - Certificate Issues (Self-signed, Expired, Weak algorithms)
   - Information Disclosure (Error messages, Debug info)
   - Security Headers (CSP, HSTS, X-Frame-Options)

8. **ثغرات API والخدمات:**
   - REST API Security (Authentication, Authorization)
   - GraphQL Vulnerabilities (Introspection, DoS)
   - SOAP Injection
   - API Rate Limiting
   - Microservices Security

9. **ثغرات متقدمة وغير تقليدية:**
   - Business Logic Flaws
   - Zero-day Potential
   - Advanced Persistent Threats
   - Social Engineering Vectors
   - Supply Chain Attacks
`;

// محاكاة دالة الاستخراج الجديدة
function extractAllTechniquesFromParagraph(paragraph) {
    const techniques = [];

    // 🔥 استخراج شامل لكل شيء في البرومبت - بدون قيود!
    const comprehensivePatterns = [
        // 1. استخراج أي كلمة تقنية (2-4 كلمات)
        /\b[A-Z][a-zA-Z]{2,}(?:\s+[A-Z][a-zA-Z]{2,}){0,3}\b/g,
        
        // 2. استخراج أي مصطلح أمني
        /\b(?:SQL|XSS|CSRF|SSRF|XXE|LFI|RFI|IDOR|SSTI|CSTI|LDAP|XPath|NoSQL|Command|Code|Template|DOM|Stored|Reflected|Blind|Time-based|Boolean-based|Union-based|Error-based|JWT|OAuth|SAML|API|REST|GraphQL|SOAP|JSON|XML|HTML|CSS|JavaScript|PHP|Python|Java)\b[^.!?؟]*?/gi,
        
        // 3. استخراج أي عبارة تحتوي على كلمات أمنية
        /\b(?:Injection|Attack|Vulnerability|Bypass|Exploit|Flaw|Issue|Security|Testing|Analysis|Assessment|Scanning|Penetration|Audit|Hacking|Cracking|Phishing|Malware|Virus|Trojan|Worm|Ransomware)\b[^.!?؟\n]*?/gi,
        
        // 4. استخراج أي مصطلح تقني (بدون قيود)
        /\b[A-Z][a-zA-Z0-9_-]{2,}(?:\s+[A-Z][a-zA-Z0-9_-]{2,})*\b/g,
        
        // 5. استخراج أي عبارة بين علامات ترقيم
        /[^\n.!?؟]{5,50}(?=\.|!|\?|؟|\n|$)/g,
        
        // 6. استخراج أي كلمة تبدأ بحرف كبير (أسماء التقنيات)
        /\b[A-Z][a-zA-Z]{3,}\b/g
    ];

    // تطبيق جميع الـ patterns
    comprehensivePatterns.forEach(pattern => {
        try {
            const matches = paragraph.match(pattern);
            if (matches) {
                techniques.push(...matches.map(match => match.trim()).filter(t => t.length > 2));
            }
        } catch (error) {
            console.warn('⚠️ خطأ في pattern:', error.message);
        }
    });

    // إزالة المكررات والتنظيف
    const uniqueTechniques = [...new Set(techniques)]
        .filter(t => t && t.length > 2 && t.length < 200)
        .map(t => t.replace(/[^\w\s\-_]/g, '').trim())
        .filter(t => t.length > 2);

    return uniqueTechniques;
}

// اختبار الاستخراج
const paragraphs = testPrompt.split(/\n\s*\n/);
let totalTechniques = 0;

console.log('📋 نتائج الاستخراج:');
paragraphs.forEach((paragraph, index) => {
    if (paragraph.trim().length > 50) {
        const techniques = extractAllTechniquesFromParagraph(paragraph);
        console.log(`\n📄 الفقرة ${index + 1}: ${techniques.length} تقنية`);
        console.log(`   أول 10 تقنيات: ${techniques.slice(0, 10).join(', ')}`);
        totalTechniques += techniques.length;
    }
});

console.log(`\n🎯 إجمالي التقنيات المستخرجة: ${totalTechniques}`);
console.log(`📈 مقارنة: النظام القديم كان يستخرج ~40 تقنية`);
console.log(`🔥 النظام الجديد يستخرج: ${totalTechniques} تقنية`);

if (totalTechniques > 100) {
    console.log('✅ نجح الإصلاح! النظام يستخرج المئات من التقنيات');
} else {
    console.log('❌ الإصلاح لم ينجح بالكامل');
}
