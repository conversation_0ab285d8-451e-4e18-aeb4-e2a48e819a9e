@echo off
echo 🐍 بدء تشغيل خدمة Python لالتقاط الصور...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 💡 قم بتثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python موجود

REM التحقق من المتطلبات وتثبيتها
echo 📦 فحص وتثبيت المتطلبات...
pip install flask flask-cors selenium webdriver-manager

REM تحميل ChromeDriver تلقائياً
echo 🔧 تحميل ChromeDriver...
python -c "from selenium import webdriver; from webdriver_manager.chrome import ChromeDriverManager; ChromeDriverManager().install()"

echo.
echo 🚀 بدء تشغيل الخدمة...
echo 📡 الخدمة ستعمل على: http://localhost:8000
echo 🔗 للاختبار: http://localhost:8000/health
echo.
echo ⚠️ لإيقاف الخدمة اضغط Ctrl+C
echo.

REM تشغيل الخدمة
python python_web_service.py

pause
