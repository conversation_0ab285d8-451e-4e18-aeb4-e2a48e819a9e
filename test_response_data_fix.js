// اختبار إصلاح البيانات في الصور بعد الاستغلال
console.log('🔍 اختبار إصلاح البيانات في الصور بعد الاستغلال...');

async function testResponseDataFix() {
    console.log('🚀 بدء اختبار إصلاح البيانات...');
    
    // اختبار ثغرة SQL Injection مع payload قوي
    const testData = {
        url: 'http://testphp.vulnweb.com',
        vulnerability_name: 'SQL_Injection_Response_Test',
        vulnerability_type: 'SQL_Injection',
        payload_data: "admin' UNION SELECT 1,2,3,database(),version() -- response_test",
        target_parameter: 'id',
        report_id: `response_fix_test_${Date.now()}`
    };
    
    console.log('📤 إرسال بيانات اختبار الاستجابة:');
    console.log(`   💉 Payload: ${testData.payload_data}`);
    console.log(`   🎯 Type: ${testData.vulnerability_type}`);
    console.log(`   🔧 Parameter: ${testData.target_parameter}`);
    
    try {
        const startTime = Date.now();
        
        const response = await fetch('http://localhost:8000/vulnerability_sequence', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        });
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`📡 حالة الاستجابة: ${response.status}`);
        console.log(`⏱️ مدة الطلب: ${duration}ms`);
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ نجح الطلب!');
            console.log(`📊 المراحل المكتملة: ${result.stages_completed?.length || 0}/${result.total_stages || 3}`);
            console.log(`📝 الرسالة: ${result.message}`);
            
            // فحص تفصيلي لكل مرحلة
            if (result.results) {
                console.log('\n📸 فحص تفصيلي للصور والبيانات:');
                
                ['before', 'during', 'after'].forEach(stage => {
                    console.log(`\n🔍 مرحلة ${stage.toUpperCase()}:`);
                    
                    if (result.results[stage]) {
                        const stageResult = result.results[stage];
                        
                        if (stageResult.success) {
                            console.log(`   ✅ نجح التقاط الصورة`);
                            console.log(`   📁 حجم الملف: ${stageResult.file_size || 0} bytes`);
                            console.log(`   📄 مسار الملف: ${stageResult.file_path || 'غير محدد'}`);
                            
                            // فحص البيانات المرسلة
                            if (stageResult.payload_used) {
                                console.log(`   💉 Payload مستخدم: ${stageResult.payload_used}`);
                                if (stageResult.payload_used === testData.payload_data) {
                                    console.log(`   🎯 Payload صحيح ومطابق!`);
                                } else {
                                    console.log(`   ⚠️ Payload مختلف!`);
                                }
                            } else {
                                console.log(`   ❌ لا يوجد payload في النتيجة`);
                            }
                            
                            if (stageResult.vulnerability_type) {
                                console.log(`   🔍 Type: ${stageResult.vulnerability_type}`);
                            }
                            
                            // فحص بيانات الاستجابة
                            if (stageResult.exploitation_details) {
                                console.log(`   📊 تفاصيل الاستغلال موجودة: نعم`);
                                console.log(`   📊 Payload في التفاصيل: ${stageResult.exploitation_details.payload || 'غير موجود'}`);
                            } else {
                                console.log(`   📊 تفاصيل الاستغلال: غير موجودة`);
                            }
                            
                            // فحص البيانات الإضافية
                            if (stageResult.url_with_payload) {
                                console.log(`   🔗 URL مع Payload: ${stageResult.url_with_payload}`);
                            }
                            
                        } else {
                            console.log(`   ❌ فشل: ${stageResult.error || 'خطأ غير محدد'}`);
                        }
                    } else {
                        console.log(`   ⚠️ لا توجد نتيجة لهذه المرحلة`);
                    }
                });
                
                // فحص خاص لمرحلة AFTER
                if (result.results.after && result.results.after.success) {
                    console.log('\n🔥 فحص خاص لمرحلة AFTER (بعد الاستغلال):');
                    const afterResult = result.results.after;
                    
                    console.log(`   📸 حجم صورة AFTER: ${afterResult.file_size} bytes`);
                    console.log(`   💉 Payload في AFTER: ${afterResult.payload_used || 'غير موجود'}`);
                    console.log(`   🎯 Type في AFTER: ${afterResult.vulnerability_type || 'غير موجود'}`);
                    
                    if (afterResult.file_size > 50000) {
                        console.log(`   ✅ حجم الصورة جيد (${(afterResult.file_size / 1024).toFixed(1)}KB)`);
                    } else {
                        console.log(`   ⚠️ حجم الصورة صغير (${(afterResult.file_size / 1024).toFixed(1)}KB)`);
                    }
                }
                
            } else {
                console.log('❌ لا توجد نتائج في الاستجابة');
            }
            
        } else {
            const errorText = await response.text();
            console.log(`❌ فشل الطلب: ${response.status}`);
            console.log(`📄 تفاصيل الخطأ: ${errorText}`);
        }
        
    } catch (error) {
        console.error(`❌ خطأ في الطلب: ${error.message}`);
    }
    
    console.log('\n🎯 ملخص الاختبار:');
    console.log('✅ تم اختبار إصلاح البيانات في الصور');
    console.log('📁 تحقق من مجلد screenshots لرؤية الصور');
    console.log('📋 تحقق من سجلات سيرفر Python لرؤية v4_real_data');
    console.log('🔍 ابحث عن "v4_real_data" في السجلات لرؤية البيانات الحقيقية');
}

// تشغيل الاختبار
testResponseDataFix()
    .then(() => {
        console.log('\n🎉 انتهى اختبار إصلاح البيانات!');
    })
    .catch(error => {
        console.error('❌ خطأ في اختبار إصلاح البيانات:', error);
    });
