#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حقيقي للنظام v4 بدون أي بيانات يدوية
"""

import sys
import asyncio
import os
from pathlib import Path

# إضافة المسار للوحدات
sys.path.append('.')

from assets.modules.bugbounty.screenshot_service import ScreenshotService

async def test_real_v4_system():
    """اختبار حقيقي للنظام v4 التلقائي الديناميكي بدون أي بيانات يدوية"""
    
    print("🔥 اختبار حقيقي للنظام v4 التلقائي الديناميكي...")
    print("📋 بدون أي بيانات يدوية أو افتراضية")
    print("🤖 النظام سيعمل تلقائياً حسب الاستجابة الحقيقية من v4")
    
    service = ScreenshotService()
    
    try:
        # اختبار 1: ثغرات الحقن (Injection-based)
        print("\n" + "="*80)
        print("🔥 اختبار الثغرات القائمة على الحقن (Injection-based)")
        print("="*80)
        
        injection_tests = [
            {
                'name': 'SQL Injection Test',
                'url': 'https://httpbin.org/get?id=1',
                'payload': "' OR 1=1 --",
                'type': 'SQL Injection'
            },
            {
                'name': 'XSS Test',
                'url': 'https://httpbin.org/get?search=test',
                'payload': '<script>alert("XSS")</script>',
                'type': 'Cross-Site Scripting'
            },
            {
                'name': 'Command Injection Test',
                'url': 'https://httpbin.org/get?cmd=ping',
                'payload': '; whoami',
                'type': 'Command Injection'
            }
        ]
        
        injection_results = []
        
        for i, test in enumerate(injection_tests):
            print(f"\n📸 اختبار حقن {i+1}: {test['name']}")
            print(f"   🎯 النوع: {test['type']}")
            print(f"   💉 Payload: {test['payload']}")
            print(f"   🔗 URL: {test['url']}")
            print("   🤖 النظام سيحدد التأثيرات تلقائياً حسب الاستجابة...")
            
            # اختبار بدون أي بيانات يدوية - فقط النظام v4
            result = await service.capture_with_playwright(
                url=test['url'],
                filename=f'real_injection_{i+1}_{test["name"].replace(" ", "_").lower()}',
                stage='after',
                report_id=f'real_injection_{i+1}',
                vulnerability_name=test['name'],
                payload_data=test['payload'],
                vulnerability_type=test['type'],
                # بدون v4_data أو v4_real_data - النظام سيعمل تلقائياً
            )
            
            if result and result.get('success'):
                image_path = result.get('path')
                file_size = result.get('file_size', 0)
                
                print(f"   ✅ تم التقاط الصورة: {os.path.basename(image_path)}")
                print(f"   📊 حجم الملف: {file_size:,} bytes")
                
                injection_results.append({
                    'test': test['name'],
                    'type': test['type'],
                    'success': True,
                    'image_path': image_path,
                    'file_size': file_size
                })
            else:
                print(f"   ❌ فشل في التقاط صورة {test['name']}")
                injection_results.append({
                    'test': test['name'],
                    'type': test['type'],
                    'success': False
                })
            
            await asyncio.sleep(3)
        
        # اختبار 2: ثغرات غير الحقن (Non-injection based)
        print("\n" + "="*80)
        print("🔥 اختبار الثغرات غير القائمة على الحقن (Non-injection based)")
        print("="*80)
        
        non_injection_tests = [
            {
                'name': 'Directory Traversal Test',
                'url': 'https://httpbin.org/get?file=config.txt',
                'payload': '../../../etc/passwd',
                'type': 'Directory Traversal'
            },
            {
                'name': 'IDOR Test',
                'url': 'https://httpbin.org/get?user_id=123',
                'payload': '456',
                'type': 'Insecure Direct Object Reference'
            },
            {
                'name': 'Open Redirect Test',
                'url': 'https://httpbin.org/get?redirect=home',
                'payload': 'http://evil.com',
                'type': 'Open Redirect'
            },
            {
                'name': 'Information Disclosure Test',
                'url': 'https://httpbin.org/get?debug=false',
                'payload': 'true',
                'type': 'Information Disclosure'
            }
        ]
        
        non_injection_results = []
        
        for i, test in enumerate(non_injection_tests):
            print(f"\n📸 اختبار غير حقن {i+1}: {test['name']}")
            print(f"   🎯 النوع: {test['type']}")
            print(f"   🔧 Parameter: {test['payload']}")
            print(f"   🔗 URL: {test['url']}")
            print("   🤖 النظام سيحدد التأثيرات تلقائياً حسب نوع الثغرة...")
            
            # اختبار بدون أي بيانات يدوية - فقط النظام v4
            result = await service.capture_with_playwright(
                url=test['url'],
                filename=f'real_non_injection_{i+1}_{test["name"].replace(" ", "_").lower()}',
                stage='after',
                report_id=f'real_non_injection_{i+1}',
                vulnerability_name=test['name'],
                payload_data=test['payload'],
                vulnerability_type=test['type'],
                # بدون v4_data أو v4_real_data - النظام سيعمل تلقائياً
            )
            
            if result and result.get('success'):
                image_path = result.get('path')
                file_size = result.get('file_size', 0)
                
                print(f"   ✅ تم التقاط الصورة: {os.path.basename(image_path)}")
                print(f"   📊 حجم الملف: {file_size:,} bytes")
                
                non_injection_results.append({
                    'test': test['name'],
                    'type': test['type'],
                    'success': True,
                    'image_path': image_path,
                    'file_size': file_size
                })
            else:
                print(f"   ❌ فشل في التقاط صورة {test['name']}")
                non_injection_results.append({
                    'test': test['name'],
                    'type': test['type'],
                    'success': False
                })
            
            await asyncio.sleep(3)
        
        # عرض النتائج النهائية
        print("\n" + "="*100)
        print("🎯 ملخص نتائج اختبار النظام v4 التلقائي الديناميكي:")
        print("="*100)
        
        all_results = injection_results + non_injection_results
        successful_tests = [r for r in all_results if r['success']]
        failed_tests = [r for r in all_results if not r['success']]
        
        print(f"✅ اختبارات ناجحة: {len(successful_tests)}/{len(all_results)}")
        print(f"❌ اختبارات فاشلة: {len(failed_tests)}/{len(all_results)}")
        
        if successful_tests:
            print("\n📸 الصور المُنشأة بالنظام التلقائي:")
            
            print("\n🔥 ثغرات الحقن (Injection-based):")
            injection_successful = [r for r in injection_results if r['success']]
            for result in injection_successful:
                print(f"   ✅ {result['test']}")
                print(f"      📁 {os.path.basename(result['image_path'])}")
                print(f"      📊 {result['file_size']:,} bytes")
            
            print("\n🔧 ثغرات غير الحقن (Non-injection based):")
            non_injection_successful = [r for r in non_injection_results if r['success']]
            for result in non_injection_successful:
                print(f"   ✅ {result['test']}")
                print(f"      📁 {os.path.basename(result['image_path'])}")
                print(f"      📊 {result['file_size']:,} bytes")
        
        if failed_tests:
            print("\n❌ الاختبارات الفاشلة:")
            for result in failed_tests:
                print(f"   ❌ {result['test']} ({result['type']})")
        
        print("\n📂 مجلد الصور: assets/modules/bugbounty/screenshots/")
        print("🔍 افتح الصور للتحقق من:")
        print("   ✅ التأثيرات التلقائية حسب نوع الثغرة")
        print("   ✅ قسم REAL SERVER RESPONSE مع البيانات الحقيقية من v4")
        print("   ✅ الدلائل الديناميكية حسب الاستجابة")
        print("   ✅ التغيرات التلقائية في الواجهة")
        
        # فتح أول صورة ناجحة للفحص
        if successful_tests:
            first_image = successful_tests[0]['image_path']
            import webbrowser
            webbrowser.open(f"file:///{os.path.abspath(first_image)}")
            print(f"\n🌐 تم فتح أول صورة للفحص: {os.path.basename(first_image)}")
            print("🤖 تحقق من عمل النظام التلقائي الديناميكي!")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await service.cleanup()
        print("🔒 تم تنظيف الموارد")

if __name__ == "__main__":
    asyncio.run(test_real_v4_system())
