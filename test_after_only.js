const http = require('http');

async function testAfterOnly() {
    console.log('🔥 اختبار مرحلة after فقط...');
    
    const testData = {
        url: 'http://testphp.vulnweb.com/login.php',
        report_id: 'after_only_test',
        vulnerability_name: 'After Test XSS',
        vulnerability_type: 'Cross-Site Scripting',
        stage: 'sequence',
        payload: '<script>alert("AFTER_TEST")</script>',
        width: 1920,
        height: 1080,
        wait_time: 3,
        // إضافة البيانات الحقيقية للاختبار
        actual_response_content: `🔥 REAL XSS VULNERABILITY RESPONSE 🔥
═══════════════════════════════════════════════════════════════════════════════

📡 REQUEST DETAILS:
   🔗 Target URL: http://testphp.vulnweb.com/login.php
   💉 Payload Used: <script>alert("AFTER_TEST")</script>
   🎯 Vulnerability Type: Cross-Site Scripting
   ⏰ Timestamp: ${new Date().toISOString()}

📥 RESPONSE DETAILS:
   📊 Status Code: HTTP 200 OK
   📊 Response Size: 2,847 characters (2.78 KB)
   📊 Headers Count: 12 headers

📋 HTTP HEADERS:
Content-Type: text/html; charset=UTF-8
Server: nginx/1.19.0
X-Powered-By: PHP/5.6.40
Set-Cookie: PHPSESSID=abc123def456; path=/
Cache-Control: no-cache, must-revalidate
Pragma: no-cache

📄 RESPONSE BODY:
═══════════════════════════════════════════════════════════════════════════════
<!DOCTYPE html>
<html>
<head>
    <title>Login Page - Vulnerable</title>
</head>
<body>
    <h1>Login Form</h1>
    <form method="POST" action="/login.php">
        <input type="text" name="username" placeholder="Username">
        <input type="password" name="password" placeholder="Password">
        <input type="submit" value="Login">
    </form>

    <!-- XSS VULNERABILITY DETECTED -->
    <script>alert("AFTER_TEST")</script>
    <!-- PAYLOAD EXECUTED SUCCESSFULLY -->

    <div class="error">Invalid credentials</div>
</body>
</html>
═══════════════════════════════════════════════════════════════════════════════

🔥 EXPLOITATION SUMMARY:
   ✅ Payload delivered successfully: <script>alert("AFTER_TEST")</script>
   ✅ Server responded with vulnerable HTML
   ✅ XSS vulnerability confirmed through payload injection
   ✅ JavaScript code will execute in victim browsers
   📊 Total response size: 2,847 characters

🔍 SECURITY IMPACT:
   • Cross-Site Scripting (XSS) vulnerability confirmed
   • Arbitrary JavaScript execution possible
   • Session hijacking potential
   • Data theft and account compromise possible
   • Client-side code injection successful

═══════════════════════════════════════════════════════════════════════════════`,
        exploitation_results: [
            'XSS payload injection successful',
            'JavaScript code executed in browser context',
            'Vulnerable parameter: search field',
            'Attack vector: Reflected XSS via GET parameter',
            'Impact: High - Full client-side code execution',
            'Recommendation: Implement proper input sanitization'
        ],
        vulnerability_impact_data: 'High severity Cross-Site Scripting vulnerability allows arbitrary JavaScript execution in victim browsers. This can lead to session hijacking, credential theft, and complete account compromise. The vulnerability exists in the search parameter and allows reflected XSS attacks.'
    };

    try {
        console.log('📤 إرسال طلب مرحلة after...');
        
        const postData = JSON.stringify(testData);
        const options = {
            hostname: 'localhost',
            port: 8000,
            path: '/capture',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const response = await new Promise((resolve, reject) => {
            const req = http.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => data += chunk);
                res.on('end', () => {
                    try {
                        resolve({
                            status: res.statusCode,
                            data: JSON.parse(data)
                        });
                    } catch (e) {
                        resolve({
                            status: res.statusCode,
                            data: data
                        });
                    }
                });
            });
            
            req.on('error', reject);
            req.write(postData);
            req.end();
        });

        console.log('📡 حالة الاستجابة:', response.status);
        console.log('📄 تفاصيل الاستجابة:', JSON.stringify(response.data, null, 2));

    } catch (error) {
        console.log('❌ خطأ في الطلب:', error.message);
    }

    console.log('🎉 انتهى اختبار مرحلة after!');
}

testAfterOnly();
