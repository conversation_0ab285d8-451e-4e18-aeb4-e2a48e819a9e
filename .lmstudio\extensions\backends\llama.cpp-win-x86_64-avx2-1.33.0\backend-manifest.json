{"name": "llama.cpp-win-x86_64-avx2", "domains": ["llm", "embedding"], "version": "1.33.0", "engine": "llama.cpp", "target_libraries": [{"name": "llm_engine.node", "type": "llm_engine", "version": "0.1.2"}, {"name": "liblmstudio_bindings.node", "type": "liblmstudio", "version": "0.2.26"}], "platform": "win", "cpu": {"architecture": "x86_64", "instruction_set_extensions": ["AVX2"]}, "supported_model_formats": ["gguf"], "manifest_version": "3", "vendor_lib_package_names": ["win-llama-cpu-x86-vendor-v1"]}