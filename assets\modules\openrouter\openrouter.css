/**
 * OpenRouter Integration Styles
 * تنسيقات نظام تكامل OpenRouter
 */

/* أنيميشن التحميل */
@keyframes openrouter-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes openrouter-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes openrouter-slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* نافذة OpenRouter الرئيسية */
.openrouter-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
    animation: openrouter-slideIn 0.3s ease;
}

.openrouter-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    color: white;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    position: relative;
}

/* أزرار OpenRouter */
.openrouter-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    margin: 10px 5px;
    transition: all 0.3s ease;
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.openrouter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.openrouter-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* مؤشر الحالة */
.openrouter-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #95a5a6;
    position: absolute;
    top: 5px;
    right: 5px;
    transition: all 0.3s ease;
}

.openrouter-status-indicator.connected {
    background: #2ecc71;
    animation: openrouter-pulse 2s infinite;
}

.openrouter-status-indicator.connecting {
    background: #f39c12;
    animation: openrouter-spin 1s linear infinite;
}

.openrouter-status-indicator.error {
    background: #e74c3c;
}

/* الزر العائم */
.openrouter-floating-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 9999;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.openrouter-floating-btn:hover {
    transform: scale(1.1);
}

/* شبكة النماذج */
.openrouter-models-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.openrouter-model-card {
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(255,255,255,0.2);
    color: white;
    padding: 15px;
    border-radius: 10px;
    cursor: pointer;
    text-align: right;
    transition: all 0.3s ease;
    font-size: 13px;
}

.openrouter-model-card:hover {
    background: rgba(255,255,255,0.2);
    border-color: #ffd700;
    transform: translateY(-2px);
}

.openrouter-model-card.selected {
    border-color: #2ecc71;
    background: rgba(46, 204, 113, 0.2);
}

.openrouter-model-name {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 14px;
}

.openrouter-model-id {
    opacity: 0.7;
    font-size: 11px;
    word-break: break-all;
}

/* رسائل الحالة */
.openrouter-status-message {
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
    text-align: center;
    font-weight: bold;
}

.openrouter-status-message.success {
    background: rgba(46, 204, 113, 0.8);
    color: white;
}

.openrouter-status-message.error {
    background: rgba(231, 76, 60, 0.8);
    color: white;
}

.openrouter-status-message.info {
    background: rgba(52, 152, 219, 0.8);
    color: white;
}

.openrouter-status-message.warning {
    background: rgba(243, 156, 18, 0.8);
    color: white;
}

/* حقول الإدخال */
.openrouter-input {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 10px;
    background: rgba(255,255,255,0.9);
    color: #333;
    font-size: 14px;
    margin: 10px 0;
    transition: all 0.3s ease;
}

.openrouter-input:focus {
    outline: none;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    background: rgba(255,255,255,1);
}

/* مؤشر التحميل */
.openrouter-loading {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: openrouter-spin 1s ease-in-out infinite;
}

/* فئات النماذج */
.openrouter-category {
    margin-bottom: 25px;
}

.openrouter-category-title {
    margin-bottom: 15px;
    color: #ffd700;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* إشعارات النجاح */
.openrouter-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2ecc71;
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    z-index: 10001;
    animation: openrouter-slideIn 0.5s ease;
}

.openrouter-notification.error {
    background: #e74c3c;
}

.openrouter-notification.warning {
    background: #f39c12;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .openrouter-content {
        width: 95%;
        padding: 20px;
        margin: 10px;
    }
    
    .openrouter-models-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .openrouter-floating-btn {
        width: 50px;
        height: 50px;
        font-size: 16px;
        bottom: 15px;
        left: 15px;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .openrouter-input {
        background: rgba(255,255,255,0.1);
        color: white;
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .openrouter-input::placeholder {
        color: rgba(255,255,255,0.6);
    }
}

/* تأثيرات خاصة */
.openrouter-glow {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.openrouter-shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* تحسينات الوصولية */
.openrouter-btn:focus,
.openrouter-model-card:focus,
.openrouter-input:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* تحسينات الطباعة */
@media print {
    .openrouter-modal,
    .openrouter-floating-btn {
        display: none !important;
    }
}
