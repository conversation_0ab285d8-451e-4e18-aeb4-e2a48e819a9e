#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def debug_data_flow():
    """تتبع تدفق البيانات من النظام v4 إلى العرض"""
    
    print('🔍 تتبع تدفق البيانات من النظام v4...')
    
    # بيانات اختبار بسيطة
    test_data = {
        'url': 'http://testphp.vulnweb.com/artists.php?artist=1',
        'vulnerability_name': 'SQL_Debug_Test',
        'vulnerability_type': 'SQL Injection',
        'payload_data': "1' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --",
        'target_parameter': 'artist',
        'stage': 'after'
    }
    
    print(f'📊 إرسال بيانات بسيطة بدون v4_data:')
    print(f'   - URL: {test_data["url"]}')
    print(f'   - Vulnerability: {test_data["vulnerability_name"]}')
    print(f'   - Payload: {test_data["payload_data"]}')
    
    try:
        print('\n🚀 إرسال الطلب...')
        start_time = time.time()
        
        response = requests.post('http://localhost:8000/v4_website', 
                               json=test_data, 
                               timeout=120)
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f'⏱️ وقت الاستجابة: {response_time:.2f} ثانية')
        print(f'📊 كود الاستجابة: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            print(f'✅ نجح الاختبار!')
            
            # فحص النتائج التفصيلية
            print(f'\n📊 تحليل النتائج:')
            print(f'   - Success: {result.get("success", False)}')
            print(f'   - Total Stages: {result.get("total_stages", 0)}')
            print(f'   - Successful Stages: {result.get("successful_stages", 0)}')
            
            if 'stages_results' in result:
                stages = result['stages_results']
                print(f'\n📸 نتائج الصور:')
                for stage, stage_result in stages.items():
                    if stage_result.get('success'):
                        file_size = stage_result.get('file_size', 0)
                        print(f'   ✅ {stage}: {file_size} bytes')
                        
                        # فحص مرحلة after بالتفصيل
                        if stage == 'after':
                            print(f'   🔍 تفاصيل مرحلة AFTER:')
                            for key, value in stage_result.items():
                                if key not in ['success', 'file_size', 'base64']:
                                    print(f'      - {key}: {value}')
                    else:
                        error = stage_result.get('error', 'Unknown error')
                        print(f'   ❌ {stage}: فشل - {error}')
            
            # عرض النتيجة الكاملة
            print(f'\n📄 النتيجة الكاملة (مختصرة):')
            result_copy = result.copy()
            # إزالة base64 للعرض
            if 'stages_results' in result_copy:
                for stage in result_copy['stages_results']:
                    if 'base64' in result_copy['stages_results'][stage]:
                        result_copy['stages_results'][stage]['base64'] = '[BASE64_DATA_REMOVED]'
            
            print(json.dumps(result_copy, indent=2, ensure_ascii=False))
            
        else:
            print(f'❌ فشل الاختبار: {response.status_code}')
            print(f'📄 تفاصيل الخطأ: {response.text}')
            
    except Exception as e:
        print(f'❌ خطأ في الاختبار: {e}')

if __name__ == '__main__':
    debug_data_flow()
