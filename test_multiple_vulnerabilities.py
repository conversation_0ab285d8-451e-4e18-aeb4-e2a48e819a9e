#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لثغرات متعددة مع استجابات حقيقية مختلفة
"""

import sys
import asyncio
import os
from pathlib import Path

# إضافة المسار للوحدات
sys.path.append('.')

from assets.modules.bugbounty.screenshot_service import ScreenshotService

async def test_multiple_vulnerabilities():
    """اختبار شامل لثغرات متعددة مع استجابات حقيقية"""
    
    print("🔥 اختبار شامل لثغرات متعددة مع استجابات حقيقية...")
    
    service = ScreenshotService()
    
    try:
        # تعريف ثغرات متعددة مع استجابات حقيقية مختلفة
        vulnerabilities = [
            {
                'name': 'SQL Injection - Union Based',
                'type': 'SQL Injection',
                'payload': "' UNION SELECT 1,2,3,4,5,6,7,8,9,10 --",
                'url': 'https://httpbin.org/get?id=1',
                'real_response': '''HTTP/1.1 500 Internal Server Error
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubuntu)

MySQL Error: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'UNION SELECT 1,2,3,4,5,6,7,8,9,10 --' at line 1

Query: SELECT * FROM users WHERE id = '' UNION SELECT 1,2,3,4,5,6,7,8,9,10 --'
Error Code: 1064
Affected Rows: 0

Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)

Warning: mysql_fetch_array() expects parameter 1 to be resource, boolean given in /var/www/html/vulnerable.php on line 42

🔥 SQL INJECTION CONFIRMED
🔥 UNION-BASED ATTACK SUCCESSFUL
🔥 DATABASE STRUCTURE EXPOSED'''
            },
            {
                'name': 'XSS - Reflected',
                'type': 'Cross-Site Scripting',
                'payload': '<script>alert("XSS_VULNERABILITY_CONFIRMED")</script>',
                'url': 'https://httpbin.org/get?search=test',
                'real_response': '''HTTP/1.1 200 OK
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubuntu)

<!DOCTYPE html>
<html>
<head><title>Search Results</title></head>
<body>
<h1>Search Results for: <script>alert("XSS_VULNERABILITY_CONFIRMED")</script></h1>
<p>You searched for: <script>alert("XSS_VULNERABILITY_CONFIRMED")</script></p>
<div class="results">
    No results found for your search query.
</div>
</body>
</html>

🔥 XSS VULNERABILITY DETECTED
🔥 SCRIPT INJECTION SUCCESSFUL
🔥 USER INPUT NOT SANITIZED
🔥 REFLECTED XSS CONFIRMED'''
            },
            {
                'name': 'Command Injection',
                'type': 'Command Injection',
                'payload': '; cat /etc/passwd',
                'url': 'https://httpbin.org/get?cmd=whoami',
                'real_response': '''HTTP/1.1 200 OK
Content-Type: text/plain

Command executed: whoami; cat /etc/passwd
Output:
www-data
root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
sync:x:4:65534:sync:/bin:/bin/sync
games:x:5:60:games:/usr/games:/usr/sbin/nologin
man:x:6:12:man:/var/cache/man:/usr/sbin/nologin
lp:x:7:7:lp:/var/spool/lpd:/usr/sbin/nologin
mail:x:8:8:mail:/var/mail:/usr/sbin/nologin
news:x:9:9:news:/var/spool/news:/usr/sbin/nologin
www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin

🔥 COMMAND INJECTION SUCCESSFUL
🔥 SYSTEM FILES ACCESSED
🔥 PRIVILEGE ESCALATION POSSIBLE'''
            },
            {
                'name': 'Local File Inclusion (LFI)',
                'type': 'File Inclusion',
                'payload': '../../../etc/passwd',
                'url': 'https://httpbin.org/get?file=index.php',
                'real_response': '''HTTP/1.1 200 OK
Content-Type: text/plain

File: ../../../etc/passwd
Content:
root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
sync:x:4:65534:sync:/bin:/bin/sync
games:x:5:60:games:/usr/games:/usr/sbin/nologin
man:x:6:12:man:/var/cache/man:/usr/sbin/nologin
lp:x:7:7:lp:/var/spool/lpd:/usr/sbin/nologin
mail:x:8:8:mail:/var/mail:/usr/sbin/nologin
news:x:9:9:news:/var/spool/news:/usr/sbin/nologin
uucp:x:10:10:uucp:/var/spool/uucp:/usr/sbin/nologin
proxy:x:13:13:proxy:/bin:/usr/sbin/nologin
www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin

🔥 LOCAL FILE INCLUSION CONFIRMED
🔥 SENSITIVE FILES EXPOSED
🔥 DIRECTORY TRAVERSAL SUCCESSFUL'''
            },
            {
                'name': 'LDAP Injection',
                'type': 'LDAP Injection',
                'payload': '*)(uid=*))(|(uid=*',
                'url': 'https://httpbin.org/get?username=admin',
                'real_response': '''HTTP/1.1 500 Internal Server Error
Content-Type: text/html; charset=UTF-8
Server: Apache/2.4.41 (Ubuntu)

LDAP Error: Invalid DN syntax
Query: (&(objectClass=user)(uid=*)(uid=*))(|(uid=*))
Error Code: 34
Description: Invalid DN syntax

LDAP bind failed: Invalid credentials
Server: ldap://localhost:389
Base DN: dc=example,dc=com

🔥 LDAP INJECTION DETECTED
🔥 AUTHENTICATION BYPASS POSSIBLE
🔥 DIRECTORY ENUMERATION SUCCESSFUL'''
            },
            {
                'name': 'XML External Entity (XXE)',
                'type': 'XXE Injection',
                'payload': '<!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><foo>&xxe;</foo>',
                'url': 'https://httpbin.org/post',
                'real_response': '''HTTP/1.1 200 OK
Content-Type: application/xml

<?xml version="1.0" encoding="UTF-8"?>
<response>
    <status>error</status>
    <message>XML parsing error</message>
    <details>
        External entity reference detected:
        file:///etc/passwd
        
        Content:
        root:x:0:0:root:/root:/bin/bash
        daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
        bin:x:2:2:bin:/bin:/usr/sbin/nologin
        sys:x:3:3:sys:/dev:/usr/sbin/nologin
        sync:x:4:65534:sync:/bin:/bin/sync
        games:x:5:60:games:/usr/games:/usr/sbin/nologin
    </details>
</response>

🔥 XXE VULNERABILITY CONFIRMED
🔥 EXTERNAL ENTITY PROCESSED
🔥 FILE SYSTEM ACCESS ACHIEVED'''
            }
        ]
        
        print(f"\n🎯 سيتم اختبار {len(vulnerabilities)} ثغرات مختلفة...")
        
        results = []
        
        for i, vuln in enumerate(vulnerabilities):
            print(f"\n📸 اختبار {i+1}/{len(vulnerabilities)}: {vuln['name']}")
            print(f"   🎯 النوع: {vuln['type']}")
            print(f"   💉 Payload: {vuln['payload'][:50]}...")
            print(f"   🔗 URL: {vuln['url']}")
            
            # إعداد البيانات الحقيقية للثغرة
            real_server_data = {
                'actual_response_content': vuln['real_response'],
                'response_data': f'{vuln["type"]} error detected',
                'full_response_content': f'Complete {vuln["type"]} response with vulnerability confirmation'
            }
            
            result = await service.capture_with_playwright(
                url=vuln['url'],
                filename=f'multi_test_{i+1}_{vuln["name"].replace(" ", "_").replace("-", "_").lower()}',
                stage='after',
                report_id=f'multi_test_{i+1}',
                vulnerability_name=vuln['name'],
                payload_data=vuln['payload'],
                vulnerability_type=vuln['type'],
                v4_data={
                    'response': f'{vuln["type"]} detected in multi test',
                    'server_response': f'Error in {vuln["name"]} during multi test'
                },
                v4_real_data=real_server_data
            )
            
            if result and result.get('success'):
                image_path = result.get('path')
                file_size = result.get('file_size', 0)
                
                print(f"   ✅ تم التقاط الصورة: {os.path.basename(image_path)}")
                print(f"   📊 حجم الملف: {file_size:,} bytes")
                
                results.append({
                    'vulnerability': vuln['name'],
                    'type': vuln['type'],
                    'success': True,
                    'image_path': image_path,
                    'file_size': file_size,
                    'payload': vuln['payload']
                })
                
                # التحقق من وجود الملف
                if os.path.exists(image_path):
                    print(f"   ✅ ملف الصورة موجود")
                    
                    # حفظ نسخة مصغرة للفحص
                    try:
                        from PIL import Image
                        img = Image.open(image_path)
                        thumbnail_path = image_path.replace('.png', '_multi_thumbnail.png')
                        img.thumbnail((800, 600))
                        img.save(thumbnail_path)
                        print(f"   💾 نسخة مصغرة: {os.path.basename(thumbnail_path)}")
                        
                    except Exception as e:
                        print(f"   ⚠️ خطأ في معالجة الصورة: {e}")
                        
                else:
                    print(f"   ❌ ملف الصورة غير موجود")
                    results[-1]['success'] = False
            else:
                print(f"   ❌ فشل في التقاط صورة {vuln['name']}")
                results.append({
                    'vulnerability': vuln['name'],
                    'type': vuln['type'],
                    'success': False,
                    'error': 'فشل في التقاط الصورة'
                })
            
            # انتظار بين الاختبارات
            await asyncio.sleep(3)
        
        # عرض النتائج النهائية
        print("\n" + "="*80)
        print("🎯 ملخص نتائج اختبار الثغرات المتعددة:")
        print("="*80)
        
        successful_tests = [r for r in results if r['success']]
        failed_tests = [r for r in results if not r['success']]
        
        print(f"✅ اختبارات ناجحة: {len(successful_tests)}/{len(results)}")
        print(f"❌ اختبارات فاشلة: {len(failed_tests)}/{len(results)}")
        
        if successful_tests:
            print("\n📸 الصور المُنشأة بنجاح:")
            for result in successful_tests:
                print(f"   🔥 {result['vulnerability']}")
                print(f"      📁 {os.path.basename(result['image_path'])}")
                print(f"      📊 {result['file_size']:,} bytes")
                print(f"      💉 {result['payload'][:30]}...")
                print()
        
        if failed_tests:
            print("\n❌ الاختبارات الفاشلة:")
            for result in failed_tests:
                print(f"   ❌ {result['vulnerability']}: {result.get('error', 'خطأ غير محدد')}")
        
        print("\n📂 مجلد الصور: assets/modules/bugbounty/screenshots/")
        print("🔍 افتح الصور للتحقق من ظهور قسم 'REAL SERVER RESPONSE' لكل ثغرة")
        
        # فتح أول صورة ناجحة للفحص
        if successful_tests:
            first_image = successful_tests[0]['image_path']
            import webbrowser
            webbrowser.open(f"file:///{os.path.abspath(first_image)}")
            print(f"🌐 تم فتح أول صورة للفحص: {os.path.basename(first_image)}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await service.cleanup()
        print("🔒 تم تنظيف الموارد")

if __name__ == "__main__":
    asyncio.run(test_multiple_vulnerabilities())
