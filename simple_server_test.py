#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للسيرفر الحقيقي - فحص البيانات فقط
"""

import requests
import json
import time
from datetime import datetime

def test_server_data_only():
    """اختبار البيانات فقط بدون صور"""
    server_url = 'http://localhost:8000'
    
    print('🔍 اختبار البيانات الحقيقية للسيرفر v4')
    print('=' * 60)
    
    # التحقق من صحة السيرفر
    try:
        response = requests.get(f'{server_url}/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f'✅ السيرفر يعمل: {data.get("service", "Unknown")}')
        else:
            print(f'❌ السيرفر لا يعمل: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ خطأ في الاتصال: {e}')
        return False
    
    # إنشاء بيانات v4 حقيقية شاملة
    v4_real_data = {
        'test_results': 'Real XSS vulnerability test results from v4 system - This is comprehensive test data that should appear in SERVER RESPONSE section',
        'exploitation_status': 'XSS exploitation successful - Payload executed and vulnerability confirmed',
        'verification_proof': 'Verified XSS vulnerability with payload: <script>alert("XSS_TEST_SUCCESS")</script> - Server responded with vulnerable behavior',
        'response_data': 'Complete HTTP response data for XSS vulnerability - Headers: Content-Type: text/html, Status: 200 OK, Body contains vulnerable script execution',
        'error_messages': [],
        'success_indicators': [
            'XSS payload executed successfully in browser context',
            'Server responded with vulnerable behavior and script injection',
            f'Exploitation confirmed at {datetime.now()}',
            'JavaScript alert() function triggered successfully',
            'DOM manipulation successful - script tags injected'
        ],
        'vulnerability_meta': {
            'type': 'XSS',
            'severity': 'Critical',
            'confidence': 'High',
            'impact': 'Critical XSS vulnerability detected - Full script execution possible',
            'remediation': 'Fix XSS vulnerability by proper input validation and output encoding',
            'cvss_score': '9.6',
            'affected_parameter': 'user_input',
            'injection_point': 'GET parameter'
        },
        'original_v4_data': {
            'scan_id': f'v4_scan_{int(time.time())}',
            'target_url': 'https://httpbin.org/html',
            'payload_used': '<script>alert("XSS_TEST_SUCCESS")</script>',
            'vulnerability_type': 'XSS',
            'timestamp': datetime.now().isoformat(),
            'scanner_version': 'v4.0.0',
            'detection_method': 'XSS_detection_engine',
            'request_method': 'GET',
            'response_time': '0.234s',
            'payload_length': 45
        }
    }
    
    # البيانات التي يرسلها النظام v4 للسيرفر
    request_data = {
        'url': 'https://httpbin.org/html',
        'filename': 'xss_data_test',
        'report_id': f'data_test_{int(time.time())}',
        'vulnerability_name': 'XSS Data Test',
        'vulnerability_type': 'XSS',
        'stage': 'after',
        'payload_data': '<script>alert("XSS_TEST_SUCCESS")</script>',
        'target_parameter': 'test_param',
        'response_callback': None,
        # البيانات الحقيقية من النظام v4
        'real_test_results': v4_real_data['test_results'],
        'real_exploitation_status': v4_real_data['exploitation_status'],
        'real_verification_proof': v4_real_data['verification_proof'],
        'real_response_data': v4_real_data['response_data'],
        'real_error_messages': v4_real_data['error_messages'],
        'real_success_indicators': v4_real_data['success_indicators'],
        'vulnerability_meta': v4_real_data['vulnerability_meta'],
        'v4_data_main': v4_real_data['original_v4_data']
    }
    
    print(f'📤 إرسال البيانات الشاملة للسيرفر الحقيقي...')
    print(f'📊 حجم البيانات الإجمالي: {len(str(request_data))} حرف')
    print(f'📊 حجم real_test_results: {len(v4_real_data["test_results"])} حرف')
    print(f'📊 حجم real_verification_proof: {len(v4_real_data["verification_proof"])} حرف')
    print(f'📊 حجم real_response_data: {len(v4_real_data["response_data"])} حرف')
    print(f'📊 عدد success_indicators: {len(v4_real_data["success_indicators"])} عنصر')
    
    try:
        response = requests.post(
            f'{server_url}/v4_website',
            json=request_data,
            timeout=120,  # وقت أطول للاختبار
            headers={'Content-Type': 'application/json'}
        )
        
        print(f'📥 استجابة السيرفر: {response.status_code}')
        
        if response.status_code == 200:
            result_data = response.json()
            
            success = result_data.get('success', False)
            screenshot_data = result_data.get('screenshot_data')
            v4_real_data_received = result_data.get('v4_real_data', {})
            
            print(f'✅ نجح الاختبار: {success}')
            print(f'📸 بيانات الصورة: {"موجودة" if screenshot_data else "غير موجودة"}')
            print(f'📊 بيانات v4 المُستلمة: {len(str(v4_real_data_received))} حرف')
            
            # فحص تفصيلي لبيانات v4
            if v4_real_data_received:
                print(f'\n🔍 فحص تفصيلي لبيانات v4 المُستلمة:')
                for key, value in v4_real_data_received.items():
                    if isinstance(value, str):
                        print(f'   ✅ {key}: {len(value)} حرف')
                        if len(value) > 100:
                            print(f'      📝 المحتوى: {value[:100]}...')
                        else:
                            print(f'      📝 المحتوى: {value}')
                    elif isinstance(value, list):
                        print(f'   ✅ {key}: {len(value)} عنصر')
                        for i, item in enumerate(value[:3]):  # أول 3 عناصر
                            print(f'      - [{i}]: {item}')
                    elif isinstance(value, dict):
                        print(f'   ✅ {key}: {len(value)} مفتاح')
                        for k, v in list(value.items())[:3]:  # أول 3 مفاتيح
                            print(f'      - {k}: {v}')
                    else:
                        print(f'   ✅ {key}: {type(value)} - {value}')
            
            # فحص إضافي للبيانات المهمة
            print(f'\n🎯 فحص البيانات المهمة:')
            important_keys = ['real_test_results', 'real_verification_proof', 'real_response_data', 'real_success_indicators']
            for key in important_keys:
                if key in v4_real_data_received:
                    value = v4_real_data_received[key]
                    if isinstance(value, str) and len(value) > 0:
                        print(f'   ✅ {key}: موجود ({len(value)} حرف)')
                    elif isinstance(value, list) and len(value) > 0:
                        print(f'   ✅ {key}: موجود ({len(value)} عنصر)')
                    else:
                        print(f'   ⚠️ {key}: فارغ')
                else:
                    print(f'   ❌ {key}: غير موجود')
            
            print('\n✅ اختبار البيانات نجح!')
            return True
            
        else:
            print(f'❌ فشل الطلب: {response.status_code}')
            print(f'📄 رسالة الخطأ: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ خطأ في الاختبار: {e}')
        return False

if __name__ == "__main__":
    success = test_server_data_only()
    print('\n🎉 انتهى اختبار البيانات')
    if success:
        print('✅ جميع البيانات وصلت بنجاح!')
        print('✅ قسم SERVER RESPONSE يحتوي على البيانات الحقيقية!')
    else:
        print('❌ فشل في اختبار البيانات!')
