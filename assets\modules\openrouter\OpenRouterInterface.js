/**
 * OpenRouter.ai Interface
 * واجهة إعدادات OpenRouter.ai
 */

class OpenRouterInterface {
    constructor() {
        this.modal = null;
        this.isVisible = false;
        this.currentStep = 'setup'; // setup, models, connected
        
        console.log('🎨 تم تهيئة واجهة OpenRouter');
    }

    // إنشاء الواجهة
    createInterface() {
        // إنشاء النافذة المنبثقة
        this.modal = document.createElement('div');
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;

        // محتوى النافذة
        const content = document.createElement('div');
        content.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            color: white;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        `;

        // زر الإغلاق
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '✕';
        closeBtn.style.cssText = `
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        `;
        closeBtn.onmouseover = () => closeBtn.style.background = 'rgba(255,255,255,0.3)';
        closeBtn.onmouseout = () => closeBtn.style.background = 'rgba(255,255,255,0.2)';
        closeBtn.onclick = () => this.hide();

        // منطقة المحتوى الديناميكي
        this.contentArea = document.createElement('div');
        this.contentArea.id = 'openrouter-content';

        content.appendChild(closeBtn);
        content.appendChild(this.contentArea);
        this.modal.appendChild(content);
        document.body.appendChild(this.modal);

        // إغلاق عند النقر خارج النافذة
        this.modal.onclick = (e) => {
            if (e.target === this.modal) this.hide();
        };
    }

    // عرض الواجهة
    show() {
        if (!this.modal) {
            this.createInterface();
        }

        this.modal.style.display = 'flex';
        this.isVisible = true;
        this.updateContent();
        
        console.log('📱 عرض واجهة OpenRouter');
    }

    // إخفاء الواجهة
    hide() {
        if (this.modal) {
            this.modal.style.display = 'none';
        }
        this.isVisible = false;
        console.log('📱 إخفاء واجهة OpenRouter');
    }

    // تحديث المحتوى حسب الحالة
    updateContent() {
        const status = window.openRouterManager.getConnectionStatus();
        
        if (status.isConnected && status.selectedModel) {
            this.showConnectedView();
        } else if (status.hasApiKey) {
            this.showModelsView();
        } else {
            this.showSetupView();
        }
    }

    // عرض شاشة الإعداد الأولي
    showSetupView() {
        this.contentArea.innerHTML = `
            <div style="text-align: center;">
                <h2 style="margin-bottom: 20px; color: #fff;">
                    🔌 الاتصال بـ OpenRouter.ai
                </h2>
                
                <p style="margin-bottom: 30px; opacity: 0.9; line-height: 1.6;">
                    احصل على وصول لأكثر من 100 نموذج ذكاء اصطناعي من خلال OpenRouter.ai
                </p>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin-bottom: 25px;">
                    <h4 style="margin-bottom: 15px;">📋 خطوات الإعداد:</h4>
                    <ol style="text-align: right; padding-right: 20px; line-height: 1.8;">
                        <li>اذهب إلى <a href="https://openrouter.ai/keys" target="_blank" style="color: #ffd700;">OpenRouter.ai</a></li>
                        <li>أنشئ حساب جديد أو سجل دخولك</li>
                        <li>انسخ مفتاح API الخاص بك</li>
                        <li>الصقه في الحقل أدناه</li>
                    </ol>
                </div>

                <div style="margin-bottom: 25px;">
                    <label style="display: block; margin-bottom: 10px; font-weight: bold;">
                        🔑 مفتاح OpenRouter API:
                    </label>
                    <input type="password" id="openrouter-api-key" placeholder="sk-or-v1-..." 
                           style="width: 100%; padding: 12px; border: none; border-radius: 10px; 
                                  background: rgba(255,255,255,0.9); color: #333; font-size: 14px;">
                </div>

                <button id="connect-openrouter" style="
                    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
                    color: white; border: none; padding: 12px 30px; border-radius: 25px;
                    cursor: pointer; font-size: 16px; font-weight: bold;
                    transition: all 0.3s ease; margin: 10px;
                ">
                    🚀 الاتصال والمتابعة
                </button>

                <div id="connection-status" style="margin-top: 20px; min-height: 30px;"></div>
            </div>
        `;

        // ربط الأحداث
        document.getElementById('connect-openrouter').onclick = () => this.connectToOpenRouter();
        
        // السماح بالضغط على Enter
        document.getElementById('openrouter-api-key').onkeypress = (e) => {
            if (e.key === 'Enter') this.connectToOpenRouter();
        };
    }

    // عرض شاشة اختيار النماذج
    showModelsView() {
        this.contentArea.innerHTML = `
            <div style="text-align: center;">
                <h2 style="margin-bottom: 20px; color: #fff;">
                    🎯 اختيار النموذج
                </h2>
                
                <p style="margin-bottom: 25px; opacity: 0.9;">
                    اختر النموذج الذي تريد استخدامه في المساعد التقني
                </p>

                <div id="models-loading" style="margin: 30px 0;">
                    <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid rgba(255,255,255,0.3); 
                                border-radius: 50%; border-top-color: #fff; animation: spin 1s ease-in-out infinite;"></div>
                    <p style="margin-top: 15px;">جاري تحميل النماذج المتاحة...</p>
                </div>

                <div id="models-container" style="display: none;"></div>
            </div>

            <style>
                @keyframes spin {
                    to { transform: rotate(360deg); }
                }
            </style>
        `;

        // تحميل النماذج
        this.loadModels();
    }

    // عرض شاشة الاتصال المكتمل
    showConnectedView() {
        const modelInfo = window.openRouterManager.getSelectedModelInfo();
        const status = window.openRouterManager.getConnectionStatus();

        this.contentArea.innerHTML = `
            <div style="text-align: center;">
                <h2 style="margin-bottom: 20px; color: #fff;">
                    ✅ متصل بـ OpenRouter.ai
                </h2>
                
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin-bottom: 25px;">
                    <h4 style="margin-bottom: 15px;">📊 معلومات الاتصال:</h4>
                    <p><strong>النموذج المختار:</strong> ${modelInfo ? modelInfo.name : 'غير محدد'}</p>
                    <p><strong>عدد النماذج المتاحة:</strong> ${status.modelsCount}</p>
                    <p><strong>حالة الاتصال:</strong> <span style="color: #2ecc71;">متصل ✅</span></p>
                </div>

                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                    <button id="change-model" style="
                        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                        color: white; border: none; padding: 12px 20px; border-radius: 25px;
                        cursor: pointer; font-size: 14px; transition: all 0.3s ease;
                    ">
                        🔄 تغيير النموذج
                    </button>

                    <button id="test-connection" style="
                        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
                        color: white; border: none; padding: 12px 20px; border-radius: 25px;
                        cursor: pointer; font-size: 14px; transition: all 0.3s ease;
                    ">
                        🧪 اختبار الاتصال
                    </button>

                    <button id="disconnect-openrouter" style="
                        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                        color: white; border: none; padding: 12px 20px; border-radius: 25px;
                        cursor: pointer; font-size: 14px; transition: all 0.3s ease;
                    ">
                        🔌 قطع الاتصال
                    </button>
                </div>

                <div id="test-result" style="margin-top: 20px; min-height: 30px;"></div>
            </div>
        `;

        // ربط الأحداث
        document.getElementById('change-model').onclick = () => this.showModelsView();
        document.getElementById('test-connection').onclick = () => this.testConnection();
        document.getElementById('disconnect-openrouter').onclick = () => this.disconnectOpenRouter();
    }

    // الاتصال بـ OpenRouter
    async connectToOpenRouter() {
        const apiKey = document.getElementById('openrouter-api-key').value.trim();
        const statusDiv = document.getElementById('connection-status');
        const connectBtn = document.getElementById('connect-openrouter');

        if (!apiKey) {
            this.showStatus(statusDiv, 'يرجى إدخال مفتاح API', 'error');
            return;
        }

        try {
            connectBtn.disabled = true;
            connectBtn.textContent = '🔄 جاري الاتصال...';
            
            this.showStatus(statusDiv, '🔄 جاري التحقق من مفتاح API...', 'info');

            window.openRouterManager.setApiKey(apiKey);
            const result = await window.openRouterManager.validateConnection();

            this.showStatus(statusDiv, `✅ تم الاتصال بنجاح! تم العثور على ${result.modelsCount} نموذج`, 'success');
            
            setTimeout(() => {
                this.showModelsView();
            }, 1500);

        } catch (error) {
            this.showStatus(statusDiv, `❌ فشل الاتصال: ${error.message}`, 'error');
            connectBtn.disabled = false;
            connectBtn.textContent = '🚀 الاتصال والمتابعة';
        }
    }

    // تحميل النماذج
    async loadModels() {
        try {
            const models = await window.openRouterManager.fetchAvailableModels();
            const categories = window.openRouterManager.getModelsByCategory();
            
            document.getElementById('models-loading').style.display = 'none';
            document.getElementById('models-container').style.display = 'block';
            
            this.displayModels(categories);

        } catch (error) {
            document.getElementById('models-loading').innerHTML = `
                <p style="color: #e74c3c;">❌ خطأ في تحميل النماذج: ${error.message}</p>
                <button onclick="window.openRouterInterface.showSetupView()" style="
                    background: #3498db; color: white; border: none; padding: 10px 20px;
                    border-radius: 20px; cursor: pointer; margin-top: 15px;
                ">العودة للإعدادات</button>
            `;
        }
    }

    // عرض النماذج مصنفة
    displayModels(categories) {
        const container = document.getElementById('models-container');
        let html = '';

        Object.entries(categories).forEach(([category, models]) => {
            if (models.length === 0) return;

            const categoryNames = {
                'gpt': '🧠 نماذج GPT',
                'claude': '🤖 نماذج Claude',
                'gemini': '💎 نماذج Gemini',
                'llama': '🦙 نماذج Llama',
                'mistral': '🌪️ نماذج Mistral',
                'other': '🔧 نماذج أخرى'
            };

            html += `
                <div style="margin-bottom: 25px;">
                    <h4 style="margin-bottom: 15px; color: #ffd700;">${categoryNames[category]}</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
            `;

            models.slice(0, 6).forEach(model => { // عرض أول 6 نماذج من كل فئة
                html += `
                    <button class="model-btn" data-model-id="${model.id}" style="
                        background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.2);
                        color: white; padding: 15px; border-radius: 10px; cursor: pointer;
                        text-align: right; transition: all 0.3s ease; font-size: 13px;
                    ">
                        <div style="font-weight: bold; margin-bottom: 5px;">${model.name}</div>
                        <div style="opacity: 0.7; font-size: 11px;">${model.id}</div>
                    </button>
                `;
            });

            html += '</div></div>';
        });

        container.innerHTML = html;

        // ربط أحداث اختيار النماذج
        document.querySelectorAll('.model-btn').forEach(btn => {
            btn.onmouseover = () => {
                btn.style.background = 'rgba(255,255,255,0.2)';
                btn.style.borderColor = '#ffd700';
            };
            btn.onmouseout = () => {
                btn.style.background = 'rgba(255,255,255,0.1)';
                btn.style.borderColor = 'rgba(255,255,255,0.2)';
            };
            btn.onclick = () => this.selectModel(btn.dataset.modelId);
        });
    }

    // اختيار نموذج
    async selectModel(modelId) {
        try {
            const model = window.openRouterManager.setSelectedModel(modelId);
            
            // عرض رسالة نجاح
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed; top: 20px; right: 20px; background: #2ecc71;
                color: white; padding: 15px 25px; border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3); z-index: 10001;
            `;
            successDiv.innerHTML = `✅ تم اختيار: ${model.name}`;
            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.remove();
                this.showConnectedView();
            }, 2000);

        } catch (error) {
            alert(`خطأ في اختيار النموذج: ${error.message}`);
        }
    }

    // اختبار الاتصال
    async testConnection() {
        const resultDiv = document.getElementById('test-result');
        const testBtn = document.getElementById('test-connection');

        try {
            testBtn.disabled = true;
            testBtn.textContent = '🔄 جاري الاختبار...';
            
            this.showStatus(resultDiv, '🧪 جاري اختبار الاتصال...', 'info');

            const response = await window.openRouterManager.sendMessage('مرحبا، هذا اختبار بسيط');
            
            this.showStatus(resultDiv, `✅ الاختبار نجح! الرد: "${response.text.substring(0, 100)}..."`, 'success');

        } catch (error) {
            this.showStatus(resultDiv, `❌ فشل الاختبار: ${error.message}`, 'error');
        } finally {
            testBtn.disabled = false;
            testBtn.textContent = '🧪 اختبار الاتصال';
        }
    }

    // قطع الاتصال
    disconnectOpenRouter() {
        if (confirm('هل أنت متأكد من قطع الاتصال مع OpenRouter؟')) {
            window.openRouterManager.disconnect();
            this.showSetupView();
        }
    }

    // عرض رسالة حالة
    showStatus(element, message, type) {
        const colors = {
            success: '#2ecc71',
            error: '#e74c3c',
            info: '#3498db',
            warning: '#f39c12'
        };

        element.innerHTML = `
            <div style="background: ${colors[type]}; color: white; padding: 10px 15px; 
                        border-radius: 8px; margin: 10px 0; text-align: center;">
                ${message}
            </div>
        `;
    }
}

// إنشاء مثيل عام
window.openRouterInterface = new OpenRouterInterface();

console.log('🎨 تم تحميل واجهة OpenRouter بنجاح');
