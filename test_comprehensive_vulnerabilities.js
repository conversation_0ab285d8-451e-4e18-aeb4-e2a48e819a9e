// اختبار شامل لعدة ثغرات مختلفة لرؤية الاستجابة في الصور
console.log('🔍 اختبار شامل لعدة ثغرات مختلفة...');

// قائمة الثغرات المختلفة للاختبار
const vulnerabilities = [
    {
        name: 'SQL_Injection_Login_Form',
        type: 'SQL_Injection',
        payload: "admin' OR '1'='1' -- comprehensive_test",
        parameter: 'username',
        description: 'SQL Injection في نموذج تسجيل الدخول'
    },
    {
        name: 'XSS_Search_Field',
        type: 'Cross_Site_Scripting',
        payload: "<script>alert('XSS_COMPREHENSIVE_TEST_2025')</script>",
        parameter: 'search',
        description: 'XSS في حقل البحث'
    },
    {
        name: 'LFI_File_Include',
        type: 'Local_File_Inclusion',
        payload: "../../../etc/passwd?comprehensive_test=true",
        parameter: 'file',
        description: 'Local File Inclusion'
    },
    {
        name: 'Command_Injection_Upload',
        type: 'Command_Injection',
        payload: "; cat /etc/passwd # comprehensive_test",
        parameter: 'cmd',
        description: 'Command Injection في رفع الملفات'
    },
    {
        name: 'IDOR_User_Profile',
        type: 'Insecure_Direct_Object_Reference',
        payload: "../../admin/profile?user_id=1&test=comprehensive",
        parameter: 'id',
        description: 'IDOR في ملف المستخدم'
    }
];

async function testComprehensiveVulnerabilities() {
    console.log('🚀 بدء الاختبار الشامل لعدة ثغرات...');
    console.log(`📊 عدد الثغرات للاختبار: ${vulnerabilities.length}`);
    
    const results = [];
    
    for (let i = 0; i < vulnerabilities.length; i++) {
        const vuln = vulnerabilities[i];
        console.log(`\n${'='.repeat(80)}`);
        console.log(`🎯 اختبار ${i + 1}/${vulnerabilities.length}: ${vuln.name}`);
        console.log(`📝 النوع: ${vuln.type}`);
        console.log(`💉 Payload: ${vuln.payload}`);
        console.log(`🔧 Parameter: ${vuln.parameter}`);
        console.log(`📄 الوصف: ${vuln.description}`);
        console.log(`${'='.repeat(80)}`);
        
        const testData = {
            url: 'http://testphp.vulnweb.com',
            vulnerability_name: vuln.name,
            vulnerability_type: vuln.type,
            payload_data: vuln.payload,
            target_parameter: vuln.parameter,
            report_id: `comprehensive_test_${i + 1}_${Date.now()}`
        };
        
        console.log('📤 إرسال بيانات الثغرة...');
        
        try {
            const startTime = Date.now();
            
            const response = await fetch('http://localhost:8000/vulnerability_sequence', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            });
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`📡 حالة الاستجابة: ${response.status}`);
            console.log(`⏱️ مدة الطلب: ${duration}ms`);
            
            if (response.ok) {
                const result = await response.json();
                console.log('✅ نجح الطلب!');
                console.log(`📊 المراحل المكتملة: ${result.stages_completed?.length || 0}/${result.total_stages || 3}`);
                console.log(`📝 الرسالة: ${result.message}`);
                
                // التحقق من البيانات المرجعة
                if (result.results) {
                    console.log('\n📸 نتائج الصور:');
                    ['before', 'during', 'after'].forEach(stage => {
                        if (result.results[stage]) {
                            const stageResult = result.results[stage];
                            if (stageResult.success) {
                                console.log(`   ✅ ${stage.toUpperCase()}: نجح - ${stageResult.file_size || 0} bytes`);
                                if (stageResult.payload_used) {
                                    console.log(`      💉 Payload مستخدم: ${stageResult.payload_used}`);
                                    if (stageResult.payload_used === vuln.payload) {
                                        console.log(`      🎯 Payload صحيح ومطابق!`);
                                    } else {
                                        console.log(`      ⚠️ Payload مختلف!`);
                                    }
                                }
                                if (stageResult.vulnerability_type) {
                                    console.log(`      🔍 Type: ${stageResult.vulnerability_type}`);
                                }
                            } else {
                                console.log(`   ❌ ${stage.toUpperCase()}: فشل - ${stageResult.error || 'خطأ غير محدد'}`);
                            }
                        } else {
                            console.log(`   ⚠️ ${stage.toUpperCase()}: لا توجد نتيجة`);
                        }
                    });
                }
                
                results.push({
                    vulnerability: vuln.name,
                    type: vuln.type,
                    success: true,
                    stages_completed: result.stages_completed?.length || 0,
                    total_stages: result.total_stages || 3,
                    duration: duration,
                    payload_correct: result.results?.after?.payload_used === vuln.payload
                });
                
            } else {
                const errorText = await response.text();
                console.log(`❌ فشل الطلب: ${response.status}`);
                console.log(`📄 تفاصيل الخطأ: ${errorText}`);
                
                results.push({
                    vulnerability: vuln.name,
                    type: vuln.type,
                    success: false,
                    error: `HTTP ${response.status}: ${errorText}`,
                    duration: duration
                });
            }
            
        } catch (error) {
            console.error(`❌ خطأ في الطلب: ${error.message}`);
            
            results.push({
                vulnerability: vuln.name,
                type: vuln.type,
                success: false,
                error: error.message,
                duration: 0
            });
        }
        
        // انتظار بين الثغرات لتجنب الحمل الزائد
        if (i < vulnerabilities.length - 1) {
            console.log('\n⏳ انتظار 5 ثواني قبل الثغرة التالية...');
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
    }
    
    // ملخص النتائج
    console.log(`\n${'='.repeat(100)}`);
    console.log('📊 ملخص نتائج الاختبار الشامل:');
    console.log(`${'='.repeat(100)}`);
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`✅ نجح: ${successful.length}/${results.length} ثغرة`);
    console.log(`❌ فشل: ${failed.length}/${results.length} ثغرة`);
    
    if (successful.length > 0) {
        console.log('\n✅ الثغرات الناجحة:');
        successful.forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.vulnerability} (${result.type})`);
            console.log(`      📸 الصور: ${result.stages_completed}/${result.total_stages}`);
            console.log(`      ⏱️ المدة: ${result.duration}ms`);
            console.log(`      💉 Payload صحيح: ${result.payload_correct ? 'نعم' : 'لا'}`);
        });
    }
    
    if (failed.length > 0) {
        console.log('\n❌ الثغرات الفاشلة:');
        failed.forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.vulnerability} (${result.type})`);
            console.log(`      📄 الخطأ: ${result.error}`);
        });
    }
    
    console.log(`\n🎯 معدل النجاح: ${((successful.length / results.length) * 100).toFixed(1)}%`);
    console.log('📁 تحقق من مجلد screenshots لرؤية الصور المولدة');
    console.log('📋 تحقق من سجلات سيرفر Python لرؤية التفاصيل');
    
    return results;
}

// تشغيل الاختبار
testComprehensiveVulnerabilities()
    .then(results => {
        console.log('\n🎉 انتهى الاختبار الشامل بنجاح!');
    })
    .catch(error => {
        console.error('❌ خطأ في الاختبار الشامل:', error);
    });
