#!/bin/bash

echo "🐍 بدء تشغيل خدمة Python لالتقاط الصور..."
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 غير مثبت"
    echo "💡 قم بتثبيت Python3 أولاً"
    exit 1
fi

echo "✅ Python3 موجود"

# التحقق من pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 غير مثبت"
    echo "💡 قم بتثبيت pip3 أولاً"
    exit 1
fi

echo "✅ pip3 موجود"

# تثبيت المتطلبات
echo "📦 فحص وتثبيت المتطلبات..."
pip3 install flask flask-cors selenium webdriver-manager

# تحميل ChromeDriver تلقائياً
echo "🔧 تحميل ChromeDriver..."
python3 -c "from selenium import webdriver; from webdriver_manager.chrome import ChromeDriverManager; ChromeDriverManager().install()"

echo
echo "🚀 بدء تشغيل الخدمة..."
echo "📡 الخدمة ستعمل على: http://localhost:8000"
echo "🔗 للاختبار: http://localhost:8000/health"
echo
echo "⚠️ لإيقاف الخدمة اضغط Ctrl+C"
echo

# تشغيل الخدمة
python3 python_web_service.py
