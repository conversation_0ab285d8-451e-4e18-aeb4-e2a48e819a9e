#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مع السيرفر الحقيقي لرؤية الاستجابة الحقيقية
"""

import sys
import asyncio
import os
from pathlib import Path

# إضافة المسار للوحدات
sys.path.append('.')

from assets.modules.bugbounty.screenshot_service import ScreenshotService

async def test_with_real_server():
    """اختبار مع السيرفر الحقيقي لرؤية الاستجابة الحقيقية في قسم REAL SERVER RESPONSE"""
    
    print("🔥 اختبار مع السيرفر الحقيقي...")
    print("🌐 سيتم استخدام مواقع حقيقية لرؤية الاستجابة الفعلية")
    
    service = ScreenshotService()
    
    try:
        # اختبار مع مواقع حقيقية تعطي استجابات فعلية
        real_tests = [
            {
                'name': 'HTTPBin JSON Response Test',
                'url': 'https://httpbin.org/json',
                'payload': '',
                'type': 'Information Disclosure',
                'description': 'اختبار استجابة JSON حقيقية'
            },
            {
                'name': 'HTTPBin Headers Test',
                'url': 'https://httpbin.org/headers',
                'payload': '',
                'type': 'Information Disclosure',
                'description': 'اختبار عرض Headers حقيقية'
            },
            {
                'name': 'HTTPBin User-Agent Test',
                'url': 'https://httpbin.org/user-agent',
                'payload': '',
                'type': 'Information Disclosure',
                'description': 'اختبار عرض User-Agent'
            },
            {
                'name': 'HTTPBin IP Test',
                'url': 'https://httpbin.org/ip',
                'payload': '',
                'type': 'Information Disclosure',
                'description': 'اختبار عرض IP Address'
            },
            {
                'name': 'HTTPBin GET with Parameters',
                'url': 'https://httpbin.org/get?test=value&debug=true&admin=false',
                'payload': 'admin=true',
                'type': 'Parameter Manipulation',
                'description': 'اختبار تلاعب بالمعاملات'
            }
        ]
        
        print(f"\n🎯 سيتم اختبار {len(real_tests)} مواقع حقيقية...")
        
        results = []
        
        for i, test in enumerate(real_tests):
            print(f"\n📸 اختبار {i+1}/{len(real_tests)}: {test['name']}")
            print(f"   🎯 النوع: {test['type']}")
            print(f"   🔗 URL: {test['url']}")
            print(f"   📝 الوصف: {test['description']}")
            print("   🌐 سيتم الحصول على استجابة حقيقية من الخادم...")
            
            # اختبار مع موقع حقيقي - النظام سيحصل على استجابة حقيقية
            result = await service.capture_with_playwright(
                url=test['url'],
                filename=f'real_server_{i+1}_{test["name"].replace(" ", "_").lower()}',
                stage='after',
                report_id=f'real_server_{i+1}',
                vulnerability_name=test['name'],
                payload_data=test['payload'],
                vulnerability_type=test['type'],
                # بدون v4_data - النظام سيحصل على الاستجابة الحقيقية من الموقع
            )
            
            if result and result.get('success'):
                image_path = result.get('path')
                file_size = result.get('file_size', 0)
                
                print(f"   ✅ تم التقاط الصورة: {os.path.basename(image_path)}")
                print(f"   📊 حجم الملف: {file_size:,} bytes")
                
                results.append({
                    'test': test['name'],
                    'type': test['type'],
                    'success': True,
                    'image_path': image_path,
                    'file_size': file_size,
                    'url': test['url']
                })
                
                # التحقق من وجود الملف
                if os.path.exists(image_path):
                    print(f"   ✅ ملف الصورة موجود")
                    
                    # حفظ نسخة مصغرة للفحص
                    try:
                        from PIL import Image
                        img = Image.open(image_path)
                        thumbnail_path = image_path.replace('.png', '_real_server_thumbnail.png')
                        img.thumbnail((1000, 800))
                        img.save(thumbnail_path)
                        print(f"   💾 نسخة مصغرة: {os.path.basename(thumbnail_path)}")
                        
                    except Exception as e:
                        print(f"   ⚠️ خطأ في معالجة الصورة: {e}")
                        
                else:
                    print(f"   ❌ ملف الصورة غير موجود")
                    results[-1]['success'] = False
            else:
                print(f"   ❌ فشل في التقاط صورة {test['name']}")
                results.append({
                    'test': test['name'],
                    'type': test['type'],
                    'success': False,
                    'error': 'فشل في التقاط الصورة'
                })
            
            # انتظار بين الاختبارات
            await asyncio.sleep(3)
        
        # عرض النتائج النهائية
        print("\n" + "="*100)
        print("🎯 ملخص نتائج اختبار السيرفر الحقيقي:")
        print("="*100)
        
        successful_tests = [r for r in results if r['success']]
        failed_tests = [r for r in results if not r['success']]
        
        print(f"✅ اختبارات ناجحة: {len(successful_tests)}/{len(results)}")
        print(f"❌ اختبارات فاشلة: {len(failed_tests)}/{len(results)}")
        
        if successful_tests:
            print("\n📸 الصور المُنشأة مع الاستجابات الحقيقية:")
            
            for result in successful_tests:
                print(f"\n🔥 {result['test']}")
                print(f"   📁 الملف: {os.path.basename(result['image_path'])}")
                print(f"   📊 حجم الصورة: {result['file_size']:,} bytes ({result['file_size']/1024:.1f} KB)")
                print(f"   🌐 URL: {result['url']}")
                print(f"   🎯 النوع: {result['type']}")
        
        if failed_tests:
            print("\n❌ الاختبارات الفاشلة:")
            for result in failed_tests:
                print(f"   ❌ {result['test']}: {result.get('error', 'خطأ غير محدد')}")
        
        print("\n📂 مجلد الصور: assets/modules/bugbounty/screenshots/")
        print("🔍 افتح الصور للتحقق من:")
        print("   ✅ قسم REAL SERVER RESPONSE مع البيانات الحقيقية")
        print("   ✅ الاستجابة الفعلية من الخوادم")
        print("   ✅ JSON responses, Headers, IP info, etc.")
        print("   ✅ التأثيرات التلقائية حسب نوع المحتوى")
        
        # فتح أول صورة ناجحة للفحص
        if successful_tests:
            first_image = successful_tests[0]['image_path']
            import webbrowser
            webbrowser.open(f"file:///{os.path.abspath(first_image)}")
            print(f"\n🌐 تم فتح أول صورة للفحص: {os.path.basename(first_image)}")
            print("🔍 تحقق من قسم REAL SERVER RESPONSE - هل يعرض الاستجابة الحقيقية؟")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await service.cleanup()
        print("🔒 تم تنظيف الموارد")

if __name__ == "__main__":
    asyncio.run(test_with_real_server())
