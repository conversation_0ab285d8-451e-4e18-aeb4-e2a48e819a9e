# 🐍 نظام Python لالتقاط الصور الحقيقية

## نظرة عامة
هذا النظام يوفر التقاط صور حقيقية عالية الجودة للمواقع باستخدام Python مع Selenium و ChromeDriver.

## المتطلبات
- Python 3.7+ 
- Google Chrome متصفح
- اتصال بالإنترنت (لتحميل ChromeDriver)

## التثبيت والتشغيل

### Windows
```bash
# تشغيل الملف التلقائي
start_python_service.bat
```

### Linux/Mac
```bash
# إعطاء صلاحيات التنفيذ
chmod +x start_python_service.sh

# تشغيل الخدمة
./start_python_service.sh
```

### التثبيت اليدوي
```bash
# تثبيت المتطلبات
pip install flask flask-cors selenium webdriver-manager

# تشغيل الخدمة
python python_web_service.py
```

## الاختبار

### 1. فحص صحة الخدمة
```
GET http://localhost:8000/health
```

### 2. التقاط صورة
```
POST http://localhost:8000/capture
Content-Type: application/json

{
    "url": "https://example.com",
    "report_id": "test_screenshot",
    "width": 1920,
    "height": 1080,
    "wait_time": 3
}
```

### 3. الحصول على الإحصائيات
```
GET http://localhost:8000/stats
```

## كيفية عمل النظام

### 1. تشغيل الخدمة
- تشغيل `start_python_service.bat` (Windows) أو `start_python_service.sh` (Linux/Mac)
- الخدمة ستعمل على المنفذ 8000
- سيتم تحميل ChromeDriver تلقائياً

### 2. التكامل مع النظام v4
- النظام v4 سيتحقق تلقائياً من وجود خدمة Python
- إذا كانت متوفرة، سيستخدمها لالتقاط الصور
- إذا لم تكن متوفرة، سيستخدم الطرق البديلة

### 3. حفظ الصور
- الصور تُحفظ في مجلد `./screenshots/[report_id]/`
- كل صورة لها اسم فريد مع الوقت والتاريخ
- الصور تُرجع كـ base64 للنظام v4

## استكشاف الأخطاء

### خطأ: "Python غير مثبت"
- قم بتثبيت Python من https://python.org
- تأكد من إضافة Python إلى PATH

### خطأ: "ChromeDriver غير موجود"
- سيتم تحميله تلقائياً عند أول تشغيل
- تأكد من وجود اتصال بالإنترنت

### خطأ: "المنفذ 8000 مستخدم"
- أغلق أي تطبيق يستخدم المنفذ 8000
- أو غيّر المنفذ في `python_web_service.py`

### خطأ: "فشل في التقاط الصورة"
- تحقق من صحة الرابط
- تأكد من أن الموقع يمكن الوصول إليه
- تحقق من إعدادات الجدار الناري

## الميزات

### ✅ ما يعمل
- التقاط صور حقيقية عالية الجودة
- دعم مواقع JavaScript المعقدة
- حفظ تلقائي للصور
- تحويل إلى base64 للتكامل
- إحصائيات مفصلة
- معالجة أخطاء شاملة

### 🔄 قيد التطوير
- دعم متصفحات أخرى (Firefox, Safari)
- التقاط صور متحركة (GIF)
- تحسين الأداء والسرعة
- دعم البروكسي والمصادقة

## الدعم الفني
إذا واجهت أي مشاكل:
1. تحقق من تشغيل الخدمة: `http://localhost:8000/health`
2. راجع رسائل الخطأ في وحدة التحكم
3. تأكد من تثبيت جميع المتطلبات
4. أعد تشغيل الخدمة

## الأمان
- الخدمة تعمل محلياً فقط (localhost)
- لا تحفظ أي بيانات حساسة
- الصور تُحفظ محلياً في مجلد screenshots
- يمكن تشفير الاتصال بـ HTTPS إذا لزم الأمر
