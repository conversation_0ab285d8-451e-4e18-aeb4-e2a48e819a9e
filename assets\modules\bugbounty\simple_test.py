#!/usr/bin/env python3
"""
اختبار بسيط لتحديد مشكلة JavaScript
"""

import asyncio
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from screenshot_service import ScreenshotService

async def test_simple_effects():
    """اختبار تأثيرات بسيطة"""
    print("🔍 اختبار تأثيرات JavaScript بسيطة")
    
    service = ScreenshotService()
    await service.initialize_playwright()
    
    try:
        page = await service.playwright_browser.new_page()
        await page.goto("https://httpbin.org/html")
        
        print("📍 تطبيق تأثيرات بسيطة...")
        
        # اختبار JavaScript بسيط
        simple_js = """
            console.log('🔥 اختبار JavaScript بسيط');
            document.body.style.background = 'red';
            document.body.style.border = '10px solid yellow';
            document.title = 'HACKED - TEST';
            
            const banner = document.createElement('div');
            banner.innerHTML = 'TEST BANNER';
            banner.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; background: red; color: white; text-align: center; padding: 20px; z-index: 999999;';
            document.body.appendChild(banner);
            
            console.log('✅ تم تطبيق التأثيرات البسيطة');
        """
        
        await page.evaluate(simple_js)
        print("✅ نجح تطبيق JavaScript البسيط")
        
        # فحص التأثيرات
        effects_check = await page.evaluate("""
            () => {
                const bodyStyle = window.getComputedStyle(document.body);
                return {
                    background: bodyStyle.background,
                    border: bodyStyle.border,
                    title: document.title,
                    bannerExists: document.querySelector('div') ? true : false
                };
            }
        """)
        
        print(f"📊 نتائج فحص التأثيرات:")
        print(f"   🎨 Background: {effects_check['background'][:50]}...")
        print(f"   🔲 Border: {effects_check['border']}")
        print(f"   📝 Title: {effects_check['title']}")
        print(f"   🏷️ Banner: {effects_check['bannerExists']}")
        
        # التقاط صورة
        await page.screenshot(path="simple_test.png", full_page=True)
        print("📸 تم التقاط صورة الاختبار: simple_test.png")
        
        await page.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار البسيط: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await service.cleanup()

if __name__ == "__main__":
    asyncio.run(test_simple_effects())
