// اختبار مبسط وحقيقي للنظام v4 - إنشاء صور + تقارير + اختبار العرض

const fs = require('fs');
const path = require('path');

async function simpleRealTest() {
    console.log('🔥 اختبار مبسط وحقيقي للنظام v4');
    console.log('🎯 إنشاء صور حقيقية + تقارير + اختبار العرض');
    console.log('=' * 60);
    
    const testId = `simple_real_${Date.now()}`;
    console.log(`📋 Test ID: ${testId}`);
    
    try {
        // الخطوة 1: إنشاء صور حقيقية
        console.log('\n📸 الخطوة 1: إنشاء صور حقيقية...');
        const images = await createRealImages(testId);
        
        if (images.length === 0) {
            console.log('❌ لم يتم إنشاء أي صور - توقف الاختبار');
            return;
        }
        
        console.log(`✅ تم إنشاء ${images.length} صورة حقيقية`);
        
        // الخطوة 2: إنشاء ثغرات مع مسارات الصور
        console.log('\n🎯 الخطوة 2: إنشاء بيانات الثغرات...');
        const vulnerabilities = createVulnerabilitiesWithImages(images, testId);
        console.log(`✅ تم إنشاء ${vulnerabilities.length} ثغرة مع صور`);
        
        // الخطوة 3: إنشاء التقارير
        console.log('\n📋 الخطوة 3: إنشاء التقارير...');
        const reports = await createReportsWithImages(vulnerabilities, testId);
        console.log(`✅ تم إنشاء ${reports.length} تقرير`);
        
        // الخطوة 4: اختبار عرض الصور
        console.log('\n🔍 الخطوة 4: اختبار عرض الصور...');
        await testImageDisplay(reports);
        
        // النتائج النهائية
        console.log('\n🎉 نتائج الاختبار المبسط:');
        console.log(`   📸 الصور: ${images.length}`);
        console.log(`   🎯 الثغرات: ${vulnerabilities.length}`);
        console.log(`   📋 التقارير: ${reports.length}`);
        
        console.log('\n📖 افتح التقارير في المتصفح:');
        reports.forEach(report => {
            const fullPath = path.resolve(report);
            console.log(`   🔗 file:///${fullPath.replace(/\\/g, '/')}`);
        });
        
        console.log('\n✅ اختبار مبسط مكتمل بنجاح!');
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error);
    }
}

async function createRealImages(testId) {
    console.log('📸 إنشاء صور حقيقية باستخدام سيرفر الصور...');
    
    const images = [];
    
    // بيانات الثغرات للاختبار
    const testVulns = [
        { name: 'XSS Cross Site Scripting', type: 'XSS', payload: '<script>alert("SIMPLE_TEST")</script>' },
        { name: 'SQL Injection Attack', type: 'SQL_Injection', payload: "' UNION SELECT 'SIMPLE_TEST' --" }
    ];
    
    for (let i = 0; i < testVulns.length; i++) {
        const vuln = testVulns[i];
        console.log(`\n🎯 إنشاء صور للثغرة ${i + 1}: ${vuln.name}`);
        
        const stages = ['before', 'during', 'after'];
        
        for (const stage of stages) {
            console.log(`   📸 إنشاء صورة ${stage}...`);
            
            try {
                const imageResult = await callImageServiceDirect(vuln, stage, i + 1, testId);
                
                if (imageResult.success && imageResult.file_path) {
                    images.push({
                        vulnIndex: i,
                        vulnName: vuln.name,
                        vulnType: vuln.type,
                        stage: stage,
                        path: imageResult.file_path,
                        size: imageResult.file_size || 0
                    });
                    
                    console.log(`      ✅ ${(imageResult.file_size || 0).toLocaleString()} bytes`);
                } else {
                    console.log(`      ❌ فشل: ${imageResult.error || 'Unknown'}`);
                }
                
            } catch (error) {
                console.log(`      ❌ خطأ: ${error.message}`);
            }
        }
    }
    
    return images;
}

async function callImageServiceDirect(vuln, stage, vulnNum, testId) {
    // استدعاء مباشر لسيرفر الصور باستخدام http
    try {
        const http = require('http');

        const data = {
            url: 'http://testphp.vulnweb.com/search.php',
            filename: `${stage}_${vuln.type}_SIMPLE_TEST_${vulnNum}`,
            report_id: testId,
            vulnerability_name: vuln.name,
            vulnerability_type: vuln.type,
            stage: stage,
            payload_data: vuln.payload,
            target_parameter: 'searchFor'
        };

        console.log(`      🔗 استدعاء: POST /v4_website`);

        return new Promise((resolve, reject) => {
            const postData = JSON.stringify(data);

            const options = {
                hostname: 'localhost',
                port: 8000,
                path: '/v4_website',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData)
                },
                timeout: 45000
            };

            const req = http.request(options, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        if (res.statusCode === 200) {
                            const result = JSON.parse(responseData);
                            resolve(result);
                        } else {
                            resolve({
                                success: false,
                                error: `HTTP ${res.statusCode}`
                            });
                        }
                    } catch (parseError) {
                        resolve({
                            success: false,
                            error: `Parse error: ${parseError.message}`
                        });
                    }
                });
            });

            req.on('error', (error) => {
                resolve({
                    success: false,
                    error: error.message
                });
            });

            req.on('timeout', () => {
                req.destroy();
                resolve({
                    success: false,
                    error: 'Request timeout'
                });
            });

            req.write(postData);
            req.end();
        });

    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}

function createVulnerabilitiesWithImages(images, testId) {
    console.log('🎯 إنشاء بيانات الثغرات مع مسارات الصور...');
    
    const vulnerabilities = [];
    
    // تجميع الصور حسب الثغرة
    const imagesByVuln = {};
    images.forEach(img => {
        if (!imagesByVuln[img.vulnIndex]) {
            imagesByVuln[img.vulnIndex] = {
                name: img.vulnName,
                type: img.vulnType,
                images: {}
            };
        }
        imagesByVuln[img.vulnIndex].images[img.stage] = img.path;
    });
    
    // إنشاء بيانات الثغرات
    Object.keys(imagesByVuln).forEach(vulnIndex => {
        const vulnData = imagesByVuln[vulnIndex];
        
        const vulnerability = {
            name: vulnData.name,
            type: vulnData.type,
            severity: vulnData.type === 'SQL_Injection' ? 'Critical' : 'High',
            description: `Real ${vulnData.type} vulnerability found during simple testing`,
            impact: `High impact ${vulnData.type} vulnerability with visual proof`,
            recommendation: `Fix ${vulnData.type} vulnerability immediately`,
            
            // مسارات الصور للتقرير الرئيسي
            screenshots: vulnData.images,
            
            // مسارات الصور للتقرير المنفصل
            visual_proof: {},
            
            testId: testId
        };
        
        // إضافة مسارات visual_proof
        Object.keys(vulnData.images).forEach(stage => {
            vulnerability.visual_proof[`${stage}_screenshot_path`] = vulnData.images[stage];
        });
        
        vulnerabilities.push(vulnerability);
        
        console.log(`   ✅ ثغرة ${vulnData.name}: ${Object.keys(vulnData.images).length} صورة`);
    });
    
    return vulnerabilities;
}

async function createReportsWithImages(vulnerabilities, testId) {
    console.log('📋 إنشاء التقارير مع الصور...');
    
    const reports = [];
    
    // التقرير الرئيسي
    console.log('   📋 إنشاء التقرير الرئيسي...');
    const mainReport = generateSimpleMainReport(vulnerabilities, testId);
    const mainReportPath = `SIMPLE_main_report_${testId}.html`;
    
    fs.writeFileSync(mainReportPath, mainReport, 'utf8');
    reports.push(mainReportPath);
    console.log(`   ✅ التقرير الرئيسي: ${mainReportPath}`);
    
    // التقرير المنفصل
    console.log('   📄 إنشاء التقرير المنفصل...');
    const separateReport = generateSimpleSeparateReport(vulnerabilities, testId);
    const separateReportPath = `SIMPLE_separate_report_${testId}.html`;
    
    fs.writeFileSync(separateReportPath, separateReport, 'utf8');
    reports.push(separateReportPath);
    console.log(`   ✅ التقرير المنفصل: ${separateReportPath}`);
    
    return reports;
}

function generateSimpleMainReport(vulnerabilities, testId) {
    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>🔥 التقرير الرئيسي المبسط v4 - ${testId}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .vulnerability { margin: 30px 0; padding: 20px; border: 2px solid #007bff; border-radius: 8px; }
        .visual-changes { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #17a2b8; }
        .image-container { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: white; }
        .image-container img { max-width: 500px; border: 2px solid #007bff; border-radius: 5px; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; font-weight: bold; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; font-weight: bold; }
        .path-info { font-family: monospace; background: #e9ecef; padding: 8px; border-radius: 3px; font-size: 12px; }
        h1 { color: white; margin: 0; }
        h2 { color: #007bff; }
        h3 { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 التقرير الرئيسي المبسط v4</h1>
            <p>اختبار حقيقي لعرض الصور في التقارير</p>
            <p><strong>Test ID:</strong> ${testId}</p>
            <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleString('ar-SA')}</p>
        </div>
        
        <div style="background: #17a2b8; color: white; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
            <h2 style="color: white; margin: 0;">📊 ملخص الاختبار</h2>
            <p>تم إنشاء ${vulnerabilities.length} ثغرة مع صور حقيقية</p>
            <p>إجمالي الصور: ${vulnerabilities.length * 3} صورة</p>
        </div>
        
        ${vulnerabilities.map((vuln, index) => `
        <div class="vulnerability">
            <h2>🎯 الثغرة ${index + 1}: ${vuln.name}</h2>
            <p><strong>النوع:</strong> ${vuln.type}</p>
            <p><strong>الخطورة:</strong> ${vuln.severity}</p>
            <p><strong>الوصف:</strong> ${vuln.description}</p>
            
            <div class="visual-changes">
                <h3>📸 الصور التوضيحية والتأثيرات البصرية</h3>
                <p><strong>هذا اختبار حقيقي:</strong> الصور التالية تم إنشاؤها تلقائياً أثناء الاختبار</p>
                
                ${Object.keys(vuln.screenshots).map(stage => {
                    const imagePath = vuln.screenshots[stage];
                    const stageNames = { before: 'قبل الاستغلال', during: 'أثناء الاستغلال', after: 'بعد الاستغلال' };
                    
                    return `
                    <div class="image-container">
                        <h4>${stageNames[stage]}:</h4>
                        <div class="path-info">المسار: ${imagePath}</div>
                        <img src="file:///${imagePath.replace(/\\\\/g, '/')}" alt="${stageNames[stage]}" 
                             onload="this.nextElementSibling.style.display='block'; this.nextElementSibling.nextElementSibling.style.display='none'; console.log('✅ تم تحميل صورة ${stage} في التقرير الرئيسي المبسط');"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block'; console.log('❌ فشل في تحميل صورة ${stage} في التقرير الرئيسي المبسط');">
                        <div class="success" style="display:none;">✅ تم تحميل الصورة بنجاح - الاختبار نجح!</div>
                        <div class="error" style="display:none;">❌ فشل في تحميل الصورة: ${imagePath}</div>
                    </div>
                    `;
                }).join('')}
            </div>
        </div>
        `).join('')}
    </div>
    
    <script>
        console.log('🔥 تم تحميل التقرير الرئيسي المبسط v4');
        
        let simpleMainStats = { total: 0, loaded: 0, failed: 0 };
        
        document.querySelectorAll('img').forEach(img => {
            simpleMainStats.total++;
            
            img.addEventListener('load', function() {
                simpleMainStats.loaded++;
                console.log(\`✅ تحميل صورة في التقرير الرئيسي المبسط (\${simpleMainStats.loaded}/\${simpleMainStats.total})\`);
                checkSimpleMainComplete();
            });
            
            img.addEventListener('error', function() {
                simpleMainStats.failed++;
                console.log(\`❌ فشل تحميل صورة في التقرير الرئيسي المبسط (\${simpleMainStats.failed}/\${simpleMainStats.total})\`);
                checkSimpleMainComplete();
            });
        });
        
        function checkSimpleMainComplete() {
            if (simpleMainStats.loaded + simpleMainStats.failed === simpleMainStats.total) {
                console.log(\`📊 إحصائيات التقرير الرئيسي المبسط: \${simpleMainStats.loaded} نجحت، \${simpleMainStats.failed} فشلت من أصل \${simpleMainStats.total}\`);
                
                if (simpleMainStats.loaded === simpleMainStats.total) {
                    console.log('🎉 جميع الصور في التقرير الرئيسي المبسط تعمل بشكل مثالي!');
                    console.log('✅ الاختبار المبسط للتقرير الرئيسي نجح بالكامل!');
                } else if (simpleMainStats.loaded > 0) {
                    console.log(\`⚠️ بعض الصور تعمل (\${simpleMainStats.loaded}/\${simpleMainStats.total}) - الاختبار نجح جزئياً\`);
                } else {
                    console.log('❌ لا توجد صور تعمل - الاختبار فشل');
                }
            }
        }
        
        console.log(\`📸 إجمالي الصور في التقرير الرئيسي المبسط: \${simpleMainStats.total}\`);
    </script>
</body>
</html>
    `;
}

function generateSimpleSeparateReport(vulnerabilities, testId) {
    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>📄 التقرير المنفصل المبسط v4 - ${testId}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { text-align: center; background: linear-gradient(135deg, #764ba2 0%, #667eea 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .vulnerability { margin: 30px 0; padding: 25px; border: 2px solid #17a2b8; border-radius: 10px; }
        .visual-changes { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); padding: 25px; border-radius: 8px; margin: 20px 0; border: 2px solid #17a2b8; }
        .image-container { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; background: white; }
        .image-container img { max-width: 500px; border: 2px solid #17a2b8; border-radius: 5px; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; font-weight: bold; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; font-weight: bold; }
        .path-info { font-family: monospace; background: #f8f9fa; padding: 8px; border-radius: 3px; font-size: 12px; }
        h1 { color: white; margin: 0; }
        h2 { color: #17a2b8; }
        h3 { color: #495057; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 التقرير المنفصل المبسط v4</h1>
            <p>اختبار حقيقي للأدلة البصرية</p>
            <p><strong>Test ID:</strong> ${testId}</p>
        </div>
        
        ${vulnerabilities.map((vuln, index) => `
        <div class="vulnerability">
            <h2>📋 تفاصيل الثغرة ${index + 1}: ${vuln.name}</h2>
            
            <div class="visual-changes">
                <h3>🖼️ الأدلة البصرية (Visual Proof)</h3>
                <p><strong>اختبار حقيقي:</strong> هذه أدلة بصرية حقيقية تم إنشاؤها تلقائياً</p>
                
                ${Object.keys(vuln.visual_proof).filter(key => key.endsWith('_screenshot_path')).map(key => {
                    const stage = key.replace('_screenshot_path', '');
                    const imagePath = vuln.visual_proof[key];
                    const stageNames = { before: 'الحالة الأصلية', during: 'عملية الاستغلال', after: 'نتيجة الاستغلال' };
                    
                    return `
                    <div class="image-container">
                        <h4>${stageNames[stage]}:</h4>
                        <div class="path-info">مسار الدليل البصري: ${imagePath}</div>
                        <img src="file:///${imagePath.replace(/\\\\/g, '/')}" alt="${stageNames[stage]}" 
                             onload="this.nextElementSibling.style.display='block'; this.nextElementSibling.nextElementSibling.style.display='none'; console.log('✅ تم تحميل دليل بصري ${stage} في التقرير المنفصل المبسط');"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block'; console.log('❌ فشل في تحميل دليل بصري ${stage} في التقرير المنفصل المبسط');">
                        <div class="success" style="display:none;">✅ تم تحميل الدليل البصري بنجاح - الاختبار نجح!</div>
                        <div class="error" style="display:none;">❌ فشل في تحميل الدليل البصري: ${imagePath}</div>
                    </div>
                    `;
                }).join('')}
            </div>
        </div>
        `).join('')}
    </div>
    
    <script>
        console.log('📄 تم تحميل التقرير المنفصل المبسط v4');
        
        let simpleSeparateStats = { total: 0, loaded: 0, failed: 0 };
        
        document.querySelectorAll('img').forEach(img => {
            simpleSeparateStats.total++;
            
            img.addEventListener('load', function() {
                simpleSeparateStats.loaded++;
                console.log(\`✅ تحميل دليل بصري في التقرير المنفصل المبسط (\${simpleSeparateStats.loaded}/\${simpleSeparateStats.total})\`);
                checkSimpleSeparateComplete();
            });
            
            img.addEventListener('error', function() {
                simpleSeparateStats.failed++;
                console.log(\`❌ فشل تحميل دليل بصري في التقرير المنفصل المبسط (\${simpleSeparateStats.failed}/\${simpleSeparateStats.total})\`);
                checkSimpleSeparateComplete();
            });
        });
        
        function checkSimpleSeparateComplete() {
            if (simpleSeparateStats.loaded + simpleSeparateStats.failed === simpleSeparateStats.total) {
                console.log(\`📊 إحصائيات التقرير المنفصل المبسط: \${simpleSeparateStats.loaded} نجحت، \${simpleSeparateStats.failed} فشلت من أصل \${simpleSeparateStats.total}\`);
                
                if (simpleSeparateStats.loaded === simpleSeparateStats.total) {
                    console.log('🎉 جميع الأدلة البصرية في التقرير المنفصل المبسط تعمل بشكل مثالي!');
                    console.log('✅ الاختبار المبسط للتقرير المنفصل نجح بالكامل!');
                } else if (simpleSeparateStats.loaded > 0) {
                    console.log(\`⚠️ بعض الأدلة البصرية تعمل (\${simpleSeparateStats.loaded}/\${simpleSeparateStats.total}) - الاختبار نجح جزئياً\`);
                } else {
                    console.log('❌ لا توجد أدلة بصرية تعمل - الاختبار فشل');
                }
            }
        }
        
        console.log(\`🖼️ إجمالي الأدلة البصرية في التقرير المنفصل المبسط: \${simpleSeparateStats.total}\`);
    </script>
</body>
</html>
    `;
}

async function testImageDisplay(reports) {
    console.log('🔍 اختبار عرض الصور في التقارير...');
    
    for (const reportPath of reports) {
        if (fs.existsSync(reportPath)) {
            const size = fs.statSync(reportPath).size;
            console.log(`   ✅ ${reportPath} - ${size.toLocaleString()} bytes`);
            
            // فحص محتوى التقرير
            const content = fs.readFileSync(reportPath, 'utf8');
            const imageCount = (content.match(/<img/g) || []).length;
            console.log(`      📸 عدد الصور: ${imageCount}`);
            
        } else {
            console.log(`   ❌ التقرير غير موجود: ${reportPath}`);
        }
    }
}

// تشغيل الاختبار المبسط
if (require.main === module) {
    simpleRealTest().catch(console.error);
}

module.exports = { simpleRealTest };
